############################################################################
#                           GITLAB CI/CD PIPELINE                           #
############################################################################
# Pipeline Behavior:
# - Feature branches: Only validation (build test, no ECR push)
# - develop branch: Build (dev-runner) + push to ECR + deploy to dev environment
# - staging branch: Build (staging-runner) + push to ECR + deploy to staging environment
# - Tags: Build (prod-runner) + push to ECR + deploy to production environment
#
# Runner Strategy:
# - Build and deploy jobs use the same runner for each environment
# - Ensures consistency and shared resources (Docker cache, etc.)
############################################################################

workflow:
  rules:
    # Always run for tags (production releases)
    - if: "$CI_COMMIT_TAG"
      when: always

    # Always run for merge requests
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      when: always

    # Run only when relevant (non-docs) files change on branch pushes
    - changes:
        - backend/**/*
        - frontend/**/*
        - executor/**/*
        - payment/**/*
        - connection-manager/**/*
        - slack-integration/**/*
        - scripts/**/*
        # Compose and CI files impact deployments
        - docker-compose*.yml
        - .gitlab-ci.yml
      when: always

    # Otherwise (e.g., docs-only, *.md-only), skip creating the pipeline
    - when: never

stages:
  - build
  - dev
  - staging
  - production
  # - evaluation

variables:
  # AWS ECR Configuration
  AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-us-east-1}
  ECR_REGISTRY: ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_DEFAULT_REGION}.amazonaws.com
  ECR_REPOSITORY_PREFIX: ${ECR_REPOSITORY_PREFIX:-cloudthinker}

  # Core Configuration
  DOMAIN: ${DOMAIN}
  FRONTEND_HOST: ${FRONTEND_HOST}
  STACK_NAME: ${STACK_NAME}
  PAYMENT_ENABLED: ${PAYMENT_ENABLED}

  # Security & Authentication
  SECRET_KEY: ${SECRET_KEY}
  FIRST_SUPERUSER: ${FIRST_SUPERUSER}
  FIRST_SUPERUSER_PASSWORD: ${FIRST_SUPERUSER_PASSWORD}
  RECAPTCHA_V3_SECRET_KEY: ${RECAPTCHA_V3_SECRET_KEY}
  RECAPTCHA_V3_SITE_KEY: ${RECAPTCHA_V3_SITE_KEY}

  # Email Configuration
  SMTP_HOST: ${SMTP_HOST}
  SMTP_USER: ${SMTP_USER}
  SMTP_PASSWORD: ${SMTP_PASSWORD}
  EMAILS_FROM_EMAIL: ${EMAILS_FROM_EMAIL}

  # Database Configuration
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}

  # Payment Integration
  STRIPE_API_KEY: ${STRIPE_API_KEY}
  STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}

  # Third-Party Integration
  DISCORD_INVITE_LINK: ${DISCORD_INVITE_LINK}

  # Additional Service Configuration (Optional)
  SENTRY_DSN: ${SENTRY_DSN}
  LANGFUSE_SECRET_KEY: ${LANGFUSE_SECRET_KEY}
  LANGFUSE_PUBLIC_KEY: ${LANGFUSE_PUBLIC_KEY}
  LANGFUSE_HOST: ${LANGFUSE_HOST}

  # Slack Integration (Optional)
  SLACK_SIGNING_SECRET: ${SLACK_SIGNING_SECRET}
  SLACK_CLIENT_ID: ${SLACK_CLIENT_ID}
  SLACK_CLIENT_SECRET: ${SLACK_CLIENT_SECRET}
  NEXT_PUBLIC_SLACK_URL: ${NEXT_PUBLIC_SLACK_URL}

# Build and push images to ECR
.build_template: &build_template
  before_script:
    - aws --version
    - docker --version
    - echo "Logging into Amazon ECR..."
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - export IMAGE_TAG=${CI_COMMIT_SHORT_SHA:-latest}
    - echo "Building and pushing images with tag $IMAGE_TAG"

    # Build and push backend image
    - echo "Building backend image..."
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:$IMAGE_TAG ./backend
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:$IMAGE_TAG

    # Build and push frontend image
    - echo "Building frontend image... "
    - echo $DOMAIN
    - echo https://${DOMAIN?Variable not set}
    - echo ${RECAPTCHA_V3_SITE_KEY?Variable not set}
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:$IMAGE_TAG
      --build-arg NEXT_PUBLIC_API_URL=https://${DOMAIN?Variable not set}
      --build-arg NEXT_PUBLIC_RECAPTCHA_V3_SITE_KEY=${RECAPTCHA_V3_SITE_KEY?Variable not set}
      --build-arg NEXT_PUBLIC_SLACK_URL=https://${DOMAIN?Variable not set}
      ./frontend
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:$IMAGE_TAG

    # Build and push executor image
    - echo "Building executor image..."
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:$IMAGE_TAG ./executor
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:$IMAGE_TAG

    # Build and push payment image
    - echo "Building payment image..."
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:$IMAGE_TAG ./payment
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:$IMAGE_TAG

    # Build and push slack-integration image
    - echo "Building slack-integration image..."
    - docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:$IMAGE_TAG ./slack-integration
    - docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:$IMAGE_TAG

    # Tag images as latest for the current environment
    - |
      if [ "$CI_COMMIT_BRANCH" = "main" ] || [ "$CI_COMMIT_BRANCH" = "master" ]; then
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:latest
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:latest
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:latest
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:latest
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:latest

        docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:latest
      fi

.deploy_template: &deploy_template
  before_script:
    - aws --version
    - docker --version
    - echo "Logging into Amazon ECR..."
    - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY
  script:
    - export IMAGE_TAG=${CI_COMMIT_SHORT_SHA:-latest}
    - echo "Deploying with image tag $IMAGE_TAG"

    # Load ECR environment configuration as base
    - cp .env.ecr .env.deploy

    # Override with GitLab CI variables - Core Configuration
    - echo "TAG=$IMAGE_TAG" >> .env.deploy
    - echo "ENVIRONMENT=$ENVIRONMENT" >> .env.deploy
    - echo "DOMAIN=$DOMAIN" >> .env.deploy
    - echo "STACK_NAME=$STACK_NAME" >> .env.deploy
    - echo "PAYMENT_ENABLED=$PAYMENT_ENABLED" >> .env.deploy
    - echo "FRONTEND_HOST=$FRONTEND_HOST" >> .env.deploy

    # Override with GitLab CI variables - Security & Authentication
    - echo "SECRET_KEY=$SECRET_KEY" >> .env.deploy
    - echo "FIRST_SUPERUSER=$FIRST_SUPERUSER" >> .env.deploy
    - echo "FIRST_SUPERUSER_PASSWORD=$FIRST_SUPERUSER_PASSWORD" >> .env.deploy
    - echo "RECAPTCHA_V3_SECRET_KEY=$RECAPTCHA_V3_SECRET_KEY" >> .env.deploy
    - echo "RECAPTCHA_V3_SITE_KEY=$RECAPTCHA_V3_SITE_KEY" >> .env.deploy

    # Override with GitLab CI variables - Email Configuration
    - echo "SMTP_HOST=$SMTP_HOST" >> .env.deploy
    - echo "SMTP_USER=$SMTP_USER" >> .env.deploy
    - echo "SMTP_PASSWORD=$SMTP_PASSWORD" >> .env.deploy
    - echo "EMAILS_FROM_EMAIL=$EMAILS_FROM_EMAIL" >> .env.deploy

    # Override with GitLab CI variables - Database Configuration
    - echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" >> .env.deploy

    # Override with GitLab CI variables - Payment Integration
    - echo "STRIPE_API_KEY=$STRIPE_API_KEY" >> .env.deploy
    - echo "STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET" >> .env.deploy

    # Override with GitLab CI variables - Third-Party Integration
    - echo "DISCORD_INVITE_LINK=$DISCORD_INVITE_LINK" >> .env.deploy

    # Override with GitLab CI variables - Optional Services (if provided)
    - |
      if [ -n "$SENTRY_DSN" ]; then
        echo "SENTRY_DSN=$SENTRY_DSN" >> .env.deploy
      fi
    - |
      if [ -n "$LANGFUSE_SECRET_KEY" ]; then
        echo "LANGFUSE_SECRET_KEY=$LANGFUSE_SECRET_KEY" >> .env.deploy
      fi
    - |
      if [ -n "$LANGFUSE_PUBLIC_KEY" ]; then
        echo "LANGFUSE_PUBLIC_KEY=$LANGFUSE_PUBLIC_KEY" >> .env.deploy
      fi
    - |
      if [ -n "$LANGFUSE_HOST" ]; then
        echo "LANGFUSE_HOST=$LANGFUSE_HOST" >> .env.deploy
      fi

    # Override with GitLab CI variables - Slack Integration (if provided)
    - |
      if [ -n "$SLACK_SIGNING_SECRET" ]; then
        echo "SLACK_SIGNING_SECRET=$SLACK_SIGNING_SECRET" >> .env.deploy
      fi
    - |
      if [ -n "$SLACK_CLIENT_ID" ]; then
        echo "SLACK_CLIENT_ID=$SLACK_CLIENT_ID" >> .env.deploy
      fi
    - |
      if [ -n "$SLACK_CLIENT_SECRET" ]; then
        echo "SLACK_CLIENT_SECRET=$SLACK_CLIENT_SECRET" >> .env.deploy
      fi
    - |
      if [ -n "$NEXT_PUBLIC_SLACK_URL" ]; then
        echo "NEXT_PUBLIC_SLACK_URL=$NEXT_PUBLIC_SLACK_URL" >> .env.deploy
      fi

    # Set Docker image variables to use ECR registry
    - echo "DOCKER_IMAGE_BACKEND=$ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend" >> .env.deploy
    - echo "DOCKER_IMAGE_FRONTEND=$ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend" >> .env.deploy
    - echo "DOCKER_IMAGE_EXECUTOR=$ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor" >> .env.deploy
    - echo "DOCKER_IMAGE_PAYMENT=$ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment" >> .env.deploy
    - echo "DOCKER_IMAGE_SLACK_INTEGRATION=$ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration" >> .env.deploy

    # Debug: Show the TAG being used
    - echo "Deploying with TAG=$IMAGE_TAG"
    - echo "ECR Registry $ECR_REGISTRY"

    # Source the deployment environment
    - set -a && source .env.deploy && set +a

    # Verify images exist before deployment
    - echo "Verifying ECR images exist..."
    - docker pull $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/backend:$TAG
    - docker pull $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/frontend:$TAG
    - docker pull $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/executor:$TAG
    - docker pull $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/payment:$TAG
    - docker pull $ECR_REGISTRY/$ECR_REPOSITORY_PREFIX/slack-integration:$TAG

    # Deploy with docker-compose using ECR environment
    - |
      # Debug: Show the environment variables being used
      echo "Using Docker image variables:"
      grep DOCKER_IMAGE .env.deploy

      # Deploy with docker-compose using ECR environment
      if [ "${PAYMENT_ENABLED}" = "True" ]; then
        docker compose -f docker-compose.yml -f docker-compose.prod.yml -f docker-compose.monitoring.yml -f docker-compose.override.monitoring.yml --env-file .env.deploy \
          --project-name ${STACK_NAME} up -d
      else
        docker compose -f docker-compose.yml -f docker-compose.prod.yml -f docker-compose.monitoring.yml -f docker-compose.override.monitoring.yml --env-file .env.deploy \
          --project-name ${STACK_NAME} up -d --scale payment=0 --scale payment-prestart=0
      fi

# Build stage - separate jobs for each environment using matching runners
build:dev:
  <<: *build_template
  stage: build
  environment: dev
  variables:
    ENVIRONMENT: dev
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
    PAYMENT_ENABLED: ${PAYMENT_ENABLED}
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  tags:
    - dev-runner

build:staging:
  <<: *build_template
  stage: build
  environment: staging
  variables:
    ENVIRONMENT: staging
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
    PAYMENT_ENABLED: ${PAYMENT_ENABLED}
  rules:
    - if: $CI_COMMIT_BRANCH == "staging"
  tags:
    - staging-runner

build:production:
  <<: *build_template
  stage: build
  environment: production
  variables:
    ENVIRONMENT: production
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
    PAYMENT_ENABLED: ${PAYMENT_ENABLED}
  rules:
    - if: $CI_COMMIT_TAG
  tags:
    - prod-runner

dev:
  <<: *deploy_template
  stage: dev
  environment: dev
  variables:
    ENVIRONMENT: dev
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
    PAYMENT_ENABLED: ${PAYMENT_ENABLED}
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
  tags:
    - dev-runner
  needs:
    - build:dev

staging:
  <<: *deploy_template
  stage: staging
  environment: staging
  variables:
    ENVIRONMENT: staging
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
  rules:
    - if: $CI_COMMIT_BRANCH == "staging"
  tags:
    - staging-runner
  needs:
    - build:staging

production:
  <<: *deploy_template
  stage: production
  environment: production
  variables:
    ENVIRONMENT: production
    DOMAIN: ${DOMAIN}
    STACK_NAME: ${STACK_NAME}
    PAYMENT_ENABLED: ${PAYMENT_ENABLED}
  rules:
    - if: $CI_COMMIT_TAG
  tags:
    - prod-runner
  needs:
    - build:production
# evaluation:
#   stage: evaluation
#   script:
#     - |
#       curl -X POST \
#         -F token=$EVALUATION_TRIGGER_TOKEN \
#         -F ref=develop \
#         https://gitlab.com/api/v4/projects/72587656/trigger/pipeline
#   rules:
#     - if: $CI_COMMIT_BRANCH == "develop"
#   needs:
#     - dev
