# Message Duplication Issue Documentation

## Issue Summary
After implementing quota error handling, user messages are being duplicated in the conversation list when quota errors occur early in the stream processing.

## Problem Description
When a user sends a message (e.g., "@<PERSON> hi") and a quota exceeded error occurs at position 3 in the stream, the user message appears twice in the conversation list on the left sidebar.

## Root Cause Analysis

### Current Flow
1. **User sends message** → Frontend adds message to UI immediately
2. **Backend stream processing starts** → `stream_persistence.py:42-66` stores user message in Redis
3. **Quota error occurs early** (position 3) → Stream terminates with quota error
4. **Result**: User message exists both in:
   - Frontend UI (added immediately)
   - Redis stream events (for reconnection context)

### Key Files Involved

#### Backend: `/backend/app/services/agent/stream_persistence.py`
- **Lines 42-66**: User message storage logic
- **Purpose**: Stores user message in Redis for reconnection context
- **Issue**: This creates a duplicate when frontend already shows the message

```python
# Store user message in Redis as a special event for reconnection context
if ctx.user_prompt and ctx.user_prompt.strip():
    position += 1
    
    user_message_event = {
        "event": {
            "type": "user_message",
            "content": ctx.user_prompt,
            "user_id": str(ctx.user_id),
            "timestamp": datetime.datetime.now(UTC).isoformat(),
            "namespace": "user",
        },
        "position": position,
        "message_id": f"user_{uuid.uuid4()}",
    }
    
    redis_manager.queue_stream_event(conversation_id, user_message_event, user_message_event["message_id"])
```

#### Frontend: `/frontend/hooks/use-autonomous-message-stream.ts`
- **Message handling logic**: Processes both immediate UI updates and Redis stream events
- **Potential conflict**: Frontend may be processing the user_message event from Redis when it already has the message in UI

## Reproduction Steps
1. Send a message to trigger AI response
2. Ensure user has exceeded quota (to trigger early termination)
3. Observe the conversation list shows duplicate user messages

## Expected Behavior
User message should appear only once in the conversation list, regardless of when quota errors occur.

## Proposed Solutions

### Option 1: Conditional User Message Storage
Only store user message in Redis if the stream processing gets past a certain point (e.g., after AI starts responding).

**Files to modify**: `backend/app/services/agent/stream_persistence.py`

### Option 2: Frontend Deduplication
Implement message deduplication logic in the frontend to prevent showing duplicate messages with the same content and timestamp.

**Files to modify**: `frontend/hooks/use-autonomous-message-stream.ts`

### Option 3: Message State Management
Improve the coordination between immediate UI updates and Redis stream event processing.

**Files to modify**: Both frontend and backend coordination

## Investigation Questions
1. Is the user message being persisted to the database separately from the Redis stream events?
2. Does the message deduplication happen during normal (non-error) flows?
3. Should user messages be part of the Redis stream events at all, or only AI responses?

## Success Criteria
- ✅ User messages appear only once in conversation list
- ✅ Quota error toast still works correctly
- ✅ Message reconnection functionality remains intact
- ✅ No regression in normal chat flows

## Context Notes
- This issue emerged after implementing quota error handling
- The quota error toast functionality is working correctly and should not be affected
- The duplication only seems to occur when quota errors happen early in the stream

## Related Files
- `backend/app/services/agent/stream_persistence.py` - Main file with user message storage
- `frontend/hooks/use-autonomous-message-stream.ts` - Frontend message handling
- `backend/app/core/redis/redis_manager.py` - Redis event management (potential involvement)

## Priority
**Medium** - UX issue that affects user experience but doesn't break core functionality

---

*Issue documented on: 2025-08-27*
*Context: Quota error handling implementation completed successfully*