---
description:
globs:
alwaysApply: true
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

### Running the Application

You dont need to run the application or writing tests, just follow the user instruction.

### Database Migration

```bash
uv run alembic revision --autogenerate -m "Your migration message"
uv run alembic upgrade heads
```

### Database Management

Execute SQL queries directly against the database:

```bash
make run-sql SQL="YOUR_QUERY_HERE"
```

**Example Queries:**

```bash
# Get active workspaces with owner info
make run-sql SQL="SELECT w.id, w.name, w.provider, u.email as owner_email FROM workspace w JOIN public.user u ON w.owner_id = u.id WHERE w.is_deleted = false"

# User onboarding completion status
make run-sql SQL="SELECT u.email, u.full_name, u.is_active, uo.current_step, uo.is_completed, uo.selected_provider, uo.completed_at, CASE WHEN uo.is_completed THEN 'Completed' WHEN uo.current_step = 1 THEN 'Getting Started' WHEN uo.current_step = 2 THEN 'Provider Selection' WHEN uo.current_step = 3 THEN 'Final Setup' ELSE 'Unknown' END as onboarding_status FROM public.user u LEFT JOIN user_onboarding uo ON u.id = uo.user_id ORDER BY u.created_at DESC"
```

**View Database Logs:**

```bash
make db-logs
```

## Architecture Overview

CloudThinker is a cloud cost optimization platform with a microservices architecture:

### Core Services

- **Backend API** (`backend/`): FastAPI-based REST API with core business logic
- **Executor Service** (`executor/`): AI-powered agents for infrastructure optimization
- **Payment Service** (`payment/`): Stripe-based subscription and payment processing
- **Connection Manager** (`connection-manager/`): Manages cloud provider connections
- **Slack Integration** (`slack-integration/`): Slack workspace integration

### Data Layer

- **PostgreSQL**: Primary database (with separate sandbox database)
- **Redis**: Message broker and caching
- **Qdrant**: Vector database for AI embeddings
- **MinIO**: S3-compatible object storage (commented out, can be enabled)

### Key Application Modules

#### Multi-Agent System (`backend/app/modules/multi_agents/`)

- **Core**: Agent orchestration and factory patterns
- **Agents**: Specialized AI agents for different cloud optimization tasks
- **Tools**: Agent tooling and integrations
- **Prompts**: AI prompt templates and configurations

#### Knowledge Base (`backend/app/modules/knowledge_base/`)

- Document processing and vector storage
- Embedding generation and similarity search

#### Resource Crawlers (`backend/app/modules/resource_crawlers/`)

- Cloud provider resource discovery and monitoring
- Cost data collection and analysis

#### Connectors (`backend/app/modules/connectors/`)

- Cloud provider API integrations
- Authentication and credential management

## Development Environment

### Local URLs

- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Database Admin (Adminer): http://localhost:8080

### Environment Configuration

Key environment variables are defined in `.env`:

- Database credentials (`POSTGRES_*`)
- AWS configuration (`AWS_*`)
- Stripe settings (`STRIPE_*`)
- Redis and other service endpoints

## Code Organization

### Backend Structure

The backend follows a clean architecture pattern with clear separation of concerns:

#### **API Layer** (`app/api/`)

- **`main.py`**: Central router configuration with all API endpoints
- **`deps.py`**: Dependency injection for authentication, database sessions, and common dependencies
- **`routes/`**: Feature-based API endpoints (36 route files)
  - **Authentication**: `login.py`, `google_login.py`, `auth.py`
  - **User Management**: `users.py`, `onboarding.py`
  - **Workspace & Resources**: `workspaces.py`, `resources.py`, `aws_accounts.py`, `gcp_accounts.py`
  - **AI Agents**: `agents.py`, `autonomous_agent.py`, `agent_connections.py`
  - **Knowledge Base**: `kb.py`
  - **Tasks & Templates**: `tasks.py`, `task_templates.py`
  - **Recommendations**: `recommendations.py`
  - **Subscriptions & Billing**: `subscriptions.py`
  - **Attachments & Files**: `attachments.py`, `message_feedback.py`
  - **Monitoring**: `alerts.py`, `notifications.py`, `dashboards.py`
  - **Utilities**: `utils.py`, `console_proxy.py`, `sandbox.py`

#### **Core Infrastructure** (`app/core/`)

- **`config.py`**: Comprehensive settings management with environment-specific configurations
- **`db.py`**: Database connection management with async SQLModel
- **`db_session.py`**: Database session factory and dependency injection
- **`celery_app.py`**: Celery configuration for background task processing
- **`redis/`**: Redis connection and caching utilities
- **`qdrant.py`**: Vector database integration for AI embeddings
- **`security.py`**: Security utilities and JWT token management
- **`activation.py`**: Application activation and licensing
- **`constants.py`**: Application-wide constants and enums

#### **Data Layer**

- **Models** (`app/models/`): SQLModel-based database models (18 model files)

  - **User & Auth**: `users.py`, `tokens.py`, `notifications.py`
  - **Workspace & Resources**: `workspaces.py`, `resources.py`
  - **AI & Agents**: `agents.py`, `conversations.py`, `messages.py`
  - **Knowledge Base**: `knowledge_bases.py`, `document_kbs.py`
  - **Tasks & Workflows**: `tasks.py`
  - **Cloud Integration**: `connections.py`, `clouds.py`, `cloud_sync_config.py`
  - **Analytics**: `recommendations.py`, `reports.py`, `dashboards.py`

- **Repositories** (`app/repositories/`): Data access layer with repository pattern

  - **`base.py`**: Abstract repository interface and base implementation
  - **Feature-specific repositories**: 23 repository files covering all data entities
  - **`object_storage/`**: S3/MinIO file storage abstractions

- **Schemas** (`app/schemas/`): Pydantic request/response models
  - **API Schemas**: Request/response models for all endpoints
  - **`mime_type.py`**: Comprehensive MIME type handling
  - **`token_usage.py`**: Token tracking and quota management

#### **Business Logic Layer**

- **Services** (`app/services/`): Business logic and orchestration
  - **Core Services**: `user_service.py`, `workspace_service.py`, `task_service.py`
  - **AI Services**: `agents_service.py`, `conversation_service.py`
  - **Cloud Integration**: `aws_account_service.py`, `gcp_account_service.py`, `cloud_sync_config_service.py`
  - **Knowledge Base**: `kb_service_legacy.py`
  - **File Management**: `attachment_service.py`
  - **Payment & Billing**: `payment_service.py`, `token_usage_service.py`
  - **Agent Services**: `agent/` directory with specialized agent services
  - **Memory Services**: `memory/` directory for AI memory management
  - **Security Services**: `security/` directory for authentication/authorization

#### **Background Processing**

- **Tasks** (`app/tasks/`): Celery background tasks
  - **Agent Tasks**: `autonomous_agent_tasks.py`, `continue_agent_tasks.py`
  - **Cloud Operations**: `multicloud_scan_tasks.py`, `cloud_sync_scheduler_tasks.py`
  - **Knowledge Base**: `kb_tasks.py`, `attachment_tasks.py`
  - **User Management**: `user_onboarding_tasks.py`, `notification_tasks.py`
  - **Scheduling**: `shedule_task.py`, `template_tasks.py`

#### **Feature Modules** (`app/modules/`)

- **Multi-Agents** (`multi_agents/`): AI agent orchestration system

  - **`core/`**: Agent factory, configuration, and state management
  - **`agents/`**: Specialized agents (conversation, coordinator)
  - **`tools/`**: Agent tooling and builtin tools management
  - **`prompts/`**: AI prompt templates and configurations
  - **`config/`**: Agent-specific configurations
  - **`common/`**: Shared agent utilities

- **Knowledge Base** (`knowledge_base/`): Document processing and AI embeddings

  - **`readers/`**: Document parsing and processing
  - **`crawlers/`**: Web crawling and content extraction

- **Connectors** (`connectors/`): External service integrations

  - **`mcp_client/`**: Model Context Protocol client
  - **`connection_client/`**: Cloud provider connection management

- **Resource Crawlers** (`resource_crawlers/`): Cloud resource discovery
- **Payment** (`payment/`): Payment processing and billing

#### **Supporting Infrastructure**

- **Exceptions** (`app/exceptions/`): Custom exception classes for error handling
- **Schedulers** (`app/schedulers/`): Task scheduling and cron job management
- **Email Templates** (`email-templates/`): MJML-based email templates
- **Constants** (`app/constants.py`): Application-wide constants
- **Logger** (`app/logger.py`): Centralized logging configuration
- **Utils** (`app/utils.py`): Common utilities and helpers

### Key Application Files

- **`app/main.py`**: FastAPI application entry point with lifespan management, CORS, and middleware setup
- **`app/worker.py`**: Celery worker configuration with automatic task discovery
- **`app/backend_pre_start.py`**: Application startup scripts and initialization
- **`app/healthcheck.py`**: Health check endpoints for monitoring
- **`app/crud.py`**: Legacy CRUD operations (being replaced by repositories)
- **`app/initial_data.py`**: Database seeding and initial data setup

### Architecture Patterns

- **Repository Pattern**: Abstract data access with `IRepository` interface and `BaseRepository` implementation
- **Dependency Injection**: FastAPI dependency injection for services, database sessions, and authentication
- **Service Layer**: Business logic encapsulation in service classes
- **Background Processing**: Celery-based task queue for async operations
- **Modular Design**: Feature-based modules for scalability and maintainability
- **Type Safety**: Comprehensive type hints and Pydantic validation throughout

## Development Guidelines

### Repository Pattern Implementation

**Important**: The `IRepository` interface and `BaseRepository` implementation exist in `app/repositories/base.py`, but only a few files currently apply this pattern. When creating new repositories:

- **New repositories MUST follow the `IRepository` interface and extend `BaseRepository`**
- **For existing repositories**: Follow the current code pattern but recommend refactoring to use the repository pattern for consistency

**Example of proper repository implementation:**

```python
from app.repositories.base import BaseRepository
from app.models.your_model import YourModel
from app.schemas.your_schema import YourCreate, YourUpdate

class YourRepository(BaseRepository[YourModel, YourCreate, YourUpdate]):
    def __init__(self, async_session: AsyncSession):
        super().__init__(YourModel, async_session)

    # Add custom methods specific to your model
    async def get_by_custom_field(self, field_value: str) -> YourModel | None:
        statement = select(self.model).where(self.model.custom_field == field_value)
        result = await self.async_session.exec(statement)
        return result.first()
```

### Feature Relationship Analysis

**Before coding new features, agents MUST:**

1. **Analyze existing relationships** between features to understand dependencies
2. **Identify if existing methods/functions can be leveraged** instead of creating new ones
3. **Propose 2-3 alternative solutions** before implementing any code
4. **Consider the impact on related modules** (services, repositories, schemas, etc.)

**Example analysis process:**

- Check if similar functionality exists in other services
- Review related models and their relationships
- Examine existing API patterns for consistency
- Consider if the feature should be a new endpoint or extend existing ones

### Bug Fixing Approach

**When fixing bugs, agents MUST:**

1. **Analyze the root cause** of the bug thoroughly
2. **Identify bad practices** in the surrounding code that may contribute to similar issues
3. **Propose comprehensive solutions** that address both the immediate bug and underlying issues
4. **Consider refactoring opportunities** to prevent similar bugs in the future

**Example bug analysis:**

- Identify the specific error and its location
- Review related code for similar patterns that could cause issues
- Propose fixes for both the immediate problem and potential improvements
- Suggest tests to prevent regression

### Frontend API Synchronization

**When backend API changes are made, agents MUST:**

1. **Run the API generation command** to update frontend types:

   ```bash
   cd frontend
   pnpm gen:local
   ```

2. **Verify the updated file** `frontend/openapi-ts/gens.ts` contains the new API definitions

3. **Update frontend code** to use the new generated types and API endpoints

**Important Notes:**

- The `gens.ts` file is auto-generated and should not be manually edited
- Always run the generation command after backend API changes
- Ensure frontend components use the updated types for type safety
- Check for breaking changes that might affect existing frontend functionality

### SQLModel/SQLAlchemy typing fixes (for linters)

- Prefer precise casts over `Any`:
  - Use `col(Model.field)` for `.in_`, `.ilike`, comparisons, and when passing to `func.sum`.
  - Example: `col(Recommendation.resource_id).in_(ids)`.
- Aggregations and labels:
  - `func.sum(cast("ColumnElement[float]", Model.numeric_field)).label("name")`.
  - Use `func.distinct(Model.id)` instead of `Model.id.distinct()`.
- Delete statements:
  - Use `await async_session.execute(delete_stmt)` instead of `exec(delete_stmt)`.
- Row access from aggregate queries:
  - Treat results as tuples: access via `row[0]`, `row[1]` rather than attributes.
- Enum and string fields:
  - Use `col()` before `.in_`: `col(Resource.status).in_(statuses)`.
  - Use `col()` before `.ilike`: `col(Resource.name).ilike("%q%")`.
- Guard optional IDs when building dicts:
  - Skip `None` UUIDs before using as dict keys.
- Cache toggling pattern:
  - Add `CACHE_ENABLED` class flag, constructor `cache_enabled` override, and wrapper methods `_get_from_cache`, `_set_cache`, `_delete_cache_pattern` that no-op when disabled.
- Import hygiene:
  - Add any missing enums (e.g., `ResourceStatus`) used in casts.
  - Keep imports sorted to avoid style warnings.
