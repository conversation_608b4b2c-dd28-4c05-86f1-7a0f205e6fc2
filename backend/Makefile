# Backend Makefile for Cloud Cost Optimization Platform
# This file provides convenient commands for development and deployment tasks

.PHONY: help install migrate migrate-all migrate-shortcuts migrate-connections migrate-tools run db-logs db-shell test clean lint format

# Default target
help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'

# Database migrations
migrate: ## Run Alembic database migrations
	uv run alembic upgrade heads

migrate-shortcuts: ## Run default shortcuts migration
	uv run python -m app.migrate_default_shortcuts

migrate-connections: ## Run builtin connections migration
	uv run python -m app.migrate_builtin_connection

migrate-tools: ## Run builtin tools migration
	uv run python -m app.migrate_builtin_tools

migrate-all: migrate migrate-shortcuts migrate-connections migrate-tools ## Run all migrations (Alembic + custom migrations)

# Development
run: ## Run the FastAPI application
	uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

run-worker: ## Run Celery worker
	uv run celery -A app.worker worker --loglevel=info
