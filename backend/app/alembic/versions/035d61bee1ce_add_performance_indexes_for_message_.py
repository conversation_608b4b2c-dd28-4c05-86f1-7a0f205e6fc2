"""Add performance indexes for message queries

Revision ID: 035d61bee1ce
Revises: 9253e50d232c
Create Date: 2025-08-14 16:00:53.465066

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '035d61bee1ce'
down_revision = '9253e50d232c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text
    
    # Check and create indexes only if they don't exist
    conn = op.get_bind()
    
    # Check ix_message_conversation_deleted_created
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_conversation_deleted_created'")).fetchone()
    if not result:
        op.create_index('ix_message_conversation_deleted_created', 'message', ['conversation_id', 'is_deleted', 'created_at'], unique=False)
    
    # Check ix_message_is_deleted
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_is_deleted'")).fetchone()
    if not result:
        op.create_index(op.f('ix_message_is_deleted'), 'message', ['is_deleted'], unique=False)
    
    # Check ix_message_attachments_message_id
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_attachments_message_id'")).fetchone()
    if not result:
        op.create_index(op.f('ix_message_attachments_message_id'), 'message_attachments', ['message_id'], unique=False)
    
    # Check ix_messagecomponent_message_id
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_messagecomponent_message_id'")).fetchone()
    if not result:
        op.create_index(op.f('ix_messagecomponent_message_id'), 'messagecomponent', ['message_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text
    
    # Check and drop indexes only if they exist
    conn = op.get_bind()
    
    # Check and drop ix_messagecomponent_message_id
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_messagecomponent_message_id'")).fetchone()
    if result:
        op.drop_index(op.f('ix_messagecomponent_message_id'), table_name='messagecomponent')
    
    # Check and drop ix_message_attachments_message_id
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_attachments_message_id'")).fetchone()
    if result:
        op.drop_index(op.f('ix_message_attachments_message_id'), table_name='message_attachments')
    
    # Check and drop ix_message_is_deleted
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_is_deleted'")).fetchone()
    if result:
        op.drop_index(op.f('ix_message_is_deleted'), table_name='message')
    
    # Check and drop ix_message_conversation_deleted_created
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_conversation_deleted_created'")).fetchone()
    if result:
        op.drop_index('ix_message_conversation_deleted_created', table_name='message')
    # ### end Alembic commands ###
