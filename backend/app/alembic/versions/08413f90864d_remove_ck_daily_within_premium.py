"""remove ck_daily_within_premium

Revision ID: 08413f90864d
Revises: 1af9dbfb7299
Create Date: 2025-08-26 17:34:29.915826

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '08413f90864d'
down_revision = '1af9dbfb7299'
branch_labels = None
depends_on = None


def upgrade():
    """Remove the ck_daily_within_premium constraint from usage_quota table."""
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_daily_within_premium")


def downgrade():
    """Re-add the ck_daily_within_premium constraint to usage_quota table."""
    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_daily_within_premium
        CHECK (daily_messages_used <= quota_used_messages OR quota_used_messages = -1)
    """)
