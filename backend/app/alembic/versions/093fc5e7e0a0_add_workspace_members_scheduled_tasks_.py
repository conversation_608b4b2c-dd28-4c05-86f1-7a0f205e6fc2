"""Add workspace, members, scheduled_tasks, and kb_storage fields to usage_quota

Revision ID: 093fc5e7e0a0
Revises: 7f4589759d04
Create Date: 2025-08-20 09:47:17.900588

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '093fc5e7e0a0'
down_revision = '7f4589759d04'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add columns as nullable first, then set defaults and make non-nullable
    op.add_column('usage_quota', sa.Column('workspaces_used', sa.Integer(), nullable=True))
    op.add_column('usage_quota', sa.Column('members_used', sa.Integer(), nullable=True))
    op.add_column('usage_quota', sa.Column('scheduled_tasks_used', sa.Integer(), nullable=True))
    op.add_column('usage_quota', sa.Column('kb_storage_used', sa.Integer(), nullable=True))
    
    # Set default values for existing records
    op.execute("UPDATE usage_quota SET workspaces_used = 0 WHERE workspaces_used IS NULL")
    op.execute("UPDATE usage_quota SET members_used = 0 WHERE members_used IS NULL")
    op.execute("UPDATE usage_quota SET scheduled_tasks_used = 0 WHERE scheduled_tasks_used IS NULL")
    op.execute("UPDATE usage_quota SET kb_storage_used = 0 WHERE kb_storage_used IS NULL")
    
    # Make columns non-nullable
    op.alter_column('usage_quota', 'workspaces_used', nullable=False)
    op.alter_column('usage_quota', 'members_used', nullable=False)
    op.alter_column('usage_quota', 'scheduled_tasks_used', nullable=False)
    op.alter_column('usage_quota', 'kb_storage_used', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('usage_quota', 'kb_storage_used')
    op.drop_column('usage_quota', 'scheduled_tasks_used')
    op.drop_column('usage_quota', 'members_used')
    op.drop_column('usage_quota', 'workspaces_used')
    # ### end Alembic commands ###
