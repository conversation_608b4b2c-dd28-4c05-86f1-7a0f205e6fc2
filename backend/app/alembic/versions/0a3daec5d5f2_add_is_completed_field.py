"""add is completed field

Revision ID: 0a3daec5d5f2
Revises: d5f4e132b259
Create Date: 2025-08-18 15:24:14.220804

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '0a3daec5d5f2'
down_revision = 'd5f4e132b259'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messagetoolcomponent', sa.Column('is_completed', sa.<PERSON>(), nullable=False, server_default='true'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messagetoolcomponent', 'is_completed')
    # ### end Alembic commands ###
