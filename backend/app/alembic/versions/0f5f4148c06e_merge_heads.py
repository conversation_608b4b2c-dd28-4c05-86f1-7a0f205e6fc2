"""Me<PERSON> heads

Revision ID: 0f5f4148c06e
Revises: 0a3daec5d5f2, 27793d861402
Create Date: 2025-08-19 23:07:32.271453

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '0f5f4148c06e'
down_revision = ('0a3daec5d5f2', '27793d861402')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
