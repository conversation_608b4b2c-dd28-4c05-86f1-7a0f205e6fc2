"""add_usage_quota_constraints

Revision ID: 1af9dbfb7299
Revises: 6ecd486d2e0e
Create Date: 2025-08-26 13:49:43.489629

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '1af9dbfb7299'
down_revision = '6ecd486d2e0e'
branch_labels = None
depends_on = None


def upgrade():
    """Add database constraints to usage_quota table for data integrity."""
    # Clean existing data to satisfy new constraints
    op.execute(
        """
        UPDATE usage_quota SET
            quota_used_messages = COALESCE(GREATEST(quota_used_messages, 0), 0),
            quota_used_tokens = COALESCE(GREATEST(quota_used_tokens, 0), 0),
            daily_messages_used = COALESCE(GREATEST(daily_messages_used, 0), 0),
            workspaces_used = COALESCE(GREATEST(workspaces_used, 0), 0),
            members_used = COALESCE(GREATEST(members_used, 0), 0),
            scheduled_tasks_used = COALESCE(GREATEST(scheduled_tasks_used, 0), 0),
            kb_storage_used = COALESCE(GREATEST(kb_storage_used, 0), 0)
        """
    )

    # Ensure logical relationship holds before adding the constraint
    op.execute(
        """
        UPDATE usage_quota
        SET daily_messages_used = LEAST(daily_messages_used, quota_used_messages)
        WHERE quota_used_messages <> -1
        """
    )

    # Add positive value constraints
    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_quota_used_messages
        CHECK (quota_used_messages >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_quota_used_tokens
        CHECK (quota_used_tokens >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_daily_messages_used
        CHECK (daily_messages_used >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_workspaces_used
        CHECK (workspaces_used >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_members_used
        CHECK (members_used >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_scheduled_tasks_used
        CHECK (scheduled_tasks_used >= 0)
    """)

    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_positive_kb_storage_used
        CHECK (kb_storage_used >= 0)
    """)

    # Add logical relationship constraints
    op.execute("""
        ALTER TABLE usage_quota
        ADD CONSTRAINT ck_daily_within_premium
        CHECK (daily_messages_used <= quota_used_messages OR quota_used_messages = -1)
    """)


def downgrade():
    """Remove database constraints from usage_quota table."""
    # Remove logical relationship constraints
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_daily_within_premium")

    # Remove positive value constraints
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_kb_storage_used")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_scheduled_tasks_used")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_members_used")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_workspaces_used")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_daily_messages_used")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_quota_used_tokens")
    op.execute("ALTER TABLE usage_quota DROP CONSTRAINT IF EXISTS ck_positive_quota_used_messages")
