"""Add total_document and total_size fields to knowledge_bases table

Revision ID: 1fc604ba7a1a
Revises: d5f4e132b259
Create Date: 2025-08-19 05:28:51.601147

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '1fc604ba7a1a'
down_revision = 'd5f4e132b259'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add columns as nullable first
    op.add_column('knowledge_bases', sa.Column('total_document', sa.Integer(), nullable=True))
    op.add_column('knowledge_bases', sa.Column('total_size', sa.Integer(), nullable=True))
    
    # Set default values for existing records
    op.execute("UPDATE knowledge_bases SET total_document = 0 WHERE total_document IS NULL")
    op.execute("UPDATE knowledge_bases SET total_size = 0 WHERE total_size IS NULL")
    
    # Now make the columns non-nullable
    op.alter_column('knowledge_bases', 'total_document', nullable=False)
    op.alter_column('knowledge_bases', 'total_size', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('knowledge_bases', 'total_size')
    op.drop_column('knowledge_bases', 'total_document')
    # ### end Alembic commands ###
