"""change kb_storage_used from int to float

Revision ID: 20c9c18d0e94
Revises: 093fc5e7e0a0
Create Date: 2025-08-21 18:34:56.047929

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '20c9c18d0e94'
down_revision = '093fc5e7e0a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('usage_quota', 'kb_storage_used',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('usage_quota', 'kb_storage_used',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###
