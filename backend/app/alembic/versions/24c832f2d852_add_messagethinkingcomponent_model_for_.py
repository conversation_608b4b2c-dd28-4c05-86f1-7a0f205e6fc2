"""Add MessageThinkingComponent model for storing thinking content

Revision ID: 24c832f2d852
Revises: 20c9c18d0e94
Create Date: 2025-08-21 18:56:59.485431

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '24c832f2d852'
down_revision = '20c9c18d0e94'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('messagethinkingcomponent',
    sa.<PERSON>umn('id', sa.Uuid(), nullable=False),
    sa.<PERSON>umn('message_component_id', sa.Uuid(), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('thinking_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['message_component_id'], ['messagecomponent.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('messagethinkingcomponent')
    # ### end Alembic commands ###
