"""rename messages to credits

Revision ID: 257ac400d7ad
Revises: efe60e7dbcff
Create Date: 2025-08-28 16:44:41.675385

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '257ac400d7ad'
down_revision = 'efe60e7dbcff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new columns as nullable initially
    op.add_column('usage_quota', sa.Column('credit_used', sa.Integer(), nullable=True))
    op.add_column('usage_quota', sa.Column('daily_credit_used', sa.Integer(), nullable=True))

    # Copy data from old columns to new columns
    op.execute('UPDATE usage_quota SET credit_used = quota_used_messages WHERE credit_used IS NULL')
    op.execute('UPDATE usage_quota SET daily_credit_used = daily_messages_used WHERE daily_credit_used IS NULL')

    # Make the new columns NOT NULL
    op.alter_column('usage_quota', 'credit_used', nullable=False)
    op.alter_column('usage_quota', 'daily_credit_used', nullable=False)

    # Drop old indexes
    op.drop_index('idx_usage_quota_user_daily', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_premium', table_name='usage_quota')

    # Create new indexes with updated column names
    op.create_index('idx_usage_quota_user_daily', 'usage_quota', ['user_id', 'daily_credit_used'], unique=False)
    op.create_index('idx_usage_quota_user_premium', 'usage_quota', ['user_id', 'credit_used'], unique=False)

    # Drop old columns
    op.drop_column('usage_quota', 'quota_used_messages')
    op.drop_column('usage_quota', 'daily_messages_used')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add old columns as nullable initially
    op.add_column('usage_quota', sa.Column('daily_messages_used', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('usage_quota', sa.Column('quota_used_messages', sa.INTEGER(), autoincrement=False, nullable=True))

    # Copy data from new columns to old columns
    op.execute('UPDATE usage_quota SET quota_used_messages = credit_used WHERE quota_used_messages IS NULL')
    op.execute('UPDATE usage_quota SET daily_messages_used = daily_credit_used WHERE daily_messages_used IS NULL')

    # Make the old columns NOT NULL
    op.alter_column('usage_quota', 'quota_used_messages', nullable=False)
    op.alter_column('usage_quota', 'daily_messages_used', nullable=False)

    # Drop new indexes
    op.drop_index('idx_usage_quota_user_premium', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_daily', table_name='usage_quota')

    # Create old indexes with original column names
    op.create_index('idx_usage_quota_user_premium', 'usage_quota', ['user_id', 'quota_used_messages'], unique=False)
    op.create_index('idx_usage_quota_user_daily', 'usage_quota', ['user_id', 'daily_messages_used'], unique=False)

    # Drop new columns
    op.drop_column('usage_quota', 'daily_credit_used')
    op.drop_column('usage_quota', 'credit_used')
    # ### end Alembic commands ###
