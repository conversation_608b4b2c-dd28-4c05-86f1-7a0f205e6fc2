"""Add file_size field to document_kbs table

Revision ID: 27793d861402
Revises: 1fc604ba7a1a
Create Date: 2025-08-18 17:12:24.520095

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '27793d861402'
down_revision = '1fc604ba7a1a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('document_kbs', sa.Column('file_size', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('document_kbs', 'file_size')
    # ### end Alembic commands ###
