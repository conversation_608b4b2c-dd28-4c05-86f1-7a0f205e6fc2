"""add shortcut table

Revision ID: 3861399560c0
Revises: b93fcd32b254
Create Date: 2025-08-28 22:37:43.286065

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3861399560c0'
down_revision = 'b93fcd32b254'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('shortcut',
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('slug', sa.String(length=255), nullable=False),
    sa.Column('content', sa.String(length=1000), nullable=False),
    sa.Column('category', sa.String(length=255), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('workspace_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('workspace_id', 'slug', name='uq_shortcut_workspace_slug')
    )
    op.create_index(op.f('ix_shortcut_is_default'), 'shortcut', ['is_default'], unique=False)
    op.create_index(op.f('ix_shortcut_user_id'), 'shortcut', ['user_id'], unique=False)
    op.create_index(op.f('ix_shortcut_workspace_id'), 'shortcut', ['workspace_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_shortcut_workspace_id'), table_name='shortcut')
    op.drop_index(op.f('ix_shortcut_user_id'), table_name='shortcut')
    op.drop_index(op.f('ix_shortcut_is_default'), table_name='shortcut')
    op.drop_table('shortcut')
    # ### end Alembic commands ###
