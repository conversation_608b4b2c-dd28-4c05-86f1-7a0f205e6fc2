"""populate_usage_quota_fields_for_existing_users

Revision ID: 53883d6fb5f1
Revises: 24c832f2d852
Create Date: 2025-08-22 10:00:32.302491

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '53883d6fb5f1'
down_revision = '24c832f2d852'
branch_labels = None
depends_on = None


def upgrade():

    # Populate workspaces_used: count workspaces where user is owner or member
    op.execute("""
        UPDATE usage_quota
        SET workspaces_used = COALESCE((
            SELECT COUNT(DISTINCT w.id)
            FROM workspace w
            LEFT JOIN userworkspace uw ON w.id = uw.workspace_id
            WHERE w.is_deleted = false
            AND (w.owner_id = usage_quota.user_id OR uw.user_id = usage_quota.user_id)
        ), 0)
    """)

    # Populate members_used: count unique users in workspaces where user is owner
    op.execute("""
        UPDATE usage_quota
        SET members_used = COALESCE((
            SELECT COUNT(DISTINCT uw.user_id)
            FROM workspace w
            LEFT JOIN userworkspace uw ON w.id = uw.workspace_id
            WHERE w.is_deleted = false
            AND w.owner_id = usage_quota.user_id
        ), 0)
    """)

    # Populate scheduled_tasks_used: count scheduled tasks where user is owner
    op.execute("""
        UPDATE usage_quota
        SET scheduled_tasks_used = COALESCE((
            SELECT COUNT(*)
            FROM task t
            JOIN workspace w ON t.workspace_id = w.id
            WHERE w.is_deleted = false
            AND t.owner_id = usage_quota.user_id
            AND t.scheduled_status = 'SCHEDULED'
        ), 0)
    """)


def downgrade():
    # Reset fields back to 0
    op.execute("UPDATE usage_quota SET workspaces_used = 0")
    op.execute("UPDATE usage_quota SET members_used = 0")
    op.execute("UPDATE usage_quota SET scheduled_tasks_used = 0")
