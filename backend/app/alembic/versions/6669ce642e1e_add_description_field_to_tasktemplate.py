"""Add description field to TaskTemplate

Revision ID: 6669ce642e1e
Revises: 53883d6fb5f1
Create Date: 2025-08-24 13:17:08.643890

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6669ce642e1e'
down_revision = '53883d6fb5f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tasktemplate', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tasktemplate', 'description')
    # ### end Alembic commands ###
