"""add_composite_indexes_to_usage_quota

Revision ID: 6ecd486d2e0e
Revises: f4608102492f
Create Date: 2025-08-26 13:27:59.800159

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '6ecd486d2e0e'
down_revision = 'f4608102492f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_usage_quota_daily_reset', 'usage_quota', ['daily_messages_reset_at'], unique=False)
    op.create_index('idx_usage_quota_user_daily', 'usage_quota', ['user_id', 'daily_messages_used'], unique=False)
    op.create_index('idx_usage_quota_user_members', 'usage_quota', ['user_id', 'members_used'], unique=False)
    op.create_index('idx_usage_quota_user_premium', 'usage_quota', ['user_id', 'quota_used_messages'], unique=False)
    op.create_index('idx_usage_quota_user_scheduled', 'usage_quota', ['user_id', 'scheduled_tasks_used'], unique=False)
    op.create_index('idx_usage_quota_user_storage', 'usage_quota', ['user_id', 'kb_storage_used'], unique=False)
    op.create_index('idx_usage_quota_user_workspaces', 'usage_quota', ['user_id', 'workspaces_used'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_usage_quota_user_workspaces', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_storage', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_scheduled', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_premium', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_members', table_name='usage_quota')
    op.drop_index('idx_usage_quota_user_daily', table_name='usage_quota')
    op.drop_index('idx_usage_quota_daily_reset', table_name='usage_quota')
    # ### end Alembic commands ###
