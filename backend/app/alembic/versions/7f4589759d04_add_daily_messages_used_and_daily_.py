"""add daily_messages_used and daily_messages_reset_at for usage_quota table

Revision ID: 7f4589759d04
Revises: 0f5f4148c06e
Create Date: 2025-08-19 23:16:11.060690

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '7f4589759d04'
down_revision = '0f5f4148c06e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text
    
    # Check if index exists before dropping it
    conn = op.get_bind()
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_attachments_owner_id'")).fetchone()
    if result:
        op.drop_index('ix_message_attachments_owner_id', table_name='message_attachments')
    
    # Add columns as nullable first
    op.add_column('usage_quota', sa.Column('daily_messages_used', sa.Integer(), nullable=True))
    op.add_column('usage_quota', sa.Column('daily_messages_reset_at', sa.DateTime(timezone=True), nullable=True))
    
    # Update existing rows with default values
    op.execute("UPDATE usage_quota SET daily_messages_used = 0 WHERE daily_messages_used IS NULL")
    op.execute("UPDATE usage_quota SET daily_messages_reset_at = NOW() WHERE daily_messages_reset_at IS NULL")
    
    # Make columns NOT NULL
    op.alter_column('usage_quota', 'daily_messages_used', nullable=False)
    op.alter_column('usage_quota', 'daily_messages_reset_at', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    from sqlalchemy import text
    
    op.drop_column('usage_quota', 'daily_messages_reset_at')
    op.drop_column('usage_quota', 'daily_messages_used')
    
    # Check if index exists before creating it
    conn = op.get_bind()
    result = conn.execute(text("SELECT 1 FROM pg_indexes WHERE indexname = 'ix_message_attachments_owner_id'")).fetchone()
    if not result:
        op.create_index('ix_message_attachments_owner_id', 'message_attachments', ['owner_id'], unique=False)
    # ### end Alembic commands ###
