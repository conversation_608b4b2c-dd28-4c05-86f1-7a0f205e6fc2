"""add_query_sandbox_builtin_tool

Revision ID: af017cc7bf5c
Revises: b91ba669f6de
Create Date: 2025-09-01 22:14:49.507930

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from uuid import uuid4


# revision identifiers, used by Alembic.
revision = 'af017cc7bf5c'
down_revision = 'b91ba669f6de'
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Add query_sandbox builtin tool
    query_sandbox_tool = {
        "name": "query_sandbox",
        "display_name": "Query Sandbox",
        "description": "Built-in tool for querying CSV data stored in the sandbox database",
        "default_required_permission": False,
    }

    # Insert the query_sandbox tool
    conn.execute(
        sa.text(
            """
            INSERT INTO builtintool (id, name, display_name, description, default_required_permission, created_at, updated_at)
            VALUES (:id, :name, :display_name, :description, :default_required_permission, NOW(), NOW())
            ON CONFLICT (name) DO UPDATE
            SET display_name = EXCLUDED.display_name,
                description = EXCLUDED.description,
                default_required_permission = EXCLUDED.default_required_permission,
                updated_at = NOW()
            """
        ),
        {
            "id": str(uuid4()),
            "name": query_sandbox_tool["name"],
            "display_name": query_sandbox_tool["display_name"],
            "description": query_sandbox_tool["description"],
            "default_required_permission": query_sandbox_tool["default_required_permission"],
        }
    )

    # Add query_sandbox tool to all existing workspaces
    tool_result = conn.execute(
        sa.text("SELECT id FROM builtintool WHERE name = 'query_sandbox'")
    ).fetchone()

    if tool_result:
        tool_id = tool_result[0]
        workspaces = conn.execute(sa.text("SELECT id FROM workspace")).fetchall()

        for workspace in workspaces:
            workspace_id = workspace[0]

            # Check if this workspace-tool combination already exists
            exists = conn.execute(
                sa.text(
                    """
                    SELECT 1 FROM workspacebuiltintool
                    WHERE workspace_id = :workspace_id AND builtin_tool_id = :tool_id
                    """
                ),
                {"workspace_id": workspace_id, "tool_id": tool_id}
            ).fetchone()

            if not exists:
                # Insert the new workspace-tool relationship
                conn.execute(
                    sa.text(
                        """
                        INSERT INTO workspacebuiltintool (id, workspace_id, builtin_tool_id, required_permission)
                        VALUES (:id, :workspace_id, :tool_id, :required_permission)
                        """
                    ),
                    {
                        "id": str(uuid4()),
                        "workspace_id": workspace_id,
                        "tool_id": tool_id,
                        "required_permission": query_sandbox_tool["default_required_permission"]
                    }
                )

    # Add query_sandbox tool to agents that should have it by default
    # Based on the default agent configurations: Anna, Alex, and Oliver
    agent_aliases_with_query_sandbox = ['anna', 'alex', 'oliver']

    for alias in agent_aliases_with_query_sandbox:
        # Get agents with this alias across all workspaces
        agents = conn.execute(
            sa.text("SELECT id, workspace_id FROM agent WHERE alias = :alias"),
            {"alias": alias}
        ).fetchall()

        for agent in agents:
            agent_id = agent[0]
            workspace_id = agent[1]

            # Get the workspace builtin tool id for query_sandbox in this workspace
            workspace_tool = conn.execute(
                sa.text(
                    """
                    SELECT wbt.id FROM workspacebuiltintool wbt
                    JOIN builtintool bt ON wbt.builtin_tool_id = bt.id
                    WHERE wbt.workspace_id = :workspace_id AND bt.name = 'query_sandbox'
                    """
                ),
                {"workspace_id": workspace_id}
            ).fetchone()

            if workspace_tool:
                workspace_tool_id = workspace_tool[0]

                # Check if agent already has this tool
                exists = conn.execute(
                    sa.text(
                        """
                        SELECT 1 FROM agent_builtin_tools
                        WHERE agent_id = :agent_id AND workspace_builtin_tool_id = :workspace_tool_id
                        """
                    ),
                    {"agent_id": agent_id, "workspace_tool_id": workspace_tool_id}
                ).fetchone()

                if not exists:
                    # Add the tool to the agent with is_active=True
                    conn.execute(
                        sa.text(
                            """
                            INSERT INTO agent_builtin_tools (agent_id, workspace_builtin_tool_id, is_active, created_at, updated_at)
                            VALUES (:agent_id, :workspace_tool_id, :is_active, NOW(), NOW())
                            """
                        ),
                        {
                            "agent_id": agent_id,
                            "workspace_tool_id": workspace_tool_id,
                            "is_active": True
                        }
                    )


def downgrade():
    conn = op.get_bind()

    # Remove query_sandbox tool from agent_builtin_tools
    conn.execute(
        sa.text(
            """
            DELETE FROM agent_builtin_tools
            WHERE workspace_builtin_tool_id IN (
                SELECT wbt.id FROM workspacebuiltintool wbt
                JOIN builtintool bt ON wbt.builtin_tool_id = bt.id
                WHERE bt.name = 'query_sandbox'
            )
            """
        )
    )

    # Remove query_sandbox tool from workspacebuiltintool
    conn.execute(
        sa.text(
            """
            DELETE FROM workspacebuiltintool
            WHERE builtin_tool_id IN (
                SELECT id FROM builtintool WHERE name = 'query_sandbox'
            )
            """
        )
    )

    # Remove query_sandbox tool from builtintool
    conn.execute(
        sa.text("DELETE FROM builtintool WHERE name = 'query_sandbox'")
    )
