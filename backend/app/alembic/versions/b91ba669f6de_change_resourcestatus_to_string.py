"""change ResourceStatus to string

Revision ID: b91ba669f6de
Revises: f201c5fe115b
Create Date: 2025-09-01 14:50:58.047794

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b91ba669f6de'
down_revision = 'f201c5fe115b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('resource', 'status',
               existing_type=postgresql.ENUM('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus'),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=False)
    sa.Enum('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus').create(op.get_bind())
    op.alter_column('resource', 'status',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=postgresql.ENUM('STOPPED', 'STARTING', 'RUNNING', 'FOUND', 'DELETED', name='resourcestatus'),
               existing_nullable=False)
    # ### end Alembic commands ###
