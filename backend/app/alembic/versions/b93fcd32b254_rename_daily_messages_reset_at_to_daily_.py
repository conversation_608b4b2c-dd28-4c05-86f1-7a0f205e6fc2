"""rename daily_messages_reset_at to daily_credits_reset_at

Revision ID: b93fcd32b254
Revises: 257ac400d7ad
Create Date: 2025-08-28 16:51:17.243803

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b93fcd32b254'
down_revision = '257ac400d7ad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new column as nullable first
    op.add_column('usage_quota', sa.Column('daily_credits_reset_at', sa.DateTime(timezone=True), nullable=True))

    # Copy values from old column to new column, or set default if old column doesn't exist
    op.execute("UPDATE usage_quota SET daily_credits_reset_at = COALESCE(daily_messages_reset_at, NOW())")

    # Make the column NOT NULL
    op.alter_column('usage_quota', 'daily_credits_reset_at', nullable=False)

    # Update indexes
    op.drop_index('idx_usage_quota_daily_reset', table_name='usage_quota')
    op.create_index('idx_usage_quota_daily_reset', 'usage_quota', ['daily_credits_reset_at'], unique=False)

    # Drop old column
    op.drop_column('usage_quota', 'daily_messages_reset_at')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Add old column as nullable first
    op.add_column('usage_quota', sa.Column('daily_messages_reset_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))

    # Copy values from new column to old column
    op.execute("UPDATE usage_quota SET daily_messages_reset_at = daily_credits_reset_at")

    # Make the column NOT NULL
    op.alter_column('usage_quota', 'daily_messages_reset_at', nullable=False)

    # Update indexes
    op.drop_index('idx_usage_quota_daily_reset', table_name='usage_quota')
    op.create_index('idx_usage_quota_daily_reset', 'usage_quota', ['daily_messages_reset_at'], unique=False)

    # Drop new column
    op.drop_column('usage_quota', 'daily_credits_reset_at')
    # ### end Alembic commands ###
