"""remove index in shortcut table

Revision ID: b9bd633bc7da
Revises: 3861399560c0
Create Date: 2025-08-29 10:18:27.116015

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'b9bd633bc7da'
down_revision = '3861399560c0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_shortcut_is_default', table_name='shortcut')
    op.drop_index('ix_shortcut_user_id', table_name='shortcut')
    op.drop_index('ix_shortcut_workspace_id', table_name='shortcut')
    op.drop_constraint('uq_shortcut_workspace_slug', 'shortcut', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('uq_shortcut_workspace_slug', 'shortcut', ['workspace_id', 'slug'])
    op.create_index('ix_shortcut_workspace_id', 'shortcut', ['workspace_id'], unique=False)
    op.create_index('ix_shortcut_user_id', 'shortcut', ['user_id'], unique=False)
    op.create_index('ix_shortcut_is_default', 'shortcut', ['is_default'], unique=False)
    # ### end Alembic commands ###
