"""Add MFA support to users and create MFA tables

Revision ID: efe60e7dbcff
Revises: 08413f90864d
Create Date: 2025-08-27 00:14:39.760428

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'efe60e7dbcff'
down_revision = '08413f90864d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mfa_sessions',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('session_token', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('is_verified', sa.<PERSON>(), nullable=False),
    sa.Column('ip_address', sqlmodel.sql.sqltypes.AutoString(length=45), nullable=True),
    sa.Column('user_agent', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mfa_sessions_session_token'), 'mfa_sessions', ['session_token'], unique=True)
    op.create_index(op.f('ix_mfa_sessions_user_id'), 'mfa_sessions', ['user_id'], unique=False)
    op.create_table('security_events',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=True),
    sa.Column('event_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('success', sa.Boolean(), nullable=False),
    sa.Column('ip_address', sqlmodel.sql.sqltypes.AutoString(length=45), nullable=True),
    sa.Column('user_agent', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True),
    sa.Column('details', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_security_events_created_at'), 'security_events', ['created_at'], unique=False)
    op.create_index(op.f('ix_security_events_user_id'), 'security_events', ['user_id'], unique=False)

    # Add MFA columns with default values for existing users
    op.add_column('user', sa.Column('mfa_enabled', sa.Boolean(), nullable=True, default=False))
    op.add_column('user', sa.Column('mfa_verified', sa.Boolean(), nullable=True, default=False))
    op.add_column('user', sa.Column('totp_secret_encrypted', sqlmodel.sql.sqltypes.AutoString(length=512), nullable=True))
    op.add_column('user', sa.Column('backup_codes_encrypted', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('user', sa.Column('failed_login_attempts', sa.Integer(), nullable=True, default=0))
    op.add_column('user', sa.Column('locked_until', sa.DateTime(), nullable=True))

    # Update existing users to have default values
    op.execute("UPDATE \"user\" SET mfa_enabled = false WHERE mfa_enabled IS NULL")
    op.execute("UPDATE \"user\" SET mfa_verified = false WHERE mfa_verified IS NULL")
    op.execute("UPDATE \"user\" SET failed_login_attempts = 0 WHERE failed_login_attempts IS NULL")

    # Make columns non-nullable after setting defaults
    op.alter_column('user', 'mfa_enabled', nullable=False)
    op.alter_column('user', 'mfa_verified', nullable=False)
    op.alter_column('user', 'failed_login_attempts', nullable=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'locked_until')
    op.drop_column('user', 'failed_login_attempts')
    op.drop_column('user', 'backup_codes_encrypted')
    op.drop_column('user', 'totp_secret_encrypted')
    op.drop_column('user', 'mfa_verified')
    op.drop_column('user', 'mfa_enabled')
    op.drop_index(op.f('ix_security_events_user_id'), table_name='security_events')
    op.drop_index(op.f('ix_security_events_created_at'), table_name='security_events')
    op.drop_table('security_events')
    op.drop_index(op.f('ix_mfa_sessions_user_id'), table_name='mfa_sessions')
    op.drop_index(op.f('ix_mfa_sessions_session_token'), table_name='mfa_sessions')
    op.drop_table('mfa_sessions')
    # ### end Alembic commands ###
