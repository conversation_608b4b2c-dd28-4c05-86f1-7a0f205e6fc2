"""add_default_shortcuts_to_existing_workspaces

Revision ID: f201c5fe115b
Revises: b9bd633bc7da
Create Date: 2025-08-29 10:22:42.718644

"""
import uuid
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision = 'f201c5fe115b'
down_revision = 'b9bd633bc7da'
branch_labels = None
depends_on = None


from app.constants.default_shortcuts import DEFAULT_SHORTCUTS


def upgrade():
    # Get database connection
    conn = op.get_bind()

    # Seed default shortcuts for all existing workspaces
    workspaces_query = text("""
        SELECT id, owner_id FROM workspace WHERE is_deleted = false
    """)

    workspaces = conn.execute(workspaces_query).fetchall()

    for workspace in workspaces:
        workspace_id = workspace[0]
        owner_id = workspace[1]

        # Check if default shortcuts already exist for this workspace
        existing_count_query = text("""
            SELECT COUNT(*) FROM shortcut
            WHERE workspace_id = :workspace_id AND is_default = true
        """)
        existing_count = conn.execute(existing_count_query, {"workspace_id": workspace_id}).scalar()

        if existing_count == 0:
            # Insert default shortcuts
            for shortcut_data in DEFAULT_SHORTCUTS:
                shortcut_id = str(uuid.uuid4())
                insert_query = text("""
                    INSERT INTO shortcut (
                        id, title, slug, content, category, is_default,
                        user_id, workspace_id, created_at, updated_at
                    ) VALUES (
                        :id, :title, :slug, :content, :category, :is_default,
                        :user_id, :workspace_id, NOW(), NOW()
                    )
                """)

                conn.execute(insert_query, {
                    "id": shortcut_id,
                    "title": shortcut_data["title"],
                    "slug": shortcut_data["slug"],
                    "content": shortcut_data["content"],
                    "category": shortcut_data["category"],
                    "is_default": True,
                    "user_id": owner_id,
                    "workspace_id": workspace_id,
                })

    # ### end Alembic commands ###


def downgrade():
    # Remove all default shortcuts
    conn = op.get_bind()
    delete_query = text("""
        DELETE FROM shortcut WHERE is_default = true
    """)
    conn.execute(delete_query)
    # ### end Alembic commands ###
