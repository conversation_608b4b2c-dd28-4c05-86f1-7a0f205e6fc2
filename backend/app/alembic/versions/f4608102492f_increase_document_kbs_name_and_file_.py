"""increase document_kbs name and file_name field length to 1000

Revision ID: f4608102492f
Revises: 6669ce642e1e
Create Date: 2025-08-26 11:41:48.230566

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'f4608102492f'
down_revision = '6669ce642e1e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('document_kbs', 'name',
               existing_type=sa.VARCHAR(length=255),
               type_=sqlmodel.sql.sqltypes.AutoString(length=1000),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('document_kbs', 'name',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=1000),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    # ### end Alembic commands ###
