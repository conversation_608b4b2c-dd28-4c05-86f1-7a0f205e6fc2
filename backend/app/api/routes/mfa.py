from fastapi import APIRouter, HTTPException, Request, status

from app.api.deps import CurrentUser, SessionDep
from app.core.security import verify_password
from app.logger import logger
from app.models import User
from app.models.users import (
    MFASetupRequest,
    MFASetupResponse,
    MFAStatusResponse,
    MFAVerifyRequest,
)
from app.services.mfa_service import mfa_service

router = APIRouter()


def get_client_info(request: Request) -> tuple[str | None, str | None]:
    """Extract client IP and user agent from request"""
    # Handle X-Forwarded-For header for proxied requests
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        ip_address = forwarded_for.split(",")[0].strip()
    else:
        ip_address = request.client.host if request.client else None

    user_agent = request.headers.get("User-Agent")
    return ip_address, user_agent


@router.get("/status", response_model=MFAStatusResponse)
def get_mfa_status(current_user: Current<PERSON><PERSON>, session: SessionDep) -> MFAStatusResponse:
    """Get current MFA status for authenticated user"""
    backup_codes_remaining = 0

    if current_user.backup_codes_encrypted:
        try:
            decrypted_codes = mfa_service.decrypt_data(
                current_user.backup_codes_encrypted
            )
            backup_codes_remaining = len(eval(decrypted_codes))  # JSON list
        except Exception:
            backup_codes_remaining = 0

    return MFAStatusResponse(
        mfa_enabled=current_user.mfa_enabled,
        backup_codes_remaining=backup_codes_remaining,
    )


@router.post("/setup", response_model=MFASetupResponse)
def setup_mfa(
    request: Request,
    setup_data: MFASetupRequest,
    current_user: CurrentUser,
    session: SessionDep,
) -> MFASetupResponse:
    """Setup MFA for authenticated user"""
    try:
        # Get the full user model from database to access hashed_password
        user = session.get(User, current_user.id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
            )

        # Verify current password for security
        if not verify_password(setup_data.password, user.hashed_password):
            ip_address, user_agent = get_client_info(request)
            mfa_service.log_security_event(
                session,
                str(current_user.id),
                "mfa_setup_failed_password",
                False,
                ip_address,
                user_agent,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid password"
            )

        # Check if MFA is already enabled
        if user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MFA is already enabled for this account",
            )

        # Generate TOTP secret and backup codes
        secret = mfa_service.generate_secret()
        backup_codes = mfa_service.generate_backup_codes()

        # Encrypt sensitive data
        encrypted_secret = mfa_service.encrypt_data(secret)
        encrypted_backup_codes = mfa_service.encrypt_data(
            mfa_service.hash_backup_codes(backup_codes)
        )

        # Generate QR code
        qr_code_data_uri = mfa_service.generate_qr_code(secret, user.email)

        # Store in database (MFA not enabled until verified)
        user.totp_secret_encrypted = encrypted_secret
        user.backup_codes_encrypted = encrypted_backup_codes
        user.mfa_verified = False

        session.add(user)
        session.commit()

        # Log security event
        ip_address, user_agent = get_client_info(request)
        mfa_service.log_security_event(
            session,
            str(current_user.id),
            "mfa_setup_initiated",
            True,
            ip_address,
            user_agent,
        )

        return MFASetupResponse(
            qr_code_data_uri=qr_code_data_uri,
            secret=secret,  # Only shown during setup
            backup_codes=backup_codes,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"MFA setup error for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="MFA setup failed"
        )


@router.post("/verify-setup")
def verify_mfa_setup(
    request: Request,
    verify_data: MFAVerifyRequest,
    current_user: CurrentUser,
    session: SessionDep,
):
    """Verify and enable MFA after setup"""
    try:
        # Get the full user model from database
        user = session.get(User, current_user.id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
            )

        if not user.totp_secret_encrypted:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MFA setup not initiated",
            )

        # Decrypt and verify TOTP
        secret = mfa_service.decrypt_data(user.totp_secret_encrypted)

        if not mfa_service.verify_totp(secret, verify_data.code):
            ip_address, user_agent = get_client_info(request)
            mfa_service.log_security_event(
                session,
                str(current_user.id),
                "mfa_verify_setup_failed",
                False,
                ip_address,
                user_agent,
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid MFA code"
            )

        # Enable MFA
        user.mfa_enabled = True
        user.mfa_verified = True
        session.add(user)
        session.commit()

        # Log security event
        ip_address, user_agent = get_client_info(request)
        mfa_service.log_security_event(
            session, str(current_user.id), "mfa_enabled", True, ip_address, user_agent
        )

        return {"message": "MFA enabled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"MFA verification error for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MFA verification failed",
        )


@router.post("/disable")
def disable_mfa(
    request: Request,
    disable_data: MFASetupRequest,
    current_user: CurrentUser,
    session: SessionDep,
):
    """Disable MFA for authenticated user"""
    try:
        # Get the full user model from database
        user = session.get(User, current_user.id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
            )

        # Verify current password
        if not verify_password(disable_data.password, user.hashed_password):
            ip_address, user_agent = get_client_info(request)
            mfa_service.log_security_event(
                session,
                str(current_user.id),
                "mfa_disable_failed_password",
                False,
                ip_address,
                user_agent,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid password"
            )

        if not user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MFA is not enabled for this account",
            )

        # Disable MFA
        user.mfa_enabled = False
        user.mfa_verified = False
        user.totp_secret_encrypted = None
        user.backup_codes_encrypted = None

        session.add(user)
        session.commit()

        # Log security event
        ip_address, user_agent = get_client_info(request)
        mfa_service.log_security_event(
            session, str(current_user.id), "mfa_disabled", True, ip_address, user_agent
        )

        return {"message": "MFA disabled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"MFA disable error for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="MFA disable failed",
        )


@router.post("/regenerate-backup-codes")
def regenerate_backup_codes(
    request: Request,
    setup_data: MFASetupRequest,
    current_user: CurrentUser,
    session: SessionDep,
) -> dict[str, list[str]]:
    """Regenerate backup codes for MFA-enabled user"""
    try:
        # Get the full user model from database
        user = session.get(User, current_user.id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found"
            )

        # Verify current password
        if not verify_password(setup_data.password, user.hashed_password):
            ip_address, user_agent = get_client_info(request)
            mfa_service.log_security_event(
                session,
                str(current_user.id),
                "backup_codes_regen_failed_password",
                False,
                ip_address,
                user_agent,
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid password"
            )

        if not user.mfa_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MFA is not enabled for this account",
            )

        # Generate new backup codes
        backup_codes = mfa_service.generate_backup_codes()
        encrypted_backup_codes = mfa_service.encrypt_data(
            mfa_service.hash_backup_codes(backup_codes)
        )

        # Update database
        user.backup_codes_encrypted = encrypted_backup_codes
        session.add(user)
        session.commit()

        # Log security event
        ip_address, user_agent = get_client_info(request)
        mfa_service.log_security_event(
            session,
            str(current_user.id),
            "backup_codes_regenerated",
            True,
            ip_address,
            user_agent,
        )

        return {"backup_codes": backup_codes}

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(
            f"Backup codes regeneration error for user {current_user.id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Backup codes regeneration failed",
        )
