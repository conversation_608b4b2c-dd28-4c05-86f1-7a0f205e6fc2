import uuid
from datetime import datetime
from typing import Any

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, Query
from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.logger import logger
from app.models import (
    RecommendationCreate,
    RecommendationOveralPublic,
    RecommendationPublic,
    RecommendationsPublic,
    RecommendationStatus,
    RecommendationUpdate,
)
from app.repositories.recommendation import RecommendationRepository
from app.repositories.resources import ResourceRepository


class SimpleMessage(BaseModel):
    message: str


router = APIRouter()


@router.get("/overal", response_model=RecommendationOveralPublic)
async def get_recomendation_overal(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Get overal recommendation statistics.
    """
    try:
        logger.info(
            f"User {current_user.id} attempting to retrieve recommendation overal of workspace {current_user.current_workspace_id}"
        )

        if not current_user.current_workspace_id:
            raise HTTPException(
                status_code=400,
                detail="No workspace selected",
            )

        repo = RecommendationRepository(session)
        return await repo.get_recommendation_overal_async(
            current_user.current_workspace_id
        )
    except Exception:
        logger.exception("Error getting recommendation overal.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while getting recommendation overal.",
        )


@router.get("/", response_model=RecommendationsPublic)
async def read_recommendations(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    skip: int = 0,
    limit: int = 10,
    search: str | None = None,
    # Accept null to mean resource_id IS NULL; [] means no filter; [ids] means filter by ids
    resource_id: list[str] | None = Query(None),
    resource_type: list[str] = Query([]),
    status: list[str] = Query([]),
    start_date: datetime | None = None,
    end_date: datetime | None = None,
    order_by: str | None = None,
    order_direction: str = "desc",
) -> Any:
    """
    Retrieve recommendations.
    """
    try:
        logger.info(
            f"User {current_user.id} attempting to retrieve recommendations. Skip: {skip}, Limit: {limit}, Resource ID: {resource_id}, Type: {resource_type}, Status: {status}"
        )

        repo = RecommendationRepository(session)
        return await repo.get_recommendations_with_pagination_async(
            workspace_id=current_user.current_workspace_id,
            skip=skip,
            limit=limit,
            search=search,
            resource_id=resource_id,
            resource_type=resource_type,
            status=status,
            start_date=start_date,
            end_date=end_date,
            order_by=order_by,
            order_direction=order_direction,
        )
    except Exception:
        logger.exception("Error reading recommendations.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while reading recommendations.",
        )


@router.get("/{id}", response_model=RecommendationPublic)
async def read_recommendation(
    current_user: CurrentUser,
    id: uuid.UUID,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Get recommendation by ID.
    """
    try:
        logger.info(
            f"User {current_user.id} attempting to retrieve recommendation {id}"
        )

        repo = RecommendationRepository(session)
        recommendation = await repo.get_recommendation_by_id(id, current_user)
        if not recommendation:
            logger.warning(f"Recommendation {id} not found")
            raise HTTPException(status_code=404, detail="Recommendation not found")

        logger.info(f"Retrieved recommendation {id}")
        return recommendation
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error reading recommendation {id}.")
        raise HTTPException(
            status_code=500, detail="Unable to generate recommendations at this time"
        )


@router.post("/", response_model=RecommendationPublic)
async def create_recommendation(
    *,
    current_user: CurrentUser,
    recommendation_in: RecommendationCreate,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    try:
        recommendation_in.workspace_id = current_user.current_workspace_id

        repo = RecommendationRepository(session)
        recommendation = await repo.create_recommendation_with_validation(
            recommendation_in, current_user
        )
        if not recommendation:
            logger.warning(
                f"Resource {recommendation_in.resource_id} not found or access denied"
            )
            raise HTTPException(
                status_code=404, detail="Resource not found or access denied"
            )

        # Invalidate resource caches since creating a recommendation affects resource statistics
        try:
            resource_repo = ResourceRepository(session)
            await resource_repo.invalidate_cache(recommendation_in.workspace_id)
            logger.info(
                f"Invalidated resource caches for workspace {recommendation_in.workspace_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to invalidate resource caches: {e}")
            # Don't fail the recommendation creation if cache invalidation fails

        return recommendation
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error creating recommendation.")
        raise HTTPException(
            status_code=500, detail="Unable to generate recommendations at this time"
        )


@router.put("/{id}", response_model=RecommendationPublic)
async def update_recommendation(
    *,
    current_user: CurrentUser,
    id: uuid.UUID,
    recommendation_in: RecommendationUpdate,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Update a recommendation.
    """
    try:
        logger.debug(f"User {current_user.id} attempting to update recommendation {id}")

        repo = RecommendationRepository(session)
        recommendation = await repo.update_recommendation_by_id(
            id, recommendation_in, current_user
        )
        if not recommendation:
            logger.warning(f"Recommendation {id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Recommendation not found or access denied"
            )

        # Invalidate resource caches since updating a recommendation affects resource statistics
        try:
            resource_repo = ResourceRepository(session)
            await resource_repo.invalidate_cache(recommendation.workspace_id)
            logger.info(
                f"Invalidated resource caches for workspace {recommendation.workspace_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to invalidate resource caches: {e}")
            # Don't fail the recommendation update if cache invalidation fails

        return recommendation
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error updating recommendation {id}.")
        raise HTTPException(
            status_code=500, detail="Unable to generate recommendations at this time"
        )


@router.delete("/{id}")
async def delete_recommendation(
    current_user: CurrentUser,
    id: uuid.UUID,
    session: AsyncSession = Depends(get_async_session),
) -> SimpleMessage:
    """
    Delete a recommendation.
    """
    try:
        logger.debug(f"User {current_user.id} attempting to delete recommendation {id}")

        repo = RecommendationRepository(session)

        # First fetch the recommendation to get workspace_id for cache invalidation
        recommendation = await repo.get_recommendation_by_id(id, current_user)
        if not recommendation:
            logger.warning(f"Recommendation {id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Recommendation not found or access denied"
            )

        deleted = await repo.delete_recommendation_by_id(id, current_user)
        if not deleted:
            logger.warning(f"Recommendation {id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Recommendation not found or access denied"
            )

        # Invalidate resource caches since deleting a recommendation affects resource statistics
        try:
            resource_repo = ResourceRepository(session)
            await resource_repo.invalidate_cache(recommendation.workspace_id)
            logger.info(
                f"Invalidated resource caches for workspace {recommendation.workspace_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to invalidate resource caches: {e}")
            # Don't fail the recommendation deletion if cache invalidation fails

        return SimpleMessage(message="Recommendation deleted successfully")
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error deleting recommendation {id}.")
        raise HTTPException(
            status_code=500, detail="Unable to generate recommendations at this time"
        )


@router.put("/{id}/status", response_model=RecommendationPublic)
async def update_recommendation_status(
    *,
    current_user: CurrentUser,
    id: uuid.UUID,
    status: RecommendationStatus,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Update the status of a recommendation.
    """
    try:
        logger.debug(
            f"User {current_user.id} attempting to update status of recommendation {id} to {status}"
        )

        repo = RecommendationRepository(session)
        recommendation = await repo.update_recommendation_status_by_id(
            id, status, current_user
        )
        if not recommendation:
            logger.warning(f"Recommendation {id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Recommendation not found or access denied"
            )

        # Invalidate resource caches since updating recommendation status affects resource statistics
        try:
            resource_repo = ResourceRepository(session)
            await resource_repo.invalidate_cache(recommendation.workspace_id)
            logger.info(
                f"Invalidated resource caches for workspace {recommendation.workspace_id}"
            )
        except Exception as e:
            logger.warning(f"Failed to invalidate resource caches: {e}")
            # Don't fail the recommendation status update if cache invalidation fails

        logger.debug(f"Updated status of recommendation {id} to {status}")
        return recommendation
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error updating recommendation status {id}.")
        raise HTTPException(
            status_code=500, detail="Unable to generate recommendations at this time"
        )
