from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.logger import logger
from app.models import ResourceDetail, ResourcesPublic, ResourceStatistics
from app.repositories.resources import ResourceRepository
from app.schemas.response import MessageResponse

router = APIRouter()


@router.get("/", response_model=ResourcesPublic)
async def read_resources(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    skip: int = 0,
    limit: int = 10,
    name: str | None = None,
    resource_type: list[str] = Query([]),
    status: list[str] = Query([]),
    region: list[str] = Query([]),
    category: list[str] = Query([]),
) -> Any:
    """
    Retrieve resources with pagination and caching.
    """
    try:
        repository = ResourceRepository(session)

        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        return await repository.get_resources_with_pagination(
            workspace_id=current_user.current_workspace_id,
            skip=skip,
            limit=limit,
            name=name,
            resource_type=resource_type,
            status=status,
            region=region,
            category=category,
        )
    except Exception:
        logger.exception("Error reading resources.")
        raise HTTPException(
            status_code=500, detail="Unable to process resource request"
        )


@router.get("/statistics", response_model=ResourceStatistics)
async def get_resource_statistics(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Get resource statistics for the current workspace including status counts,
    total resources, top resource types, and potential monthly savings.
    """
    try:
        repository = ResourceRepository(session)

        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        statistics = await repository.get_resource_statistics(
            workspace_id=current_user.current_workspace_id
        )

        return statistics
    except Exception:
        logger.exception("Error getting resource statistics.")
        raise HTTPException(
            status_code=500, detail="Unable to process resource statistics request"
        )


@router.get("/{resource_id}", response_model=ResourceDetail)
async def read_resource_by_id(
    resource_id: str,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Retrieve a specific resource by ID with detailed information.
    """
    try:
        repository = ResourceRepository(session)

        # Ensure workspace_id is not None
        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        resource = await repository.get_by_id_and_workspace(
            resource_id=resource_id,
            workspace_id=current_user.current_workspace_id,
        )

        if not resource:
            raise HTTPException(status_code=404, detail="Resource not found")

        return resource
    except HTTPException:
        raise
    except Exception:
        logger.exception(f"Error reading resource with ID: {resource_id}")
        raise HTTPException(
            status_code=500, detail="Unable to process resource request"
        )


@router.delete("/{resource_id}", response_model=MessageResponse)
async def delete_resource_by_id(
    resource_id: str,
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
) -> MessageResponse:
    """
    Delete a specific resource by ID within the current workspace.
    """
    try:
        repository = ResourceRepository(session)

        if current_user.current_workspace_id is None:
            raise HTTPException(status_code=400, detail="No workspace selected")

        deleted = await repository.delete_by_id_and_workspace(
            resource_id=resource_id,
            workspace_id=current_user.current_workspace_id,
        )

        if not deleted:
            raise HTTPException(status_code=404, detail="Resource not found")

        return MessageResponse(message="Resource deleted successfully")
    except HTTPException:
        raise
    except Exception:
        logger.exception(f"Error deleting resource with ID: {resource_id}")
        raise HTTPException(
            status_code=500, detail="Unable to process resource deletion request"
        )
