import uuid
from typing import Any

from fastapi import <PERSON>Router, Depends, HTTPException, Query
from pydantic import BaseModel
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser, get_async_session
from app.logger import logger
from app.models.shortcuts import (
    ShortcutCreate,
    ShortcutPublic,
    ShortcutsPublic,
    ShortcutUpdate,
)
from app.services.shortcut_service import ShortcutService


class SimpleMessage(BaseModel):
    message: str


router = APIRouter()


@router.get("/", response_model=ShortcutsPublic)
async def read_shortcuts(
    current_user: CurrentUser,
    session: AsyncSession = Depends(get_async_session),
    include_defaults: bool = Query(True, description="Include default shortcuts"),
    search: str | None = Query(None, description="Search term for filtering shortcuts"),
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
) -> Any:
    """
    Retrieve shortcuts for a workspace.
    """
    try:
        logger.info(
            f"User {current_user.id} retrieving shortcuts for workspace {current_user.current_workspace_id}. "
            f"Page: {page}, Size: {page_size}, Include defaults: {include_defaults}, Search: {search}"
        )

        service = ShortcutService(session)
        return await service.list_shortcuts(
            current_user=current_user,
            workspace_id=current_user.current_workspace_id,
            include_defaults=include_defaults,
            search=search,
            page=page,
            page_size=page_size,
        )
    except Exception:
        logger.exception("Error reading shortcuts.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while reading shortcuts.",
        )


@router.get("/{shortcut_id}", response_model=ShortcutPublic)
async def read_shortcut(
    current_user: CurrentUser,
    shortcut_id: uuid.UUID,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Get shortcut by ID.
    """
    try:
        logger.info(
            f"User {current_user.id} attempting to retrieve shortcut {shortcut_id}"
        )

        service = ShortcutService(session)
        shortcut = await service.get_shortcut_by_id(current_user, shortcut_id)

        if not shortcut:
            logger.warning(f"Shortcut {shortcut_id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Shortcut not found or access denied"
            )

        logger.info(f"Retrieved shortcut {shortcut_id}")
        return shortcut
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error reading shortcut {shortcut_id}.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while reading shortcut.",
        )


@router.post("/", response_model=ShortcutPublic)
async def create_shortcut(
    *,
    current_user: CurrentUser,
    shortcut_in: ShortcutCreate,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Create a new shortcut.
    """
    try:
        logger.info(
            f"User {current_user.id} creating shortcut '{shortcut_in.title}' "
            f"in workspace {current_user.current_workspace_id}"
        )

        service = ShortcutService(session)
        shortcut = await service.create_shortcut(current_user, shortcut_in)

        logger.info(f"Created shortcut {shortcut.id} with slug '{shortcut.slug}'")
        return shortcut
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception("Error creating shortcut.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while creating shortcut.",
        )


@router.patch("/{shortcut_id}", response_model=ShortcutPublic)
async def update_shortcut(
    *,
    current_user: CurrentUser,
    shortcut_id: uuid.UUID,
    shortcut_in: ShortcutUpdate,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Update a shortcut.
    """
    try:
        logger.debug(
            f"User {current_user.id} attempting to update shortcut {shortcut_id}"
        )

        service = ShortcutService(session)
        shortcut = await service.update_shortcut(current_user, shortcut_id, shortcut_in)

        if not shortcut:
            logger.warning(f"Shortcut {shortcut_id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Shortcut not found or access denied"
            )

        logger.info(f"Updated shortcut {shortcut_id}")
        return shortcut
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error updating shortcut {shortcut_id}.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while updating shortcut.",
        )


@router.delete("/{shortcut_id}")
async def delete_shortcut(
    current_user: CurrentUser,
    shortcut_id: uuid.UUID,
    session: AsyncSession = Depends(get_async_session),
) -> SimpleMessage:
    """
    Delete a shortcut.
    """
    try:
        logger.debug(
            f"User {current_user.id} attempting to delete shortcut {shortcut_id}"
        )

        service = ShortcutService(session)
        deleted = await service.delete_shortcut(current_user, shortcut_id)

        if not deleted:
            logger.warning(f"Shortcut {shortcut_id} not found or access denied")
            raise HTTPException(
                status_code=404, detail="Shortcut not found or access denied"
            )

        logger.info(f"Deleted shortcut {shortcut_id}")
        return SimpleMessage(message="Shortcut deleted successfully")
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error deleting shortcut {shortcut_id}.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while deleting shortcut.",
        )


@router.get("/slug/{slug}", response_model=ShortcutPublic)
async def get_shortcut_by_slug(
    current_user: CurrentUser,
    slug: str,
    session: AsyncSession = Depends(get_async_session),
) -> Any:
    """
    Get shortcut by slug for slash command integration.
    """
    try:
        logger.debug(
            f"User {current_user.id} retrieving shortcut by slug '{slug}' "
            f"in workspace {current_user.current_workspace_id}"
        )

        service = ShortcutService(session)
        shortcut = await service.get_shortcut_by_slug(
            current_user.current_workspace_id, slug
        )

        if not shortcut:
            raise HTTPException(status_code=404, detail="Shortcut not found")

        return shortcut
    except HTTPException as e:
        raise e
    except Exception:
        logger.exception(f"Error reading shortcut by slug '{slug}'.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while reading shortcut.",
        )
