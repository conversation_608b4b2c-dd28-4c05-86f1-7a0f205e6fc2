from typing import Any

# Default shortcuts that all users will have access to
# These are structured prompts designed for AI assistants in cloud cost optimization

DEFAULT_SHORTCUTS: list[dict[str, Any]] = [
    {
        "title": "Resource Rightsizing Analysis",
        "slug": "resource-rightsizing-analysis",
        "content": "Identify underutilized resources across your infrastructure. Analyze CPU, memory, and storage usage patterns to find instances that can be downsized to reduce costs.",
        "category": "Cost Optimization",
    },
    {
        "title": "Automated Backup Strategy",
        "slug": "automated-backup-strategy",
        "content": "Review and optimize backup schedules and retention policies. Ensure critical data is backed up regularly while avoiding unnecessary storage costs.",
        "category": "Operational Excellence",
    },
    {
        "title": "Monitoring and Alerting Setup",
        "slug": "monitoring-alerting-setup",
        "content": "Configure comprehensive monitoring and alerting for key metrics. Set up alerts for resource utilization, performance issues, and cost anomalies.",
        "category": "Operational Excellence",
    },
    {
        "title": "Security Configuration Audit",
        "slug": "security-configuration-audit",
        "content": "Audit security configurations across your infrastructure. Review access controls, encryption settings, and compliance with security best practices.",
        "category": "Security",
    },
    {
        "title": "Cost Allocation and Tagging",
        "slug": "cost-allocation-tagging",
        "content": "Implement consistent resource tagging for cost allocation. Track costs by department, project, or environment to improve budget visibility and accountability.",
        "category": "Cost Optimization",
    },
]

# Categories for organization
DEFAULT_SHORTCUT_CATEGORIES = [
    "Well-Architected",
    "Operational Excellence",
    "Security",
    "Reliability",
    "Performance",
    "Cost Optimization",
]
