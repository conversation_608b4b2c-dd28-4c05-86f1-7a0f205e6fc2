"""
Circuit Breaker implementation for resilient external service calls.

This module provides a circuit breaker pattern to handle failures in external
service dependencies, particularly useful for payment service integrations
and other external API calls.
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict

from app.logger import logger


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"  # Normal operation
    OPEN = "open"  # Failing, requests rejected
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior."""
    failure_threshold: int = 5  # Failures before opening
    recovery_timeout: int = 60  # Seconds before trying half-open
    success_threshold: int = 3  # Successes needed to close from half-open
    timeout: float = 30.0  # Request timeout in seconds


@dataclass
class CircuitBreakerMetrics:
    """Metrics for monitoring circuit breaker state."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    consecutive_failures: int = 0
    consecutive_successes: int = 0
    last_failure_time: float = 0.0
    state_changes: int = 0


class CircuitBreaker:
    """
    Circuit breaker implementation for external service calls.

    This class implements the circuit breaker pattern to prevent cascading failures
    when external services are unavailable or slow.
    """

    def __init__(
        self,
        name: str,
        config: CircuitBreakerConfig | None = None,
        metrics: CircuitBreakerMetrics | None = None
    ):
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.metrics = metrics or CircuitBreakerMetrics()
        self._state = CircuitBreakerState.CLOSED
        self._lock = asyncio.Lock()

    @property
    def state(self) -> CircuitBreakerState:
        """Get current circuit breaker state."""
        return self._state

    @property
    def is_closed(self) -> bool:
        """Check if circuit breaker is closed (normal operation)."""
        return self._state == CircuitBreakerState.CLOSED

    @property
    def is_open(self) -> bool:
        """Check if circuit breaker is open (failing)."""
        return self._state == CircuitBreakerState.OPEN

    @property
    def is_half_open(self) -> bool:
        """Check if circuit breaker is half-open (testing recovery)."""
        return self._state == CircuitBreakerState.HALF_OPEN

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function through circuit breaker.

        Args:
            func: Async function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the function call

        Raises:
            CircuitBreakerOpenError: If circuit is open
            Exception: Original exception if call fails
        """
        if self.is_open:
            if not self._should_attempt_reset():
                raise CircuitBreakerOpenError(
                    f"Circuit breaker '{self.name}' is OPEN"
                )
            await self._transition_to_half_open()

        if self.is_half_open or self.is_closed:
            try:
                # Execute with timeout
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.config.timeout
                )
                await self._on_success()
                return result
            except asyncio.TimeoutError:
                await self._on_failure()
                raise
            except Exception as e:
                await self._on_failure()
                raise
        else:
            # This should not happen, but handle it gracefully
            raise CircuitBreakerOpenError(
                f"Circuit breaker '{self.name}' is in an invalid state: {self._state}"
            )

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt a reset."""
        if self._state != CircuitBreakerState.OPEN:
            return False

        time_since_failure = time.time() - self.metrics.last_failure_time
        return time_since_failure >= self.config.recovery_timeout

    async def _transition_to_half_open(self):
        """Transition from OPEN to HALF_OPEN state."""
        async with self._lock:
            if self._state == CircuitBreakerState.OPEN:
                self._state = CircuitBreakerState.HALF_OPEN
                self.metrics.state_changes += 1
                logger.info(f"Circuit breaker '{self.name}' transitioned to HALF_OPEN")

    async def _on_success(self):
        """Handle successful call."""
        async with self._lock:
            self.metrics.total_requests += 1
            self.metrics.successful_requests += 1
            self.metrics.consecutive_failures = 0
            self.metrics.consecutive_successes += 1

            if self.is_half_open:
                if self.metrics.consecutive_successes >= self.config.success_threshold:
                    self._state = CircuitBreakerState.CLOSED
                    self.metrics.state_changes += 1
                    self.metrics.consecutive_successes = 0
                    logger.info(f"Circuit breaker '{self.name}' transitioned to CLOSED")

    async def _on_failure(self):
        """Handle failed call."""
        async with self._lock:
            self.metrics.total_requests += 1
            self.metrics.failed_requests += 1
            self.metrics.consecutive_failures += 1
            self.metrics.consecutive_successes = 0
            self.metrics.last_failure_time = time.time()

            if self.is_closed or self.is_half_open:
                if self.metrics.consecutive_failures >= self.config.failure_threshold:
                    self._state = CircuitBreakerState.OPEN
                    self.metrics.state_changes += 1
                    logger.warning(
                        f"Circuit breaker '{self.name}' transitioned to OPEN "
                        f"after {self.metrics.consecutive_failures} consecutive failures"
                    )

    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics for monitoring."""
        return {
            "name": self.name,
            "state": self.state.value,
            "total_requests": self.metrics.total_requests,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "consecutive_failures": self.metrics.consecutive_failures,
            "consecutive_successes": self.metrics.consecutive_successes,
            "last_failure_time": self.metrics.last_failure_time,
            "state_changes": self.metrics.state_changes,
            "failure_rate": (
                self.metrics.failed_requests / self.metrics.total_requests
                if self.metrics.total_requests > 0 else 0
            ),
        }


class CircuitBreakerRegistry:
    """
    Registry for managing multiple circuit breakers.

    This class provides a centralized way to manage circuit breakers
    across the application.
    """

    def __init__(self):
        self._breakers: Dict[str, CircuitBreaker] = {}
        self._lock = asyncio.Lock()

    def get_or_create(
        self,
        name: str,
        config: CircuitBreakerConfig | None = None
    ) -> CircuitBreaker:
        """Get existing circuit breaker or create new one."""
        if name not in self._breakers:
            self._breakers[name] = CircuitBreaker(name, config)
        return self._breakers[name]

    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all registered circuit breakers."""
        return {
            name: breaker.get_metrics()
            for name, breaker in self._breakers.items()
        }

    def reset_all(self):
        """Reset all circuit breakers to closed state."""
        for breaker in self._breakers.values():
            breaker._state = CircuitBreakerState.CLOSED
            breaker.metrics.consecutive_failures = 0
            breaker.metrics.consecutive_successes = 0


# Global registry instance
circuit_breaker_registry = CircuitBreakerRegistry()


class CircuitBreakerOpenError(Exception):
    """Raised when circuit breaker is open and request is rejected."""
    pass


def circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    success_threshold: int = 3,
    timeout: float = 30.0
):
    """
    Decorator to apply circuit breaker pattern to async functions.

    Args:
        name: Unique name for the circuit breaker
        failure_threshold: Number of failures before opening
        recovery_timeout: Seconds before trying half-open
        success_threshold: Successes needed to close from half-open
        timeout: Request timeout in seconds
    """
    def decorator(func: Callable) -> Callable:
        config = CircuitBreakerConfig(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            success_threshold=success_threshold,
            timeout=timeout
        )

        async def wrapper(*args, **kwargs):
            breaker = circuit_breaker_registry.get_or_create(name, config)
            return await breaker.call(func, *args, **kwargs)

        return wrapper
    return decorator