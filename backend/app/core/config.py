import secrets
import warnings
from typing import Annotated, Any, Literal

from llama_index.embeddings.cohere.base import CohereAIInputType
from pydantic import (
    AnyUrl,
    BeforeValidator,
    Field,
    HttpUrl,
    PostgresDsn,
    RedisDsn,
    computed_field,
    model_validator,
    validator,
)
from pydantic_core import MultiHostUrl
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing_extensions import Self


def parse_cors(v: Any) -> list[str] | str:
    if isinstance(v, str) and not v.startswith("["):
        return [i.strip() for i in v.split(",")]
    elif isinstance(v, list | str):
        return v
    raise ValueError(v)


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        # Use top level .env file (one level above ./backend/)
        env_file="../.env",
        env_ignore_empty=True,
        extra="ignore",
    )
    API_V1_STR: str = "/api/v1"
    APP_API_KEY: str | None = None
    SECRET_KEY: str = secrets.token_urlsafe(32)
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    FRONTEND_HOST: str | None = "http://localhost:5173"
    PAYMENT_ENABLED: bool | None = False
    PAYMENT_HOST: str | None = None
    ENVIRONMENT: Literal["local", "dev", "staging", "production"] = "local"

    # Object Storage Configuration
    OBJECT_STORAGE_OPTION: Literal["s3", "minio"] = "minio"
    IMAGES_BUCKET: str = "cloudthinker-images-bucket-environment-dev"
    KB_BUCKET: str = "cloudthinker-kb-bucket-environment-dev"
    ATTACHMENT_BUCKET: str = "cloudthinker-attachment-bucket-environment-dev"

    # S3 Configuration
    S3_ACCESS_KEY: str | None = None
    S3_SECRET_KEY: str | None = None
    S3_REGION: str | None = None

    # MinIO Configuration
    MINIO_ENDPOINT: str = "minio:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin"
    MINIO_SECURE: bool = False

    @computed_field  # type: ignore[prop-decorator]
    @property
    def storage_is_s3(self) -> bool:
        return self.OBJECT_STORAGE_OPTION == "s3"

    @computed_field  # type: ignore[prop-decorator]
    @property
    def storage_is_minio(self) -> bool:
        return self.OBJECT_STORAGE_OPTION == "minio"

    @computed_field  # type: ignore[prop-decorator]
    @property
    def current_storage_access_key(self) -> str:
        if self.storage_is_s3:
            return self.S3_ACCESS_KEY or ""
        return self.MINIO_ACCESS_KEY

    @computed_field  # type: ignore[prop-decorator]
    @property
    def current_storage_secret_key(self) -> str:
        if self.storage_is_s3:
            return self.S3_SECRET_KEY or ""
        return self.MINIO_SECRET_KEY

    BACKEND_CORS_ORIGINS: Annotated[
        list[AnyUrl] | str, BeforeValidator(parse_cors)
    ] = []

    @computed_field  # type: ignore[prop-decorator]
    @property
    def all_cors_origins(self) -> list[str]:
        return [str(origin).rstrip("/") for origin in self.BACKEND_CORS_ORIGINS] + [
            self.FRONTEND_HOST
        ]

    PROJECT_NAME: str
    SENTRY_DSN: HttpUrl | None = None
    POSTGRES_SERVER: str
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str = ""
    POSTGRES_DB: str = ""
    LANGGRAPH_SCHEMA_NAME: str = "langgraph"

    # Sandbox Database Configuration (for CSV processing)
    SANDBOX_POSTGRES_SERVER: str = "sandbox-db"
    SANDBOX_POSTGRES_PORT: int = 5432
    SANDBOX_POSTGRES_USER: str = "user"
    SANDBOX_POSTGRES_PASSWORD: str = "password"
    SANDBOX_POSTGRES_DB: str = "app"

    # QDRANT Configuration
    QDRANT_HOST: str = "qdrant"
    QDRANT_PORT: int = 6333
    QDRANT_GRPC_PORT: int = 6334
    QDRANT_COLLECTION_NAME: str = "cloudthinker-collections"

    # Connection Manager
    MCP_MANAGER_BASE_URL: str = "http://connection-manager:8000"

    # Litellm Proxy Server
    LITELLM_BASE_URL: str = "http://litellm:4000"

    RECAPTCHA_V3_SECRET_KEY: str | None = None

    @computed_field  # type: ignore[prop-decorator]
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> PostgresDsn:
        return PostgresDsn(
            str(
                MultiHostUrl.build(
                    scheme="postgresql+psycopg",
                    username=self.POSTGRES_USER,
                    password=self.POSTGRES_PASSWORD,
                    host=self.POSTGRES_SERVER,
                    port=self.POSTGRES_PORT,
                    path=self.POSTGRES_DB,
                )
            )
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def KB_VECTOR_STORE_CONNECTION(self) -> PostgresDsn:
        return PostgresDsn(
            str(
                MultiHostUrl.build(
                    scheme="postgresql+psycopg",
                    username=self.POSTGRES_USER,
                    password=self.POSTGRES_PASSWORD,
                    host=self.POSTGRES_SERVER,
                    port=self.POSTGRES_PORT,
                    path=f"{self.POSTGRES_DB}?options=-c search_path={self.LANGGRAPH_SCHEMA_NAME}",
                )
            )
        )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def SANDBOX_DATABASE_URI(self) -> PostgresDsn:
        return PostgresDsn(
            str(
                MultiHostUrl.build(
                    scheme="postgresql+psycopg",
                    username=self.SANDBOX_POSTGRES_USER,
                    password=self.SANDBOX_POSTGRES_PASSWORD,
                    host=self.SANDBOX_POSTGRES_SERVER,
                    port=self.SANDBOX_POSTGRES_PORT,
                    path=self.SANDBOX_POSTGRES_DB,
                )
            )
        )

    REDIS_SERVER: str
    REDIS_PORT: int = 6379
    REDIS_CELERY_DB: int = 0

    @computed_field  # type: ignore[prop-decorator]
    @property
    def REDIS_DATABASE_URI(self) -> RedisDsn:
        return RedisDsn(
            str(
                MultiHostUrl.build(
                    scheme="redis", host=self.REDIS_SERVER, port=self.REDIS_PORT
                )
            )
        )

    # Redis Connection Pool Configuration
    REDIS_SOCKET_CONNECT_TIMEOUT: int = 5
    REDIS_SOCKET_TIMEOUT: int = 5
    REDIS_HEALTH_CHECK_INTERVAL: int = 30
    REDIS_MAX_CONNECTIONS: int = 20
    REDIS_RETRY_ATTEMPTS: int = 3
    REDIS_RETRY_BACKOFF_BASE: int = 1
    REDIS_RETRY_BACKOFF_CAP: int = 10

    # Circuit Breaker Configuration
    REDIS_CIRCUIT_FAILURE_THRESHOLD: int = 5
    REDIS_CIRCUIT_RECOVERY_TIMEOUT: int = 60
    REDIS_HEALTH_CHECK_CACHE_INTERVAL: int = 30

    # Connection Monitoring
    REDIS_HEALTH_MONITOR_INTERVAL: int = 30
    REDIS_HEALTH_MONITOR_FAST_INTERVAL: int = 5

    SMTP_TLS: bool = True
    SMTP_SSL: bool = False
    SMTP_PORT: int = 587
    SMTP_HOST: str | None = None
    SMTP_USER: str | None = None
    SMTP_PASSWORD: str | None = None
    # TODO: update type to EmailStr when sqlmodel supports it
    EMAILS_FROM_EMAIL: str | None = None
    EMAILS_FROM_NAME: str | None = None
    # Email address for the growth team to receive enterprise enquiry notifications
    GROWTH_TEAM_EMAIL: str | None = None

    # Mailchimp Configuration
    MAILCHIMP_API_KEY: str | None = None
    MAILCHIMP_SERVER_PREFIX: str | None = None  # e.g., "us1", "us2", etc.
    MAILCHIMP_AUDIENCE_ID: str | None = None
    MAILCHIMP_ENABLED: bool = False

    @computed_field  # type: ignore[prop-decorator]
    @property
    def mailchimp_is_enabled(self) -> bool:
        return (
            self.MAILCHIMP_ENABLED
            and self.MAILCHIMP_API_KEY is not None
            and self.MAILCHIMP_SERVER_PREFIX is not None
            and self.MAILCHIMP_AUDIENCE_ID is not None
        )

    @model_validator(mode="after")
    def _set_default_emails_from(self) -> Self:
        if not self.EMAILS_FROM_NAME:
            self.EMAILS_FROM_NAME = self.PROJECT_NAME
        return self

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48

    @computed_field  # type: ignore[prop-decorator]
    @property
    def emails_enabled(self) -> bool:
        return bool(self.SMTP_HOST and self.EMAILS_FROM_EMAIL)

    # TODO: update type to EmailStr when sqlmodel supports it
    EMAIL_TEST_USER: str = "<EMAIL>"
    # TODO: update type to EmailStr when sqlmodel supports it
    FIRST_SUPERUSER: str
    FIRST_SUPERUSER_PASSWORD: str

    EXECUTOR_HOST: str
    SCHEDULE_ENABLED: bool

    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str
    GOOGLE_LOGIN_CALLBACK: str | None = FRONTEND_HOST + "/auth/google-callback"

    PERPLEXITY_API_KEY: str | None = None
    PERPLEXITY_MODEL: str = "sonar"  # or "sonar-pro"

    LANGFUSE_SECRET_KEY: str | None = None
    LANGFUSE_PUBLIC_KEY: str | None = None
    LANGFUSE_HOST: str | None = None
    LANGFUSE_FLUSH_INTERVAL: int = 30
    LANGFUSE_FLUSH_AT: int = 2
    LANGFUSE_TRACING_ENABLED: bool = True
    LANGFUSE_TRACING_ENVIRONMENT: str = "local"
    LANGFUSE_MAX_EVENT_SIZE_BYTES: int = (
        64 * 1024
    )  # 64KB per event (reduced from 256KB)
    LANGFUSE_MAX_BATCH_SIZE_BYTES: int = (
        32 * 1024
    )  # 32KB per batch (reduced from 128KB)

    # OpenTelemetry configuration
    OTEL_BSP_MAX_EXPORT_BATCH_SIZE: int = (
        1  # Reduced to 1 to prevent 413 errors from large batches
    )
    OTEL_BSP_EXPORT_TIMEOUT: int = (
        10000  # Reduced to 10 seconds for more frequent exports
    )
    OTEL_BSP_MAX_QUEUE_SIZE: int = (
        16  # Further reduced queue size to prevent memory issues
    )

    def _check_default_secret(self, var_name: str, value: str | None) -> None:
        if value == "changethis":
            message = (
                f'The value of {var_name} is "changethis", '
                "for security, please change it, at least for deployments."
            )
            if self.ENVIRONMENT == "local":
                warnings.warn(message, stacklevel=1)
            else:
                raise ValueError(message)

    @model_validator(mode="after")
    def _enforce_non_default_secrets(self) -> Self:
        self._check_default_secret("SECRET_KEY", self.SECRET_KEY)
        self._check_default_secret("POSTGRES_PASSWORD", self.POSTGRES_PASSWORD)
        self._check_default_secret(
            "SANDBOX_POSTGRES_PASSWORD", self.SANDBOX_POSTGRES_PASSWORD
        )
        self._check_default_secret(
            "FIRST_SUPERUSER_PASSWORD", self.FIRST_SUPERUSER_PASSWORD
        )

        return self

    DEFAULT_USER_QUOTA_LIMIT: int = 20

    MAIN_MODEL: str = "bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0"
    # MAIN_MODEL: str = "vertexai/gemini-2.5-pro-latest"
    MAIN_MODEL_REGION: str = "us-east-1"
    FALL_BACK_MODEL: str = "bedrock/apac.anthropic.claude-sonnet-4-20250514-v1:0"
    FALL_BACK_MODEL_REGION: str = "ap-southeast-1"
    AWS_DEFAULT_REGION: str

    NETWORKING_MODEL: str = "litellm/us-claude-sonnet-4"
    TITLE_GENERATION_MODEL: str = "litellm/us-claude-haiku-3.5"
    SUMMARY_MODEL: str = "bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0"
    MAX_TOKEN_BEFORE_COMPACT: int = 100_000

    ########################## Embedding configs
    # EMBEDDING_MODEL_DIMS: int = 1536  # Cohere V4 multimodal embeddings are 1536 dimensions
    # EMBEDDING_MODEL_NAME: CohereAIModelName = CohereAIModelName.MULTILINGUAL_V3

    EMBEDDING_MODEL_DIMS: int = 1024
    EMBEDDING_MODEL_NAME: str = "cohere.embed-multilingual-v3"
    # EMBEDDING_MODEL_NAME: str = "apac-cohere-embed-multilingual-v3"

    COHERE_EMBEDDING_SEARCH_DOC: CohereAIInputType = CohereAIInputType.SEARCH_DOCUMENT
    COHERE_EMBEDDING_SEARCH_QUERY: CohereAIInputType = CohereAIInputType.SEARCH_QUERY
    MAX_EMBEDDING_RETRIES: int = 3
    EMBEDDING_RETRY_DELAY: int = 2  # seconds
    EMBEDDING_REGION_NAME: str = "us-east-1"

    ########################## Cohere Reranking
    COHERE_API_KEY: str | None = None
    KB_SYNTHESIZE_MODEL: str = "litellm/us-claude-haiku-3.5"
    KB_RERANK_MODEL_ID: str = "cohere.rerank-v3-5:0"
    KB_RERANK_REGION_NAME: str = "us-west-2"
    KB_RERANK_TOP_N: int = 5

    ########################## Memory configs
    MEMORY_EXTRACTION_DECISION_MODEL: str = "litellm/us-claude-haiku-3.5"
    MEMORY_EXTRACTION_MODEL: str = "litellm/us-claude-haiku-3.5"
    MEMORY_MAX_RELATED_MEMORY_NODES: int = 7
    MEMORY_RELATEDNESS_THRESHOLD: float = 0.4
    MAX_MEMORY_TOOL_RESULTS: int = 10

    # Memory processing thresholds
    MEMORY_MIN_MESSAGES_FOR_EXTRACTION: int = 3
    MEMORY_MIN_MESSAGES_FOR_ROLE_PROCESSING: int = 2
    MEMORY_BATCH_SIZE: int = 10
    MEMORY_MAX_CONCURRENT_OPERATIONS: int = 5

    ########################## Knowledge Base configs
    KB_CHUNK_SIZE: int = 1024
    KB_CHUNK_OVERLAP: int = 256
    KB_SEARCH_LIMIT: int = 10
    KB_SEARCH_SCORE_THRESHOLD: float = 0.5
    SPARSE_TOP_K: int = 5
    LLAMA_PARSE_MODE: str = "parse_page_without_llm"

    ########################## FastEmbed Configuration
    FASTEMBED_SPARSE_MODEL_NAME: str = "Qdrant/bm25"

    ########################## Crawling config
    MAX_DEPTH: int = 2
    MAX_PAGES: int = 20

    ########################## MCP config
    DEFAULT_MCP_TIMEOUT: int = 5  # seconds
    DEFAULT_MCP_SSE_READ_TIMEOUT: int = 60 * 5  # seconds

    ########################## Tool Response Limit
    TOOL_RESPONSE_LIMIT_TOKENS: int = 2500
    CHARACTER_PER_TOKEN: int = 4
    TOOL_RESPONSE_LIMIT_CHARACTERS: int = (
        TOOL_RESPONSE_LIMIT_TOKENS * CHARACTER_PER_TOKEN
    )

    # Large response truncation limit (20k tokens)
    LARGE_RESPONSE_TRUNCATION_LIMIT_TOKENS: int = 20_000
    LARGE_RESPONSE_TRUNCATION_MESSAGE: str = "\n\n... [Response truncated due to length limit. The remaining content has been omitted.] ..."

    ########################## Networking Agent
    NETWORKING_AGENT_RETRY_COUNT: int = 3

    ########################## Scheduler
    TASK_SCHEDULE_WINDOW_MINUTES: int = 2

    @validator("PAYMENT_HOST")
    def validate_payment_config(cls, v, values):
        if values.get("PAYMENT_ENABLED") and not v:
            raise ValueError("Payment configuration missing when PAYMENT_ENABLED=True")
        return v

    DISCORD_INVITE_LINK: str | None = None

    ########################## Console config
    CONSOLE_TIMEOUT: int = 30  # seconds

    ########################## MCP Auto Context Tools
    MCP_AUTO_CONTEXT_TOOLS: list[str] = [
        "get_database_overview",
    ]

    ########################## Agent Context Settings
    AGENT_CONTEXT_ENABLED_AGENTS: list[str] = ["Tony", "Kai"]

    ########################## PAYMENT MODULE
    PAYMENT_MODULE_NAME: str = "Payment"

    ########################## RATE LIMITING
    # Rate limiting for public API endpoints (requests per minute)
    RATE_LIMIT_PUBLIC_REQUESTS_PER_MINUTE: int = 60
    RATE_LIMIT_PUBLIC_BURST_SIZE: int = 10
    # Rate limiting for public shared reports
    RATE_LIMIT_SHARED_REPORTS_PER_MINUTE: int = 30
    # Rate limiting for public shared dashboards
    RATE_LIMIT_SHARED_DASHBOARDS_PER_MINUTE: int = 30

    ########################## MFA CONFIGURATION
    # MFA Encryption
    MFA_ENCRYPTION_KEY: str = Field(default_factory=lambda: secrets.token_hex(32))

    # TOTP Configuration
    MFA_TOTP_ISSUER: str = Field(default="CloudThinker")
    MFA_TOTP_WINDOW: int = Field(default=1, description="TOTP time window tolerance")

    # MFA Session Management
    MFA_SESSION_EXPIRE_MINUTES: int = Field(
        default=10, description="MFA session timeout"
    )

    # Backup Codes
    MFA_BACKUP_CODES_COUNT: int = Field(
        default=10, description="Number of backup codes to generate"
    )
    MFA_BACKUP_CODE_LENGTH: int = Field(
        default=8, description="Length of each backup code"
    )

    # Account Security
    MAX_LOGIN_ATTEMPTS: int = Field(
        default=5, description="Max failed login attempts before lockout"
    )
    LOCKOUT_DURATION_MINUTES: int = Field(
        default=30, description="Account lockout duration"
    )

    @computed_field  # type: ignore[prop-decorator]
    @property
    def mfa_encryption_key_bytes(self) -> bytes:
        """Convert MFA encryption key to bytes for Fernet"""
        if isinstance(self.MFA_ENCRYPTION_KEY, str):
            return self.MFA_ENCRYPTION_KEY.encode()
        return self.MFA_ENCRYPTION_KEY


settings = Settings()  # type: ignore
