from fastapi import HTTPException, status


class AlertRepositoryError(HTTPException):
    def __init__(
        self,
        detail: str = "Alert repository error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class AlertNotFound(AlertRepositoryError):
    def __init__(self, alert_id: str):
        detail = f"Alert not found: {alert_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)


class AlertAccessDenied(AlertRepositoryError):
    def __init__(self, alert_id: str, workspace_id: str):
        detail = f"Access denied to alert '{alert_id}' in workspace '{workspace_id}'"
        super().__init__(detail=detail, status_code=status.HTTP_403_FORBIDDEN)


class AlertCreationError(AlertRepositoryError):
    def __init__(self, detail: str = "Failed to create alert"):
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)


class AlertUpdateError(AlertRepositoryError):
    def __init__(self, alert_id: str, detail: str = "Failed to update alert"):
        detail = f"Failed to update alert '{alert_id}': {detail}"
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)


class AlertDeletionError(AlertRepositoryError):
    def __init__(self, alert_id: str):
        detail = f"Failed to delete alert '{alert_id}'"
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)


class InvalidAlertOperation(AlertRepositoryError):
    def __init__(self, detail: str):
        super().__init__(detail=detail, status_code=status.HTTP_400_BAD_REQUEST)


class WorkspaceNotFound(AlertRepositoryError):
    def __init__(self, workspace_id: str):
        detail = f"Workspace not found: {workspace_id}"
        super().__init__(detail=detail, status_code=status.HTTP_404_NOT_FOUND)
