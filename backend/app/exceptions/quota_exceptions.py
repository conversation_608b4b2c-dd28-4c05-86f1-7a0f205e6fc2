from fastapi import HTTPException, status


class QuotaError(HTTPException):
    """Base exception for quota-related errors."""

    def __init__(
        self,
        detail: str = "Quota error",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class InsufficientQuotaError(QuotaError):
    """Raised when trying to deduct more than available quota."""

    def __init__(
        self,
        detail: str = "Insufficient quota available",
        status_code: int = status.HTTP_403_FORBIDDEN,
    ):
        super().__init__(status_code=status_code, detail=detail)


class InvalidQuotaRequestError(QuotaError):
    """Raised when quota update request is invalid."""

    def __init__(
        self,
        detail: str = "Invalid quota request",
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ):
        super().__init__(status_code=status_code, detail=detail)


class QuotaNotFoundError(QuotaError):
    """Raised when quota record is not found for user."""

    def __init__(
        self,
        detail: str = "Quota record not found",
        status_code: int = status.HTTP_404_NOT_FOUND,
    ):
        super().__init__(status_code=status_code, detail=detail)


class QuotaUpdateError(QuotaError):
    """Raised when quota update operation fails."""

    def __init__(
        self,
        detail: str = "Failed to update quota",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)


class DailyQuotaExceededError(InsufficientQuotaError):
    """Raised when daily message quota is exceeded."""

    def __init__(
        self,
        detail: str = "Daily message quota exceeded",
        status_code: int = status.HTTP_403_FORBIDDEN,
    ):
        super().__init__(status_code=status_code, detail=detail)


class PremiumQuotaExceededError(InsufficientQuotaError):
    """Raised when premium message quota is exceeded."""

    def __init__(
        self,
        detail: str = "Premium message quota exceeded",
        status_code: int = status.HTTP_403_FORBIDDEN,
    ):
        super().__init__(status_code=status_code, detail=detail)


class ResourceQuotaExceededError(InsufficientQuotaError):
    """Raised when resource quota (workspaces, members, etc.) is exceeded."""

    def __init__(
        self,
        resource_type: str,
        detail: str | None = None,
        status_code: int = status.HTTP_403_FORBIDDEN,
    ):
        if detail is None:
            detail = f"{resource_type} quota exceeded"
        super().__init__(status_code=status_code, detail=detail)


class QuotaValidationError(InvalidQuotaRequestError):
    """Raised when quota request validation fails."""

    def __init__(
        self,
        detail: str = "Quota request validation failed",
        status_code: int = status.HTTP_400_BAD_REQUEST,
    ):
        super().__init__(status_code=status_code, detail=detail)


class QuotaContextResolutionError(QuotaError):
    """Raised when quota context cannot be resolved."""

    def __init__(
        self,
        detail: str = "Failed to resolve quota context",
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    ):
        super().__init__(status_code=status_code, detail=detail)
