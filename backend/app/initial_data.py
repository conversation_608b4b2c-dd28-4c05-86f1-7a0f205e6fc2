import uuid
from sqlmodel import Session, col, select

from app.constants.default_shortcuts import DEFAULT_SHORTCUTS
from app.core.db import engine, init_db, init_module_setting
from app.logger import logger
from app.models.shortcuts import Shortcut
from app.models.users import User, UserWorkspace
from app.models.workspaces import Workspace


def seed_default_shortcuts_for_workspace(
    session: Session, workspace_id: uuid.UUID
) -> None:
    """Seed default shortcuts for a workspace."""
    logger.info(f"Seeding default shortcuts for workspace {workspace_id}")

    # Check if default shortcuts already exist for this workspace
    stm = select(Shortcut).where(
        col(Shortcut.workspace_id) == workspace_id,
        col(Shortcut.is_default) == True,
    )

    existing_defaults = session.exec(stm)
    result = existing_defaults.all()

    if len(result) > 0:
        logger.info(
            f"Default shortcuts already exist for workspace {workspace_id}, skipping"
        )
        return

    # Get users associated with this workspace through UserWorkspace junction table
    # This includes all users who are members of the workspace
    workspace_users = (
        session.query(User)
        .select_from(User)
        .join(UserWorkspace, col(User.id) == col(UserWorkspace.user_id))
        .filter(col(UserWorkspace.workspace_id) == workspace_id)
        .all()
    )

    if not workspace_users:
        logger.warning(
            f"No users found for workspace {workspace_id}, skipping default shortcuts"
        )
        return

    # Use the first user as the creator of default shortcuts
    system_user = workspace_users[0]

    # Create default shortcuts
    for shortcut_data in DEFAULT_SHORTCUTS:
        shortcut = Shortcut(
            title=shortcut_data["title"],
            slug=shortcut_data["slug"],
            content=shortcut_data["content"],
            category=shortcut_data["category"],
            is_default=True,
            user_id=system_user.id,
            workspace_id=workspace_id,
        )
        session.add(shortcut)

    session.commit()
    logger.info(
        f"Successfully seeded {len(DEFAULT_SHORTCUTS)} default shortcuts for workspace {workspace_id}"
    )


def seed_default_shortcuts(session: Session) -> None:
    """Seed default shortcuts for all existing workspaces."""
    workspaces = session.query(Workspace).all()

    for workspace in workspaces:
        seed_default_shortcuts_for_workspace(session, workspace.id)


def init() -> None:
    with Session(engine) as session:
        init_db(session)
        init_module_setting(session)
        seed_default_shortcuts(session)


def main() -> None:
    logger.info("Creating initial data")
    init()
    logger.info("Initial data created")


if __name__ == "__main__":
    main()
