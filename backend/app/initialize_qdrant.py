#!/usr/bin/env python3
"""
Initialize Qdrant collection and indexes during prestart.
This script ensures the Qdrant collection and payload indexes are created
before the application starts, preventing runtime initialization issues.
"""

import asyncio
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.core.config import settings
from app.logger import logger
from app.services.base_vector_store import BaseVectorStore


async def initialize_qdrant_collection():
    """Initialize Qdrant collection and create payload indexes."""
    try:
        logger.info("Starting Qdrant collection initialization...")

        vector_store = BaseVectorStore()

        # Test Qdrant connection
        collections = await vector_store.aqdrant_client.get_collections()
        logger.info(
            f"Qdrant connection successful. Found {len(collections.collections)} collections"
        )

        # Get collection name from settings
        collection_name = settings.QDRANT_COLLECTION_NAME
        logger.info(f"Ensuring collection '{collection_name}' exists with indexes...")

        # Create collection and indexes explicitly
        await vector_store.ensure_collection_exists(collection_name)

        logger.info(
            f"Qdrant collection '{collection_name}' and indexes initialized successfully"
        )

        # Verify collection exists
        if vector_store.qdrant_client.collection_exists(collection_name):
            logger.info(f"✓ Collection '{collection_name}' verified to exist")
        else:
            logger.error(
                f"✗ Collection '{collection_name}' was not created successfully"
            )
            sys.exit(1)

    except Exception as e:
        logger.error(f"Failed to initialize Qdrant collection: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    logger.info("Running Qdrant initialization script...")
    asyncio.run(initialize_qdrant_collection())
    logger.info("Qdrant initialization completed successfully")
