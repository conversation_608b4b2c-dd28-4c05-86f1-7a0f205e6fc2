import asyncio
import sys
from datetime import UTC, datetime

from sqlmodel import select

from app.constants import B<PERSON>LTIN_TOOLS
from app.core.db_session import get_task_session
from app.logger import logger
from app.models import (
    Agent,
    AgentBuiltInTool,
    BuiltInTool,
    Workspace,
    WorkspaceBuiltInTool,
)


class BuiltinToolsMigrator:
    """Migrator for builtin tools - ensures all builtin tools exist and are added to all workspaces."""

    async def migrate_builtin_tools(self) -> None:
        """Migration: upsert builtin tools and ensure they are added to all workspaces."""
        try:
            async with get_task_session() as session:
                # 1. Get existing builtin tools from database
                existing_tools = await self._get_existing_builtin_tools(session)
                existing_tools_map = {tool.name: tool for tool in existing_tools}

                # 2. Upsert all tools from BUILTIN_TOOLS
                new_tools_to_add_to_workspaces = []
                for tool_config in BUILTIN_TOOLS:
                    tool_name: str = tool_config["name"]

                    if tool_name in existing_tools_map:
                        # Update existing tool
                        existing_tool = existing_tools_map[tool_name]
                        await self._update_builtin_tool(
                            session, existing_tool, tool_config
                        )
                        logger.info(f"Updated builtin tool '{tool_name}'")
                    else:
                        # Create new tool
                        new_tool = await self._create_builtin_tool(session, tool_config)
                        new_tools_to_add_to_workspaces.append(new_tool)
                        logger.info(f"Created builtin tool '{tool_name}'")

                # 3. Remove obsolete builtin tools that are no longer in BUILTIN_TOOLS
                current_tool_names = {
                    tool_config["name"] for tool_config in BUILTIN_TOOLS
                }
                await self._remove_obsolete_builtin_tools(
                    session, existing_tools, current_tool_names
                )

                # 4. Add new builtin tools to workspaces that have agents, and immediately add to those agents
                if new_tools_to_add_to_workspaces:
                    await self._add_tools_to_workspaces_and_agents(
                        session, new_tools_to_add_to_workspaces
                    )

                await session.commit()

        except Exception as e:
            logger.error(f"Error during builtin tools migration: {str(e)}")
            raise

    async def _get_existing_builtin_tools(self, session) -> list[BuiltInTool]:
        """Get all existing builtin tools from database."""
        try:
            statement = select(BuiltInTool)
            result = await session.exec(statement)
            tools = result.all()
            logger.info(f"Found {len(tools)} existing builtin tools")
            return tools
        except Exception as e:
            logger.error(f"Error retrieving existing builtin tools: {str(e)}")
            raise

    async def _create_builtin_tool(self, session, tool_config: dict) -> BuiltInTool:
        """Create a new builtin tool."""
        try:
            tool = BuiltInTool(
                name=tool_config["name"],
                display_name=tool_config["display_name"],
                description=tool_config["description"],
                default_required_permission=tool_config["default_required_permission"],
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC),
            )

            session.add(tool)
            await session.flush()  # Flush to get the ID
            logger.info(f"Created builtin tool '{tool_config['name']}'")
            return tool

        except Exception as e:
            logger.error(f"Error creating builtin tool {tool_config['name']}: {str(e)}")
            raise

    async def _update_builtin_tool(
        self, session, existing_tool: BuiltInTool, tool_config: dict
    ) -> None:
        """Update an existing builtin tool with new configuration."""
        try:
            # Update fields that might have changed
            existing_tool.display_name = tool_config["display_name"]
            existing_tool.description = tool_config["description"]
            existing_tool.default_required_permission = tool_config[
                "default_required_permission"
            ]
            existing_tool.updated_at = datetime.now(UTC)

            # Mark the object as modified for the session
            session.add(existing_tool)

            logger.info(f"Updated builtin tool '{tool_config['name']}'")

        except Exception as e:
            logger.error(f"Error updating builtin tool {tool_config['name']}: {str(e)}")
            raise

    async def _add_tools_to_workspaces_and_agents(
        self, session, new_tools: list[BuiltInTool]
    ) -> None:
        """Add new builtin tools to workspaces that contain agents, and immediately add to all agents in those workspaces."""
        try:
            # Get workspaces that have active agents
            workspaces_with_agents_statement = (
                select(Workspace)
                .join(Agent, Agent.workspace_id == Workspace.id)
                .where(
                    Workspace.is_deleted == False,
                    Agent.is_deleted == False,
                    Agent.is_active == True,
                )
                .distinct()
            )
            workspaces_result = await session.exec(workspaces_with_agents_statement)
            workspaces = workspaces_result.all()

            logger.info(f"Found {len(workspaces)} workspaces with active agents")

            for workspace in workspaces:
                # Get existing workspace builtin tools
                existing_workspace_tools_statement = select(WorkspaceBuiltInTool).where(
                    WorkspaceBuiltInTool.workspace_id == workspace.id
                )
                existing_workspace_tools_result = await session.exec(
                    existing_workspace_tools_statement
                )
                existing_workspace_tools = existing_workspace_tools_result.all()
                existing_tool_ids = {
                    wbt.builtin_tool_id for wbt in existing_workspace_tools
                }

                # Add missing tools to workspace and collect the new workspace tools
                new_workspace_tools = []
                workspace_tools_added_count = 0
                for tool in new_tools:
                    if tool.id not in existing_tool_ids:
                        workspace_builtin_tool = WorkspaceBuiltInTool(
                            workspace_id=workspace.id,
                            builtin_tool_id=tool.id,
                            required_permission=tool.default_required_permission,
                        )
                        session.add(workspace_builtin_tool)
                        await session.flush()  # Flush to get the ID
                        new_workspace_tools.append(workspace_builtin_tool)
                        workspace_tools_added_count += 1

                if workspace_tools_added_count > 0:
                    logger.info(
                        f"Added {workspace_tools_added_count} builtin tools to workspace '{workspace.name}'"
                    )

                    # Now add these new workspace tools to all agents in this workspace
                    agents_in_workspace_statement = select(Agent).where(
                        Agent.workspace_id == workspace.id
                    )
                    agents_result = await session.exec(agents_in_workspace_statement)
                    agents_in_workspace = agents_result.all()

                    for agent in agents_in_workspace:
                        # Get existing agent builtin tools
                        existing_agent_tools_statement = select(AgentBuiltInTool).where(
                            AgentBuiltInTool.agent_id == agent.id
                        )
                        existing_agent_tools_result = await session.exec(
                            existing_agent_tools_statement
                        )
                        existing_agent_tools = existing_agent_tools_result.all()
                        existing_workspace_tool_ids = {
                            abt.workspace_builtin_tool_id
                            for abt in existing_agent_tools
                        }

                        # Add new workspace tools to this agent
                        agent_tools_added_count = 0
                        for workspace_tool in new_workspace_tools:
                            if workspace_tool.id not in existing_workspace_tool_ids:
                                agent_builtin_tool = AgentBuiltInTool(
                                    agent_id=agent.id,
                                    workspace_builtin_tool_id=workspace_tool.id,
                                    is_active=False,  # Default to inactive for new tools
                                )
                                session.add(agent_builtin_tool)
                                agent_tools_added_count += 1

                        if agent_tools_added_count > 0:
                            logger.info(
                                f"Added {agent_tools_added_count} builtin tools to agent '{agent.title}' (ID: {agent.id}) in workspace '{workspace.name}'"
                            )

            logger.info(
                "Successfully added builtin tools to all workspaces and their agents"
            )

        except Exception as e:
            logger.error(f"Error adding tools to workspaces and agents: {str(e)}")
            raise

    async def _remove_obsolete_builtin_tools(
        self, session, existing_tools: list[BuiltInTool], current_tool_names: set[str]
    ) -> None:
        """Remove builtin tools from database that are no longer in BUILTIN_TOOLS constant."""
        try:
            tools_to_remove = [
                tool for tool in existing_tools if tool.name not in current_tool_names
            ]

            if not tools_to_remove:
                logger.info("No obsolete builtin tools to remove")
                return

            for tool in tools_to_remove:
                # First, remove all agent associations with this tool
                agent_tools_statement = (
                    select(AgentBuiltInTool)
                    .join(
                        WorkspaceBuiltInTool,
                        AgentBuiltInTool.workspace_builtin_tool_id
                        == WorkspaceBuiltInTool.id,
                    )
                    .where(WorkspaceBuiltInTool.builtin_tool_id == tool.id)
                )
                agent_tools_result = await session.exec(agent_tools_statement)
                agent_tools = agent_tools_result.all()

                for agent_tool in agent_tools:
                    await session.delete(agent_tool)

                # Then, remove all workspace associations with this tool
                workspace_tools_statement = select(WorkspaceBuiltInTool).where(
                    WorkspaceBuiltInTool.builtin_tool_id == tool.id
                )
                workspace_tools_result = await session.exec(workspace_tools_statement)
                workspace_tools = workspace_tools_result.all()

                for workspace_tool in workspace_tools:
                    await session.delete(workspace_tool)

                # Finally, remove the builtin tool itself
                await session.delete(tool)

                logger.info(
                    f"Removed obsolete builtin tool '{tool.name}' and all its associations"
                )

            logger.info(
                f"Successfully removed {len(tools_to_remove)} obsolete builtin tools from database"
            )

        except Exception as e:
            logger.error(f"Error removing obsolete builtin tools: {str(e)}")
            raise


async def main():
    """Main function for running the migration."""
    try:
        logger.info("Starting builtin tools migration...")

        # Run migration
        migrator = BuiltinToolsMigrator()
        await migrator.migrate_builtin_tools()

        logger.info("Builtin tools migration completed successfully")

    except Exception as e:
        logger.error(f"Builtin tools migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
