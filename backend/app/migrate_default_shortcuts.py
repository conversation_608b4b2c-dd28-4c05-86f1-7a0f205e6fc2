import async<PERSON>
import uuid
from datetime import UTC, datetime

from sqlmodel import select

from app.constants.default_shortcuts import DEFAULT_SHORTCUTS
from app.core.db_session import get_task_session
from app.logger import logger
from app.models.shortcuts import Shortcut
from app.models.users import User, UserWorkspace
from app.models.workspaces import Workspace


class DefaultShortcutsMigrator:
    """Mi<PERSON><PERSON> for default shortcuts - ensures all workspaces have default shortcuts."""

    async def migrate_default_shortcuts(self) -> None:
        """Migration: upsert default shortcuts for all existing workspaces."""
        try:
            async with get_task_session() as session:
                # 1. Get all existing workspaces
                workspaces = await self._get_all_workspaces(session)

                if not workspaces:
                    logger.warning(
                        "No workspaces found, skipping default shortcuts migration"
                    )
                    return

                logger.info(
                    f"Found {len(workspaces)} workspaces to migrate default shortcuts"
                )

                # 2. Get existing default shortcuts
                existing_defaults = await self._get_existing_default_shortcuts(session)
                existing_defaults_map = {
                    (shortcut.workspace_id, shortcut.slug): shortcut
                    for shortcut in existing_defaults
                }

                # 3. Migrate default shortcuts for each workspace
                total_created = 0
                total_updated = 0

                for workspace in workspaces:
                    # Get users in this workspace to assign shortcuts to
                    workspace_users = await self._get_workspace_users(
                        session, workspace.id
                    )

                    if not workspace_users:
                        logger.warning(
                            f"No users found for workspace '{workspace.name}' (ID: {workspace.id}), skipping"
                        )
                        continue

                    # Use the first user as the creator of default shortcuts
                    system_user = workspace_users[0]

                    # Create/update default shortcuts for this workspace
                    (
                        created_count,
                        updated_count,
                    ) = await self._migrate_workspace_shortcuts(
                        session, workspace, system_user, existing_defaults_map
                    )

                    total_created += created_count
                    total_updated += updated_count

                await session.commit()

                logger.info(
                    f"Default shortcuts migration completed: {total_created} created, {total_updated} updated"
                )

        except Exception as e:
            logger.error(f"Error during default shortcuts migration: {str(e)}")
            raise

    async def _get_all_workspaces(self, session) -> list[Workspace]:
        """Get all workspaces from database."""
        try:
            statement = select(Workspace).where(Workspace.is_deleted == False)
            result = await session.exec(statement)
            workspaces = result.all()
            logger.info(f"Found {len(workspaces)} active workspaces")
            return workspaces
        except Exception as e:
            logger.error(f"Error retrieving workspaces: {str(e)}")
            raise

    async def _get_workspace_users(
        self, session, workspace_id: uuid.UUID
    ) -> list[User]:
        """Get all users in a workspace."""
        try:
            statement = (
                select(User)
                .join(UserWorkspace)
                .where(UserWorkspace.workspace_id == workspace_id)
            )
            result = await session.exec(statement)
            users = result.all()
            return users
        except Exception as e:
            logger.error(
                f"Error retrieving users for workspace {workspace_id}: {str(e)}"
            )
            raise

    async def _get_existing_default_shortcuts(self, session) -> list[Shortcut]:
        """Get all existing default shortcuts from database."""
        try:
            statement = select(Shortcut).where(Shortcut.is_default == True)
            result = await session.exec(statement)
            shortcuts = result.all()
            logger.info(f"Found {len(shortcuts)} existing default shortcuts")
            return shortcuts
        except Exception as e:
            logger.error(f"Error retrieving existing default shortcuts: {str(e)}")
            raise

    async def _migrate_workspace_shortcuts(
        self,
        session,
        workspace: Workspace,
        system_user: User,
        existing_defaults_map: dict,
    ) -> tuple[int, int]:
        """Migrate default shortcuts for a specific workspace."""
        try:
            created_count = 0
            updated_count = 0

            for shortcut_config in DEFAULT_SHORTCUTS:
                shortcut_slug = shortcut_config["slug"]
                key = (workspace.id, shortcut_slug)

                if key in existing_defaults_map:
                    # Update existing shortcut
                    existing_shortcut = existing_defaults_map[key]
                    await self._update_default_shortcut(
                        session, existing_shortcut, shortcut_config
                    )
                    updated_count += 1
                    logger.info(
                        f"Updated default shortcut '{shortcut_slug}' in workspace '{workspace.name}'"
                    )
                else:
                    # Create new shortcut
                    await self._create_default_shortcut(
                        session, shortcut_config, system_user, workspace
                    )
                    created_count += 1
                    logger.info(
                        f"Created default shortcut '{shortcut_slug}' in workspace '{workspace.name}'"
                    )

            return created_count, updated_count

        except Exception as e:
            logger.error(
                f"Error migrating shortcuts for workspace '{workspace.name}': {str(e)}"
            )
            raise

    async def _create_default_shortcut(
        self, session, shortcut_config: dict, user: User, workspace: Workspace
    ) -> None:
        """Create a new default shortcut."""
        try:
            shortcut = Shortcut(
                title=shortcut_config["title"],
                slug=shortcut_config["slug"],
                content=shortcut_config["content"],
                category=shortcut_config["category"],
                is_default=True,
                user_id=user.id,
                workspace_id=workspace.id,
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC),
            )

            session.add(shortcut)

        except Exception as e:
            logger.error(
                f"Error creating default shortcut {shortcut_config['slug']}: {str(e)}"
            )
            raise

    async def _update_default_shortcut(
        self, session, existing_shortcut: Shortcut, shortcut_config: dict
    ) -> None:
        """Update an existing default shortcut with new configuration."""
        try:
            # Update all fields that might have changed
            existing_shortcut.title = shortcut_config["title"]
            existing_shortcut.content = shortcut_config["content"]
            existing_shortcut.category = shortcut_config["category"]
            existing_shortcut.updated_at = datetime.now(UTC)

            # Mark the object as modified for the session
            session.add(existing_shortcut)

        except Exception as e:
            logger.error(
                f"Error updating default shortcut {shortcut_config['slug']}: {str(e)}"
            )
            raise


async def main():
    """Main function for running the default shortcuts migration."""
    try:
        logger.info("Starting default shortcuts migration...")

        # Run migration
        migrator = DefaultShortcutsMigrator()
        await migrator.migrate_default_shortcuts()

        logger.info("Default shortcuts migration completed successfully")

    except Exception as e:
        logger.error(f"Default shortcuts migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    import sys

    asyncio.run(main())
