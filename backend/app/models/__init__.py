# flake8: noqa
from .agents import *
from .alerts import *
from .builtin_tools import *
from .cloud_sync_config import *
from .clouds import *
from .connections import *
from .conversations import *
from .dashboards import *
from .document_kbs import *
from .knowledge_bases import *
from .messages import *
from .notifications import *
from .recommendations import *
from .reports import *
from .resources import *
from .shared import *
from .tasks import *
from .tokens import *
from .users import *
from .workspaces import *

# Import all models that need rebuilding
from .users import AuthorizedUser, UserDetail
from .workspaces import Workspace, WorkspaceDetail
from .document_kbs import DocumentKBRead
from .conversations import ConversationPublic
from .resources import ResourcesPublic, ResourcePublicSimple, ResourceTypeInfo
from .recommendations import RecommendationsPublic
from .shared import PaginationMeta
from .knowledge_bases import KBsPublic
from .shortcuts import ShortcutsPublic

# Rebuild all models with forward references
# The order matters - rebuild dependencies first
PaginationMeta.model_rebuild()
ResourcePublicSimple.model_rebuild()
Workspace.model_rebuild()
WorkspaceDetail.model_rebuild()
DocumentKBRead.model_rebuild()
ConversationPublic.model_rebuild()
ResourcesPublic.model_rebuild()
ResourceTypeInfo.model_rebuild()
RecommendationsPublic.model_rebuild()
AuthorizedUser.model_rebuild()
UserDetail.model_rebuild()
KBsPublic.model_rebuild()
ShortcutsPublic.model_rebuild()
