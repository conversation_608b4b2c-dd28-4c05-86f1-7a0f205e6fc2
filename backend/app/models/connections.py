import uuid
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING, Union
from uuid import UUID

from sqlalchemy_utils import EncryptedType
from sqlalchemy_utils.types.encrypted.encrypted_type import FernetEngine
from sqlmodel import (
    JSON,
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    String,
)

from app.core.config import settings

if TYPE_CHECKING:
    from .agents import Agent
    from .workspaces import Workspace


class AgentConnection(SQLModel, table=True):
    __tablename__ = "agent_connection"  # type: ignore

    agent_id: uuid.UUID = Field(
        foreign_key="agent.id", primary_key=True, ondelete="CASCADE"
    )
    conn_id: uuid.UUID = Field(
        foreign_key="connection.id", primary_key=True, ondelete="CASCADE"
    )


class ConnectionStatus(str, Enum):
    CONNECTED = "connected"
    ERROR = "error"


class ConnectionType(str, Enum):
    BUILTIN = "builtin"
    MCP = "mcp"


class BuiltinConnectionType(str, Enum):
    CLOUD = "cloud"
    DB = "db"
    ORCHESTRATION = "orchestration"
    OBSERVABILITY = "observability"
    CLI = "cli"


class MCPTransportType(str, Enum):
    STREAMABLE_HTTP = "streamable_http"
    SSE = "sse"


class BuiltinConnectionEnvType(str, Enum):
    SANDBOX = "sandbox"
    PRODUCTION = "production"


class ConnectionTemplateBase(SQLModel, table=False):
    name: str = Field(max_length=255)
    prefix: str = Field(max_length=255)
    builtin_connection_type: BuiltinConnectionType | None = Field(default=None)


class ConnectionTemplate(ConnectionTemplateBase, table=True):
    __tablename__ = "connection_template"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    description: str = Field(max_length=255, nullable=True)
    builtin_connection_type: BuiltinConnectionType = Field(...)
    is_sandbox_supported: bool = Field(default=False)
    config: dict = Field(default={}, sa_column=Column(JSON))
    env: str = Field(
        default="{}",
        sa_column=Column(
            EncryptedType(String, settings.SECRET_KEY, FernetEngine), nullable=False
        ),
    )
    tools: list[dict] = Field(
        default=[],
        sa_column=Column(JSON, nullable=False),
        description="Tool configuration: [{name: str, is_enabled: bool, is_required_permission: bool}]",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    connections: list["Connection"] = Relationship(back_populates="connection_template")


class ConnectionBase(ConnectionTemplateBase, table=False):
    connection_type: ConnectionType = Field(default=ConnectionType.MCP)
    mcp_transport_type: MCPTransportType | None = Field(default=None)
    config: dict = Field(default={}, sa_column=Column(JSON))
    env: str = Field(
        default="{}",
        sa_column=Column(
            EncryptedType(String, settings.SECRET_KEY, FernetEngine), nullable=False
        ),
    )
    tools: list[dict] = Field(
        default=[],
        sa_column=Column(JSON, nullable=False),
        description="Tool configuration: [{name: str, is_enabled: bool, is_required_permission: bool}]",
    )
    tool_schemas: list[dict] = Field(
        default=[], sa_column=Column(JSON), description="Deprecated"
    )
    connection_env_type: BuiltinConnectionEnvType = Field(
        default=BuiltinConnectionEnvType.PRODUCTION
    )
    # Status
    status: ConnectionStatus = Field(default=ConnectionStatus.CONNECTED)
    status_message: str = Field(default="")
    status_updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class Connection(ConnectionBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID | None = Field(
        foreign_key="workspace.id", ondelete="CASCADE", index=True
    )
    workspace: "Workspace" = Relationship(back_populates="connections")
    agents: list["Agent"] = Relationship(
        link_model=AgentConnection, sa_relationship_kwargs={"lazy": "selectin"}
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    # Link to connection template
    connection_template_id: uuid.UUID | None = Field(
        foreign_key="connection_template.id", ondelete="CASCADE", index=True
    )
    connection_template: Union["ConnectionTemplate", None] = Relationship(
        back_populates="connections", sa_relationship_kwargs={"lazy": "joined"}
    )


class ConnectionStatusResponse(SQLModel):
    status: ConnectionStatus
    status_message: str
    tools: list[dict]
    tool_schemas: list[dict]


class ConnectionToolConfig(SQLModel):
    name: str
    is_enabled: bool
    is_required_permission: bool


class ConnectionMCPConfig(SQLModel):
    url: str
    headers: dict
    timeout: float
    sse_read_timeout: float


class ConnectionEnvType(str, Enum):
    STRING = "string"
    YAML = "yaml"
    JSON = "json"
    REGION = "region"


class ConnectionEnvConfig(SQLModel):
    key: str
    value: str
    type: ConnectionEnvType


class MCPConnectionCreate(SQLModel):
    name: str
    prefix: str
    mcp_transport_type: MCPTransportType
    config: ConnectionMCPConfig
    env: list[ConnectionEnvConfig]


class MCPConnectionUpdate(SQLModel):
    name: str | None = None
    prefix: str | None = None
    mcp_transport_type: MCPTransportType | None = None
    config: ConnectionMCPConfig | None = None
    env: list[ConnectionEnvConfig] | None = None


class MCPConnectionPublic(SQLModel):
    id: UUID
    name: str
    prefix: str
    mcp_transport_type: MCPTransportType
    config: ConnectionMCPConfig
    env: list[ConnectionEnvConfig]
    tools: list[ConnectionToolConfig]
    status: ConnectionStatus
    status_message: str


class MCPConnectionsPublic(SQLModel):
    data: list[MCPConnectionPublic]
    count: int


class MCPConnectionToolUpdateRequest(SQLModel):
    tool_name: str = Field(description="Name of the tool to update")
    is_enabled: bool = Field(description="Whether the tool is enabled")
    is_required_permission: bool = Field(
        description="Whether the tool is required permission"
    )


class BuiltinConnectionPublic(SQLModel):
    id: UUID
    name: str
    prefix: str
    description: str
    builtin_connection_type: BuiltinConnectionType
    env: list[ConnectionEnvConfig]
    tools: list[ConnectionToolConfig]
    is_connected: bool
    is_sandbox_supported: bool
    connection_env_type: BuiltinConnectionEnvType


class BuiltinConnectionsPublic(SQLModel):
    data: list[BuiltinConnectionPublic]


class BuiltinConnectionInstallRequest(SQLModel):
    """Request model for installing builtin connections with custom configuration"""

    env: list[ConnectionEnvConfig] = Field(
        default=[],
        description="Environment variables override for the builtin connection",
    )


class BuiltinConnectionUpdateRequest(SQLModel):
    """Request model for updating builtin connections with custom configuration"""

    env: list[ConnectionEnvConfig] = Field(
        default=[],
        description="Environment variables override for the builtin connection",
    )


class BuiltinConnectionToolUpdateRequest(SQLModel):
    """Request model for updating builtin connections with custom tools"""

    tool_name: str = Field(description="Name of the tool to update")
    is_enabled: bool = Field(description="Whether the tool is enabled")
    is_required_permission: bool = Field(
        description="Whether the tool is required permission"
    )


class SandboxConnectionInstallRequest(SQLModel):
    """Request model for installing sandbox connections"""

    sandbox_conn_ids: list[UUID] = Field(
        description="IDs of the sandbox connections to install"
    )
