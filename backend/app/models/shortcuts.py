import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING

from sqlmodel import (
    Column,
    DateTime,
    Field,
    SQLModel,
)

if TYPE_CHECKING:
    from .shared import PaginationMeta


class ShortcutBase(SQLModel):
    title: str = Field(max_length=255)
    slug: str = Field(max_length=255)
    content: str = Field(max_length=1000)
    category: str | None = Field(default=None, max_length=255)
    is_default: bool = Field(default=False)


class ShortcutCreate(ShortcutBase):
    pass


class ShortcutUpdate(SQLModel):
    title: str | None = Field(default=None, max_length=255)
    slug: str | None = Field(default=None, max_length=255)
    content: str | None = Field(default=None, max_length=1000)
    category: str | None = Field(default=None, max_length=255)
    is_default: bool | None = None


class Shortcut(ShortcutBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class ShortcutPublic(ShortcutBase):
    id: uuid.UUID
    user_id: uuid.UUID
    workspace_id: uuid.UUID
    created_at: datetime
    updated_at: datetime


class ShortcutsPublic(SQLModel):
    data: list[ShortcutPublic]
    meta: "PaginationMeta"
