import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING
from uuid import UUID

from pydantic import EmailStr
from sqlmodel import (
    CheckConstraint,
    Column,
    DateTime,
    Field,
    Index,
    Relationship,
    SQLModel,
    UniqueConstraint,
)

if TYPE_CHECKING:
    from .knowledge_bases import KB
    from .messages import MessageAttachment
    from .notifications import Notification
    from .tasks import Task
    from .workspaces import Workspace

from .clouds import CloudProvider


class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = Field(default=False)
    is_email_verified: bool = Field(default=False)
    full_name: str = Field(max_length=255)
    is_superuser: bool = False
    last_login_time: datetime | None = None
    avatar_url: str | None = Field(default=None, max_length=1024)

    # MFA fields
    mfa_enabled: bool = Field(
        default=False, description="Whether MFA is enabled for this user"
    )
    mfa_verified: bool = Field(
        default=False, description="Whether MFA setup has been verified"
    )
    totp_secret_encrypted: str | None = Field(
        default=None, max_length=512, description="Encrypted TOTP secret"
    )
    backup_codes_encrypted: str | None = Field(
        default=None, description="Encrypted JSON array of backup codes"
    )
    failed_login_attempts: int = Field(
        default=0, ge=0, description="Number of consecutive failed login attempts"
    )
    locked_until: datetime | None = Field(
        default=None, description="Account lockout expiration time"
    )


class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)
    avatar_url: str | None = Field(default=None, max_length=1024)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


class MFASession(SQLModel, table=True):
    """Temporary sessions for MFA verification during login"""

    __tablename__ = "mfa_sessions"

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="user.id", ondelete="CASCADE", index=True)
    session_token: str = Field(unique=True, index=True, max_length=512)
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC))
    expires_at: datetime = Field()
    is_verified: bool = Field(default=False)
    ip_address: str | None = Field(default=None, max_length=45)
    user_agent: str | None = Field(default=None, max_length=512)

    # Relationships
    user: "User" = Relationship(back_populates="mfa_sessions")


class SecurityEvent(SQLModel, table=True):
    """Security event logging for audit trails"""

    __tablename__ = "security_events"

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID | None = Field(foreign_key="user.id", index=True)
    event_type: str = Field(max_length=50, description="Type of security event")
    success: bool = Field(description="Whether the event was successful")
    ip_address: str | None = Field(default=None, max_length=45)
    user_agent: str | None = Field(default=None, max_length=512)
    details: str | None = Field(
        default=None, description="Additional event details as JSON"
    )
    created_at: datetime = Field(default_factory=lambda: datetime.now(UTC), index=True)

    # Relationships
    user: "User" = Relationship(back_populates="security_events")

    __table_args__ = (
        {"schema": None},  # This would need to be adjusted based on your database setup
    )


class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    workspaces: list["UserWorkspace"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    activation_tokens: list["UserActivationToken"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    owned_tasks: list["Task"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"foreign_keys": "[Task.owner_id]"},
    )

    own_workspaces: list["Workspace"] = Relationship(
        back_populates="owner", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    usage_quota: "UsageQuota" = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    notifications: list["Notification"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    # Knowledge Base relationship
    knowledge_bases: list["KB"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={
            "foreign_keys": "[KB.owner_id]",
            "cascade": "all, delete-orphan",
        },
    )

    attachments: list["MessageAttachment"] = Relationship(
        back_populates="owner",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )

    onboarding: "UserOnboarding" = Relationship(
        back_populates="user",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )

    # MFA relationships
    mfa_sessions: list["MFASession"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    security_events: list["SecurityEvent"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )


class UserPublic(UserBase):
    id: uuid.UUID


class AuthorizedUser(UserPublic):
    current_workspace_id: uuid.UUID
    workspaces: list["Workspace"]
    own_workspaces: list["Workspace"]


class AuthorizedUserOnboarding(UserPublic):
    pass


class UserDetail(UserPublic):
    workspaces: list["Workspace"] | None = None
    own_workspaces: list["Workspace"] | None = None


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# MFA-related Pydantic schemas
class MFASetupRequest(SQLModel):
    password: str = Field(
        min_length=8, max_length=40, description="Current password for verification"
    )


class MFASetupResponse(SQLModel):
    qr_code_data_uri: str = Field(
        description="QR code as data URI for authenticator apps"
    )
    backup_codes: list[str] = Field(description="One-time use backup codes")
    secret: str = Field(description="TOTP secret (only shown during setup)")


class MFAVerifyRequest(SQLModel):
    code: str = Field(min_length=6, max_length=6, description="6-digit TOTP code")


class MFALoginRequest(SQLModel):
    mfa_code: str = Field(
        min_length=6, max_length=6, description="6-digit TOTP code or backup code"
    )
    session_token: str = Field(description="MFA session token from initial login")
    remember_me: bool = Field(default=False, description="Extend session duration")


class MFAStatusResponse(SQLModel):
    mfa_enabled: bool = Field(description="Whether MFA is enabled")
    backup_codes_remaining: int = Field(description="Number of unused backup codes")


class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"
    workspace_id: uuid.UUID | None = None
    is_first_login: bool = False
    slack_oauth: bool = False
    app_id: str | None = None
    team_id: str | None = None
    mfa_session_token: str | None = None  # For MFA flow


class TokenPayload(SQLModel):
    sub: uuid.UUID
    workspace_id: uuid.UUID | None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


class UsageQuota(SQLModel, table=True):
    __tablename__ = "usage_quota"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    user_id: UUID = Field(foreign_key="user.id", ondelete="CASCADE", index=True)

    user: "User" = Relationship(back_populates="usage_quota")

    credit_used: int = Field(default=0, ge=0)

    quota_used_tokens: int = Field(default=0, ge=0)

    # Daily quota tracking
    daily_credit_used: int = Field(default=0, ge=0)

    daily_credits_reset_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    # Quota usage tracking for additional resources
    workspaces_used: int = Field(default=0, ge=0)
    members_used: int = Field(default=0, ge=0)
    scheduled_tasks_used: int = Field(default=0, ge=0)
    kb_storage_used: float = Field(default=0, ge=0)  # in MB

    reset_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    # Define composite indexes and constraints for data integrity
    __table_args__ = (
        # Index for daily quota queries
        Index("idx_usage_quota_user_daily", "user_id", "daily_credit_used"),
        # Index for premium message quota queries
        Index("idx_usage_quota_user_premium", "user_id", "credit_used"),
        # Index for workspace quota queries
        Index("idx_usage_quota_user_workspaces", "user_id", "workspaces_used"),
        # Index for member quota queries
        Index("idx_usage_quota_user_members", "user_id", "members_used"),
        # Index for scheduled tasks quota queries
        Index("idx_usage_quota_user_scheduled", "user_id", "scheduled_tasks_used"),
        # Index for KB storage quota queries
        Index("idx_usage_quota_user_storage", "user_id", "kb_storage_used"),
        # Index for daily reset timestamp queries
        Index("idx_usage_quota_daily_reset", "daily_credits_reset_at"),
        # Database-level constraints for data integrity
        CheckConstraint("credit_used >= 0", name="ck_positive_quota_used_messages"),
        CheckConstraint("quota_used_tokens >= 0", name="ck_positive_quota_used_tokens"),
        CheckConstraint("daily_credit_used >= 0", name="ck_positive_daily_credit_used"),
        CheckConstraint("workspaces_used >= 0", name="ck_positive_workspaces_used"),
        CheckConstraint("members_used >= 0", name="ck_positive_members_used"),
        CheckConstraint(
            "scheduled_tasks_used >= 0", name="ck_positive_scheduled_tasks_used"
        ),
        CheckConstraint("kb_storage_used >= 0", name="ck_positive_kb_storage_used"),
    )


class UserActivationToken(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    token: str = Field(unique=True, index=True)
    expires_at: datetime
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    is_used: bool = Field(default=False)

    user: "User" = Relationship(
        back_populates="activation_tokens", sa_relationship_kwargs={"lazy": "joined"}
    )


class ActivationResponse(SQLModel):
    message: str
    expires_at: datetime


class ResendActivationRequest(SQLModel):
    email: EmailStr
    captcha_token: str


class UserWorkspace(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", ondelete="CASCADE")
    user: "User" = Relationship(back_populates="workspaces")
    workspace_id: uuid.UUID = Field(foreign_key="workspace.id", ondelete="CASCADE")
    workspace: "Workspace" = Relationship(back_populates="users")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    __table_args__ = (
        UniqueConstraint("user_id", "workspace_id", name="uq_user_workspace"),
    )


class UserOnboarding(SQLModel, table=True):
    __tablename__ = "user_onboarding"  # type: ignore

    id: UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: UUID = Field(
        foreign_key="user.id", ondelete="CASCADE", unique=True, index=True
    )
    current_step: int = Field(
        default=1, ge=1, le=3, description="Current onboarding step (1-3)"
    )
    is_completed: bool = Field(
        default=False, description="Whether onboarding is completed"
    )
    selected_provider: CloudProvider | None = Field(
        default=None, description="Selected cloud provider"
    )
    completed_at: datetime | None = Field(default=None)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )

    user: "User" = Relationship(back_populates="onboarding")
