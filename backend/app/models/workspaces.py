import uuid
from datetime import UTC, datetime
from typing import TYPE_CHECKING, Optional

from pydantic import BaseModel
from sqlalchemy.dialects.postgresql import ARRAY as PostgreSQLArray
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    String,
    UniqueConstraint,
)

from .clouds import CloudProvider

if TYPE_CHECKING:
    from .agents import Agent
    from .builtin_tools import WorkspaceBuiltInTool
    from .clouds import (
        AWSAccount,
        AWSAccountDetail,
        GCPAccount,
        GCPAccountDetail,
    )
    from .connections import Connection
    from .knowledge_bases import KB
    from .resources import Resource
    from .tasks import Task
    from .users import User, UserWorkspace


class WorkspaceBase(SQLModel):
    name: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=1000)
    organization_name: str | None = Field(default=None)
    provider: CloudProvider | None = Field(default=None)


class WorkspaceCreate(BaseModel):
    name: str
    description: str
    organization_name: str
    provider: CloudProvider


class WorkspaceUpdate(WorkspaceBase):
    name: str | None
    description: str | None
    organization_name: str | None
    provider: CloudProvider | None


class WorkspacePublic(WorkspaceBase):
    id: uuid.UUID
    is_default: bool
    is_deleted: bool = Field(default=False)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )


class WorkspaceDetail(WorkspacePublic):
    aws_account: Optional["AWSAccountDetail"] = Field(default=None)
    gcp_account: Optional["GCPAccountDetail"] = Field(default=None)
    settings: Optional["WorkspaceSetting"]


class WorkspacesPublic(SQLModel):
    data: list[WorkspacePublic]
    count: int


class WorkspaceSetting(SQLModel, table=True):
    __table_args__ = (
        UniqueConstraint("workspace_id", "provider", name="uix_workspace_provider"),
    )
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    workspace_id: uuid.UUID = Field(
        foreign_key="workspace.id",
        ondelete="CASCADE",
    )
    provider: CloudProvider = Field(default=CloudProvider.AWS)
    regions: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    types: list[str] = Field(default=[], sa_column=Column(PostgreSQLArray(String)))
    cron_pattern: str = Field(max_length=250)

    workspace: "Workspace" = Relationship(back_populates="settings")


class Workspace(WorkspaceBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    aws_account: Optional["AWSAccount"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    gcp_account: Optional["GCPAccount"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan", "uselist": False},
    )
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: "User" = Relationship(back_populates="own_workspaces")
    agents: list["Agent"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    tasks: list["Task"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    users: list["UserWorkspace"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    resources: list["Resource"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    connections: list["Connection"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(UTC),
        sa_column=Column(DateTime(timezone=True), nullable=False),
    )
    settings: "WorkspaceSetting" = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    builtin_tools: list["WorkspaceBuiltInTool"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    knowledge_bases: list["KB"] = Relationship(
        back_populates="workspace",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )
    is_default: bool = Field(default=False)
    is_deleted: bool = Field(default=False)

    def delete(self):
        self.is_deleted = True
        self.updated_at = datetime.now(UTC)
