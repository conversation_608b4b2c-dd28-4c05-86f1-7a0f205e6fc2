import asyncio
import os
import shutil
import tempfile
from pathlib import Path

from llama_index.core import Simple<PERSON>irectoryReader
from llama_index.core.schema import Document
from llama_index.readers.docling import DoclingReader

from app.core.config import settings
from app.logger import logger
from app.models import DocumentKB
from app.modules.knowledge_base.readers.pdf_multimodal_reader import (
    read_pdf_as_multimodal,
)
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository

FILE_EXTRACTORS = {
    ".pdf": "",
}


async def read(
    docs_to_ingest: list[DocumentKB],
    enable_pdf_multimodal: bool = False,
) -> tuple[list[Document], list[DocumentKB]]:
    """
    Process all documents by downloading them to a temp directory and using readers to process them all at once.

    Args:
        docs_to_ingest: List of documents to process
        enable_pdf_multimodal: Whether to enable multimodal processing for PDFs (converts to images)
    """
    if not docs_to_ingest:
        return [], []

    # Separate PDF and non-PDF documents
    pdf_docs = [
        doc
        for doc in docs_to_ingest
        if doc.file_type and ("pdf" in doc.file_type.lower())
    ]
    non_pdf_docs = [
        doc
        for doc in docs_to_ingest
        if not doc.file_type or ("pdf" not in doc.file_type.lower())
    ]

    # Debug logging
    if pdf_docs:
        logger.info(f"Found {len(pdf_docs)} PDF documents for multimodal processing")
        for doc in pdf_docs:
            logger.info(f"PDF document: {doc.file_name} (type: {doc.file_type})")

    all_docs = []
    all_successful_docs = []

    # Process PDF documents with multimodal support if enabled
    if pdf_docs and enable_pdf_multimodal:
        try:
            logger.info(
                f"Processing {len(pdf_docs)} PDF documents with multimodal support"
            )
            pdf_processed_docs, pdf_successful_docs = await read_pdf_as_multimodal(
                pdf_docs,
                include_images=True,
                dpi=300,
            )
            all_docs.extend(pdf_processed_docs)
            all_successful_docs.extend(pdf_successful_docs)
        except Exception as e:
            logger.error(f"Error in multimodal PDF processing: {e}")
            # Fallback to regular PDF processing
            logger.info("Falling back to regular PDF processing")
            pdf_processed_docs, pdf_successful_docs = await _process_regular_pdfs(
                pdf_docs
            )
            all_docs.extend(pdf_processed_docs)
            all_successful_docs.extend(pdf_successful_docs)
    elif pdf_docs:
        # Process PDFs with regular text extraction
        pdf_processed_docs, pdf_successful_docs = await _process_regular_pdfs(pdf_docs)
        all_docs.extend(pdf_processed_docs)
        all_successful_docs.extend(pdf_successful_docs)

    # Process non-PDF documents with existing logic
    if non_pdf_docs:
        (
            non_pdf_processed_docs,
            non_pdf_successful_docs,
        ) = await _process_non_pdf_documents(non_pdf_docs)
        all_docs.extend(non_pdf_processed_docs)
        all_successful_docs.extend(non_pdf_successful_docs)

    return all_docs, all_successful_docs


async def _process_regular_pdfs(
    docs_to_ingest: list[DocumentKB],
) -> tuple[list[Document], list[DocumentKB]]:
    """Process PDF documents using regular text extraction."""
    if not docs_to_ingest:
        return [], []

    tmp_dir = tempfile.mkdtemp()
    try:
        # Download all files concurrently to the temp directory
        download_tasks = [_download_document(doc, tmp_dir) for doc in docs_to_ingest]
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)

        # Create mapping between file paths and DocumentKB objects
        doc_to_file_map = {}
        successful_docs = []

        for doc, result in zip(docs_to_ingest, download_results, strict=False):
            if isinstance(result, Exception):
                logger.error(f"Failed to download {doc.object_name}: {result}")
            else:
                doc_to_file_map[result] = doc
                successful_docs.append(doc)

        if not doc_to_file_map:
            logger.warning("No files were successfully downloaded")
            return [], docs_to_ingest

        file_paths = list(doc_to_file_map.keys())
        all_docs = []

        # Process PDF files with SimpleDirectoryReader
        if file_paths:
            reader = SimpleDirectoryReader(input_files=file_paths)
            pdf_docs = await reader.aload_data()
            all_docs.extend(pdf_docs)

        # Add metadata to all documents
        for document in all_docs:
            # Find the corresponding DocumentKB by matching the source file
            matching_doc = _find_matching_document(document, doc_to_file_map)

            if matching_doc:
                if document.metadata is None:
                    document.metadata = {}
                document.doc_id = str(matching_doc.id)
                document.metadata.update(
                    {
                        "kb_id": str(matching_doc.kb_id),
                        "document_id": str(matching_doc.id),
                        "object_name": matching_doc.object_name,
                        "file_name": matching_doc.file_name,
                        "file_type": matching_doc.file_type,
                    }
                )

        return all_docs, successful_docs

    except Exception as e:
        logger.error(f"Error processing PDF documents: {e}", exc_info=True)
        return [], docs_to_ingest
    finally:
        # Clean up temp directory
        try:
            shutil.rmtree(tmp_dir)
        except OSError:
            logger.warning(f"Failed to clean up temp directory: {tmp_dir}")


async def _process_non_pdf_documents(
    docs_to_ingest: list[DocumentKB],
) -> tuple[list[Document], list[DocumentKB]]:
    """Process non-PDF documents using existing logic."""
    if not docs_to_ingest:
        return [], []

    tmp_dir = tempfile.mkdtemp()
    try:
        # Download all files concurrently to the temp directory
        download_tasks = [_download_document(doc, tmp_dir) for doc in docs_to_ingest]
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)

        # Create mapping between file paths and DocumentKB objects
        doc_to_file_map = {}
        successful_docs = []

        for doc, result in zip(docs_to_ingest, download_results, strict=False):
            if isinstance(result, Exception):
                logger.error(f"Failed to download {doc.object_name}: {result}")
            else:
                doc_to_file_map[result] = doc
                successful_docs.append(doc)

        if not doc_to_file_map:
            logger.warning("No files were successfully downloaded")
            return [], docs_to_ingest

        file_paths = list(doc_to_file_map.keys())
        all_docs = []

        # Process files with DoclingReader
        if file_paths:
            reader = DoclingReader()
            other_docs = await reader.aload_data(file_paths)
            all_docs.extend(other_docs)

        # Add metadata to all documents
        for document in all_docs:
            # Find the corresponding DocumentKB by matching the source file
            matching_doc = _find_matching_document(document, doc_to_file_map)

            if matching_doc:
                if document.metadata is None:
                    document.metadata = {}
                document.doc_id = str(matching_doc.id)
                document.metadata.update(
                    {
                        "kb_id": str(matching_doc.kb_id),
                        "document_id": str(matching_doc.id),
                        "object_name": matching_doc.object_name,
                        "file_name": matching_doc.file_name,
                        "file_type": matching_doc.file_type,
                    }
                )

        return all_docs, successful_docs

    except Exception as e:
        logger.exception(f"Error processing non-PDF documents: {e}")
        return [], docs_to_ingest
    finally:
        # Clean up temp directory
        try:
            shutil.rmtree(tmp_dir)
        except OSError:
            logger.warning(f"Failed to clean up temp directory: {tmp_dir}")


async def _download_document(doc: DocumentKB, tmp_dir: str) -> str:
    """
    Download a single document to the temp directory.
    Returns the local file path.
    """
    if not doc.object_name:
        raise ValueError(f"Document {doc.id} has no object_name")

    local_path = os.path.join(tmp_dir, Path(doc.object_name).name)
    ob_repo: BaseStorageRepository = get_object_storage_repository()
    await ob_repo.download_file(
        doc.object_name,
        file_path=local_path,
        bucket_name=settings.KB_BUCKET,
    )
    return local_path


def _find_matching_document(
    document: Document, doc_to_file_map: dict[str, DocumentKB]
) -> DocumentKB | None:
    """
    Find the matching DocumentKB object for a processed document by comparing file paths.
    """
    # Get the source path from document metadata
    source_path = None
    if document.metadata:
        source_path = document.metadata.get("file_path") or document.metadata.get(
            "source"
        )

    if source_path:
        # Direct path match
        if source_path in doc_to_file_map:
            return doc_to_file_map[source_path]

        # Match by filename if direct path doesn't work
        source_name = Path(source_path).name
        for file_path, _ in doc_to_file_map.items():
            if Path(file_path).name == source_name:
                return doc_to_file_map[file_path]

    # Fallback: if we only have one document, return it
    if len(doc_to_file_map) == 1:
        return next(iter(doc_to_file_map.values()))

    logger.warning(
        f"Could not find matching DocumentKB for processed document with source: {source_path}"
    )
    return None
