import re
from urllib.parse import urlparse
from uuid import UUID

from fastapi import HTTPException

from app.logger import logger
from app.models import KB, KBUsageMode


def validate_kb_access(kb: KB, user_id: UUID) -> None:
    """
    Validate that a user has access to a knowledge base.

    Args:
        kb: The knowledge base to check access for
        user_id: The user ID to check access for

    Raises:
        HTTPException: If user doesn't have access to the KB
    """
    # Owner always has access
    if kb.owner_id == user_id:
        return

    # For shared KBs, check if user is in allowed_users
    if kb.access_level == "shared" and user_id in kb.allowed_users:
        return

    # If we get here, user doesn't have access
    raise HTTPException(
        status_code=403,
        detail="You don't have access to this knowledge base",
    )


def validate_usage_mode(usage_mode: str | None) -> KBUsageMode | None:
    """
    Validate and convert usage_mode string to enum.

    Args:
        usage_mode: The usage mode string to validate

    Returns:
        KBUsageMode enum value or None if usage_mode is None

    Raises:
        HTTPException: If usage_mode is invalid
    """
    if not usage_mode:
        return None

    try:
        return KBUsageMode(usage_mode)
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid usage_mode: {usage_mode}")


def validate_url(url: str) -> None:
    """
    Validate URL for security and format compliance.

    Args:
        url: The URL to validate

    Raises:
        ValueError: If URL is invalid or contains security issues
    """
    if not url or len(url) > 2048:
        raise ValueError("URL is empty or too long")

    if not url.startswith(("http://", "https://")):
        raise ValueError("URL must start with http:// or https://")

    try:
        parsed = urlparse(url)
        hostname = parsed.hostname

        if not hostname:
            raise ValueError("Invalid URL hostname")

        # Block localhost and private IPs
        hostname_lower = hostname.lower()
        if (
            hostname_lower in ["localhost", "127.0.0.1", "::1"]
            or re.match(r"^10\.", hostname_lower)
            or re.match(r"^192\.168\.", hostname_lower)
            or re.match(r"^172\.(1[6-9]|2[0-9]|3[0-1])\.", hostname_lower)
            or re.match(r"^169\.254\.", hostname_lower)
        ):
            raise ValueError("Private or localhost URLs are not allowed")

        # Check for suspicious patterns
        suspicious_patterns = [
            r"javascript:",
            r"data:",
            r"vbscript:",
            r"<script",
            r"onload=",
            r"onerror=",
        ]
        for pattern in suspicious_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                raise ValueError("URL contains suspicious content")

    except Exception as e:
        if isinstance(e, ValueError):
            raise
        raise ValueError("Invalid URL format")


def validate_file_upload_security(
    file_content_type: str, filename: str
) -> tuple[str, str]:
    """
    Validate file upload security including MIME type and filename.

    Args:
        file_content_type: The MIME type of the file
        filename: The original filename

    Returns:
        Tuple of (sanitized_filename, validated_content_type)

    Raises:
        HTTPException: If file type is not allowed or filename is invalid
    """
    from app.schemas.mime_type import (
        sanitize_filename,
        validate_mime_type_for_kb,
    )

    # Validate MIME type
    if not validate_mime_type_for_kb(file_content_type):
        raise HTTPException(
            status_code=400,
            detail=f"File type '{file_content_type}' is not allowed",
        )

    # Sanitize filename
    try:
        sanitized_filename = sanitize_filename(filename)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid filename: {str(e)}")

    return sanitized_filename, file_content_type


def validate_urls_batch(urls: list[str] | None) -> None:
    """
    Validate a batch of URLs.

    Args:
        urls: List of URLs to validate (can be None)

    Raises:
        HTTPException: If any URL is invalid
    """
    if not urls:
        return

    for url in urls:
        try:
            validate_url(url)
        except ValueError as e:
            raise HTTPException(
                status_code=400, detail=f"Invalid URL '{url}': {str(e)}"
            )


def normalize_url_for_comparison(url: str) -> str:
    """
    Normalize URL for comparison by removing trailing slashes and fragments.

    Args:
        url: The URL to normalize

    Returns:
        Normalized URL string
    """
    parsed = urlparse(url)
    normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path.rstrip('/')}"
    return normalized


def validate_crawling_url(url: str) -> tuple[bool, str]:
    """
    Validate URL specifically for web crawling operations.

    Args:
        url: The URL to validate for crawling

    Returns:
        Tuple of (is_valid, normalized_url)

    Raises:
        ValueError: If URL is invalid for crawling
    """
    try:
        validate_url(url)
        normalized = normalize_url_for_comparison(url)
        return True, normalized
    except ValueError as e:
        raise ValueError(f"URL not suitable for crawling: {str(e)}")


def log_validation_error(operation: str, error: Exception, **context) -> None:
    """
    Log validation errors with context for debugging.

    Args:
        operation: The operation that failed validation
        error: The validation error
        **context: Additional context to log
    """
    logger.warning(
        f"Validation failed for {operation}: {str(error)}",
        extra={"operation": operation, "context": context},
    )
