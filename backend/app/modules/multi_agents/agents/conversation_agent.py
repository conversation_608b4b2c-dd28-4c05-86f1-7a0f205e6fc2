from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.types import Command, StreamWriter

from app.core.redis.redis_manager import RedisManager
from app.logger import logger
from app.modules.multi_agents.agents.base import BaseAgent
from app.modules.multi_agents.common import ToolNode
from app.modules.multi_agents.core import Configuration, GlobalState
from app.modules.multi_agents.core.context_manager import ContextManager
from app.modules.multi_agents.core.prompt_manager import prompt_manager
from app.modules.multi_agents.core.utils import (
    has_tool_calls,
    build_retryable_model_with_fallback,
)


class ConversationalAgent(BaseAgent, ToolNode):
    graph: StateGraph

    def __init__(self):
        self.context_manager = ContextManager()
        self.build_graph()

    async def _prepare_tools(
        self,
        configuration: Configuration,
        agent_config,
        state: GlobalState,
        config: RunnableConfig,
    ):
        tool_manager = configuration.tool_manager
        tools = await tool_manager.get_tools(
            agent_config=agent_config,
            state=state,
        )
        return tools

    def _update_token_usage(self, state: GlobalState, response: AIMessage):
        if not response.usage_metadata:
            return state

        state.instance_states[state.name].total_tokens = response.usage_metadata.get(
            "total_tokens", 0
        )

        return state

    def _handle_model_response(self, state: GlobalState, response: AIMessage):
        # Check for tool calls
        has_tool_calls_response = has_tool_calls(response)

        # Update messages
        state.instance_states[state.name].messages.append(response)

        if not has_tool_calls_response:
            # Should end with a group chat tool call
            rem = list(state.instance_states[state.name].messages)
            rem.append(HumanMessage(content="PLEASE END WITH A GROUP CHAT TOOL CALL"))
            state.instance_states[state.name].messages = rem
            return Command(goto="reasoning_agent", update=state)

        # Reset should_end_after_tools
        state.instance_states[state.name].should_end_after_tools = False

        # Handle tool flow
        state.executed_tools = [
            {
                "name": tool_call["name"],
                "id": tool_call["id"],
                "executed": False,
            }
            for tool_call in response.tool_calls
        ]
        state.last_message = response
        return Command(goto="run_tools", update=state)

    async def reasoning_agent(
        self,
        state: GlobalState,
        config: RunnableConfig,
        writer: StreamWriter,
    ) -> Command[str]:
        """Call the LLM powering our agent."""

        writer({"type": "on_agent_start", "content": state.name})

        # Get the instance state and configuration
        configuration = Configuration.from_runnable_config(config)
        agent_config = configuration.agents_config.agents[state.name.lower()]

        # Detect and store manual tools from current user prompt
        prompt_manager.detect_and_store_manual_tools(configuration.user_prompt, state)

        # Prepare tools (model not used here; helper builds models)
        tools = await self._prepare_tools(configuration, agent_config, state, config)

        system_prompt = await prompt_manager.get_conversation_agent_system_prompt(
            agent_config=agent_config, state=state, config=config
        )

        # Check Redis for cached task-specific memories
        self._check_and_override_cached_memories(configuration, state)

        objectives_prompt = await prompt_manager.get_objectives_prompt(
            agent_config=agent_config, state=state, config=config
        )

        # Build optimized cache hierarchy using ContextManager
        messages, state = await self.context_manager.build_message_hierarchy(
            state,
            config,
            system_prompt,
            objectives_prompt,
            configuration.attachment_content,
        )

        config["run_name"] = f"{state.name.capitalize()}"
        config["metadata"] = {
            "observation_type": "agent",
        }

        # Use shared helper to build retryable model with fallback
        model_chain = build_retryable_model_with_fallback(
            tools=tools,
            metadata=config.get("metadata", {}),
        )

        response = await model_chain.ainvoke(messages, config)
        assert isinstance(response, AIMessage)

        state = self._update_token_usage(state, response)

        return self._handle_model_response(state, response)

    def build_graph(self) -> None:
        """Build the agent system graph structure."""
        self.graph = StateGraph(GlobalState)
        self.graph.add_node("reasoning_agent", self.reasoning_agent)
        self.graph.add_node("run_tools", self.run_tools)
        self.graph.add_node("run_single_tool", self.run_single_tool)
        self.graph.add_edge("__start__", "reasoning_agent")

    def get_graph(self) -> StateGraph:
        """Return the final graph."""
        return self.graph

    def compile(self, **kwargs):
        return self.graph.compile(**kwargs)

    def _check_and_override_cached_memories(
        self, configuration: Configuration, state: GlobalState
    ) -> None:
        """Check Redis for cached task-specific memories and override configuration if found."""
        try:
            # Get workspace_id and agent_name from configuration
            workspace_id = getattr(configuration, "workspace_id", None)
            agent_name = state.name

            if not workspace_id or not agent_name:
                logger.debug(
                    f"Missing workspace_id ({workspace_id}) or agent_name ({agent_name}) "
                    "for Redis memory check"
                )
                return

            # Check Redis for cached memories
            redis_manager = RedisManager()
            if not redis_manager:
                logger.warning("Redis manager not found, skipping cache")
                return
            cache_key = f"memories:{workspace_id}:{agent_name}"
            cached_memories = redis_manager.get(cache_key)

            if cached_memories:
                # Override the memory_prompt with cached task-specific memories
                configuration.memory_prompt = cached_memories
                logger.info(
                    f"Using cached task-specific memories for agent {agent_name} "
                    f"in workspace {workspace_id}"
                )
            else:
                logger.debug(
                    f"No cached memories found for agent {agent_name} "
                    f"in workspace {workspace_id}, using default memory prompt"
                )

        except Exception as e:
            # Don't break the agent flow if Redis fails, just log and continue
            logger.exception(f"Error checking cached memories: {e}")
