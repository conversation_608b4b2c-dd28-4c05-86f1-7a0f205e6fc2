from typing import Callable, cast

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.state import CompiledStateGraph
from psycopg_pool import AsyncConnectionPool

from app.core.config import settings
from app.logger import logger
from app.modules.multi_agents.common import DatabaseOptimizer

from .base import BaseAgent


class AgentFactory:
    """Global Agent Factory for managing and accessing agents."""

    _instance = None
    _pre_compiled_agents: dict[str, CompiledStateGraph] = {}
    _agents: dict[str, BaseAgent] = {}
    _pool: AsyncConnectionPool | None = None
    _checkpointer: AsyncPostgresSaver | None = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    async def initialize(cls) -> None:
        """Initialize the PostgreSQL connection pool and checkpointer."""
        if cls._pool is not None:
            return  # Already initialized

        connection_kwargs = {
            "prepare_threshold": 0,
            "host": settings.POSTGRES_SERVER,
            "port": settings.POSTGRES_PORT,
            "user": settings.POSTGRES_USER,
            "password": settings.POSTGRES_PASSWORD,
            "dbname": settings.POSTGRES_DB,
            "options": f"-c search_path={settings.LANGGRAPH_SCHEMA_NAME}",
            # Required for operations such as CREATE INDEX CONCURRENTLY during
            # checkpointer setup which cannot run inside a transaction block.
            "autocommit": True,
        }

        # Create pool without auto-opening to avoid deprecation warning
        cls._pool = AsyncConnectionPool(
            max_size=50,
            kwargs=connection_kwargs,
            open=False,  # Explicitly disable auto-opening
        )
        # Explicitly open the pool
        await cls._pool.open()

        cls._checkpointer = AsyncPostgresSaver(cls._pool)

        # Create schema if not exists
        async with cls._pool.connection() as conn:
            await conn.execute(
                f"CREATE SCHEMA IF NOT EXISTS {settings.LANGGRAPH_SCHEMA_NAME};"
            )

        await cls._checkpointer.setup()

        # Optimize database indexes and handle bloat
        # await cls._optimize_database()

        # Test connection health
        try:
            async with cls._pool.connection() as conn:
                await conn.execute("SELECT 1")
                logger.info("LangGraph connection pool health check passed")
        except Exception as e:
            logger.error(f"LangGraph connection pool health check failed: {e}")
            await cls.cleanup()
            raise RuntimeError(f"Failed to establish healthy database connections: {e}")

        # Auto-register all graphs
        await cls._register_all_graphs()

    @classmethod
    async def _optimize_database(cls) -> None:
        """Optimize database by removing duplicate indexes, unused indexes, and handling bloat."""
        try:
            logger.info("Starting database optimization for LangGraph tables...")

            # Ensure pool is available
            if cls._pool is None:
                logger.warning(
                    "Connection pool not available for database optimization"
                )
                return

            # Create optimizer instance for the LangGraph schema
            optimizer = DatabaseOptimizer(cls._pool, settings.LANGGRAPH_SCHEMA_NAME)

            # Run optimization
            await optimizer.optimize_database()

            # Get optimization summary
            summary = await optimizer.get_optimization_summary()
            logger.info(f"Database optimization completed: {summary}")

        except Exception as e:
            logger.warning(f"Database optimization failed (non-critical): {e}")
            # Don't fail initialization if optimization fails

    @classmethod
    async def health_check(cls) -> bool:
        """Perform a health check on the connection pool."""
        if cls._pool is None:
            logger.warning("Connection pool not initialized")
            return False

        try:
            async with cls._pool.connection() as conn:
                await conn.execute("SELECT 1")
                logger.debug("Connection pool health check passed")
                return True
        except Exception as e:
            logger.error(f"Connection pool health check failed: {e}")
            return False

    @classmethod
    async def _register_all_graphs(cls) -> None:
        from app.modules.multi_agents.agents import CoordinatorAgent

        cls.register_agent(
            "coordinator_agent", lambda: cast(BaseAgent, CoordinatorAgent())
        )

    @classmethod
    async def cleanup(cls) -> None:
        """Cleanup resources when shutting down."""
        if cls._pool:
            await cls._pool.close()
            cls._pool = None
            cls._checkpointer = None
            cls._pre_compiled_agents.clear()

    @classmethod
    def register_agent(
        cls,
        name: str,
        builder: Callable[[], BaseAgent],
    ) -> None:
        """Register a new agent with the factory.

        Args:
            name: Name to register the agent under
            builder: Agent builder instance

        Raises:
            ValueError: If a graph with the same name exists
            RuntimeError: If provider not initialized
        """
        if name in cls._agents:
            raise ValueError(f"Graph {name} already registered")

        if cls._checkpointer is None:
            raise RuntimeError("Provider not initialized. Call initialize() first")

        cls._agents[name] = builder()
        cls._pre_compiled_agents[name] = cls._agents[name].compile(
            checkpointer=cls._checkpointer
        )

    @classmethod
    def get_pre_compiled_graph(cls, name: str) -> CompiledStateGraph | None:
        """Get a registered graph by name."""
        return cls._pre_compiled_agents.get(name)
