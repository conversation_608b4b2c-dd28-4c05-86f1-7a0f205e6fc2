import copy
import json
from uuid import UUID

from langchain_core.messages import <PERSON><PERSON>ess<PERSON>, ToolCall, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END
from langgraph.types import Command, StreamWriter

from app.core.config import settings
from app.logger import logger
from app.modules.multi_agents.core import Configuration, GlobalState
from app.modules.multi_agents.core.utils import (
    is_valid_tool_calls_message,
)
from app.modules.multi_agents.prompts import (
    INVALID_TOOL_NAME_ERROR_TEMPLATE,
    TOOL_CALL_ERROR_TEMPLATE,
    TOOL_PERMISSION_DENIED,
)
from app.repositories.agent_context import AgentContextRepository

from .tool_reponse_handler import ToolResponseHandler
from .permission import get_user_permission


class ToolNode:
    """Base class containing shared node functionality for agents."""

    def _truncate_large_response(self, response: str) -> str:
        """Truncate response if it exceeds the large response token limit.

        Args:
            response: The response content to check and potentially truncate

        Returns:
            Truncated response if it exceeds the limit, otherwise original response
        """
        # Estimate token count (rough approximation: 1 token ≈ 4 characters)
        estimated_tokens = len(response) // settings.CHARACTER_PER_TOKEN

        if estimated_tokens > settings.LARGE_RESPONSE_TRUNCATION_LIMIT_TOKENS:
            # Calculate the character limit based on token limit
            char_limit = (
                settings.LARGE_RESPONSE_TRUNCATION_LIMIT_TOKENS
                * settings.CHARACTER_PER_TOKEN
            )

            # Truncate the response and add a message about truncation
            truncated_response = (
                response[:char_limit] + settings.LARGE_RESPONSE_TRUNCATION_MESSAGE
            )

            logger.warning(
                f"Response truncated due to size limit. "
                f"Original estimated tokens: {estimated_tokens}, "
                f"Limit: {settings.LARGE_RESPONSE_TRUNCATION_LIMIT_TOKENS} tokens"
            )

            return truncated_response

        return response

    def _create_error_message(
        self,
        content: str,
        tool_call_id: str | None,
        tool_name: str = "",
        status: str = "error",
    ) -> ToolMessage:
        """Create a standardized error ToolMessage.

        Args:
            content: Error message content
            tool_call_id: ID of the tool call that failed
            tool_name: Name of the tool (optional)
            status: Status of the message (default: "error")

        Returns:
            ToolMessage with error details
        """
        return ToolMessage(
            content=content,
            tool_call_id=tool_call_id,
            name=tool_name,
            status=status,
        )

    def _handle_tool_response(
        self,
        tool_name: str,
        response: str,
        tool_call: dict,
        state: GlobalState,
        config: RunnableConfig,
        writer: StreamWriter,
    ) -> str | dict:
        """Handle tool-specific response processing.

        Returns:
            Processed response data
        """
        handler = ToolResponseHandler(writer)

        if tool_name == "group_chat":
            response_data = (
                response if isinstance(response, dict) else json.loads(response)
            )
            return handler.handle_group_chat_response(response_data, tool_call, state)
        elif tool_name == "visualize":
            return handler.handle_visualize_response(response, tool_call)
        elif tool_name == "planning":
            return handler.handle_planning_response(response, state)
        elif tool_name == "report":
            return handler.handle_report_response(response, tool_call)
        elif tool_name == "dashboard":
            return handler.handle_dashboard_response(response, tool_call)
        elif tool_name == "recommendation":
            return handler.handle_recommendation_response(response, tool_call, config)
        else:
            # Default handling for other tools
            return response

    async def _setup_tool_execution(
        self, state: GlobalState, config: RunnableConfig
    ) -> tuple[dict, object]:
        """Set up configuration and tools for execution.

        Returns:
            Tuple of (configuration, tools_by_name, agent_config)
        """
        configuration = Configuration.from_runnable_config(config)
        tool_manager = configuration.tool_manager
        agent_config = configuration.agents_config.agents[state.name]

        tools = await tool_manager.get_tools(
            agent_config=agent_config,
            state=state,
        )
        tools_by_name = {tool.name: tool for tool in tools}

        return tools_by_name, agent_config

    def _find_tool_call(self, state: GlobalState) -> ToolCall | None:
        """Find the current tool call from state."""
        last_message = state.last_message
        if not last_message:
            logger.error("No last message found")
            return None

        assert isinstance(last_message, AIMessage) and last_message.tool_calls

        tool_call = next(
            (
                tool
                for tool in last_message.tool_calls
                if tool["id"] == state.current_executed_tool_id
            ),
            None,
        )

        if not tool_call:
            logger.error("Tool call not found")
            return None

        return tool_call

    def _update_executed_tools(self, state: GlobalState) -> None:
        """Update the executed tools state to mark current tool as executed."""
        executed_tools: list[dict[str, str | bool]] = []

        for tool in state.executed_tools:
            if tool["id"] == state.current_executed_tool_id:
                executed_tools.append(
                    {
                        "name": tool["name"],
                        "id": tool["id"],
                        "executed": True,
                    }
                )
            else:
                executed_tools.append(tool)

        state.executed_tools = executed_tools

    def _validate_tool_exists(
        self, tool_call: dict, tools_by_name: dict, state: GlobalState
    ) -> Command | None:
        """Validate that the requested tool exists.

        Returns:
            Command to redirect to run_tools if validation fails, None if valid
        """
        if tool_call["name"] not in tools_by_name:
            error_msg = INVALID_TOOL_NAME_ERROR_TEMPLATE.format(
                requested_tool=tool_call["name"]
            )
            state.instance_states[state.name].messages.append(
                self._create_error_message(
                    content=error_msg,
                    tool_call_id=tool_call["id"],
                    tool_name=tool_call["name"],
                )
            )
            return Command(goto="run_tools", update=state)
        return None

    async def _check_tool_permissions(
        self, tool_call: dict, agent_config, state: GlobalState
    ) -> Command | None:
        """Check if tool requires permission and validate it.

        Returns:
            Command to redirect to run_tools if permission denied, None if allowed
        """
        require_permission = (
            True
            if tool_call["name"] in agent_config.tool_config.tool_permissions
            else False
        )
        if require_permission:
            permission_granted, approve_message = await get_user_permission(
                tool_call["name"],
                tool_call.get("args", {}),
                tool_call["id"],
            )
            if not permission_granted:
                state.instance_states[state.name].messages.append(
                    self._create_error_message(
                        content=TOOL_PERMISSION_DENIED.format(
                            tool_name=tool_call["name"],
                            approve_message=approve_message,
                        ),
                        tool_call_id=tool_call["id"],
                        tool_name=tool_call["name"],
                    )
                )
                return Command(goto="run_tools", update=state)
        return None

    def _auto_save_context(self, tool_call: dict, tool_content, agent_config) -> None:
        """Auto-save context for specific MCP tools."""
        if any(
            tool_substring in tool_call["name"]
            for tool_substring in settings.MCP_AUTO_CONTEXT_TOOLS
        ):
            try:
                logger.info(f"Auto-saving context for {tool_call['name']}")
                uuid_agent_id = UUID(agent_config.agent_id)
                title = f"Database Overview - {tool_call['name']}"
                context = str(tool_content)

                # Save to Redis with 30 minutes TTL
                agent_context_repository = AgentContextRepository()
                agent_context_repository.set_context(
                    agent_id=uuid_agent_id,
                    title=title,
                    context=context,
                    ttl_seconds=1800,  # 30 minutes
                )
            except Exception:
                logger.exception("Failed to auto-save context")

    async def run_tools(
        self, state: GlobalState, config: RunnableConfig, writer: StreamWriter
    ):
        """Execute tool calls and handle errors."""

        # Get the last message and check if it has tool calls
        last_message = state.last_message
        if not is_valid_tool_calls_message(last_message):
            logger.error("No tool calls found in message")
            return Command(goto="reasoning_agent", update=state)

        # At this point, we know last_message is an AIMessage with tool_calls
        assert isinstance(last_message, AIMessage) and last_message.tool_calls

        for tool in state.executed_tools:
            if tool["executed"]:
                continue
            else:
                assert isinstance(tool["id"], str)
                state.current_executed_tool_id = tool["id"]
                return Command(goto="run_single_tool", update=state)

        # Check if group_chat tool indicated conversation should end
        if state.instance_states[state.name].should_end_after_tools:
            return Command(goto=END, update=state)

        return Command(goto="reasoning_agent", update=state)

    async def _preprocess_tool_call(self, tool_call: ToolCall) -> ToolCall:
        """Preprocess tool call arguments if they're JSON strings."""
        if isinstance(tool_call["args"], dict):
            for key, value in tool_call["args"].items():
                if isinstance(value, str):
                    try:
                        parsed_value = json.loads(value)
                        tool_call["args"][key] = parsed_value
                    except (json.JSONDecodeError, TypeError):
                        pass
        return tool_call

    async def run_single_tool(
        self, state: GlobalState, config: RunnableConfig, writer: StreamWriter
    ):
        """Execute a single tool call with proper validation and error handling."""
        # Setup phase
        tools_by_name, agent_config = await self._setup_tool_execution(state, config)

        # Tool call extraction
        tool_call = self._find_tool_call(state)
        if not tool_call:
            return Command(goto="run_tools", update=state)

        tool_call = await self._preprocess_tool_call(tool_call)

        # State management
        self._update_executed_tools(state)

        # Validation phase
        validation_result = self._validate_tool_exists(tool_call, tools_by_name, state)
        if validation_result:
            return validation_result

        permission_result = await self._check_tool_permissions(
            tool_call, agent_config, state
        )
        if permission_result:
            return permission_result

        # Execution phase
        tool = tools_by_name[tool_call["name"]]
        tool_args = copy.deepcopy(tool_call["args"])
        tool_call_id = tool_call["id"]
        config["metadata"]["tool_call_id"] = tool_call_id
        config["metadata"]["agent_name"] = state.name
        config["metadata"]["observation_type"] = "tools"

        tool_content = None  # Initialize to avoid UnboundLocalError

        try:
            response: str | dict = await tool.ainvoke(input=tool_args, config=config)

            if isinstance(response, dict):
                tool_content = response
            else:
                tool_content = response

            # Handle tool-specific response processing
            tool_content = self._handle_tool_response(
                tool_call["name"], response, tool_call, state, config, writer
            )

            # Convert to string and apply truncation if needed
            content_str = (
                str(tool_content) if isinstance(tool_content, dict) else tool_content
            )
            truncated_content = self._truncate_large_response(content_str)

            state.instance_states[state.name].messages.append(
                ToolMessage(
                    content=truncated_content,
                    tool_call_id=tool_call["id"],
                    name=tool_call["name"],
                )
            )

        except Exception as e:
            error_msg = TOOL_CALL_ERROR_TEMPLATE.format(error=str(e))
            tool_content = (
                error_msg  # Set tool_content to error message for context saving
            )
            state.instance_states[state.name].messages.append(
                self._create_error_message(
                    content=error_msg,
                    tool_call_id=tool_call["id"],
                    tool_name=tool_call["name"],
                )
            )

        # Auto-save context for specific MCP tools (only if tool_content is not None)
        if tool_content is not None:
            self._auto_save_context(tool_call, tool_content, agent_config)

        return Command(goto="run_tools", update=state)
