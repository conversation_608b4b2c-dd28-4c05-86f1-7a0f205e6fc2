import json

from langchain_core.runnables import RunnableConfig
from langgraph.types import StreamWriter

from app.modules.multi_agents.core import GlobalState
from app.modules.multi_agents.tools.plan.model import PlanManager


class ToolResponseHandler:
    """Handles tool-specific response processing and writer notifications."""

    def __init__(self, writer: StreamWriter):
        self.writer = writer

    def handle_group_chat_response(
        self, response: dict, tool_call: dict, state: GlobalState
    ) -> dict:
        """Handle group_chat tool response."""
        tool_args = tool_call["args"]
        state.target_group_chat_member = tool_args["target_member"].lower()
        state.group_chat.messages.append((state.name, tool_args["message"]))
        # Set flag to end conversation after all tools are executed
        state.instance_states[state.name].should_end_after_tools = True
        return response

    def handle_visualize_response(self, response: str, tool_call: dict) -> str:
        """Handle visualize tool response."""
        self.writer(
            {
                "type": "on_chart_generation_response",
                "content": tool_call["args"],
            }
        )
        return response

    def handle_planning_response(self, response: str, state: GlobalState) -> str:
        """Handle planning tool response."""
        agent_ids = list(state.instance_states.keys())
        all_plans = PlanManager.get_all_agents_plans(state.plan_manager, agent_ids)
        self.writer(
            {
                "type": "planning",
                "content": all_plans,
            }
        )
        return response

    def handle_report_response(self, response: str, tool_call: dict) -> dict:
        """Handle report tool response."""
        command = tool_call["args"]["command"]
        response_data = json.loads(response)
        tool_content = {
            "status": response_data["status"],
            "message": response_data["message"],
        }
        if response_data.get("is_completed", None):
            tool_content["is_completed"] = response_data["is_completed"]
        if command == "get_report":
            tool_content["data"] = response_data.get("data", {})
        report_data = response_data.get("data", {})
        self.writer(
            {
                "type": "on_report_generation_response",
                "content": report_data,
            }
        )
        return tool_content

    def handle_dashboard_response(self, response: str, tool_call: dict) -> dict:
        """Handle dashboard tool response."""
        command = tool_call["args"]["command"]
        response_data = json.loads(response)
        tool_content = {
            "status": response_data["status"],
            "message": response_data["message"],
        }
        if command == "get_dashboard":
            tool_content["data"] = response_data.get("data", {})
        dashboard_data = response_data.get("data", {})
        self.writer(
            {
                "type": "on_dashboard_generation_response",
                "content": dashboard_data,
            }
        )
        return tool_content

    def handle_recommendation_response(
        self, response: str, tool_call: dict, config: RunnableConfig
    ) -> str:
        """Handle recommendation tool response."""
        command = tool_call["args"]["command"]
        if command == "create":
            tool_call_result = tool_call["args"]["new_recommendations"][
                "recommendations"
            ]
            resource_id = config["metadata"].get("resource_id")
            # Fix resource type
            for i, rcm in enumerate(tool_call_result):
                resource_type = rcm.get("resource_type")
                if isinstance(resource_type, str):
                    resource_type = resource_type.replace("_", " ").capitalize()
                tool_call_result[i]["resource_type"] = resource_type
            self.writer(
                {
                    "type": "on_recommendation_generation_response",
                    "content": tool_call_result,
                    "metadata": {
                        "resource_id": str(resource_id)
                        if resource_id is not None
                        else None,
                        "type": "recommendation",
                    },
                }
            )
        return response
