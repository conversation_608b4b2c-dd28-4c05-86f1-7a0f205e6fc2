from langchain_core.messages import (
    AIMessage,
    AnyMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
)
from langgraph.graph.state import RunnableConfig

from app.core.config import settings
from app.logger import logger
from app.modules.multi_agents.core.states.base import StateBase
from app.modules.multi_agents.core.states.global_state import GlobalState
from app.modules.multi_agents.core.utils import has_tool_calls, load_chat_model
from app.modules.multi_agents.prompts.summary_conv import SUMMARY_PROMPT
from app.services.attachment_service import AttachmentService


class ContextManager:
    """Main context manager for conversational agents."""

    def build_system_message(self, system_prompt: str) -> SystemMessage:
        """Build cached system message for AWS Bedrock."""
        payload = [
            {
                "type": "text",
                "text": system_prompt,
                # "cache_control": {"type": "ephemeral", "ttl": "1h"},
            },
            {"cachePoint": {"type": "default"}},
        ]
        return SystemMessage(content=payload)  # type: ignore

    def build_objectives_message(
        self,
        objectives_prompt: str,
        attachment_content: list | None = None,
        should_cache: bool = False,
    ) -> HumanMessage:
        payload = [
            {
                "type": "text",
                "text": objectives_prompt,
            },
        ]
        if attachment_content:
            payload.extend(attachment_content)

        if should_cache:
            payload.insert(0, {"cachePoint": {"type": "default"}})  # type: ignore

        return HumanMessage(content=payload)  # type: ignore

    def _fix_tool_call_messages(self, instance_state: StateBase) -> list[AnyMessage]:
        """Fix tool call messages by inserting missing tool messages for assistant responses."""
        # Get latest assistant message
        latest_assistant_message, latest_assistant_message_index = None, None
        for index, message in enumerate(reversed(instance_state.messages)):
            if isinstance(message, AIMessage):
                latest_assistant_message = message
                latest_assistant_message_index = (
                    len(instance_state.messages) - index - 1
                )
                break

        if latest_assistant_message:
            # Check if the latest assistant message has tool calls and if not have tool message append tool messages to base message
            has_tool_calls_response = has_tool_calls(latest_assistant_message)

            if has_tool_calls_response and latest_assistant_message_index is not None:
                # Check if there's a next message and if it's a ToolMessage
                next_message_index = latest_assistant_message_index + 1
                if next_message_index >= len(instance_state.messages) or not isinstance(
                    instance_state.messages[next_message_index], ToolMessage
                ):
                    # Insert a tool message with empty content and name
                    insert_tool_message = ToolMessage(
                        content="Unexpected error occurred",
                        tool_call_id=latest_assistant_message.tool_calls[0]["id"],
                        name=latest_assistant_message.tool_calls[0]["name"],
                    )
                    instance_state.messages.insert(
                        next_message_index, insert_tool_message
                    )
        return instance_state.messages

    def _find_turn_based_split_point(self, messages: list[AnyMessage]) -> int:
        """
        Find the optimal split point for message history compaction based on complete conversation turns.

        This method ensures that message history compaction preserves conversation integrity by:
        - Always keeping at least the last complete conversation turn
        - Never splitting in the middle of a user-AI exchange
        - Maintaining context for the AI to understand the current conversation flow

        Args:
            messages: List of conversation messages (HumanMessage, AIMessage, ToolMessage, etc.)

        Returns:
            int: Index where to split the message list for compaction.
                 Messages[:split_point] will be summarized,
                 Messages[split_point:] will be preserved in full.

        Example:
            Input: [H1, A1, H2, A2, H3] (H=Human, A=AI)
            Output: 2 (summarize [H1, A1], keep [H2, A2, H3])
        """
        # Handle edge case: empty message list
        if len(messages) == 0:
            return 0

        logger.info(f"Finding turn-based split point for {len(messages)} messages")

        # Phase 1: Find the last AI message to establish the end of the last complete turn
        # We search backwards to find the most recent AI response
        last_ai_index = -1
        for i in range(len(messages) - 1, -1, -1):
            if isinstance(messages[i], AIMessage):
                last_ai_index = i
                break

        # Edge case: No AI messages found (conversation only has human messages)
        # In this case, we keep all messages to avoid losing any information
        if last_ai_index == -1:
            logger.info(
                "No AI messages found, keeping all messages to preserve context"
            )
            return 0

        # Phase 3: Set the split point
        # Everything before this point will be summarized
        # Everything from this point onwards will be kept in full
        split_point = last_ai_index

        logger.info(
            f"Compaction strategy: summarize {split_point} messages, "
            f"preserve {len(messages) - split_point} recent messages"
        )
        return split_point

    async def _compact_message_history(
        self,
        instance_state: StateBase,
        objectives_prompt: str,
        config: RunnableConfig,
    ) -> tuple[str, list[AnyMessage]]:
        """Compact message history while preserving complete conversation turns."""
        logger.info(
            f"Starting turn-based message compaction for {len(instance_state.messages)} messages"
        )

        split_point = self._find_turn_based_split_point(instance_state.messages)

        # Split messages into old (to be summarized) and recent (to be preserved)
        messages_to_summarize = instance_state.messages[:split_point]
        recent_messages = instance_state.messages[split_point:]

        logger.info(
            f"Summarizing {len(messages_to_summarize)} messages, keeping {len(recent_messages)} recent messages"
        )

        if len(messages_to_summarize) == 0:
            # No messages to summarize, return empty summary
            return "", recent_messages

        message_history = f"""
        User: {objectives_prompt}
        """

        # Create message history for summarization
        message_history += "\n".join(
            f"{'Assistant' if isinstance(m, AIMessage) else 'Tool'}: {AttachmentService.format_message_content_for_summary(m.content)}"
            for m in messages_to_summarize
            if not isinstance(m, SystemMessage)
        )

        config["run_name"] = "Summarizer"
        model = load_chat_model(settings.SUMMARY_MODEL)

        try:
            response: AIMessage = await model.ainvoke(
                [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": SUMMARY_PROMPT,
                            },
                            {"cachePoint": {"type": "default"}},
                            {
                                "type": "text",
                                "text": message_history,
                            },
                        ],
                    }
                ],
                config,
            )

            logger.info(
                f"Successfully generated turn-based summary, length: {len(str(response.content))} characters"
            )
            return response.content, recent_messages  # type: ignore

        except Exception as e:
            logger.error(f"Failed to generate turn-based summary: {e}")
            raise

    def _need_compact_message_history(self, instance_state: StateBase):
        return (
            True
            if instance_state.total_tokens > settings.MAX_TOKEN_BEFORE_COMPACT
            else False
        )

    async def build_message_hierarchy(
        self,
        state: GlobalState,
        config: RunnableConfig,
        system_prompt: str,
        objectives_prompt: str,
        attachment_content: list | None = None,
    ) -> tuple[list[AnyMessage], GlobalState]:
        """
        Build message hierarchy for the agent.

        Returns:
            tuple: (full_message_hierarchy, updated_instance_messages)
                - full_message_hierarchy: Complete message list with system and objectives
                - updated_instance_messages: Updated instance state messages for clarity
        """
        try:
            # First, fix any tool call message issues when user interrupt the agent when calling tool
            instance_state = state.instance_states[state.name]
            instance_state.messages = self._fix_tool_call_messages(instance_state)

            # Then, build the message hierarchy
            system_message = self.build_system_message(system_prompt)

            should_cache = True if len(instance_state.messages) > 1 else False
            objectives_message = self.build_objectives_message(
                objectives_prompt, attachment_content, should_cache
            )

            context = list(instance_state.messages)

            if self._need_compact_message_history(instance_state):
                logger.info(
                    f"Turn-based message compaction needed - current tokens: {instance_state.total_tokens}, threshold: {settings.MAX_TOKEN_BEFORE_COMPACT}"
                )
                summary, recent_messages = await self._compact_message_history(
                    instance_state, objectives_prompt, config
                )

                if summary:
                    payload = [
                        {
                            "type": "text",
                            "text": f"Here is everything happened so far.\n{summary}",
                        },
                        {"cachePoint": {"type": "default"}},
                    ]
                    summary_message = HumanMessage(content=payload)  # type: ignore
                    context = [summary_message] + recent_messages
                else:
                    # No summary needed, just keep recent messages
                    context = recent_messages

                # Reset token count to estimate remaining tokens (recent messages)
                instance_state.total_tokens = sum(
                    [
                        msg.usage_metadata.get("total_tokens", 0)
                        for msg in recent_messages
                        if msg
                        and isinstance(msg, AIMessage)
                        and msg.usage_metadata is not None
                    ]
                )
                instance_state.messages = context
                logger.info(
                    f"Turn-based compaction completed - new context has {len(context)} messages, current tokens: {instance_state.total_tokens}"
                )

            messages = [
                system_message,
                *context,  # Flatten the context list instead of nesting it
                # NOTE: Objectives message is the last message in the hierarchy
                objectives_message,
            ]

            state.instance_states[state.name] = instance_state

            # Return both the complete hierarchy and the updated instance messages for clarity
            return messages, state
        except Exception as e:
            logger.exception(
                f"Error building message hierarchy during agent switching. Instance state: {getattr(state.instance_states[state.name], 'name', 'unknown')}, \
                    Message count: {len(getattr(state.instance_states[state.name], 'messages', []))}, \
                    Error: {e}"
            )
            raise e
