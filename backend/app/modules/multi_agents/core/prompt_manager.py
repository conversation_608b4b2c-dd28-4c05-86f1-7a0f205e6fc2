import re
from datetime import datetime

from langchain_core.runnables import RunnableConfig

from app.modules.multi_agents.config import AgentConfig
from app.modules.multi_agents.core.configuration import Configuration
from app.modules.multi_agents.core.states.global_state import GlobalState
from app.modules.multi_agents.prompts import (
    AGENT_ROLE_DESCRIPTION,
    CUSTOMER_ROLE_DESCRIPTION,
    DASHBOARD_PROMPT,
    OBJECTIVES_PROMPT,
    QUERY_SANDBOX_PROMPT,
    REGION_PROMPT,
    REPORT_PROMPT,
    ROLE_PLAY_PROMPT,
    ROLE_PLAYING_PROMPT,
    THINKING_PROMPT,
)


class PromptManager:
    def get_tool_usage_in_conversation(self, state: GlobalState) -> dict[str, bool]:
        """
        Get tool usage from manual_selected_tools in GlobalState.

        Returns:
            dict with keys: 'has_report', 'has_dashboard', 'has_visualize', 'has_query_sandbox'
        """
        if not state.manual_selected_tools:
            return {
                "has_report": False,
                "has_dashboard": False,
                "has_visualize": False,
                "has_query_sandbox": False,
            }

        # Convert to lowercase for case-insensitive matching
        has_report = bool("report" in state.manual_selected_tools)
        has_dashboard = bool("dashboard" in state.manual_selected_tools)
        has_visualize = bool("visualize" in state.manual_selected_tools)
        has_query_sandbox = bool("query_sandbox" in state.manual_selected_tools)

        return {
            "has_report": has_report,
            "has_dashboard": has_dashboard,
            "has_visualize": has_visualize,
            "has_query_sandbox": has_query_sandbox,
        }

    def detect_and_store_manual_tools(
        self, user_prompt: str, state: GlobalState
    ) -> list[str]:
        """
        Detect manual tools from current user prompt and store them persistently in GlobalState.

        Uses the same regex patterns as detect_tool_usage_in_conversation but only processes
        the current prompt. Detected tools are added to state.manual_selected_tools to
        persist across conversation turns.

        Args:
            user_prompt: Current user prompt to analyze
            state: GlobalState to store detected tools

        Returns:
            List of newly detected tools from current prompt
        """
        if not user_prompt:
            return []

        # Convert to lowercase for case-insensitive matching
        prompt_lower = user_prompt.lower()

        # Use same regex patterns as detect_tool_usage_in_conversation
        detected_tools = []

        # Look for hashtag patterns
        if re.search(r"#report\b", prompt_lower):
            detected_tools.extend(["report"])

        if re.search(r"#dashboard\b", prompt_lower):
            detected_tools.extend(["dashboard"])

        if re.search(r"#visualize", prompt_lower):
            detected_tools.append("visualize")

        if re.search(r"#query_sandbox\b", prompt_lower):
            detected_tools.append("query_sandbox")

        # Add to manual_selected_tools, avoiding duplicates
        for tool in detected_tools:
            if tool not in state.manual_selected_tools:
                state.manual_selected_tools.append(tool)

        return detected_tools

    def get_tool_specific_prompts(self, tool_usage: dict[str, bool]) -> str:
        """
        Get specific tool prompts based on detected tool usage using functional approach.
        """
        prompt_mappings = [
            (
                tool_usage["has_report"],
                f"\n{REPORT_PROMPT}",
            ),
            (
                tool_usage["has_dashboard"],
                f"\n{DASHBOARD_PROMPT}",
            ),
            (
                tool_usage["has_query_sandbox"],
                f"\n{QUERY_SANDBOX_PROMPT}",
            ),
        ]

        prompts = [prompt for condition, prompt in prompt_mappings if condition]
        return "\n\n".join(prompts) + "\n" if prompts else ""

    async def prepare_agent_list(
        self, state: GlobalState, config: RunnableConfig
    ) -> tuple[list[str], list[str]]:
        """Prepare messages for the conversational agent."""
        # Get the configuration
        configuration = Configuration.from_runnable_config(config)
        available_agents_list: list[str] = []
        unavailable_agents_list: list[str] = []

        for agent_name, agent_config in configuration.agents_config.agents.items():
            if agent_name in configuration.agents_config.active_agents:
                available_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )
            else:
                unavailable_agents_list.append(
                    AGENT_ROLE_DESCRIPTION.format(
                        role_name=agent_name,
                        role_description=agent_config.role,
                        role_goal=agent_config.goal,
                    )
                )

        return available_agents_list, unavailable_agents_list

    async def get_conversation_agent_system_prompt(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        """Get the system prompt for the conversation agent."""
        system_prompt = (
            THINKING_PROMPT
            + "\n\n"
            + ROLE_PLAY_PROMPT.format(
                name=state.name,
                role=agent_config.role,
                goal=agent_config.goal,
                instructions=agent_config.instructions,
            )
        )
        if agent_config.region_constraints:
            system_prompt += "\n\n" + REGION_PROMPT.format(
                region_constraints=agent_config.region_constraints
            )
        return system_prompt

    async def get_coordination_agent_role_playing_prompt(
        self, state: GlobalState, config: RunnableConfig
    ) -> str:
        """Get the role playing prompt for the coordination agent."""
        configuration = Configuration.from_runnable_config(config)
        available_agents_list, _ = await self.prepare_agent_list(state, config)

        role_playing_prompt = ROLE_PLAYING_PROMPT.format(
            name=state.name,
            role_descriptions="\n".join(available_agents_list),
            customer_role_description=CUSTOMER_ROLE_DESCRIPTION,
            group_chat=state.group_chat.get_messages(),
            last_group_chat_message=f"{state.group_chat.messages[-1][0]}: {state.group_chat.messages[-1][1]}",
            last_message=state.group_chat.last_message,
            participants=", ".join(configuration.agents_config.active_agents),
        )

        return role_playing_prompt

    async def get_objectives_prompt(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
        config: RunnableConfig,
    ) -> str:
        configuration = Configuration.from_runnable_config(config)
        available_agents_list, _ = await self.prepare_agent_list(state, config)

        # Detect tool usage patterns in conversation history
        conversation_history = state.group_chat.get_messages()
        tool_usage = self.get_tool_usage_in_conversation(state)
        tool_specific_prompts = self.get_tool_specific_prompts(tool_usage)

        objectives_prompt = OBJECTIVES_PROMPT.format(
            available_agents_list="\n".join(available_agents_list),
            group_chat=conversation_history,
            available_memories_prompt=configuration.memory_prompt,
            kb_prompt=configuration.kb_prompt,
            agent_context=agent_config.agent_context,
            resource_context=agent_config.resource_context,
            tool_specific_prompts=tool_specific_prompts,
            name=state.name,
            datetime=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            last_message=state.group_chat.last_message,
        )
        return objectives_prompt


prompt_manager = PromptManager()
