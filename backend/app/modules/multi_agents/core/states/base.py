"""Base state shared by all agents."""

from dataclasses import dataclass, field
from typing import Annotated

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages


@dataclass
class StateBase:
    """Base state shared by all agents."""

    # Messages
    messages: Annotated[list[AnyMessage], add_messages] = field(default_factory=list)

    # Track tokens for compacting the message history
    total_tokens: int = 0

    # Flag to indicate conversation should end after all tools are executed
    should_end_after_tools: bool = False


@dataclass
class InputState:
    """Input state shared by all agents."""

    messages: str = ""
