import json
from typing import Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import BaseTool, StructuredTool
from pydantic import BaseModel

from app.logger import logger
from app.models import ConnectionBase
from app.modules.connectors.mcp_client import MCPClientConnector
from app.modules.multi_agents.config import AgentConfig
from app.modules.multi_agents.core.states.global_state import GlobalState
from app.modules.multi_agents.prompts import CLI_TIPS_PROMPT
from app.modules.multi_agents.tools import (
    PlanManager,
    PlanningTool,
    call_executor,
    create_memory,
    dashboard,
    fetch_url,
    group_chat,
    push_alert,
    query_sandbox,
    report,
    schedule_task,
    search_internet,
    search_knowledge_base,
    use_console_read_only_permissions,
    use_console_write_permissions,
    visualize,
)
from app.modules.multi_agents.tools.recommendation.recommendation import recommendation

AVAILABLE_TOOLS: dict[str, BaseTool] = {
    "push_alert": push_alert,
    "visualize": visualize,
    "create_memory": create_memory,
    "recommendation": recommendation,
    "search_knowledge_base": search_knowledge_base,
    "search_internet": search_internet,
    "planning": PlanningTool,
    "group_chat": group_chat,
    "schedule_task": schedule_task,
    "report": report,
    "dashboard": dashboard,
    "query_sandbox": query_sandbox,
    "fetch_url": fetch_url,
    "use_console_read_only_permissions": use_console_read_only_permissions,
    "use_console_write_permissions": use_console_write_permissions,
}


class ToolManager:
    builtin_tools: dict[str, BaseTool] = AVAILABLE_TOOLS

    def __init__(
        self,
        mcp_servers: list[ConnectionBase] | None = None,
        connections_tools: dict[str, list[BaseTool]] | None = None,
    ) -> None:
        self.mcp_client = MCPClientConnector(mcp_servers) if mcp_servers else None
        self.connections_tools = connections_tools or {}

    def _filter_conflicting_tools(
        self,
        tool_names: list[str],
        state: GlobalState | None = None,
    ) -> list[str]:
        """
        Filter tools based on detected keywords in user prompt and persistent manual selections.

        Rules:
        - If #report detected → add report to tools and state.manual_selected_tools (persistent)
        - If #dashboard detected → add dashboard to tools and state.manual_selected_tools (persistent)
        - Include tools from state.manual_selected_tools (persistent across conversation)

        Args:
            tool_names: List of tool names to filter
            user_prompt: User prompt to analyze for keywords
            state: GlobalState containing persistent manual tool selections

        Returns:
            Filtered list of tool names
        """
        custom_tools = set()
        filtered_tools = tool_names.copy()

        # Add persistent manual tools from GlobalState
        if state and state.manual_selected_tools:
            custom_tools.update(state.manual_selected_tools)
            logger.info(f"Added persistent manual tools: {state.manual_selected_tools}")

        # Add custom tools to filtered tools if not already present
        for tool_name in list(custom_tools):
            if tool_name not in filtered_tools:
                filtered_tools.append(tool_name)

        return filtered_tools

    def _create_builtin_tool_instance(self, name: str, state: GlobalState) -> BaseTool:
        tool_creators = {
            "planning": lambda: self._create_planning_tool(state),
        }

        if name in tool_creators:
            return tool_creators[name]()
        return self.builtin_tools[name]

    def _create_planning_tool(self, state: GlobalState) -> PlanningTool:
        return (
            PlanningTool(plan_manager=state.plan_manager[state.name])
            if state
            else PlanningTool(plan_manager=PlanManager())
        )

    async def get_console_tools(self, console_tools_name: list[str]) -> list[BaseTool]:
        """Get console tools from the tool manager. Given console tools name, return the tools."""
        if not console_tools_name:
            return []

        tools: list[BaseTool] = []
        for name in console_tools_name:
            if "aws" in name:
                description = "AWS CLI to execute the AWS command."
            elif "gcp" in name:
                description = "GCP CLI to execute the GCP command."
            elif "azure" in name:
                description = "Azure CLI to execute the Azure command."
            elif "k8s" in name:
                description = "K8S CLI to execute the Kubernetes command."
            else:
                raise ValueError(f"Console tool {name} not found")

            if "use_cli_read_only" in name:
                description = description + CLI_TIPS_PROMPT

            async def call_console_tool(
                config: RunnableConfig,
                **arguments: dict[str, Any],
            ) -> tuple[str, None]:
                try:
                    call_tool_result = await call_executor(
                        script=str(arguments.get("script", "")),
                        timeout=int(arguments.get("timeout", 120)),  # type: ignore
                        config=config,
                    )
                except Exception as e:
                    return str(e), None
                return json.dumps(call_tool_result), None

            class ConsoleToolArgs(BaseModel):
                script: str
                timeout: int

            tool = StructuredTool(
                name=name,
                description=description,
                args_schema=ConsoleToolArgs,
                coroutine=call_console_tool,
                response_format="content_and_artifact",
            )
            tools.append(tool)
        return tools

    async def get_builtin_tools(
        self,
        builtin_tools_name: list[str],
        state: GlobalState,
    ) -> list[BaseTool]:
        """Get builtin tools from the tool manager with intelligent filtering."""
        if not builtin_tools_name:
            return []

        # Apply intelligent tool filtering based on user prompt and manual selections
        filtered_tool_names = self._filter_conflicting_tools(builtin_tools_name, state)

        tools: list[BaseTool] = []
        for name in filtered_tool_names:
            if name not in self.builtin_tools:
                continue
            tool: BaseTool = self._create_builtin_tool_instance(name, state)
            tools.append(tool)
        return tools

    async def get_mcp_tools(self, mcp_connections_name: list[str]) -> list[BaseTool]:
        if not mcp_connections_name or not self.mcp_client:
            return []

        tools: list[BaseTool] = await self.mcp_client.list_all_tools_by_server_names(
            mcp_connections_name
        )
        return tools

    async def get_connection_tools(self, connections_name: list[str]) -> list[BaseTool]:
        if not connections_name or not self.connections_tools:
            return []

        tools: list[BaseTool] = []
        for name in connections_name:
            if name not in self.connections_tools:
                continue
            tools.extend(self.connections_tools.get(name, []))
        return tools

    async def get_tools(
        self,
        agent_config: AgentConfig,
        state: GlobalState,
    ) -> list[BaseTool]:
        """Get tools from the tool manager with intelligent filtering."""

        builtin_tools_name: list[str] = agent_config.tool_config.builtin
        mcp_connections_name: list[str] = agent_config.mcp_connections or []
        builtin_connections_name: list[str] = agent_config.connections or []

        builtin_tools: list[BaseTool] = await self.get_builtin_tools(
            builtin_tools_name, state
        )
        mcp_tools: list[BaseTool] = await self.get_mcp_tools(mcp_connections_name)
        connection_tools: list[BaseTool] = await self.get_connection_tools(
            builtin_connections_name
        )
        return builtin_tools + mcp_tools + connection_tools
