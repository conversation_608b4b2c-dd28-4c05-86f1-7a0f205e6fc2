"""CLI tips and examples for console tools."""

CLI_TIPS_PROMPT = """\n
# Rules for Agent-Consumable Output:
- The script output is for the agent itself to read and process, NOT for human reading
- Do NOT add visual formatting, icons, or decorative elements (no emojis, borders, or separators)
- NEVER USE echo statements for section breaks, headers, or formatting (no "--------", "====", or similar)
- Focus on raw data extraction and minimal, parseable output
- Use plain text format with consistent delimiters for easy parsing
- Prioritize machine-readability over human presentation

# Tips:
- always consolidate related steps into single CLI Bash script if possible
- Only use read-only commands (e.g., list, describe, get) – never modify resources
- Always format CLI output as plain text (never JSON or table) so that it's easy for the agent to parse. For AWS use --output text , for Azure use --output tsv , for GCP use --format=text or --format="value(...)" as appropriate. Also use filtering/query flags to limit output to what is needed. These practices are crucial for efficiency and accuracy.

# Example of efficient CLI script:
```
# !/bin/bash
# Set date range for 30 days
END_TIME=$(date -u +"%Y-%m-%dT%H:%M:%S")
START_TIME=$(date -u -d "30 days ago" +"%Y-%m-%dT%H:%M:%S")

echo "RDS Metrics Summary ($START_TIME to $END_TIME)"

# Function to get RDS metrics with compact output
get_rds_metrics() {
    local instance_id=$1
    local metric_name=$2
    local stat_type=$3
    local unit=$4

    result=$(aws cloudwatch get-metric-statistics \
        --namespace AWS/RDS \
        --metric-name "$metric_name" \
        --dimensions Name=DBInstanceIdentifier,Value="$instance_id" \
        --start-time "$START_TIME" \
        --end-time "$END_TIME" \
        --period 86400 \
        --statistics "$stat_type" \
        --output text \
        --query "Datapoints[*].[Timestamp,$stat_type]" | sort)

    if [ -n "$result" ]; then
        echo "$metric_name ($stat_type):"
        echo "$result" | while read timestamp value; do
            date_only=$(echo $timestamp | cut -d'T' -f1)
            if [ "$unit" = "GB" ]; then
                # Convert bytes to GB and round to 1 decimal
                value_gb=$(echo "scale=1; $value / 1073741824" | bc -l)
                echo "  $date_only: ${value_gb}GB"
            elif [ "$unit" = "%" ]; then
                # Round percentage to 1 decimal
                value_rounded=$(printf "%.1f" $value)
                echo "  $date_only: ${value_rounded}%"
            else
                # Round to whole number for connections
                value_rounded=$(printf "%.0f" $value)
                echo "  $date_only: $value_rounded"
            fi
        done

        # Calculate and show summary stats
        if [ "$unit" = "GB" ]; then
            avg=$(echo "$result" | awk '{sum+=$2/1073741824; count++} END {printf "%.1f", sum/count}')
            echo "  → Avg: ${avg}GB"
        elif [ "$unit" = "%" ]; then
            avg=$(echo "$result" | awk '{sum+=$2; count++} END {printf "%.1f", sum/count}')
            max=$(echo "$result" | awk 'BEGIN{max=0} {if($2>max) max=$2} END {printf "%.1f", max}')
            echo "  → Avg: ${avg}%, Peak: ${max}%"
        else
            avg=$(echo "$result" | awk '{sum+=$2; count++} END {printf "%.0f", sum/count}')
            max=$(echo "$result" | awk 'BEGIN{max=0} {if($2>max) max=$2} END {printf "%.0f", max}')
            echo "  → Avg: $avg, Peak: $max"
        fi
        echo ""
    fi
}

# Collect metrics for both instances
for instance in "cloudthinker-prod-eks-aurora-1" "database-2-instance-1"; do
    echo "INSTANCE: $instance"

    # CPU Utilization
    get_rds_metrics "$instance" "CPUUtilization" "Average" "%"
    get_rds_metrics "$instance" "CPUUtilization" "Maximum" "%"

    # Memory metrics (convert to GB for readability)
    get_rds_metrics "$instance" "FreeableMemory" "Average" "GB"
    get_rds_metrics "$instance" "FreeableMemory" "Minimum" "GB"

    # Database connections
    get_rds_metrics "$instance" "DatabaseConnections" "Average" "count"
    get_rds_metrics "$instance" "DatabaseConnections" "Maximum" "count"

    echo ""
done
```
"""
