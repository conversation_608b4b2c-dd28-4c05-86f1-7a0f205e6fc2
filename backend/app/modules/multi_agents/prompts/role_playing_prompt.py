# NOTE: This is only used in coordinator agent
<PERSON><PERSON><PERSON>_PLAYING_PROMPT = """
You are in a role play game. The following roles are available:
{role_descriptions}
{customer_role_description}

Read the following conversation. Then select the next role from {participants} to play. Only return the role.

<group_chat>
{group_chat}
</group_chat>

The last message from the group chat is:
<last_group_chat_message>
{last_group_chat_message}
</last_group_chat_message>

The last message from the customer is:
<last_customer_message>
{last_message}
</last_customer_message>

Read the above conversation carefully. Then select the next role from {participants} to play based on these rules:

1. If there is a direct @ mention in the last group chat message, select that mentioned role
2. If the customer message appears complex (requires multiple steps or expertise areas), select the team member with manager role
3. If multiple team members are stuck or there's a lack of progress in the conversation, select the team member with manager role to provide guidance and direction
4. If team members are discussing without clear resolution or going in circles, select the team member with manager role to break the deadlock
5. Otherwise, select the most appropriate role based on the expertise needed
6. If the last group chat message SASTIFIY OR MENTION the customer, select role "Customer"

Remember:
- Direct mentions should be handled by the mentioned team member
- Direct technical questions can go straight to subject matter experts
- The team member with manager role should intervene when the team needs direction or is stuck
- The manager's role is to provide clear guidance, break deadlocks, and ensure progress
""".strip()

AGENT_ROLE_DESCRIPTION = """
Role name: {role_name}
- Description: {role_description}
- Goal: {role_goal}
"""

CUSTOMER_ROLE_DESCRIPTION = """
Role name: customer
- Description: A non-technical business professional who needs help with technical challenges. Has basic understanding of technology but relies on the technical team for implementation details and best practices. Focused on business outcomes and practical solutions that can help improve their work efficiency and effectiveness.
- Goal: To address technical challenges, including addressing permission issues and setting up the environment and tools needed for the team to improve their efficiency and effectiveness. You have access to the necessary environment and tools.
""".strip()
