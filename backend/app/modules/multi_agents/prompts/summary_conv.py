SUMMARY_PROMPT = """
Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions. This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing development work without losing context.

CRITICAL: You must begin with an "Analysis:" section that chronologically analyzes the conversation before providing the structured summary.

Structure your response as follows:

Analysis:
I need to analyze this conversation chronologically to capture all the technical work done and user requirements.

[Provide detailed chronological analysis of the conversation, including:
- Initial requests and context
- Task progression and implementation steps
- User feedback and corrections
- Technical implementation details
- Bug fixes and resolution approaches]

Summary:
1. Primary Request and Intent:
   [Capture all of the user's explicit requests and intents in detail, including any changes or refinements made during the conversation]

2. Key Technical Concepts:
   [List all important technical concepts, technologies, frameworks, and patterns discussed including:
   - Programming languages and frameworks used
   - APIs and libraries
   - Database concepts
   - Architecture patterns
   - Error handling approaches]

3. Files and Code Sections:
   [For each file examined, modified, or created, provide:
   - File path and name
   - **Why important**: Brief explanation of the file's role and significance
   - **Changes**: Summary of modifications made (if any)
   - **Code**: Key code snippets or examples that were implemented or discussed]

4. Errors and fixes:
   [For each error encountered:
   - **Error**: Detailed description of the issue
   - **Fix**: How the error was resolved
   - **User feedback**: Any specific user guidance or corrections provided]

5. Problem Solving:
   [Document problems solved and any ongoing troubleshooting efforts, including:
   - Root cause analysis approaches
   - Solution strategies implemented
   - Testing and validation steps taken]

6. All user messages:
   [List ALL user messages that are not tool results, exactly as written. These are critical for understanding the user's feedback and changing intent]

7. Pending Tasks:
   [List any pending tasks that have been explicitly requested but not yet completed]

8. Current Work:
   [Describe in detail precisely what was being worked on immediately before this summary, including:
   - Specific files and functions being modified
   - Code changes in progress
   - Context of the current implementation]

9. Optional Next Step:
   [Only if there are clear, explicitly requested next steps, describe what should be done next. Ensure this step is DIRECTLY in line with the user's explicit requests and the task that was being worked on immediately before this summary request]

Please provide your summary based on the conversation so far, following this exact structure and ensuring precision and thoroughness in your response.

There may be additional summarization instructions provided in the included context. If so, remember to follow these instructions when creating the above summary.
""".strip()
