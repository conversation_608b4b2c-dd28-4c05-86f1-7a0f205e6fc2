REPORT_PROMPT = """
### Report tool instructions:

<PERSON> (General Manager):
- If the user explicitly mentions creating a report using #report in their prompt:
  * Analyze the customer request
  * Use the `create_outline` command of #report tool to create the report outline
  * Guide corresponding and available agents in your team to execute the task. When guilding agents, ask them to return super detailed information to the group chat.
  * When writing the report, please dont use #recommend and #chart tools, use the table and chart inside the report tool instead.
  * When use the `update_sections` command, please just update max 2 sections at a time.
  * Once information is received and report is completed, you have to ask other agents to:
    - Use the `get_report` command of #report tool
    - Get the report content
    - Verify numbers in the report
  * Make sure to ask agents to verify before tell the customer the report is completed.
  * Make sure to call the `get_report` command of #report tool to get the report content before tell the customer the report is completed.

Other agents:
- If not <PERSON> (General Manager):
  * Can only use the `get_report` command of #report tool
  * Get the report and verify numbers in the report
  * Only do this after <PERSON> completes the report and asks you to do so

### Rules:
Purpose:
- Create and manage a structured, stakeholder-ready report during a conversation.

Available commands:
1) create_outline: Provide title, description, and initial sections (headers only).
2) update_sections: Update or add sections (by index). Only include fields you change.
3) remove_sections: Remove sections by index.
4) create_or_update_executive_summary: Provide the executive summary when ready.
5) get_report: Retrieve the current full report object.
6) get_instructions: Return these usage instructions.

Report generation rules (must follow):

1) Structure:
   - Executive summary → (optional) context/method → findings sections → recommendations → appendix.

2) Section pattern (strict):
   - Begin with a short paragraph that states the claim of the section (1–2 sentences).
   - Then include at most one visual (chart/table/card).
   - Immediately follow with a paragraph that interprets the visual and explains business impact (what, so‑what, now‑what).
   - Do NOT place two visuals back-to-back. Always insert a paragraph between non-paragraph content types.
   - Only place visuals side-by-side if explicitly comparing; add a comparison paragraph before and an interpretation paragraph after.

3) Visual requirements:
   - Use assertion-style titles that express the takeaway (e.g., "Storage spiked in EU after backup policy change").
   - Include units, timeframe, and labeled axes. Prefer direct labels; keep color/scale consistent across comparable visuals.
   - Add reference lines/bands for targets or thresholds where relevant and annotate anomalies.

4) KPI cards:
   - Limit to 3–5 critical metrics. Each card requires a nearby paragraph explaining implications.

5) Recommendations:
   - Prioritize and tie each recommendation to a specific finding; quantify expected impact when possible.

6) Output constraints for this tool’s schema:
   - The `sections[i].content` list must not contain two consecutive items where `type` ∈ {chart, table, card}. Insert a `paragraph` between them.
   - Every chart/table must be preceded by a `paragraph` and followed by a `paragraph` summarizing the insight and impact.
   - Keep paragraph text plain (no markdown) as required by the schema.
""".strip()

DASHBOARD_PROMPT = """
### Dashboard tool instructions:

Anna (General Manager):
- If the user explicitly mentions creating a dashboard using #dashboard in their prompt:
  * Analyze the customer request for dashboard requirements
  * Use the `create_dashboard` command of #dashboard tool to create the dashboard with title and grid configuration
  * Guide corresponding and available agents in your team to gather data for widgets. When guiding agents, ask them to return super detailed information to the group chat.
  * When building the dashboard, please dont use #recommend and #chart tools, use the widgets inside the dashboard tool instead.
  * Add widgets incrementally using the `add_widgets` command - start with KPI cards, then add charts, tables, and gauges as needed.
  * Use appropriate widget types for different data:
    - KPI cards for key metrics and numbers
    - Charts for trends and comparisons (bar, line, pie, area, radar)
    - Tables for detailed tabular data
    - Gauges for percentage or progress indicators
  * Once information is received and dashboard is completed, you have to ask other agents to:
    - Use the `get_dashboard` command of #dashboard tool
    - Get the dashboard content
    - Verify data accuracy in all widgets
  * Make sure to ask agents to verify before tell the customer the dashboard is completed.
  * Make sure to call the `get_dashboard` command of #dashboard tool to get the dashboard content before tell the customer the dashboard is completed.

Other agents:
- If not Anna (General Manager):
  * Can only use the `get_dashboard` command of #dashboard tool
  * Get the dashboard and verify data accuracy in all widgets
  * Only do this after Anna completes the dashboard and asks you to do so

### Rules:

Widget rules:
- KPI cards: keep to 3–6 key metrics; use consistent units and short labels.
- Charts: use appropriate types (trend=line/area, compare=bar, composition=stacked). Titles should be descriptive but neutral.
- Tables: concise columns; avoid wrapping text; ensure headers are clear.

Layout and consistency:
- Do not overlap widgets. Every widget must specify layout {x,y,w,h} and fit within the grid.
- Keep consistent colors/scales for the same metric across widgets.
- Reserve full-row width for crowded bar charts (w equals total columns when categories > 5).
- Prefer even vertical rhythm; avoid narrow, tall widgets for dense tables.

Output constraints for this tool's schema:
- All widgets MUST include a layout object with integer x, y, w, h.
""".strip()

QUERY_SANDBOX_PROMPT = """
### Query Sandbox tool instructions:

Anna (General Manager):
- If the user explicitly mentions using query_sandbox or #query_sandbox in their prompt:
  * Analyze the customer request for data analysis needs
  * Guide the conversation to ensure large CSV files are properly uploaded and processed
  * Use the `execute_query` command of #query_sandbox tool to run SQL queries on the imported data
  * Start with exploratory queries to understand the data structure (SELECT * FROM table_name LIMIT 10)
  * Guide corresponding and available agents in your team to analyze specific aspects of the data
  * Use appropriate SQL queries for different analysis needs:
    - COUNT, SUM, AVG for aggregations
    - GROUP BY for grouping and categorization
    - WHERE clauses for filtering
    - ORDER BY for sorting
    - JOIN operations if multiple tables are available
  * When presenting results, explain the business insights and implications
  * Use `list_tables` command to see available tables when needed
  * Use `get_table_info` command to understand table structure before querying

Other agents:
- If not Anna (General Manager):
  * Can use all #query_sandbox commands to analyze data
  * Focus on specific aspects of the data analysis as directed by Anna
  * Verify query results and suggest follow-up analyses
  * Help interpret the business meaning of query results

### Rules:

Query Sandbox rules:
- Always start with exploratory queries to understand data structure
- Use appropriate SQL syntax for PostgreSQL database
- Limit result sets when exploring large datasets (use LIMIT clause)
- Explain business insights from query results
- Use proper SQL aggregation functions for data analysis
- Handle NULL values appropriately in queries
- Use table aliases for complex queries to improve readability

Available commands:
1) execute_query: Run SQL queries against the sandbox database (supports SELECT, INSERT, UPDATE, DELETE)
2) list_tables: List all available tables in the sandbox database
3) get_table_info: Get detailed information about a specific table structure

SQL best practices:
- Use LIMIT to prevent overwhelming result sets
- Use EXPLAIN to understand query performance when needed
- Index important columns if query performance is an issue
- Use transactions for multiple related operations
- Validate data types before complex operations

Output format:
- Present query results in clear, readable format
- Explain what each query result means in business terms
- Suggest follow-up queries based on findings
- Highlight key insights and anomalies in the data
""".strip()
