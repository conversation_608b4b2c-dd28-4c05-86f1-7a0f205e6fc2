import json
from typing import Annotated, Literal
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.modules.multi_agents.tools.dashboard.schema import (
    DashboardWidget,
    GridConfig,
    LayoutConfig,
)
from app.services.dashboard_service import DashboardService


@tool
async def dashboard(
    command: Literal[
        "create_dashboard",
        "add_widgets",
        "update_widgets",
        "remove_widgets",
        "update_grid",
        "get_dashboard",
    ]
    | str,
    title: str | None = None,
    description: str | None = None,
    grid_config: GridConfig | str | None = None,
    widgets: list[DashboardWidget] | str | None = None,
    widget_positions: list[LayoutConfig] | None = None,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for managing dashboards in a conversation.

    ###### TOOL USE POLICY #####
    Tool Tag: #dashboard
    Tool Use Policy: #manual
    ##########################

    Commands:
    - create_dashboard: Initialize or overwrite the dashboard with `title`, `description`, `grid_config`, and `widgets`.
    - add_widgets: Add new `widgets` to the existing dashboard.
    - update_widgets: Update existing `widgets` in the dashboard.
    - remove_widgets: Remove widgets using `widget_positions`; accepts layout configs.
    - update_grid: Update the dashboard `grid_config`.
    - get_dashboard: Fetch the whole dashboard object.
    """
    try:
        workspace_id = UUID(config.get("configurable", {}).get("workspace_id"))
        conversation_id = UUID(config.get("configurable", {}).get("conversation_id"))

        # Validate and parse string inputs if needed
        normalized_widgets: list[DashboardWidget] | None = None
        if widgets is not None:
            if isinstance(widgets, str):
                try:
                    parsed_widgets = json.loads(widgets)
                    if not isinstance(parsed_widgets, list):
                        return json.dumps(
                            {"status": "error", "message": "widgets must be a list"}
                        )
                    from pydantic import TypeAdapter

                    widget_adapter = TypeAdapter(list[DashboardWidget])
                    normalized_widgets = widget_adapter.validate_python(parsed_widgets)
                except json.JSONDecodeError as e:
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Invalid JSON format for widgets: {str(e)}",
                        }
                    )
                except Exception as e:
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Validation error for widgets: {str(e)}",
                        }
                    )
            else:
                # Already a list, validate if needed for specific commands
                if command in ["add_widgets", "update_widgets"]:
                    from pydantic import TypeAdapter

                    widget_adapter = TypeAdapter(DashboardWidget)
                    tmp: list[DashboardWidget] = []
                    for widget in widgets:
                        tmp.append(widget_adapter.validate_python(widget))
                    normalized_widgets = tmp
                else:
                    normalized_widgets = widgets

        normalized_grid_config: GridConfig | None = None
        if grid_config is not None:
            if isinstance(grid_config, str):
                try:
                    parsed_config = json.loads(grid_config)
                    normalized_grid_config = GridConfig.model_validate(parsed_config)
                except json.JSONDecodeError as e:
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Invalid JSON format for grid_config: {str(e)}",
                        }
                    )
                except Exception as e:
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Validation error for grid_config: {str(e)}",
                        }
                    )
            else:
                if command in ["create_dashboard", "update_grid"]:
                    normalized_grid_config = GridConfig.model_validate(grid_config)
                else:
                    normalized_grid_config = grid_config

        # Define async helper functions
        async def _create_dashboard():
            return await DashboardService.create_dashboard(
                workspace_id,
                conversation_id,
                title,
                description,
                normalized_grid_config,
                normalized_widgets,
            )

        async def _add_widgets():
            return await DashboardService.add_widgets(
                workspace_id, conversation_id, normalized_widgets
            )

        async def _update_widgets():
            return await DashboardService.update_widgets(
                workspace_id, conversation_id, normalized_widgets
            )

        async def _remove_widgets():
            return await DashboardService.remove_widgets_by_positions(
                workspace_id, conversation_id, widget_positions
            )

        async def _update_grid():
            return await DashboardService.update_grid(
                workspace_id, conversation_id, normalized_grid_config
            )

        async def _get_dashboard():
            return await DashboardService.get_or_create_dashboard(
                workspace_id, conversation_id
            )

        # Command dispatch using unified async approach
        command_handlers = {
            "create_dashboard": _create_dashboard,
            "add_widgets": _add_widgets,
            "update_widgets": _update_widgets,
            "remove_widgets": _remove_widgets,
            "update_grid": _update_grid,
            "get_dashboard": _get_dashboard,
        }

        handler = command_handlers.get(command)
        if not handler:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Invalid command",
                }
            )

        return await handler()

    except Exception as e:
        logger.error(f"Dashboard tool error for command '{command}': {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to execute {command}: {str(e)}",
            }
        )
