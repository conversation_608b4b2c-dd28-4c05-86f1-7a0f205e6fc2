from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field

from ..shared_schema import (
    ChartStructuredOutput,
    KPICard,
    TableStructuredOutput,
)


class LayoutConfig(BaseModel):
    """
    Defines the position and size of a widget within the grid.
    'x' and 'y' are coordinates, while 'w' and 'h' are spans.
    """

    x: int = Field(description="The starting column of the widget (0-indexed).")
    y: int = Field(description="The starting row of the widget (0-indexed).")
    w: int = Field(description="The width of the widget in grid columns (column span).")
    h: int = Field(description="The height of the widget in grid rows (row span).")


class GridConfig(BaseModel):
    """Configuration for the overall dashboard grid."""

    columns: int = Field(
        default=12, description="The total number of columns in the dashboard grid."
    )


class ChartWidget(ChartStructuredOutput):
    """
    NOTE:
    - FOR BAR CHART AND MORE THEN 5 COLUMNS, PLEASE USE MAX WIDTH OF A ROW.
    """

    layout: LayoutConfig
    type: Literal["chart"] = "chart"


class KpiCardWidget(KPICard):
    """
    NOTE: FOR kpi_card, the width have to be 3.
    """

    layout: LayoutConfig
    type: Literal["kpi_card"] = "kpi_card"


class GaugeWidget(BaseModel):
    """A widget for displaying a value on a gauge, often a percentage."""

    layout: LayoutConfig
    type: Literal["gauge"] = "gauge"
    title: str = Field(description="The main title of the gauge.")
    value: float = Field(
        description="The primary value, typically a percentage from 0 to 100."
    )
    description: str | None = Field(
        default=None, description="A secondary line of text below the gauge."
    )


class TableWidget(TableStructuredOutput):
    """A widget for displaying tabular data."""

    layout: LayoutConfig
    type: Literal["table"] = "table"


# A Union of all possible widget types.
# This allows a list to contain different kinds of widgets.
# GaugeWidget is not included because it is not a good dashboard widget.
DashboardWidget = KpiCardWidget | TableWidget | ChartWidget


class DashboardInput(BaseModel):
    """
    A tool for managing dashboards in a conversation.

    ###### TOOL USE POLICY #####

    Tool Tag: #dashboard
    Tool Use Policy: #manual
    ##########################

    Commands:
    1. create_dashboard:
       Required: title, grid_config
       Optional: widgets, description
       NOTE: Creates a new dashboard with the specified title and grid configuration

    2. add_widgets:
       Required: widgets
       NOTE: Add new widgets to the existing dashboard

    3. update_widgets:
       Required: widgets
       NOTE: Update existing widgets (match by position or provide widget_ids)

    4. remove_widgets:
       Required: widget_positions
       NOTE: Remove widgets at specified grid positions

    5. update_grid:
       Required: grid_config
       NOTE: Update the dashboard grid configuration

    6. get_dashboard:
       Required: None
       NOTE: Get the complete dashboard configuration

    7. get_instructions:
       Required: None
       NOTE: Returns usage instructions for the dashboard tool.
    """

    command: Literal[
        "create_dashboard",
        "add_widgets",
        "update_widgets",
        "remove_widgets",
        "update_grid",
        "get_dashboard",
        "get_instructions",
    ]
    title: str | None = Field(default=None, description="Dashboard title")
    description: str | None = Field(default=None, description="Dashboard description")
    grid_config: GridConfig | None = Field(
        default=None, description="Grid configuration for the dashboard"
    )
    widgets: list[DashboardWidget] | None = Field(
        default=None, description="List of widgets to add or update"
    )
    widget_positions: list[LayoutConfig] | None = Field(
        default=None, description="Widget positions to remove"
    )

    class Config:
        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = False
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
