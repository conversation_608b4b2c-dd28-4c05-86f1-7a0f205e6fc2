from dataclasses import field
from typing import Annotated, Any, Literal

from langchain_core.runnables.base import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool
from pydantic import BaseModel, Field


class GroupChat(BaseModel):
    """Group chat between agents."""

    messages: list[tuple[str, str]] = field(default_factory=list)
    last_message: str = Field(description="Last message from the customer", default="")

    def get_messages(self) -> str:
        """Get the messages as a string."""
        return "\n\n".join([f"{msg[0]}:\n{msg[1]}" for msg in self.messages])

    def add_message(self, message: str, role: str):
        """Add a message to the group chat."""
        return GroupChat(
            messages=self.messages + [(role, message)],
            last_message=message,
        )


@tool
def group_chat(
    message: str,
    target_member: Literal["Customer"] | str,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> dict[str, Any]:
    """
    Send a targeted message to the group chat for team collaboration and customer communication.

    This tool facilitates structured communication within multi-agent workflows by enabling
    direct messaging to specific team members or the customer. Use this tool to coordinate
    tasks, share updates, request resources, or confirm completion status.

    CRITICAL REQUIREMENTS:
        - ALWAYS include @{target_member} mention in your message content
        - Use ONLY ONE target_member per message - no multiple mentions
        - Message must be clear, actionable, and contextually relevant
        - For Customer communication: Keep messages professional and outcome-focused

    USAGE PATTERNS:

    Customer Communication (target_member="Customer"):
        ✅ Task completion confirmations with clear deliverables
        ✅ Resource requests with specific requirements and justification
        ✅ Status updates on critical milestones
        ❌ Internal debugging information or technical details
        ❌ Partial progress reports without actionable outcomes

    Team Collaboration (target_member=team_member_name):
        ✅ Task delegation with clear expectations and deadlines
        ✅ Information sharing and context updates
        ✅ Blocker escalation with specific details
        ✅ Expertise requests and consultation
        ❌ Redundant status updates already covered elsewhere

    COMMUNICATION BEST PRACTICES:
        - Be specific about what you need or what you've accomplished
        - Include relevant context but avoid information overload
        - Use actionable language with clear next steps
        - Escalate blockers immediately with proposed solutions
        - Confirm understanding when receiving complex requests

    Args:
        message (str): The message content to send. Must include @{target_member} mention
                      and be appropriate for the intended recipient type.
        target_member (Literal["Customer"] | str): The message recipient. Use "Customer"
                     for end-user communication or specify the exact team member name
                     for internal coordination.

    Returns:
        dict[str, Any]: Status response containing:
            - status: "success" or "error"
            - message: Confirmation message or error details

    Examples:
        # Customer completion confirmation
        group_chat("@Customer Task completed: AWS cost analysis report generated with 15% savings identified.", "Customer")

        # Team member resource request
        group_chat("@DataAnalyst Need the latest billing data from Q4 2024 to complete optimization analysis.", "DataAnalyst")

        # Blocker escalation
        group_chat("@TechnicalLead Blocked on AWS API rate limits. Recommend implementing exponential backoff strategy.", "TechnicalLead")

    """
    from app.modules.multi_agents.core import Configuration

    configuration = Configuration.from_runnable_config(config)

    # Normalize target_member for comparison - agent names are stored in lowercase
    normalized_target = target_member.lower()
    normalized_agent_names = [
        name.lower() for name in configuration.agents_config.agents
    ]

    if normalized_target not in normalized_agent_names and target_member != "Customer":
        return {
            "status": "error",
            "message": f"Target member {target_member} is not a valid member of the workspace. Please select a valid member or use the Customer target.",
        }

    return {
        "status": "success",
        "message": f"Successfully sent message to the group chat with mentioning {target_member}",
    }
