import re
from typing import Annotated

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.services.kb.base import create_kb_services


def parse_document_mention(text: str) -> list[str] | None:
    # Pattern to match #collection_name in text
    pattern = r"#kbs?/([a-zA-Z0-9_-]+)"
    matches = re.findall(pattern, text, re.IGNORECASE)

    if matches:
        # Return the collection names in lowercase
        return [match.lower() for match in matches]

    return None


@tool
async def search_knowledge_base(
    query: str,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
):
    """
    ###### TOOL USE POLICY #####

    Tool Tag: #kb
    Tool Use Policy: #manual
    Note: NEVER USE THIS TOOL UNLESS THE CUSTOMER ASKED FOR IT.
    ##########################
    """
    from app.api.deps import get_async_session

    # Get collection_name from configurable if it exists
    available_kbs_for_search = config.get("configurable", {}).get(
        "available_kbs_for_search"
    )
    workspace_id = config.get("configurable", {}).get("workspace_id")

    # Handle case where available_kbs_for_search might be None
    if not available_kbs_for_search:
        return {
            "error": "You don't have any knowledge base configured. Please add some knowledge bases to your configuration."
        }

    kb_ids = [str(kb) for kb in available_kbs_for_search]

    if len(kb_ids) == 0:
        return {
            "error": "You don't have any knowledge base configured. Please add some knowledge bases to your configuration."
        }
    search_results = None

    try:
        async for session in get_async_session():
            kb_services = create_kb_services(session=session)
            search_service = kb_services["search"]
            search_results = await search_service.search(
                query=query,
                kb_ids=kb_ids,
                workspace_id=str(workspace_id),
            )
            break
    except Exception as e:
        logger.exception(f"Error searching knowledge base: {str(e)}", exc_info=True)
        return {"error": "Error searching knowledge base"}

    if not search_results:
        return "No results found"

    return search_results["response"]
