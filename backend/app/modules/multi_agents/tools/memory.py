from typing import Annotated

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.services.memory.schema import MemoriesExtraction
from app.tasks.memory_tasks import extract_memory


@tool
async def create_memory(
    inputs: MemoriesExtraction,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    Store tool execution patterns and error prevention strategies for future agent use.

    This tool captures successful tool call sequences, parameter patterns, and error recovery strategies
    to help agents avoid mistakes when calling tools in the future. Use this when you've found optimal
    tool usage patterns that prevent common errors.

    ## When to Create Memory

    Call create_memory after:
    - **Successful tool chains**: Documented sequences that work reliably
    - **Error recovery patterns**: How you fixed tool call errors
    - **Parameter optimization**: Discovered optimal parameter combinations
    - **API rate limiting solutions**: How you handled throttling gracefully
    - **Tool integration patterns**: How different tools work together efficiently
    - **Common mistake prevention**: Documented ways to avoid frequent errors

    ## Memory Node Structure

    Each memory node should contain:
    ```
    {
      "task": "Specific tool execution objective",
      "solution": "Exact tool call sequence with parameters",
      "context": "When to apply this pattern (conditions, prerequisites)"
    }
    ```

    ## Tool Error Prevention Examples

    **API Parameter Error Prevention:**
    ```
    {
      "task": "Prevent AWS Cost Explorer parameter validation errors",
      "solution": "Always validate TimePeriod format before calling get_cost_and_usage. Use Granularity='DAILY' for periods <90 days, 'MONTHLY' for longer periods. Include Service dimension filter to prevent data overflow",
      "context": "Apply before any Cost Explorer API calls to prevent validation errors"
    }
    ```

    **Rate Limiting Error Recovery:**
    ```
    {
      "task": "Handle AWS API throttling with exponential backoff",
      "solution": "When receiving ThrottlingException, implement exponential backoff: start at 1s, double each retry, add random jitter ±25%, maximum 60s delay, retry up to 5 times before failing",
      "context": "Use for any AWS API call that might hit rate limits"
    }
    ```

    **Tool Parameter Optimization:**
    ```
    {
      "task": "Optimize resource_crawler parameters for large datasets",
      "solution": "Use page_size=1000 for resource_crawler to prevent memory overflow, implement pagination for >10K resources, filter by service_type before crawling to reduce data volume",
      "context": "Essential for workspaces with >5K resources to prevent timeouts"
    }
    ```

    ## Error Prevention Focus

    Prioritize memories that help agents:
    - Use correct parameter formats and values
    - Handle API rate limits and throttling gracefully
    - Implement proper error recovery strategies
    - Optimize tool call sequences for efficiency
    - Avoid common parameter validation errors
    - Apply appropriate retry and backoff patterns

    Args:
        inputs: MemoriesExtraction object containing memory nodes with tool execution patterns
                and error prevention strategies

    Returns:
        Confirmation of memory creation task initiation with processing details
    """
    try:
        workspace_id = config.get("configurable", {}).get("workspace_id")

        # Prepare memory nodes for extraction
        nodes_dict: list[dict] = []
        agent_name = config.get("metadata", {}).get("agent_name")
        for node in inputs.memory_nodes:
            node_data = node.model_dump()
            if agent_name:
                node_data["role"] = agent_name
            nodes_dict.append(node_data)

        # Trigger the background memory extraction task
        task = extract_memory.delay(  # type: ignore
            memory_nodes_dict=nodes_dict,
            workspace_id=workspace_id,
        )

        logger.info(
            f"Memory creation task initiated for {len(nodes_dict)} nodes "
            f"in workspace {workspace_id}, task ID: {task.id}"
        )

        return "Memory creation task has been initiated successfully. "

    except Exception as e:
        logger.error(f"Failed to initiate memory creation task: {str(e)}")
        return f"Error: Failed to create memory - {str(e)}"
