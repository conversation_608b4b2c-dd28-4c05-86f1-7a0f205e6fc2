from datetime import datetime

from pydantic import BaseModel, Field

from .schema import Task


class PlanManager(BaseModel):
    """Manages the state of a single plan and its progress.

    This class handles the storage and retrieval of a plan, as well as managing
    its state. It provides methods for serializing and deserializing
    the plan state for persistence.
    """

    tasks: list[Task] | None = None
    updated_at: datetime = Field(default_factory=datetime.now)

    def set_state(self, tasks: list[Task]) -> None:
        """Set the current state of the plan.

        Enforces sequential blocking semantics:
        - If any task is marked as "blocked", all subsequent tasks (higher id) are also set to "blocked"
          unless already "cancelled" or explicitly "blocked".
        """
        # Sort by id to ensure sequential interpretation without mutating caller order beyond sequencing
        sorted_tasks = sorted(tasks, key=lambda t: t.id)

        has_prior_block = False
        for task in sorted_tasks:
            if has_prior_block:
                task.status = "blocked"
            if task.status == "blocked":
                has_prior_block = True

        self.tasks = sorted_tasks
        self.updated_at = datetime.now()

    def get_next_pending_task(self) -> Task | None:
        """Get the next pending task by sequential ID.

        Returns:
            The first task with status "pending" ordered by ID, or None if no pending tasks exist.
        """
        if not self.tasks:
            return None

        # Find the first task with "pending" status, sorted by ID
        pending_tasks = [task for task in self.tasks if task.status == "pending"]
        if not pending_tasks:
            return None

        # Return the task with the lowest ID (first in sequence)
        return min(pending_tasks, key=lambda t: t.id)

    def get_plan_dict(self) -> dict:
        """Get a dictionary mapping task IDs to their status types.

        Returns:
            A dictionary with task IDs as keys and status types as values.
        """
        if not self.tasks:
            return {
                "tasks": [],
                "updated_at": self.updated_at.isoformat(),
            }

        return {
            "tasks": [task.model_dump() for task in self.tasks],
            "updated_at": self.updated_at.isoformat(),
        }

    @staticmethod
    def get_all_agents_plans(
        plan_managers: dict[str, "PlanManager"], agent_ids: list[str]
    ) -> list[dict]:
        """Get the plans for all agents.

        Args:
            plan_managers: Dictionary mapping agent_id to PlanManager
            agent_ids: List of agent IDs to include in the result

        Returns:
            List of plans sorted by most recently updated first
        """
        plans = []
        for agent_id in agent_ids:
            if agent_id in plan_managers:
                plans.append(
                    {
                        "agent_id": agent_id,
                        **plan_managers[agent_id].get_plan_dict(),
                    }
                )

        plans.sort(key=lambda x: x["updated_at"], reverse=True)
        return plans
