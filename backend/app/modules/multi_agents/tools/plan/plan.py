from typing import Any, Annotated

from langchain_core.tools import ArgsSchema, BaseTool
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg
from app.logger import logger
from app.tasks.memory_tasks import search_task_memories

from .model import PlanManager
from .schema import PlanInput, Task
from .prompt import PLANNING_TOOL_DESCRIPTION, PLAN_RESPONSE


class PlanningTool(BaseTool):
    """A tool for managing multi-step tasks and tracking their progress."""

    name: str = "planning"
    description: str = PLANNING_TOOL_DESCRIPTION
    args_schema: ArgsSchema | None = PlanInput
    plan_manager: PlanManager
    agent_name: str | None = None
    workspace_id: str | None = None
    conversation_id: str | None = None

    def _run(  # type: ignore
        self,
        config: Annotated[RunnableConfig, InjectedToolArg],
        *args: Any,
        **kwargs: Any,
    ) -> str:
        # Prefer kwargs as langchain passes structured args via kwargs
        tasks: list[Task] | list[dict[str, Any]] | str | None = kwargs.get("tasks")
        # Fallback: first positional arg may be tasks
        if tasks is None and len(args) > 0:
            tasks = args[0]

        if tasks is None:
            raise ValueError("'tasks' is required and was not provided")

        # Delegate to creation; PlanInput/Task validators will normalize
        if config:
            self.agent_name = config.get("metadata", {}).get("agent_name")
            self.workspace_id = config.get("configurable", {}).get("workspace_id")
            self.conversation_id = config.get("configurable", {}).get("conversation_id")
        return self._create_plan(tasks)  # type: ignore[arg-type]

    def _create_plan(self, tasks: list[Task] | list[dict[str, Any]] | str) -> str:
        """Write or overwrite the entire plan."""
        # Store old plan state for comparison
        old_tasks = self.plan_manager.tasks or []

        # Normalize input via schema to ensure proper Task instances
        plan_input = PlanInput(tasks=tasks)  # type: ignore[arg-type]
        new_tasks = plan_input.tasks

        # Update plan state
        self.plan_manager.set_state(new_tasks)

        # Trigger memory search for next task
        self._trigger_memory_search_if_needed(old_tasks, new_tasks)

        return PLAN_RESPONSE.format(plan=[task.model_dump() for task in new_tasks])

    def _trigger_memory_search_if_needed(
        self, old_tasks: list[Task], new_tasks: list[Task]
    ) -> None:
        """Trigger memory search when tasks are completed or plan is first created."""
        if not self.agent_name or not self.workspace_id:
            logger.warning("Missing agent_name or workspace_id for memory search")
            return

        # Case 1: Initial plan creation (no old tasks)
        if not old_tasks and new_tasks:
            first_pending_task = self.plan_manager.get_next_pending_task()
            if first_pending_task:
                self._launch_memory_search_task(first_pending_task.content)
            return

        # Case 2: Task completion detection
        # Create lookup dicts for efficient comparison
        old_task_status = {task.id: task.status for task in old_tasks}
        new_task_status = {task.id: task.status for task in new_tasks}

        # Find newly completed tasks
        newly_completed_task_ids = []
        for task_id, new_status in new_task_status.items():
            old_status = old_task_status.get(task_id, "pending")
            if old_status != "completed" and new_status == "completed":
                newly_completed_task_ids.append(task_id)

        # If any tasks were completed, search memory for next pending task
        if newly_completed_task_ids:
            next_pending_task = self.plan_manager.get_next_pending_task()
            if next_pending_task:
                logger.info(
                    f"Tasks {newly_completed_task_ids} completed, searching memories "
                    f"for next task: {next_pending_task.id}"
                )
                self._launch_memory_search_task(next_pending_task.content)

    def _launch_memory_search_task(self, task_content: str) -> None:
        """Launch fire-and-forget Celery task to search and cache memories."""
        try:
            if not self.agent_name or not self.workspace_id:
                logger.warning(
                    "Missing agent_name or workspace_id for memory search, skipping"
                )
                return

            # Use Celery task for background memory search
            task = search_task_memories.delay(  # type: ignore
                task_content, self.agent_name, self.workspace_id, self.conversation_id
            )
            logger.info(
                f"Memory search task launched for content: '{task_content}', "
                f"task ID: {task.id}"
            )
        except Exception as e:
            logger.exception(f"Failed to launch memory search task: {e}")
