PLANNING_TOOL_DESCRIPTION = """
Use this tool to WRITE or OVERWRITE the entire structured task list (plan) for your current complex task or project. Each call replaces any existing plan. This helps you track progress, organize complex tasks, and demonstrate thoroughness to the user. It also helps the user understand the progress of the task and overall progress of their requests. The plan can be used for any type of task, whether it's coding, infrastructure, documentation, analysis, or any other structured work that needs to be broken down into steps.

## CRITICAL: Sequential Task Execution Rules
MUST FOLLOW THESE RULES STRICTLY:
1. Tasks MUST be executed in the exact order specified by their ID numbers (1, 2, 3, etc.)
2. You CANNOT start a task until ALL previous tasks are marked as "completed"
3. Only ONE task can be "in_progress" at any time
4. You MUST complete the current task before moving to the next one
5. If a task fails or is blocked, you MUST resolve it before proceeding to subsequent tasks
6. If any task is marked as "blocked", all subsequent tasks (with higher IDs) are implicitly "blocked" until that task is unblocked and completed

## When to Use This Tool
Use this tool proactively in these scenarios:

1. Complex multi-step tasks - When a task requires 3 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. User explicitly requests todo list - When the user directly asks you to use the todo list
4. User provides multiple tasks - When users provide a list of things to be done (numbered or comma-separated)
5. After receiving new instructions - Immediately capture user requirements as todos
6. When you start working on a task - Mark it as in_progress BEFORE beginning work. Ideally you should only have one todo as in_progress at a time
7. After completing a task - Mark it as completed and add any new follow-up tasks discovered during implementation

## When NOT to Use This Tool

Skip using this tool when:
1. There is only a single, straightforward task
2. The task is trivial and tracking it provides no organizational benefit
3. The task can be completed in less than 3 trivial steps
4. The task is purely conversational or informational

NOTE that you should not use this tool if there is only one trivial task to do. In this case you are better off just doing the task directly.

## Examples of When to Use the Plan Tool

### Example 1: Cloud Infrastructure Migration
User: "Help me migrate our AWS infrastructure to use Terraform and implement cost optimization strategies."
Assistant: I'll help you migrate to Terraform and optimize costs. Let me create a todo list to track this complex infrastructure project.
*Creates todo list with the following items:*
1. Audit current AWS resources and document existing architecture
2. Create Terraform modules for core infrastructure components
3. Implement resource tagging strategy for cost tracking
4. Set up automated cost monitoring and alerting
5. Migrate resources incrementally with zero-downtime approach
6. Validate migrated infrastructure and run cost analysis
*Begins working on the first task*

Reasoning: The assistant used the todo list because:
1. Infrastructure migration involves multiple complex steps across different services
2. Cost optimization requires systematic analysis and implementation
3. The project needs careful tracking to ensure no resources are missed
4. Each step has dependencies and requires validation before proceeding

### Example 2: Database Performance Analysis and Optimization
User: "Our PostgreSQL database is experiencing slow queries and high CPU usage. Can you help identify and fix the performance issues?"
Assistant: I'll analyze your database performance issues systematically. Let me create a todo list to track this optimization project.
*Creates todo list with the following items:*
1. Analyze current database metrics and identify bottlenecks
2. Review slow query logs and execution plans
3. Examine index usage and identify missing or redundant indexes
4. Optimize problematic queries and stored procedures
5. Implement connection pooling and caching strategies
6. Set up monitoring and alerting for ongoing performance tracking
*Begins working on the first task*

Reasoning: The assistant used the todo list because:
1. Database optimization requires multiple diagnostic and remediation steps
2. Performance issues often have interconnected causes requiring systematic approach
3. Each optimization step needs validation before moving to the next
4. Tracking progress helps ensure all performance aspects are addressed

## Examples of When NOT to Use the Plan Tool

### Example 1: Simple Configuration Query
User: "What's the recommended instance type for a PostgreSQL database with 100GB storage?"
Assistant: For a PostgreSQL database with 100GB storage, I recommend:

**AWS RDS**: db.t3.large (2 vCPUs, 8GB RAM) or db.m5.large (2 vCPUs, 8GB RAM) for general purpose workloads
**Instance considerations**:
- Choose db.m5.large for consistent performance
- Consider db.r5.large (memory-optimized) if you have high concurrent connections
- Use gp3 storage for better price/performance than gp2

Reasoning: The assistant did not use the todo list because this is a straightforward informational request requiring domain expertise but no multi-step implementation.

### Example 2: Single Resource Check
User: "Can you check the current CPU utilization of our production database?"
Assistant: I'll check the CPU utilization of your production database.

*Queries monitoring system*

Current CPU utilization: 45%
- Average over last hour: 52%
- Peak in last 24h: 78%
- Status: Normal operating range

The database is performing within acceptable parameters.

## Task States and Management

1. **Task States**: Use these states to track progress:
   - pending: Task not yet started
   - in_progress: Currently working on (limit to ONE task at a time)
   - completed: Task finished successfully
   - blocked: Task cannot proceed due to a dependency, error, or external requirement

2. **Task Management**:
   - Update task status in real-time as you work
   - Mark tasks complete IMMEDIATELY after finishing (don't batch completions)
   - Only have ONE task in_progress at any time
   - Complete current tasks before starting new ones
   - Remove tasks that are no longer relevant from the list entirely

3. **Task Breakdown**:
   - Create specific, actionable items
   - Break complex tasks into smaller, manageable steps
   - Use clear, descriptive task names

## Overwrite Semantics (Important)
- Every call to this tool REPLACES the entire plan. There is no partial update command.
- Always send the FULL, current list of tasks you want to track.
- Preserve task IDs consistently across calls so historical progress can be interpreted.
- Respect sequential constraints: only one task may be in_progress, and any blocked task implies all subsequent tasks are blocked until resolved.

When in doubt, use this tool. Being proactive with task management demonstrates attentiveness and ensures you complete all requirements successfully.
"""

PLAN_RESPONSE = """
Plan has been modified successfully. Ensure that you continue to use the plan to track your progress. Please proceed with the current tasks if applicable

<system-reminder>
Your plan has changed. DO NOT mention this explicitly to the user. Here are the latest contents of your plan:
{plan}
Continue on with the tasks at hand if applicable.
</system-reminder>
""".strip()
