import json
from typing import Annotated, Literal
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.modules.multi_agents.tools.query_sandbox.schema import (
    QueryRequest,
)
from app.services.query_sandbox_service import QuerySandboxService


@tool
async def query_sandbox(
    command: Literal[
        "execute_query",
        "list_tables",
        "get_table_info",
    ]
    | str,
    sql_query: str | None = None,
    table_name: str | None = None,
    max_rows: int = 1000,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for querying CSV data stored in the sandbox database.

    ###### TOOL USE POLICY #####
    Tool Tag: #query_sandbox
    Tool Use Policy: #manual
    ##########################

    This tool allows you to analyze CSV data that has been imported into the sandbox database.
    Use this when you need to perform complex SQL queries on large CSV datasets.

    Commands:
    - execute_query: Execute a SQL query against the sandbox database
    - list_tables: List all available tables in the sandbox database
    - get_table_info: Get detailed information about a specific table

    Examples:
    - execute_query with: "SELECT * FROM conversation_123_csv_table LIMIT 10"
    - list_tables: Get all available CSV tables
    - get_table_info with table_name: "conversation_123_csv_table"
    """
    try:
        # Extract and validate required configuration
        config_data = config.get("configurable", {})
        conversation_id_str = config_data.get("conversation_id") or None

        if not conversation_id_str:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Missing required configuration: conversation_id",
                }
            )

        try:
            conversation_id = UUID(conversation_id_str)
        except ValueError as e:
            return json.dumps(
                {"status": "error", "message": f"Invalid UUID format: {str(e)}"}
            )

        # Get the query sandbox service
        query_service = QuerySandboxService()

        # Command dispatch
        if command == "execute_query":
            if not sql_query:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "sql_query is required for execute_query command",
                    }
                )

            query_request = QueryRequest(sql_query=sql_query, max_rows=max_rows)

            result = await query_service.execute_query(conversation_id, query_request)
            return result.model_dump_json()

        elif command == "list_tables":
            result = await query_service.list_tables(conversation_id)
            return result.model_dump_json()

        elif command == "get_table_info":
            if not table_name:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "table_name is required for get_table_info command",
                    }
                )

            result = await query_service.get_table_info(conversation_id, table_name)
            return result.model_dump_json()

        else:
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Invalid command: '{command}'. Valid commands: execute_query, list_tables, get_table_info",
                }
            )

    except Exception as e:
        logger.error(f"Query sandbox tool error for command '{command}': {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to execute {command}: {str(e)}",
            }
        )
