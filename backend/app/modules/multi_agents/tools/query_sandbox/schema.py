
from pydantic import BaseModel, Field


class QueryRequest(BaseModel):
    """Request model for SQL queries against sandbox database."""

    sql_query: str = Field(
        description="The SQL query to execute against the sandbox database"
    )
    max_rows: int = Field(
        default=1000, description="Maximum number of rows to return in the result"
    )


class QueryResult(BaseModel):
    """Result model for SQL query execution."""

    success: bool = Field(description="Whether the query executed successfully")
    columns: list[str] = Field(description="Column names from the result")
    rows: list[list] = Field(description="Result rows as lists")
    row_count: int = Field(description="Number of rows returned")
    execution_time: float = Field(description="Query execution time in seconds")
    message: str | None = Field(
        default=None, description="Error message if query failed"
    )


class SandboxTableInfo(BaseModel):
    """Information about a table in the sandbox database."""

    table_name: str = Field(description="Name of the table")
    column_count: int = Field(description="Number of columns")
    row_count: int = Field(description="Approximate number of rows")
    columns: list[dict] = Field(
        description="Column information with name, type, nullable"
    )


class ListTablesResult(BaseModel):
    """Result model for listing available tables."""

    tables: list[SandboxTableInfo] = Field(description="List of available tables")
    total_tables: int = Field(description="Total number of tables")
