import json
from typing import Annotated, Literal
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.api.deps import get_async_session
from app.logger import logger
from app.repositories.recommendation import RecommendationRepository

from .prompt import RECOMMENDATION_TOOL_INSTRUCTIONS
from .schema import Recommendations


@tool
async def recommendation(
    command: Literal["get_all", "delete", "create", "get_instructions"],
    recommendation_ids: list[UUID] | None = None,
    new_recommendations: Recommendations | None = None,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for managing recommendations.

    ###### TOOL USE POLICY #####
    Tool Tag: #recommendation
    Tool Use Policy: #manual
    ##########################

    Commands:
    - get_all: Retrieve all recommendations for the current resource.
    - delete: Remove specific recommendations by their IDs.
    - create: Generate new recommendations based on analysis findings.
    - get_instructions: Return usage instructions for this tool.
    """
    try:
        if command not in ["get_all", "delete", "create", "get_instructions"]:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Invalid command. Use: get_all, delete, create, get_instructions",
                }
            )

        resource_id = config.get("configurable", {}).get("resource_id", None)
        if resource_id:
            resource_id = UUID(resource_id)

        # Command dispatch using unified async approach
        command_handlers = {
            "delete": lambda: _delete_recommendations(recommendation_ids, resource_id),
            "create": lambda: _create_recommendations(new_recommendations, resource_id),
            "get_all": lambda: _get_all_recommendations(resource_id),
            "get_instructions": lambda: _get_instructions(),
        }

        handler = command_handlers[command]
        return await handler()

        return json.dumps({"status": "error", "message": "Unhandled command"})

    except Exception as e:
        logger.error(f"Recommendation tool error for command '{command}': {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to execute {command}: {str(e)}",
            }
        )


async def _get_all_recommendations(resource_id: UUID | None) -> str:
    try:
        async for async_session in get_async_session():
            repo = RecommendationRepository(async_session)
            recommendations = await repo.get_all_recommendations(resource_id)
            return repo._format_recommendations_response(recommendations)
        return json.dumps(
            {"status": "error", "message": "No database session available"}
        )
    except Exception as e:
        logger.error(f"Error in _get_all_recommendations: {e}")
        return json.dumps(
            {"status": "error", "message": f"Failed to get recommendations: {str(e)}"}
        )


async def _get_instructions() -> str:
    return json.dumps(
        {
            "status": "success",
            "message": "Recommendation tool instructions",
            "data": {"instructions": RECOMMENDATION_TOOL_INSTRUCTIONS},
        }
    )


async def _delete_recommendations(
    recommendation_ids: list[UUID] | None, resource_id: UUID | None
) -> str:
    try:
        if not recommendation_ids:
            return json.dumps(
                {"status": "error", "message": "recommendation_ids is required"}
            )

        async for async_session in get_async_session():
            repo = RecommendationRepository(async_session)
            result = await repo.delete_recommendations(recommendation_ids, resource_id)
            if result:
                return json.dumps(
                    {"status": "success", "message": "Recommendations deleted"}
                )
            else:
                return json.dumps(
                    {"status": "error", "message": "Failed to delete recommendations"}
                )
        # Fallback if no session is available
        return json.dumps(
            {"status": "error", "message": "No database session available"}
        )
    except Exception as e:
        logger.error(f"Error in _delete_recommendations: {e}")
        return json.dumps(
            {
                "status": "error",
                "message": f"Failed to delete recommendations: {str(e)}",
            }
        )


async def _create_recommendations(
    new_recommendations: Recommendations | str | None, resource_id: UUID | None
) -> str:
    if not new_recommendations:
        return json.dumps(
            {"status": "error", "message": "new_recommendations is required"}
        )

    # Validate and parse if string
    if isinstance(new_recommendations, str):
        try:
            parsed_data = json.loads(new_recommendations)
            new_recommendations = Recommendations(**parsed_data)
        except json.JSONDecodeError as e:
            return json.dumps(
                {"status": "error", "message": f"Invalid JSON format: {str(e)}"}
            )
        except Exception as e:
            return json.dumps(
                {"status": "error", "message": f"Validation error: {str(e)}"}
            )

    return json.dumps(
        {
            "status": "success",
            "message": "Recommendation created successfully",
        }
    )
