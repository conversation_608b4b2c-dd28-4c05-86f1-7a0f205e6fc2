import json
from typing import Annotated, Literal
from uuid import UUID

from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from app.logger import logger
from app.modules.multi_agents.tools.report.schema import ExecutiveSummary, ReportSection
from app.services.report_service import ReportService


@tool
async def report(
    command: Literal[
        "create_outline",
        "update_sections",
        "remove_sections",
        "create_or_update_executive_summary",
        "get_report",
    ]
    | str,
    title: str | None = None,
    description: str | None = None,
    sections: list[ReportSection] | str | None = None,
    executive_summary: ExecutiveSummary | str | None = None,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> str:
    """
    A tool for managing report in a conversation.

    ###### TOOL USE POLICY #####
    Tool Tag: #report
    Tool Use Policy: #manual
    ##########################

    Commands:
    - create_outline: Initialize or overwrite the outline with `title`, `description`, and `sections`.
    - update_sections: Merge provided `sections` into existing ones by index; add if not present.
    - remove_sections: Remove sections using `sections` indices; accepts dicts or typed sections.
    - create_or_update_executive_summary: Set the executive summary for the report.
    - get_report: Fetch the whole report object.
    """
    try:
        # Extract and validate required configuration
        config_data = config.get("configurable", {})
        workspace_id_str = config_data.get("workspace_id") or None
        conversation_id_str = config_data.get("conversation_id") or None

        if not workspace_id_str or not conversation_id_str:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Missing required configuration: workspace_id or conversation_id",
                }
            )

        try:
            workspace_id = UUID(workspace_id_str)
            conversation_id = UUID(conversation_id_str)
        except ValueError as e:
            return json.dumps(
                {"status": "error", "message": f"Invalid UUID format: {str(e)}"}
            )

        # Validate and parse string inputs if needed
        normalized_sections: list[ReportSection] | None = None
        if sections is not None:
            if isinstance(sections, str):
                try:
                    parsed_sections = json.loads(sections)
                    if not isinstance(parsed_sections, list):
                        return json.dumps({
                            "status": "error",
                            "message": "sections must be a list"
                        })
                    from pydantic import TypeAdapter
                    sections_adapter = TypeAdapter(list[ReportSection])
                    normalized_sections = sections_adapter.validate_python(parsed_sections)
                except json.JSONDecodeError as e:
                    return json.dumps({
                        "status": "error",
                        "message": f"Invalid JSON format for sections: {str(e)}"
                    })
                except Exception as e:
                    return json.dumps({
                        "status": "error",
                        "message": f"Validation error for sections: {str(e)}"
                    })
            else:
                normalized_sections = sections

        normalized_executive_summary: ExecutiveSummary | None = None
        if executive_summary is not None:
            if isinstance(executive_summary, str):
                try:
                    parsed_summary = json.loads(executive_summary)
                    normalized_executive_summary = ExecutiveSummary.model_validate(parsed_summary)
                except json.JSONDecodeError as e:
                    return json.dumps({
                        "status": "error",
                        "message": f"Invalid JSON format for executive_summary: {str(e)}"
                    })
                except Exception as e:
                    return json.dumps({
                        "status": "error",
                        "message": f"Validation error for executive_summary: {str(e)}"
                    })
            else:
                normalized_executive_summary = executive_summary

        # Command dispatch using unified async approach
        async def create_outline_handler():
            return await ReportService.create_outline(
                workspace_id, conversation_id, title, description, normalized_sections
            )

        async def update_sections_handler():
            return await ReportService.update_sections(
                workspace_id, conversation_id, normalized_sections
            )

        async def remove_sections_handler():
            return await ReportService.remove_sections_by_objects(
                workspace_id,
                conversation_id,
                normalized_sections,
            )

        async def create_or_update_executive_summary_handler():
            return await ReportService.update_executive_summary(
                workspace_id, conversation_id, normalized_executive_summary
            )

        async def get_report_handler():
            return await ReportService.get_or_create_report(
                workspace_id, conversation_id
            )

        command_handlers = {
            "create_outline": create_outline_handler,
            "update_sections": update_sections_handler,
            "remove_sections": remove_sections_handler,
            "create_or_update_executive_summary": create_or_update_executive_summary_handler,
            "get_report": get_report_handler,
        }

        handler = command_handlers.get(command)
        if not handler:
            valid_commands = list(command_handlers.keys())
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Invalid command: '{command}'. Valid commands: {', '.join(valid_commands)}",
                }
            )

        # Execute the command handler with specific error handling
        try:
            return await handler()
        except Exception as handler_error:
            logger.error(f"Command handler error for '{command}': {handler_error}")
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Command execution failed: {str(handler_error)}",
                }
            )

    except Exception as e:
        logger.error(f"Report tool initialization error for command '{command}': {e}")
        return json.dumps(
            {"status": "error", "message": f"Tool initialization failed: {str(e)}"}
        )
