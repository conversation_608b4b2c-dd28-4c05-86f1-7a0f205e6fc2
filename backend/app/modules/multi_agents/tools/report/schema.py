from typing import Literal

from pydantic import BaseModel, Field

from ..shared_schema import (
    ChartStructuredOutput,
    KPICard,
    TableStructuredOutput,
)


class Content(BaseModel):
    """Represents a single content item within a report section."""

    index: int | None = Field(
        default=None,
        description="Optional index for ordering content within the section",
    )
    type: Literal["paragraph", "chart", "table"] = Field(
        description="The type of content: paragraph (text), chart (visualization), table (structured data)"
    )
    content: str | ChartStructuredOutput | TableStructuredOutput | None = Field(
        default=None,
        description="""
        The actual content data:
        - For paragraphs: plain text string (no markdown formatting)
        - For charts: ChartStructuredOutput object with visualization data
        - For tables: TableStructuredOutput object with structured data
        """,
    )


class ExecutiveSummary(BaseModel):
    """
    Enhanced executive summary with visual elements and key metrics.

    Use this for high-level overviews and stakeholder communication.
    Include key metrics, risk indicators, and actionable recommendations.
    """

    key_findings: list[str] = Field(description="The key findings of the report")
    business_impact: list[str] = Field(description="The business impact of the report")
    key_metrics: list[KPICard] | None = Field(
        default=None,
        description="Key performance indicators and metrics cards for quick overview, max 3 cards.",
    )
    recommendations: list[str] | None = Field(
        default=None, description="Prioritized recommendations and next steps"
    )


class ReportSection(BaseModel):
    """
    Represents a single section within a report.

    Note: DO NOT put Executive Summary content in this section.
    Use the dedicated executive_summary field instead.
    """

    index: int = Field(description="The index of the section for ordering")
    header: str | None = Field(
        default=None,
        description="""
        The section header/title. Examples:
        - "Monthly Cost Trends Analysis"
        - "Service-Level Cost Analysis"
        - "Resource Utilization Analysis"
        - "Detailed Optimization Recommendations"
        - "Security Operations Metrics"
        - "Threat Landscape & Attack Patterns"
        - "Compliance Framework Assessment"
        """,
    )
    content: list[Content] | None = Field(
        default=None,
        description="List of content items (paragraphs, charts, tables, cards) within this section",
    )
