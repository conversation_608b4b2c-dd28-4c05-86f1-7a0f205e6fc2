from enum import Enum
from typing import Literal

from pydantic import BaseModel, Field, model_validator


class ChartType(str, Enum):
    """Enumeration of supported chart types."""

    BAR = "bar"
    LINE = "line"
    AREA = "area"
    STEP_AREA = "step_area"
    RADAR = "radar"
    SANKEY = "sankey"


class ChartDataset(BaseModel):
    """Represents a single dataset in a chart."""

    data: list[float] = Field(
        description="CRITICAL: Array of numeric values (int/float only, no strings). Length MUST exactly match categories array length. Example: [1250.50, 340.25, 890.75] for 3 categories."
    )
    label: str | None = Field(
        description="User-friendly descriptive name for this dataset that appears in legend. Use clear, meaningful labels like 'March 2024 Costs' or 'Current Monthly Usage', NOT 'data1' or 'series1'."
    )


class SankeyNode(BaseModel):
    """Represents a node in a Sankey diagram."""

    id: str = Field(
        description="Unique identifier for the node (e.g., 'aws-ec2', 'production-env', 'compute-costs')"
    )
    label: str = Field(
        description="Display name for the node (e.g., 'AWS EC2', 'Production Environment', 'Compute Costs')"
    )
    color: str | None = Field(
        default=None,
        description="Optional color for the node in hex format (e.g., '#3B82F6'). If not provided, will use default theme colors.",
    )


class SankeyLink(BaseModel):
    """Represents a flow link between nodes in a Sankey diagram."""

    source: str = Field(description="ID of the source node (must match a node's id)")
    target: str = Field(description="ID of the target node (must match a node's id)")
    value: float = Field(
        description="Numeric value representing the flow amount (e.g., cost amount, resource count, data volume)"
    )
    label: str | None = Field(
        default=None,
        description="Optional label for this specific flow (e.g., 'Monthly Cost', 'Data Transfer')",
    )


class ChartAxis(BaseModel):
    """Configuration for chart axes."""

    title: str | None = Field(
        default=None,
        description="Title displayed on this axis (e.g., 'Time Period', 'Cost ($)', 'Services', 'Performance Score')",
    )
    type: str = Field(
        default="category",
        description="Type of axis: 'category' for discrete values, 'linear' for continuous numbers, 'time' for dates",
    )


class ChartStructuredOutput(BaseModel):
    """
    ## CRITICAL RULES:
    1. **Array Lengths**: data array length MUST equal categories array length
    2. **Chart Type Selection**: Line charts ONLY for continuous time series (6+ points). Use bar charts for categorical comparisons.
    3. **No Misleading Patterns**: Don't create "roller coaster" line charts for discrete categories (Peak/Off-Peak, Before/After)
    4. **Meaningful Differences**: Don't chart tiny changes that look dramatic (e.g., 15.2% vs 14.8%)
    5. **Numbers Only**: Data arrays must contain numbers, not strings

    ## WHEN TO USE EACH CHART TYPE:

    **BAR CHART** - Comparing different categories or discrete periods
    - ✅ Cost by service: ["EC2", "S3", "RDS"] with [1200, 400, 800]
    - ✅ Peak vs Off-Peak comparisons: ["Week 1 Peak", "Week 1 Off-Peak", "Week 2 Peak"]
    - ✅ Before/after comparisons, different time periods
    - ✅ Any categorical comparison (regions, instance types, etc.)
    - ❌ Continuous time series (use line instead)

    **LINE CHART** - ONLY for continuous time series trends (need 6+ points)
    - ✅ Daily/weekly/monthly metrics over time (same metric measured repeatedly)
    - ✅ Cost trends over 6+ months showing actual progression
    - ❌ Comparing different categories (Peak vs Off-Peak, Before vs After)
    - ❌ Discrete time periods that aren't continuous
    - ❌ Less than 6 data points
    - ❌ Data that creates misleading "roller coaster" patterns

    **SANKEY CHART** - Showing flow relationships and resource allocation
    - ✅ Cost flows: AWS Services → Individual Services → Cost Centers
    - ✅ Resource allocation: Budget → Departments → Projects → Tasks
    - ✅ Data flows: Sources → Processing → Destinations
    - ✅ Process flows: Input → Stages → Output
    - ❌ Simple comparisons (use bar chart instead)
    - ❌ Time series data (use line chart instead)

    ## DATA QUALITY REQUIREMENTS:

    **Categories**: Use descriptive labels
    - ✅ ["January 2024", "February 2024", "March 2024"]
    - ❌ ["1", "2", "3"] or ["jan", "feb", "mar"]

    **Data Values**: Must be meaningful numbers
    - ✅ [1250.50, 890.25, 1100.75] - clear differences
    - ❌ [15.2, 15.1, 15.0] - tiny changes that look dramatic

    **Labels**: Clear, user-friendly descriptions
    - ✅ "Monthly AWS Costs" or "CPU Utilization (%)"
    - ❌ "data1" or "series1"

    ## FORMATTING:
    - `currency_format: true` - For ANY money values ($1,234.56)
    - `percentage_format: true` - For percentages (85.5%)
    - `show_legend: false` - For single dataset charts
    - `show_grid: true` - Helps read values

    ## EXAMPLES:

    ### Good Bar Chart (Categorical Comparison):
    ```json
    {
        "title": "CPU Utilization by Time Period",
        "chart_type": "bar",
        "categories": ["Week 1 Peak Hours", "Week 1 Off-Peak", "Week 2 Peak Hours", "Week 2 Off-Peak"],
        "datasets": [{"data": [85.2, 15.8, 87.1, 14.3], "label": "CPU Utilization"}],
        "x_axis": {"title": "Time Period", "type": "category"},
        "y_axis": {"title": "CPU Utilization (%)", "type": "linear"},
        "percentage_format": true,
        "show_legend": false
    }
    ```

    ### Good Sankey Chart (Flow Relationships):
    ```json
    {
        "title": "AWS Cost Flow by Service and Environment",
        "chart_type": "sankey",
        "sankey_nodes": [
            {"id": "aws", "label": "AWS Total", "color": "#FF9900"},
            {"id": "ec2", "label": "EC2 Instances", "color": "#3B82F6"},
            {"id": "s3", "label": "S3 Storage", "color": "#10B981"},
            {"id": "rds", "label": "RDS Database", "color": "#8B5CF6"},
            {"id": "prod", "label": "Production", "color": "#EF4444"},
            {"id": "staging", "label": "Staging", "color": "#F59E0B"},
            {"id": "dev", "label": "Development", "color": "#6B7280"}
        ],
        "sankey_links": [
            {"source": "aws", "target": "ec2", "value": 2500.00, "label": "EC2 Monthly"},
            {"source": "aws", "target": "s3", "value": 800.00, "label": "S3 Monthly"},
            {"source": "aws", "target": "rds", "value": 1200.00, "label": "RDS Monthly"},
            {"source": "ec2", "target": "prod", "value": 1500.00},
            {"source": "ec2", "target": "staging", "value": 600.00},
            {"source": "ec2", "target": "dev", "value": 400.00},
            {"source": "s3", "target": "prod", "value": 500.00},
            {"source": "s3", "target": "staging", "value": 200.00},
            {"source": "s3", "target": "dev", "value": 100.00}
        ],
        "currency_format": true,
        "show_legend": false
    }
    ```

    ## AVOID THESE MISTAKES:
    - ❌ Line charts for categorical data (Peak vs Off-Peak, Before vs After)
    - ❌ "Roller coaster" line charts that mislead (like Peak-Off-Peak-Peak pattern)
    - ❌ Line charts with less than 6 continuous time points
    - ❌ Charting tiny differences (15.2% vs 14.8%)
    - ❌ Mismatched array lengths
    - ❌ Technical abbreviations in labels
    - ❌ Missing currency_format for money data
    """

    title: str = Field(
        description="Clear, concise title describing what the chart represents (e.g., 'Monthly AWS Costs by Service')"
    )
    description: str | None = Field(
        description="Detailed explanation of what the chart is showing and any key insights"
    )
    chart_type: ChartType = Field(
        description="""Type of chart to display:
        - bar: Best for comparing quantities across categories (e.g., costs by service, usage by region)
        - line: Best for showing trends and changes over time with connected points (e.g., monthly trends)
        - area: Best for showing trends over time with filled areas (e.g., cumulative costs)
        - step_area: Best for showing changes occurring at irregular intervals (e.g., pricing tier changes)
        - radar: Best for comparing multiple variables in a radial layout (e.g., performance metrics)"""
    )

    # Standard chart data (used by bar, line, area, step_area, radar)
    categories: list[str] = Field(
        default=[],
        description="""CRITICAL: Array of descriptive string labels for x-axis. Length MUST match all dataset data arrays. Use user-friendly labels:
        ✅ GOOD: ['January 2024', 'February 2024', 'March 2024']
        ❌ BAD: ['1', '2', '3'] or ['jan', 'feb', 'mar']
        ✅ GOOD: ['EC2 Instances', 'S3 Storage', 'RDS Database']
        ❌ BAD: ['ec2', 's3', 'rds']
        NOTE: Not required for Sankey diagrams.""",
    )
    datasets: list[ChartDataset] = Field(
        default=[],
        description="""Array of data series. Each dataset's data array MUST have same length as categories array.
        - Single dataset: [ChartDataset(data=[1250.50, 340.25, 890.75], label='March 2024 Costs')]
        - Multiple datasets: [
            ChartDataset(data=[1250.50, 340.25, 890.75], label='Current Costs'),
            ChartDataset(data=[980.25, 275.80, 720.50], label='Optimized Costs')
          ]
        NOTE: Not used for Sankey diagrams - use sankey_nodes and sankey_links instead.""",
    )

    # Sankey-specific data (only used when chart_type is 'sankey')
    sankey_nodes: list[SankeyNode] | None = Field(
        default=None,
        description="""Array of nodes for Sankey diagram. Required when chart_type is 'sankey'.
        Example: [
            SankeyNode(id='aws', label='AWS Services', color='#FF9900'),
            SankeyNode(id='ec2', label='EC2 Instances', color='#3B82F6'),
            SankeyNode(id='s3', label='S3 Storage', color='#10B981'),
            SankeyNode(id='rds', label='RDS Database', color='#8B5CF6'),
            SankeyNode(id='production', label='Production Costs', color='#EF4444'),
            SankeyNode(id='staging', label='Staging', color='#F59E0B'),
            SankeyNode(id='dev', label='Development', color='#6B7280')
        ]""",
    )
    sankey_links: list[SankeyLink] | None = Field(
        default=None,
        description="""Array of links/flows for Sankey diagram. Required when chart_type is 'sankey'.
        Example: [
            SankeyLink(source='aws', target='ec2', value=2500.00, label='EC2 Monthly'),
            SankeyLink(source='aws', target='s3', value=800.00, label='S3 Monthly'),
            SankeyLink(source='aws', target='rds', value=1200.00, label='RDS Monthly'),
            SankeyLink(source='ec2', target='production', value=1500.00),
            SankeyLink(source='ec2', target='staging', value=600.00),
            SankeyLink(source='ec2', target='dev', value=400.00),
            SankeyLink(source='s3', target='production', value=500.00),
            SankeyLink(source='s3', target='staging', value=200.00),
            SankeyLink(source='s3', target='dev', value=100.00)
        ]""",
    )

    # Axis configuration (not required for Sankey charts)
    x_axis: ChartAxis | None = Field(
        default=None,
        description="""X-axis configuration. Required for bar, line, area, step_area, and radar charts. Not used for Sankey diagrams. Examples:
        - ChartAxis(title='Time Period', type='time') for time series
        - ChartAxis(title='AWS Services', type='category') for service comparison
        - ChartAxis(title='Regions', type='category') for regional data""",
    )
    y_axis: ChartAxis | None = Field(
        default=None,
        description="""Y-axis configuration. Required for bar, line, area, step_area, and radar charts. Not used for Sankey diagrams. Examples:
        - ChartAxis(title='Cost (USD)', type='linear') for cost data
        - ChartAxis(title='Usage (%)', type='linear') for percentage data
        - ChartAxis(title='Performance Score', type='linear') for metrics""",
    )

    # Display options
    show_legend: bool = Field(
        default=True,
        description="Whether to show the legend. Set to False for single dataset charts where legend adds no value.",
    )
    show_grid: bool = Field(
        default=True,
        description="Whether to show grid lines. Helps with reading precise values.",
    )
    currency_format: bool = Field(
        default=False,
        description="Set to True for ANY monetary values (costs, prices, savings, revenue). Formats as $1,234.56. ALWAYS use for cost data.",
    )
    percentage_format: bool = Field(
        default=False,
        description="Set to True for percentages, ratios, utilization rates. Formats as 85.5%. Use when data represents parts of 100%.",
    )
    position: int | None = Field(
        default=0, description="Position in the display order (0 = first)"
    )

    @model_validator(mode="after")
    def validate_chart_data(self):
        """Validate that the appropriate data is provided for each chart type."""
        if self.chart_type == ChartType.SANKEY:
            # For Sankey charts, require sankey_nodes and sankey_links
            if not self.sankey_nodes or not self.sankey_links:
                raise ValueError(
                    "Sankey charts require both sankey_nodes and sankey_links to be provided"
                )
            if len(self.sankey_nodes) == 0 or len(self.sankey_links) == 0:
                raise ValueError("Sankey charts require at least one node and one link")

            # For Sankey charts, x_axis and y_axis should NOT be provided
            if self.x_axis is not None or self.y_axis is not None:
                raise ValueError(
                    "Sankey charts should not have x_axis or y_axis configuration. These are not applicable to flow diagrams."
                )
        else:
            # For other chart types, require categories, datasets, x_axis, and y_axis
            if not self.categories or not self.datasets:
                raise ValueError(
                    f"{self.chart_type.value} charts require both categories and datasets to be provided"
                )
            if len(self.categories) == 0 or len(self.datasets) == 0:
                raise ValueError(
                    f"{self.chart_type.value} charts require at least one category and one dataset"
                )

            # For non-Sankey charts, x_axis and y_axis are required
            if not self.x_axis or not self.y_axis:
                raise ValueError(
                    f"{self.chart_type.value} charts require both x_axis and y_axis configuration"
                )

            # Validate that dataset lengths match categories length
            for i, dataset in enumerate(self.datasets):
                if len(dataset.data) != len(self.categories):
                    raise ValueError(
                        f"Dataset {i} has {len(dataset.data)} values but {len(self.categories)} categories. Arrays must have the same length."
                    )

        return self


class TableColumnConfig(BaseModel):
    """Configuration for a table column"""

    header: str = Field(description="The header text for the column")


class TableStructuredOutput(BaseModel):
    """Standard table structure for displaying tabular data"""

    title: str = Field(description="The title of the table")
    description: str | None = Field(
        default=None, description="The description of the table"
    )
    columns: list[TableColumnConfig] = Field(description="The columns of the table")
    rows: list[list[str]] = Field(
        description="The rows of the table, each row is a list of strings separated by comma"
    )


class Trend(BaseModel):
    """Represents a trend indicator with direction and value"""

    direction: Literal["up", "down", "neutral"] = Field(
        description="The direction of the trend: 'up' for positive, 'down' for negative, 'neutral' for no change"
    )
    value: str = Field(description="The text value of the trend, e.g., '23%'.")
    description: str | None = Field(
        default=None, description="Context for the trend, e.g., 'vs last year'."
    )


class KPICard(BaseModel):
    """
    A key performance indicator card. Only use this schema when you want to display \
        important metrics or KPIs. You should use at least 2 cards.
    """

    title: str = Field(description="The title of the card.")
    value: str = Field(description="The main data point or value of the card.")
    description: str | None = Field(default=None)
    trend: Trend | None = Field(default=None)
    icon: str | None = Field(
        default=None, description="Name of a Lucide icon to display."
    )
    alert: bool = Field(default=False, description="Flag to indicate an alert state.")
