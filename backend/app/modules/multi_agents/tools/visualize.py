import json
from typing import Annotated, Any

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

from .shared_schema import ChartStructuredOutput


@tool
async def visualize(
    chart_data: ChartStructuredOutput | str,
    *,
    config: Annotated[RunnableConfig, InjectedToolArg],
) -> dict[str, Any]:
    """
    Creates visual charts from numerical data. ONLY use when you have meaningful data that benefits from visualization.

    # TOOL USE POLICY
    Tool Tag: #visualize
    Tool Use Policy: #manual
    """
    # Validate and parse string input if needed
    if isinstance(chart_data, str):
        try:
            parsed_data = json.loads(chart_data)
            chart_data = ChartStructuredOutput.model_validate(parsed_data)
        except json.JSONDecodeError as e:
            return {"status": "error", "message": f"Invalid JSON format: {str(e)}"}
        except Exception as e:
            return {"status": "error", "message": f"Validation error: {str(e)}"}

    return {"status": "success"}
