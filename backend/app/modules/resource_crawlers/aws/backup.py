from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class BackupResourceCrawler(BaseCrawler):
    """Crawler for AWS Backup resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "backup",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl AWS Backup resources"""
        all_resources = []

        try:
            # Get all backup vaults
            paginator = self.client.get_paginator("list_backup_vaults")
            for page in paginator.paginate():
                for vault in page["BackupVaultList"]:
                    try:
                        # Get vault details
                        vault_details = self.client.describe_backup_vault(
                            BackupVaultName=vault["BackupVaultName"]
                        )

                        # Filter backup vaults based on include_stopped_resources setting
                        vault_state = (
                            "LOCKED"
                            if vault_details.get("Locked", False)
                            else "AVAILABLE"
                        )
                        if not self.include_stopped_resources:
                            # Skip backup vaults that are failed or locked based on _STATE_MAPPING
                            if vault_state.upper() in ["FAILED", "LOCKED"]:
                                continue

                        resource = self._map_vault_to_resource(vault_details)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for backup vault {vault['BackupVaultName']}: {str(e)}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} AWS Backup resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling AWS Backup resources: {str(e)}")
            raise

    def _map_vault_to_resource(self, vault: dict[str, Any]) -> Resource:
        """Map AWS Backup vault data to a Resource object."""
        config = {
            "backup_vault_name": vault["BackupVaultName"],
            "backup_vault_arn": vault["BackupVaultArn"],
            "creation_date": vault["CreationDate"].isoformat(),
            "encryption_key_arn": vault.get("EncryptionKeyArn"),
            "number_of_recovery_points": vault.get("NumberOfRecoveryPoints", 0),
            "locked": vault.get("Locked", False),
            "min_retention_days": vault.get("MinRetentionDays"),
            "max_retention_days": vault.get("MaxRetentionDays"),
            "lock_date": vault.get("LockDate", "").isoformat()
            if vault.get("LockDate")
            else None,
            "state": "LOCKED" if vault.get("Locked", False) else "AVAILABLE",
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=vault["BackupVaultName"],
            region=self.region if self.region else "",
            type=AWSResourceType.BACKUP,
            resource_id=vault["BackupVaultArn"],
            tags=vault.get("Tags", {}),
            description=f"AWS Backup vault with {config['number_of_recovery_points']} recovery points",
            configurations=config,
            status=config["state"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.BACKUP
            ].category,
        )
