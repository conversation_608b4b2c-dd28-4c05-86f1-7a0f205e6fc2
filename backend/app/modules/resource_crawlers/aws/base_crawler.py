import time
import uuid
from abc import abstractmethod
from functools import wraps
from typing import Callable, TypeVar

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import Resource

from ..base_cloud_crawler import BaseCloudCrawler

T = TypeVar("T")


def aws_retry(
    max_retries: int = 3,
    retry_delay: int = 5,
    retryable_errors: tuple[str, ...] = (
        "Throttling",
        "ThrottlingException",
        "RequestLimitExceeded",
        "ServiceUnavailable",
        "InternalError",
        "RequestTimeout",
        "TooManyRequestsException",
    ),
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator for AWS API calls with exponential backoff retry logic.

    Args:
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Base delay between retries in seconds (default: 5)
        retryable_errors: Tuple of AWS error codes that should trigger a retry

    Returns:
        Decorated function with retry logic
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args, **kwargs) -> T:  # type: ignore
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except ClientError as e:
                    error_code = e.response.get("Error", {}).get("Code", "")
                    last_exception = e

                    # Check if this is a retryable error
                    if error_code in retryable_errors:
                        if attempt < max_retries - 1:  # Don't sleep on the last attempt
                            # Exponential backoff with jitter
                            delay = retry_delay * (2**attempt)
                            logger.warning(
                                f"AWS API call failed with {error_code}, retrying in {delay}s "
                                f"(attempt {attempt + 1}/{max_retries})"
                            )
                            time.sleep(delay)
                            continue

                    # Non-retryable error or max retries reached
                    logger.error(
                        f"AWS API call failed after {attempt + 1} attempts: {error_code} - {str(e)}"
                    )
                    raise
                except Exception as e:
                    # Non-ClientError exceptions are not retried
                    logger.error(f"Non-retryable error in AWS API call: {str(e)}")
                    raise

            # This should never be reached, but just in case
            if last_exception:
                raise last_exception

        return wrapper

    return decorator


class BaseCrawler(BaseCloudCrawler):
    """AWS-specific base crawler implementation"""

    def __init__(
        self,
        workspace_id: uuid.UUID,
        **kwargs,
    ):
        # Extract base crawler parameters
        region = kwargs.get("region")
        max_retries = kwargs.get("max_retries", 3)
        retry_delay = kwargs.get("retry_delay", 5)

        super().__init__(workspace_id, region, max_retries, retry_delay)

        # Store AWS-specific parameters
        self.aws_account_id = kwargs.get("aws_account_id")
        self.aws_access_account_id = kwargs.get("aws_access_account_id")
        self.aws_access_key_id = kwargs.get("aws_access_key_id")
        self.aws_secret_access_key = kwargs.get("aws_secret_access_key")
        self.include_stopped_resources = kwargs.get("include_stopped_resources", True)

        self.session_kwargs = {}
        if self.aws_access_key_id and self.aws_secret_access_key:
            self.session_kwargs.update(
                {
                    "aws_access_key_id": self.aws_access_key_id,
                    "aws_secret_access_key": self.aws_secret_access_key,
                }
            )

        self.session = boto3.Session(**self.session_kwargs)

    def get_provider_name(self) -> str:
        """Return AWS as the provider name"""
        return "aws"

    def validate_credentials(self) -> bool:
        """Validate AWS credentials by making a simple STS call"""
        try:
            sts_client = self.session.client(
                "sts", region_name=self.region or "us-east-1"
            )
            sts_client.get_caller_identity()
            return True
        except Exception as e:
            logger.error(f"AWS credential validation failed: {str(e)}")
            return False

    @abstractmethod
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        pass
