from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class DocumentDBResourceCrawler(BaseCrawler):
    """Crawler for DocumentDB resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "docdb",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl DocumentDB resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for DocumentDB crawler")
            return all_resources

        try:
            # Get all clusters
            paginator = self.client.get_paginator("describe_db_clusters")
            for page in paginator.paginate():
                clusters = page["DBClusters"]

                # Filter clusters based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include available clusters, exclude failed/incompatible states
                    clusters = [
                        cluster
                        for cluster in clusters
                        if cluster.get("Status", "").lower()
                        not in [
                            "failed",
                            "incompatible-restore",
                            "incompatible-parameters",
                            "incompatible-network",
                            "incompatible-credentials",
                            "incompatible-option-group",
                            "incompatible-parameter-group",
                            "incompatible-security-group",
                            "incompatible-subnet",
                            "incompatible-vpc",
                            "incompatible-version",
                            "incompatible-zone",
                            "storage-full",
                        ]
                    ]

                for cluster in clusters:
                    try:
                        # Get cluster tags
                        tags = self.client.list_tags_for_resource(
                            ResourceName=cluster["DBClusterArn"]
                        )["TagList"]

                        resource = self._map_cluster_to_resource(cluster, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.error(
                            f"Error getting details for cluster {cluster['DBClusterIdentifier']}: {str(e)}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} DocumentDB resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling DocumentDB resources: {e}")
            raise

    def _map_cluster_to_resource(
        self, cluster: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map DocumentDB cluster data to a Resource object."""
        config = {
            "engine": cluster["Engine"],
            "engine_version": cluster["EngineVersion"],
            "status": cluster["Status"],
            "master_username": cluster["MasterUsername"],
            "db_cluster_identifier": cluster["DBClusterIdentifier"],
            "endpoint": cluster["Endpoint"],
            "port": cluster["Port"],
            "vpc_security_groups": cluster.get("VpcSecurityGroups", []),
            "db_subnet_group": cluster.get("DBSubnetGroup", {}),
            "availability_zones": cluster.get("AvailabilityZones", []),
            "backup_retention_period": cluster.get("BackupRetentionPeriod"),
            "preferred_backup_window": cluster.get("PreferredBackupWindow"),
            "preferred_maintenance_window": cluster.get("PreferredMaintenanceWindow"),
            "storage_encrypted": cluster.get("StorageEncrypted", False),
            "kms_key_id": cluster.get("KmsKeyId"),
            "db_cluster_parameter_group": cluster.get("DBClusterParameterGroup"),
            "deletion_protection": cluster.get("DeletionProtection", False),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=cluster["DBClusterIdentifier"],
            region=self.region if self.region else "",
            type=AWSResourceType.DOCUMENTDB,
            resource_id=cluster["DBClusterArn"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"DocumentDB cluster running {cluster['Engine']} {cluster['EngineVersion']}",
            configurations=config,
            status=cluster["Status"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.DOCUMENTDB
            ].category,
        )
