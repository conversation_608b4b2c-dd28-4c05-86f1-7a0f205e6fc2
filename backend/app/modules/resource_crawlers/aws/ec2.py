from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class EC2ResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _crawl_ec2_instances(self, region: str) -> list[dict[str, Any]]:
        """Crawl EC2 instances in a specific region."""
        ec2_client = self.session.client("ec2", region_name=region)
        instances = []

        try:
            # Prepare filters based on include_stopped_resources setting
            filters = []
            if not self.include_stopped_resources:
                # Only include running and pending instances, exclude stopped/terminated
                filters.append(
                    {
                        "Name": "instance-state-name",
                        "Values": ["running", "pending", "rebooting", "stopping"],
                    }
                )

            paginator = ec2_client.get_paginator("describe_instances")

            # Apply filters if any are defined
            paginate_kwargs = {}
            if filters:
                paginate_kwargs["Filters"] = filters

            for page in paginator.paginate(**paginate_kwargs):
                for reservation in page["Reservations"]:
                    instances.extend(reservation["Instances"])
        except ClientError as e:
            logger.exception(f"Error crawling EC2 instances in {region}: {e}")
            raise
        return instances

    def _map_ec2_to_resource(self, instance: dict[str, Any], region: str) -> Resource:
        """Map EC2 instance data to a Resource object."""
        tags = {tag["Key"]: tag["Value"] for tag in instance.get("Tags", [])}

        # Extract relevant configuration details
        configurations = {
            "instance_id": instance["InstanceId"],
            "instance_type": instance["InstanceType"],
            "architecture": instance.get("Architecture"),
            "availability_zone": instance.get("Placement", {}).get("AvailabilityZone"),
            "availability_zone_id": instance.get("Placement", {}).get(
                "AvailabilityZoneId"
            ),
            "tenancy": instance.get("Placement", {}).get("Tenancy"),
            "placement_group": instance.get("Placement", {}).get("GroupName"),
            "vpc_id": instance.get("VpcId"),
            "subnet_id": instance.get("SubnetId"),
            "private_ip": instance.get("PrivateIpAddress"),
            "public_ip": instance.get("PublicIpAddress"),
            "public_dns": instance.get("PublicDnsName"),
            "private_dns": instance.get("PrivateDnsName"),
            "launch_time": launch_time.isoformat()
            if (launch_time := instance.get("LaunchTime")) is not None
            else None,
            "image_id": instance.get("ImageId"),
            "ebs_optimized": instance.get("EbsOptimized"),
            "ena_support": instance.get("EnaSupport"),
            "monitoring": instance.get("Monitoring", {}).get("State"),
            "root_device_name": instance.get("RootDeviceName"),
            "root_device_type": instance.get("RootDeviceType"),
            "source_dest_check": instance.get("SourceDestCheck"),
            "security_groups": [
                {"id": sg.get("GroupId"), "name": sg.get("GroupName")}
                for sg in instance.get("SecurityGroups", [])
            ],
            "block_devices": [
                {
                    "device_name": bd.get("DeviceName"),
                    "volume_id": bd.get("Ebs", {}).get("VolumeId")
                    if "Ebs" in bd
                    else None,
                    "status": bd.get("Ebs", {}).get("Status") if "Ebs" in bd else None,
                    "delete_on_termination": bd.get("Ebs", {}).get(
                        "DeleteOnTermination"
                    )
                    if "Ebs" in bd
                    else None,
                    "attach_time": bd.get("Ebs", {}).get("AttachTime").isoformat()
                    if bd.get("Ebs", {}).get("AttachTime")
                    else None,
                }
                for bd in instance.get("BlockDeviceMappings", [])
            ],
            "network_interfaces": [
                {
                    "network_interface_id": ni.get("NetworkInterfaceId"),
                    "description": ni.get("Description"),
                    "mac_address": ni.get("MacAddress"),
                    "private_ip": ni.get("PrivateIpAddress"),
                    "private_dns": ni.get("PrivateDnsName"),
                    "subnet_id": ni.get("SubnetId"),
                    "vpc_id": ni.get("VpcId"),
                    "status": ni.get("Status"),
                    "interface_type": ni.get("InterfaceType"),
                    "source_dest_check": ni.get("SourceDestCheck"),
                    "attachment": {
                        "attachment_id": ni.get("Attachment", {}).get("AttachmentId"),
                        "device_index": ni.get("Attachment", {}).get("DeviceIndex"),
                        "status": ni.get("Attachment", {}).get("Status"),
                        "delete_on_termination": ni.get("Attachment", {}).get(
                            "DeleteOnTermination"
                        ),
                        "attach_time": ni.get("Attachment", {})
                        .get("AttachTime")
                        .isoformat()
                        if ni.get("Attachment", {}).get("AttachTime")
                        else None,
                    }
                    if ni.get("Attachment")
                    else None,
                    "private_ip_addresses": [
                        {
                            "primary": addr.get("Primary"),
                            "private_ip": addr.get("PrivateIpAddress"),
                            "private_dns": addr.get("PrivateDnsName"),
                        }
                        for addr in ni.get("PrivateIpAddresses", [])
                    ],
                    "groups": [
                        {"id": g.get("GroupId"), "name": g.get("GroupName")}
                        for g in ni.get("Groups", [])
                    ],
                }
                for ni in instance.get("NetworkInterfaces", [])
            ],
            "cpu_options": {
                "core_count": instance.get("CpuOptions", {}).get("CoreCount"),
                "threads_per_core": instance.get("CpuOptions", {}).get(
                    "ThreadsPerCore"
                ),
            }
            if "CpuOptions" in instance
            else {},
            "platform_details": instance.get("PlatformDetails"),
            "virtualization_type": instance.get("VirtualizationType"),
            "hypervisor": instance.get("Hypervisor"),
            "metadata_options": {
                "state": instance.get("MetadataOptions", {}).get("State"),
                "http_tokens": instance.get("MetadataOptions", {}).get("HttpTokens"),
                "http_endpoint": instance.get("MetadataOptions", {}).get(
                    "HttpEndpoint"
                ),
                "http_put_response_hop_limit": instance.get("MetadataOptions", {}).get(
                    "HttpPutResponseHopLimit"
                ),
                "http_protocol_ipv6": instance.get("MetadataOptions", {}).get(
                    "HttpProtocolIpv6"
                ),
                "instance_metadata_tags": instance.get("MetadataOptions", {}).get(
                    "InstanceMetadataTags"
                ),
            }
            if "MetadataOptions" in instance
            else {},
            "hibernation_options": {
                "configured": instance.get("HibernationOptions", {}).get("Configured"),
            }
            if "HibernationOptions" in instance
            else {},
            "enclave_options": {
                "enabled": instance.get("EnclaveOptions", {}).get("Enabled"),
            }
            if "EnclaveOptions" in instance
            else {},
            "maintenance_options": {
                "auto_recovery": instance.get("MaintenanceOptions", {}).get(
                    "AutoRecovery"
                ),
                "reboot_migration": instance.get("MaintenanceOptions", {}).get(
                    "RebootMigration"
                ),
            }
            if "MaintenanceOptions" in instance
            else {},
            "current_instance_boot_mode": instance.get("CurrentInstanceBootMode"),
            "network_performance_options": {
                "bandwidth_weighting": instance.get(
                    "NetworkPerformanceOptions", {}
                ).get("BandwidthWeighting"),
            }
            if "NetworkPerformanceOptions" in instance
            else {},
            "capacity_reservation_specification": {
                "capacity_reservation_preference": instance.get(
                    "CapacityReservationSpecification", {}
                ).get("CapacityReservationPreference"),
            }
            if "CapacityReservationSpecification" in instance
            else {},
            "private_dns_name_options": {
                "hostname_type": instance.get("PrivateDnsNameOptions", {}).get(
                    "HostnameType"
                ),
                "enable_resource_name_dns_a_record": instance.get(
                    "PrivateDnsNameOptions", {}
                ).get("EnableResourceNameDnsARecord"),
                "enable_resource_name_dns_aaaa_record": instance.get(
                    "PrivateDnsNameOptions", {}
                ).get("EnableResourceNameDnsAAAARecord"),
            }
            if "PrivateDnsNameOptions" in instance
            else {},
            "usage_operation": instance.get("UsageOperation"),
            "usage_operation_update_time": usage_time.isoformat()
            if (usage_time := instance.get("UsageOperationUpdateTime")) is not None
            else None,
            "operator": {
                "managed": instance.get("Operator", {}).get("Managed"),
            }
            if "Operator" in instance
            else {},
            "ami_launch_index": instance.get("AmiLaunchIndex"),
            "product_codes": instance.get("ProductCodes", []),
            "state_transition_reason": instance.get("StateTransitionReason"),
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=tags.get("Name", instance["InstanceId"]),
            region=region,
            configurations=configurations,
            type=AWSResourceType.EC2,
            resource_id=instance["IamInstanceProfile"]["Arn"],
            tags=tags,
            description=f"EC2 instance of type {instance['InstanceType']}",
            status=instance["State"]["Name"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EC2
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl all EC2 resources."""
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for EC2 crawler")
            return all_resources

        instances = self._crawl_ec2_instances(self.region)
        if instances:
            resources = [
                self._map_ec2_to_resource(instance, self.region)
                for instance in instances
            ]

            for resource in resources:
                db_resource = ResourceRepository.create_or_update(db, resource)
                all_resources.append(db_resource)

        db.commit()

        logger.info(f"Crawled {len(all_resources)} EC2 resources")

        return all_resources
