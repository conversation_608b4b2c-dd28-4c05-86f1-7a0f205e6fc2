from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class EC2AutoScalingResourceCrawler(BaseCrawler):
    """Crawler for EC2 Auto Scaling resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "autoscaling",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl EC2 Auto Scaling resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for EC2 Auto Scaling crawler")
            return all_resources

        try:
            # Get all Auto Scaling Groups
            paginator = self.client.get_paginator("describe_auto_scaling_groups")
            for page in paginator.paginate():
                autoscaling_groups = page["AutoScalingGroups"]

                # Filter ASGs based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include active ASGs, exclude failed states
                    autoscaling_groups = [
                        asg
                        for asg in autoscaling_groups
                        if asg.get("Status", "Create complete").lower()
                        not in ["delete failed", "update failed", "create failed"]
                    ]

                for asg in autoscaling_groups:
                    resource = self._map_asg_to_resource(asg)
                    db_resource = ResourceRepository.create_or_update(session, resource)
                    all_resources.append(db_resource)

            session.commit()
            logger.info(f"Crawled {len(all_resources)} EC2 Auto Scaling resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling EC2 Auto Scaling resources: {e}")
            raise

    def _map_asg_to_resource(self, asg: dict[str, Any]) -> Resource:
        """Map Auto Scaling Group data to a Resource object"""
        config = {
            "min_size": asg["MinSize"],
            "max_size": asg["MaxSize"],
            "desired_capacity": asg["DesiredCapacity"],
            "launch_configuration_name": asg.get("LaunchConfigurationName"),
            "launch_template": asg.get("LaunchTemplate"),
            "mixed_instances_policy": asg.get("MixedInstancesPolicy"),
            "vpc_zone_identifier": asg.get("VPCZoneIdentifier"),
            "health_check_type": asg["HealthCheckType"],
            "health_check_grace_period": asg["HealthCheckGracePeriod"],
            "created_time": asg["CreatedTime"].isoformat(),
            "status": asg.get(
                "Status", "Create complete"
            ),  # Default to running state if not specified
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=asg["AutoScalingGroupName"],
            region=self.region if self.region else "",
            type=AWSResourceType.EC2_AUTO_SCALING,
            resource_id=asg["AutoScalingGroupARN"],
            tags={tag["Key"]: tag["Value"] for tag in asg.get("Tags", [])},
            description=f"Auto Scaling Group with desired capacity {asg['DesiredCapacity']}",
            configurations=config,
            status=asg.get("Status", "Create complete"),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EC2_AUTO_SCALING
            ].category,
        )
