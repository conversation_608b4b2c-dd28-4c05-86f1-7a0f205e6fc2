import time
from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class ECSResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.ecs_client = self.session.client("ecs", region_name=self.region)

    def _crawl_ecs_clusters(self) -> list[dict[str, Any]]:
        """
        Crawl ECS clusters in the specified region.
        """
        clusters = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.ecs_client.get_paginator("list_clusters")
                cluster_arns = []
                for page in paginator.paginate():
                    cluster_arns.extend(page["clusterArns"])

                if cluster_arns:
                    response = self.ecs_client.describe_clusters(
                        clusters=cluster_arns, include=["TAGS"]
                    )
                    clusters.extend(response["clusters"])
                return clusters

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.exception(
                        f"Error crawling ECS clusters in {self.region} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _crawl_ecs_services(self, cluster_arn: str) -> list[dict[str, Any]]:
        """
        Crawl ECS services for a specific cluster.
        """
        services = []
        for attempt in range(self.max_retries):
            try:
                paginator = self.ecs_client.get_paginator("list_services")
                service_arns = []
                for page in paginator.paginate(cluster=cluster_arn):
                    service_arns.extend(page["serviceArns"])

                if service_arns:
                    # Describe services in batches of 10 (AWS limit)
                    for i in range(0, len(service_arns), 10):
                        batch = service_arns[i : i + 10]
                        response = self.ecs_client.describe_services(
                            cluster=cluster_arn, services=batch, include=["TAGS"]
                        )
                        ecs_services = response["services"]

                        # Filter services based on include_stopped_resources setting
                        if not self.include_stopped_resources:
                            # Only include active services, exclude inactive/draining
                            ecs_services = [
                                service
                                for service in ecs_services
                                if service.get("status", "").lower()
                                not in ["inactive", "draining"]
                            ]

                        services.extend(ecs_services)
                return services

            except ClientError as e:
                if attempt == self.max_retries - 1:
                    logger.error(
                        f"Error crawling ECS services for cluster {cluster_arn} after {self.max_retries} attempts: {e}"
                    )
                    return []
                time.sleep(self.retry_delay)
        return []

    def _map_ecs_to_resource(
        self, resource_data: dict[str, Any], resource_type: str, cluster_arn: str
    ) -> Resource:
        """
        Map ECS resource data to a Resource object.
        """
        tags = {tag["key"]: tag["value"] for tag in resource_data.get("tags", [])}

        config = {}
        if resource_type == "cluster":
            config = {
                "status": resource_data["status"],
                "registeredContainerInstancesCount": resource_data[
                    "registeredContainerInstancesCount"
                ],
                "runningTasksCount": resource_data["runningTasksCount"],
                "pendingTasksCount": resource_data["pendingTasksCount"],
                "activeServicesCount": resource_data["activeServicesCount"],
                "capacityProviders": resource_data.get("capacityProviders", []),
                "defaultCapacityProviderStrategy": resource_data.get(
                    "defaultCapacityProviderStrategy", []
                ),
            }
        elif resource_type == "service":
            config = {
                "status": resource_data["status"],
                "desiredCount": resource_data["desiredCount"],
                "runningCount": resource_data["runningCount"],
                "pendingCount": resource_data["pendingCount"],
                "launchType": resource_data.get("launchType"),
                "platformVersion": resource_data.get("platformVersion"),
                "taskDefinition": resource_data["taskDefinition"],
                "deploymentConfiguration": resource_data.get(
                    "deploymentConfiguration", {}
                ),
                "networkConfiguration": resource_data.get("networkConfiguration", {}),
                "loadBalancers": resource_data.get("loadBalancers", []),
            }

        return Resource(
            workspace_id=self.workspace_id,
            name=resource_data["clusterName"]
            if resource_type == "cluster"
            else resource_data["serviceName"],
            type=AWSResourceType.ECS,
            resource_id=resource_data["clusterArn"]
            if resource_type == "cluster"
            else resource_data["serviceArn"],
            region=self.region if self.region else "",
            tags=tags,
            configurations=config,
            description=f"ECS {resource_type} in cluster {cluster_arn}",
            status=resource_data["status"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.ECS
            ].category,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """
        Crawl all ECS resources.
        """
        all_resources = []

        # Validate region is available
        if not self.region:
            logger.error("No region specified for ECS crawler")
            return all_resources

        # Crawl clusters
        clusters = self._crawl_ecs_clusters()

        for cluster in clusters:
            # Create/update cluster resource
            cluster_resource = self._map_ecs_to_resource(
                cluster, "cluster", cluster["clusterArn"]
            )
            db_cluster = ResourceRepository.create_or_update(session, cluster_resource)
            all_resources.append(db_cluster)

            # Crawl services for each cluster
            services = self._crawl_ecs_services(cluster["clusterArn"])

            for service in services:
                # Create/update service resource
                service_resource = self._map_ecs_to_resource(
                    service, "service", cluster["clusterArn"]
                )
                db_service = ResourceRepository.create_or_update(
                    session, service_resource
                )
                all_resources.append(db_service)

        session.commit()
        logger.info(f"Crawled {len(all_resources)} ECS resources")
        return all_resources
