from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class EFSResourceCrawler(BaseCrawler):
    """Crawler for EFS resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "efs",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl EFS resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for EFS crawler")
            return all_resources

        try:
            # Get all file systems
            paginator = self.client.get_paginator("describe_file_systems")
            for page in paginator.paginate():
                filesystems = page["FileSystems"]

                # Filter file systems based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include available file systems, exclude error/misconfigured states
                    filesystems = [
                        fs
                        for fs in filesystems
                        if fs.get("LifeCycleState", "").lower()
                        not in ["error", "misconfigured"]
                    ]

                for filesystem in filesystems:
                    try:
                        # Get file system tags
                        tags = self.client.describe_tags(
                            FileSystemId=filesystem["FileSystemId"]
                        )["Tags"]

                        resource = self._map_filesystem_to_resource(filesystem, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for file system {filesystem['FileSystemId']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} EFS resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling EFS resources: {e}")
            raise

    def _map_filesystem_to_resource(
        self, filesystem: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map EFS file system data to a Resource object."""
        config = {
            "file_system_id": filesystem["FileSystemId"],
            "creation_time": filesystem["CreationTime"].isoformat(),
            "life_cycle_state": filesystem["LifeCycleState"],
            "performance_mode": filesystem["PerformanceMode"],
            "throughput_mode": filesystem.get("ThroughputMode"),
            "encrypted": filesystem.get("Encrypted", False),
            "kms_key_id": filesystem.get("KmsKeyId"),
            "provisioned_throughput_in_mibps": filesystem.get(
                "ProvisionedThroughputInMibps"
            ),
            # "size_in_bytes": filesystem.get("SizeInBytes", {}),
            "availability_zone_id": filesystem.get("AvailabilityZoneId"),
            "availability_zone_name": filesystem.get("AvailabilityZoneName"),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=filesystem["FileSystemId"],
            region=self.region if self.region else "",
            type=AWSResourceType.EFS,
            resource_id=filesystem["FileSystemArn"],
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"EFS file system in {filesystem['LifeCycleState']} state",
            configurations=config,
            status=filesystem["LifeCycleState"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EFS
            ].category,
        )
