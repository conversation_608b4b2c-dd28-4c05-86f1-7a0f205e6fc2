from typing import Any

from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class EKSResourceCrawler(BaseCrawler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _crawl_eks_clusters(self, region: str) -> list[dict[str, Any]]:
        """Crawl EKS clusters in a specific region."""
        eks_client = self.session.client("eks", region_name=region)
        clusters = []
        try:
            paginator = eks_client.get_paginator("list_clusters")
            for page in paginator.paginate():
                for cluster_name in page["clusters"]:
                    try:
                        cluster_details = eks_client.describe_cluster(name=cluster_name)
                        cluster = cluster_details["cluster"]

                        # Filter clusters based on include_stopped_resources setting
                        if not self.include_stopped_resources:
                            # Skip clusters that are failed or degraded
                            if cluster.get("status", "").lower() in [
                                "failed",
                                "degraded",
                            ]:
                                continue

                        clusters.append(cluster)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for cluster {cluster_name}: {e}"
                        )
                        raise
        except ClientError as e:
            logger.exception(f"Error listing EKS clusters in {region}: {e}")
            raise
        return clusters

    def _map_eks_to_resource(self, cluster: dict[str, Any], region: str) -> Resource:
        """Map EKS cluster data to a Resource object."""
        tags = cluster.get("tags", {})
        config = {
            "name": cluster["name"],
            "arn": cluster["arn"],
            "version": cluster["version"],
            "status": cluster["status"],
            "role_arn": cluster.get("roleArn"),
            "endpoint": cluster.get("endpoint"),
            "platform_version": cluster.get("platformVersion"),
            "kubernetes_network_config": cluster.get("kubernetesNetworkConfig", {}),
            "logging": cluster.get("logging", {}),
            "identity": cluster.get("identity", {}),
            "encryption_config": cluster.get("encryptionConfig", []),
            "connector_config": cluster.get("connectorConfig", {}),
            "outpost_config": cluster.get("outpostConfig", {}),
            "access_config": cluster.get("accessConfig", {}),
            "created_at": cluster.get("createdAt", "").isoformat()
            if cluster.get("createdAt")
            else None,
            "resources_vpc_config": cluster.get("resourcesVpcConfig", {}),
        }

        resource = Resource(
            workspace_id=self.workspace_id,
            name=cluster["name"],
            region=region if region else "",
            type=AWSResourceType.EKS,
            resource_id=cluster["arn"],
            tags=tags,
            description=f"EKS cluster version {cluster['version']}",
            configurations=config,
            status=cluster["status"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.EKS
            ].category,
        )
        return resource

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl all EKS resources."""
        all_resources = []

        if not self.region:
            logger.error("No region specified for EKS crawler")
            return all_resources

        clusters = self._crawl_eks_clusters(self.region)
        if clusters:
            resources = [
                self._map_eks_to_resource(cluster, self.region) for cluster in clusters
            ]

            for resource in resources:
                db_resource = ResourceRepository.create_or_update(session, resource)
                all_resources.append(db_resource)

        session.commit()

        logger.info(f"Crawled {len(all_resources)} EKS clusters")
        return all_resources
