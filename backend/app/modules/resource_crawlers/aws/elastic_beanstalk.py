from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class ElasticBeanstalkResourceCrawler(BaseCrawler):
    """Crawler for Elastic Beanstalk resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "elasticbeanstalk",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl Elastic Beanstalk resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for Elastic Beanstalk crawler")
            return all_resources

        try:
            # Get all environments
            paginator = self.client.get_paginator("describe_environments")
            for page in paginator.paginate():
                environments = page["Environments"]

                # Filter environments based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include ready environments, exclude terminated/degraded states
                    environments = [
                        env
                        for env in environments
                        if env.get("Status", "").lower()
                        not in ["terminated", "terminating", "degraded", "severe"]
                    ]

                for environment in environments:
                    try:
                        # Get environment resources
                        resources = self.client.describe_environment_resources(
                            EnvironmentId=environment["EnvironmentId"]
                        )["EnvironmentResources"]

                        resource = self._map_environment_to_resource(
                            environment, resources
                        )
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for environment {environment['EnvironmentName']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} Elastic Beanstalk resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling Elastic Beanstalk resources: {e}")
            raise

    def _map_environment_to_resource(
        self, environment: dict[str, Any], resources: dict[str, Any]
    ) -> Resource:
        """Map Elastic Beanstalk environment data to a Resource object."""
        config = {
            "environment_name": environment["EnvironmentName"],
            "environment_id": environment["EnvironmentId"],
            "application_name": environment["ApplicationName"],
            "version_label": environment.get("VersionLabel"),
            "solution_stack_name": environment.get("SolutionStackName"),
            "platform_arn": environment.get("PlatformArn"),
            "template_name": environment.get("TemplateName"),
            "description": environment.get("Description"),
            "endpoint_url": environment.get("EndpointURL"),
            "cname": environment.get("CNAME"),
            "status": environment["Status"],
            "health": environment.get("Health"),
            "health_status": environment.get("HealthStatus"),
            "tier": environment.get("Tier", {}),
            "environment_links": environment.get("EnvironmentLinks", []),
            "resources": {
                "auto_scaling_groups": resources.get("AutoScalingGroups", []),
                "instances": resources.get("Instances", []),
                "launch_configurations": resources.get("LaunchConfigurations", []),
                "load_balancers": resources.get("LoadBalancers", []),
                "triggers": resources.get("Triggers", []),
                "queues": resources.get("Queues", []),
            },
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=environment["EnvironmentName"],
            region=self.region if self.region else "",
            type=AWSResourceType.ELASTIC_BEANSTALK,
            resource_id=environment["EnvironmentArn"],
            tags=environment.get("Tags", {}),
            description=f"Elastic Beanstalk environment in {environment['Status']} status",
            configurations=config,
            status=environment["Status"],
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.ELASTIC_BEANSTALK
            ].category,
        )
