from typing import Any

import boto3
from botocore.exceptions import ClientError
from sqlmodel import Session

from app.logger import logger
from app.models import (
    RESOURCE_TYPE_MAPPINGS,
    AWSResourceType,
    CloudProvider,
    Resource,
)
from app.repositories.resources import ResourceRepository

from .base_crawler import (
    BaseCrawler,
    aws_retry,
)


class ELBResourceCrawler(BaseCrawler):
    """Crawler for Elastic Load Balancer resources"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = boto3.client(
            "elb",
            region_name=self.region,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
        )

    @aws_retry()
    def crawl_resources_and_metrics(self, session: Session) -> list[Resource]:
        """Crawl ELB resources"""
        all_resources = []

        if not self.region:
            logger.error("No region specified for ELB crawler")
            return all_resources

        try:
            # Get all load balancers
            paginator = self.client.get_paginator("describe_load_balancers")
            for page in paginator.paginate():
                load_balancers = page["LoadBalancerDescriptions"]

                # Filter load balancers based on include_stopped_resources setting
                if not self.include_stopped_resources:
                    # Only include active load balancers, exclude failed/inactive states
                    load_balancers = [
                        lb
                        for lb in load_balancers
                        if lb.get("State", {}).get("Code", "").lower()
                        not in ["failed", "active_impaired", "inactive"]
                    ]

                for lb in load_balancers:
                    try:
                        # Get load balancer tags
                        tags = self.client.describe_tags(
                            LoadBalancerNames=[lb["LoadBalancerName"]]
                        )["TagDescriptions"][0]["Tags"]

                        resource = self._map_load_balancer_to_resource(lb, tags)
                        db_resource = ResourceRepository.create_or_update(
                            session, resource
                        )
                        all_resources.append(db_resource)
                    except ClientError as e:
                        logger.exception(
                            f"Error getting details for load balancer {lb['LoadBalancerName']}: {e}"
                        )
                        continue

            session.commit()
            logger.info(f"Crawled {len(all_resources)} ELB resources")
            return all_resources

        except ClientError as e:
            logger.exception(f"Error crawling ELB resources: {e}")
            raise

    def _map_load_balancer_to_resource(
        self, lb: dict[str, Any], tags: list[dict[str, str]]
    ) -> Resource:
        """Map ELB load balancer data to a Resource object."""
        config = {
            "load_balancer_name": lb["LoadBalancerName"],
            "dns_name": lb["DNSName"],
            "canonical_hosted_zone_name": lb.get("CanonicalHostedZoneName"),
            "canonical_hosted_zone_name_id": lb.get("CanonicalHostedZoneNameID"),
            "listener_descriptions": lb["ListenerDescriptions"],
            "policies": lb.get("Policies", {}),
            "backend_server_descriptions": lb.get("BackendServerDescriptions", []),
            "availability_zones": lb["AvailabilityZones"],
            "subnets": lb.get("Subnets", []),
            "security_groups": lb.get("SecurityGroups", []),
            "vpc_id": lb.get("VPCId"),
            "instances": lb.get("Instances", []),
            "health_check": lb.get("HealthCheck", {}),
            "source_security_group": lb.get("SourceSecurityGroup", {}),
            "scheme": lb.get("Scheme"),
            "created_time": lb.get("CreatedTime", "").isoformat()
            if lb.get("CreatedTime")
            else None,
            "state": lb.get("State", {}).get("Code", "unknown"),
        }

        return Resource(
            workspace_id=self.workspace_id,
            name=lb["LoadBalancerName"],
            region=self.region if self.region else "",
            type=AWSResourceType.ELB,
            resource_id=f"arn:aws:elasticloadbalancing:{self.region}:{self.aws_access_account_id}:loadbalancer/{lb['LoadBalancerName']}",
            tags={tag["Key"]: tag["Value"] for tag in tags},
            description=f"Classic Load Balancer in {lb.get('Scheme', 'unknown')} scheme",
            configurations=config,
            status=lb.get("State", {}).get("Code", "unknown"),
            provider=CloudProvider.AWS,
            category=RESOURCE_TYPE_MAPPINGS[CloudProvider.AWS][
                AWSResourceType.ELB
            ].category,
        )
