from typing import Any

from google.cloud import storage
from pydantic import BaseModel, Field
from rich import print
from sqlmodel import Session

from app.logger import logger
from app.models import (
    CloudProvider,
    GCPResourceType,
    Resource,
    ResourceCategory,
)
from app.repositories.resources import ResourceRepository

from .base_gcp_crawler import BaseGCPCrawler


# TODO: Replace with proper Pydantic models after seeing raw data
class GCPCloudStorageBucket(BaseModel):
    """Temporary model for GCP Cloud Storage bucket - will be updated after seeing raw data"""

    name: str
    # Add more fields after seeing actual data structure
    extra: dict[str, Any] = Field(default_factory=dict)


class CloudStorageCrawler(BaseGCPCrawler):
    """GCP Cloud Storage resource crawler"""

    def get_provider_name(self) -> str:
        """Get the provider name"""
        return "gcp"

    def get_resource_type(self) -> str:
        """Get the resource type"""
        return "CLOUD_STORAGE"

    def validate_credentials(self) -> bool:
        """Validate GCP credentials by testing Cloud Storage API access"""
        try:
            client = storage.Client(credentials=self.credentials)
            # Try to list buckets to validate credentials
            buckets = list(client.list_buckets(max_results=1))
            logger.info(f"Found {len(buckets)} buckets")
            return True
        except Exception as e:
            logger.error(f"Failed to validate GCP Cloud Storage credentials: {e}")
            return False

    def _crawl_storage_buckets(self) -> list[GCPCloudStorageBucket]:
        """Crawl all Cloud Storage buckets in the project"""
        try:
            client = storage.Client(credentials=self.credentials)

            buckets = []
            for bucket in client.list_buckets():
                # Create temporary bucket object for now
                bucket_data = GCPCloudStorageBucket(
                    name=bucket.name,
                    extra={
                        "raw_bucket": bucket,
                        "location": getattr(bucket, "location", None),
                        "storage_class": getattr(bucket, "storage_class", None),
                        "created": getattr(bucket, "time_created", None),
                        "updated": getattr(bucket, "updated", None),
                        "labels": getattr(bucket, "labels", {}),
                        "versioning_enabled": getattr(
                            bucket, "versioning_enabled", False
                        ),
                        "public_access_prevention": getattr(
                            bucket, "public_access_prevention", None
                        ),
                        "uniform_bucket_level_access": getattr(
                            bucket, "uniform_bucket_level_access", None
                        ),
                    },
                )
                buckets.append(bucket_data)

                # Debug: Print raw bucket data for first bucket
                if len(buckets) == 1:
                    logger.info(f"Sample bucket fields: {dir(bucket)}")
                    print(f"Raw bucket data: {bucket}")
                    print(f"Bucket name: {bucket.name}")
                    print(f"Bucket location: {getattr(bucket, 'location', 'N/A')}")
                    print(
                        f"Bucket storage class: {getattr(bucket, 'storage_class', 'N/A')}"
                    )
                    print(f"Bucket labels: {getattr(bucket, 'labels', {})}")

            return buckets

        except Exception as e:
            logger.error(f"Failed to crawl Cloud Storage buckets: {e}")
            return []

    def _map_bucket_to_resource(self, bucket: GCPCloudStorageBucket) -> Resource:
        """Map GCP Cloud Storage bucket to unified Resource model"""
        try:
            # Extract data from the bucket
            name = bucket.name
            location = bucket.extra.get("location", "unknown")
            storage_class = bucket.extra.get("storage_class", "STANDARD")
            created = bucket.extra.get("created")
            labels = bucket.extra.get("labels", {})

            # Build configurations
            configurations = {
                "bucket_name": name,
                "location": location,
                "storage_class": storage_class,
                "created": created.isoformat() if created else None,
                "versioning_enabled": bucket.extra.get("versioning_enabled", False),
                "public_access_prevention": bucket.extra.get(
                    "public_access_prevention"
                ),
                "uniform_bucket_level_access": bucket.extra.get(
                    "uniform_bucket_level_access"
                ),
                "labels": labels,
            }

            # Build resource ID (GCP format)
            resource_id = f"projects/{self.project_id}/buckets/{name}"

            return Resource(
                workspace_id=self.workspace_id,
                name=name,
                region=location,
                configurations=configurations,
                type=GCPResourceType.CLOUD_STORAGE,
                resource_id=resource_id,
                tags=labels,
                description=f"Cloud Storage bucket with {storage_class} storage class",
                status="found",
                provider=CloudProvider.GCP,
                category=ResourceCategory.STORAGE,
            )

        except Exception as e:
            logger.error(f"Failed to map bucket {bucket.name} to resource: {e}")
            raise

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl GCP Cloud Storage resources and return unified Resource objects"""
        all_resources = []

        logger.info(f"Starting Cloud Storage crawl for project {self.project_id}")

        try:
            # Get buckets
            buckets = self._crawl_storage_buckets()
            logger.info(f"Found {len(buckets)} Cloud Storage buckets")

            # Convert to unified Resource objects
            for bucket in buckets:
                try:
                    resource = self._map_bucket_to_resource(bucket)
                    db_resource = ResourceRepository.create_or_update(db, resource)
                    all_resources.append(db_resource)
                except Exception as e:
                    logger.error(f"Failed to map bucket {bucket.name} to resource: {e}")
                    continue

            db.commit()
            logger.info(
                f"Successfully mapped {len(all_resources)} Cloud Storage resources"
            )
            return all_resources

        except Exception as e:
            logger.error(f"Failed to crawl GCP Cloud Storage resources: {e}")
            return []
