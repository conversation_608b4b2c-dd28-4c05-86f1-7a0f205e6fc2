from google.cloud import compute_v1
from pydantic import BaseModel, Field
from rich import print
from sqlmodel import Session

from app.logger import logger
from app.models import (
    CloudProvider,
    GCPResourceType,
    Resource,
    ResourceCategory,
)
from app.repositories.resources import ResourceRepository

from .base_gcp_crawler import BaseGCPCrawler


class GCPNetworkInterface(BaseModel):
    """Pydantic model for GCP Compute Engine network interface"""

    network: str | None = None
    subnetwork: str | None = None
    internal_ip: str | None = None
    external_ip: str | None = None


class GCPDisk(BaseModel):
    """Pydantic model for GCP Compute Engine disk"""

    device_name: str
    source: str | None = None
    boot: bool = False
    auto_delete: bool = True


class GCPComputeEngineInstance(BaseModel):
    """Pydantic model for GCP Compute Engine instance data"""

    name: str
    id: str
    zone: str
    region: str
    status: str
    machine_type: str
    network_interfaces: list[GCPNetworkInterface] = Field(default_factory=list)
    disks: list[GCPDisk] = Field(default_factory=list)
    labels: dict[str, str] = Field(default_factory=dict)
    metadata: dict[str, str] = Field(default_factory=dict)
    tags: dict[str, list[str]] = Field(default_factory=dict)
    creation_timestamp: str | None = None

    class Config:
        extra = "allow"  # Allow additional fields for future flexibility


class ComputeEngineCrawler(BaseGCPCrawler):
    """Crawler for GCP Compute Engine instances"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def _crawl_compute_instances(self) -> list[GCPComputeEngineInstance]:
        """Crawl Compute Engine instances across all zones"""
        instances = []

        try:
            # Get all zones in the project
            # TODO: @khai.trinh: the region is not in the service account key, we need to get the region from the project
            # zones_client = compute_v1.ZonesClient(credentials=self.credentials)
            # zones_request = compute_v1.ListZonesRequest(project=self.project_id)
            # zones = zones_client.list(request=zones_request)
            zones = [
                compute_v1.Zone(
                    name="asia-southeast1-a",
                    region="asia-southeast1",
                )
            ]

            for zone in zones:
                try:
                    # List instances in each zone
                    instances_client = compute_v1.InstancesClient(
                        credentials=self.credentials
                    )
                    request = compute_v1.ListInstancesRequest(
                        project=self.project_id, zone=zone.name
                    )

                    zone_instances = instances_client.list(request=request)
                    for instance in zone_instances:
                        # Debug: Print available fields for first instance
                        if len(instances) == 0:
                            logger.info(f"Sample instance fields: {dir(instance)}")
                            if instance.network_interfaces:
                                logger.info(
                                    f"Sample network interface fields: {dir(instance.network_interfaces[0])}"
                                )

                            # Raw instance data
                            print(f"raw instance: {instance}")

                        # Create network interfaces data
                        network_interfaces = [
                            GCPNetworkInterface(
                                network=ni.network.split("/")[-1]
                                if ni.network
                                else None,
                                subnetwork=ni.subnetwork.split("/")[-1]
                                if ni.subnetwork
                                else None,
                                internal_ip=getattr(ni, "network_i_p", None),
                                external_ip=ni.access_configs[0].nat_i_p
                                if ni.access_configs
                                else None,
                            )
                            for ni in instance.network_interfaces
                        ]

                        # Create disks data
                        disks = [
                            GCPDisk(
                                device_name=disk.device_name,
                                source=disk.source.split("/")[-1]
                                if disk.source
                                else None,
                                boot=disk.boot,
                                auto_delete=disk.auto_delete,
                            )
                            for disk in instance.disks
                        ]

                        # Create instance data using Pydantic model
                        instance_data = GCPComputeEngineInstance(
                            name=instance.name,
                            id=str(instance.id),
                            zone=zone.name,
                            region=zone.region.split("/")[-1],
                            status=instance.status,
                            machine_type=instance.machine_type.split("/")[-1],
                            network_interfaces=network_interfaces,
                            disks=disks,
                            labels=dict(instance.labels) if instance.labels else {},
                            metadata={
                                item.key: item.value
                                for item in instance.metadata.items
                                if item.key
                                != "ssh-keys"  # Exclude ssh-keys for security
                            }
                            if instance.metadata
                            else {},
                            tags={
                                "items": list(instance.tags.items)
                                if instance.tags
                                else []
                            },
                            creation_timestamp=instance.creation_timestamp,
                        )

                        # print(instance_data)
                        instances.append(instance_data)

                except Exception as e:
                    logger.error(
                        f"Error crawling instances in zone {zone.name}: {str(e)}"
                    )
                    continue

        except Exception as e:
            logger.error(f"Error getting zones for project {self.project_id}: {str(e)}")
            raise

        return instances

    def _map_compute_to_resource(self, instance: GCPComputeEngineInstance) -> Resource:
        """Map Compute Engine instance data to Resource model"""

        # Extract labels as tags
        tags = instance.labels

        # Build configurations
        configurations = {
            "instance_id": instance.id,
            "instance_name": instance.name,
            "machine_type": instance.machine_type,
            "zone": instance.zone,
            "region": instance.region,
            "status": instance.status,
            "network_interfaces": [ni.dict() for ni in instance.network_interfaces],
            "disks": [disk.dict() for disk in instance.disks],
            "metadata": instance.metadata,
            "tags": instance.tags,
            "creation_timestamp": instance.creation_timestamp,
        }

        # Build resource ID (GCP format)
        resource_id = f"projects/{self.project_id}/zones/{instance.zone}/instances/{instance.name}"

        return Resource(
            workspace_id=self.workspace_id,
            name=instance.name,
            region=instance.region,
            configurations=configurations,
            type=GCPResourceType.COMPUTE_ENGINE,
            resource_id=resource_id,
            tags=tags,
            description=f"Compute Engine instance of type {instance.machine_type}",
            status=instance.status,
            provider=CloudProvider.GCP,
            category=ResourceCategory.COMPUTE,
        )

    def crawl_resources_and_metrics(self, db: Session) -> list[Resource]:
        """Crawl all Compute Engine resources"""
        all_resources = []

        logger.info(f"Starting Compute Engine crawl for project {self.project_id}")

        instances = self._crawl_compute_instances()

        for instance_data in instances:
            try:
                resource = self._map_compute_to_resource(instance_data)
                db_resource = ResourceRepository.create_or_update(db, resource)
                all_resources.append(db_resource)
            except Exception as e:
                logger.error(
                    f"Error processing instance {instance_data.name}: {str(e)}"
                )
                continue

        db.commit()
        logger.info(f"Crawled {len(all_resources)} Compute Engine resources")

        return all_resources
