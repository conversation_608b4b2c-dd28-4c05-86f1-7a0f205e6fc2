from uuid import UUI<PERSON>

from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import Agent, AgentConnection, Connection
from app.repositories.base import BaseRepository


class AgentRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(self, async_session: AsyncSession):
        super().__init__(Agent)
        self.async_session = async_session

    async def get_agents(self, workspace_id: UUID) -> list[Agent]:
        try:
            cache_key = self._generate_cache_key(
                "get_agents", workspace_id=str(workspace_id)
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return [Agent.model_validate(item) for item in cached]
                except Exception:
                    pass

            statement = select(Agent).where(Agent.workspace_id == workspace_id)
            agents = await self.async_session.exec(statement)
            agent_list = list(agents.all())
            self._set_cache(cache_key, [agent.model_dump() for agent in agent_list])
            return agent_list
        except Exception as e:
            logger.exception(f"Error getting agents for workspace {workspace_id}. {e}")
            raise RepositoryError(
                f"Error getting agents for workspace {workspace_id}", 500
            )

    async def get_agent(self, agent_id: UUID) -> Agent | None:
        try:
            cache_key = self._generate_cache_key("get_agent", agent_id=str(agent_id))
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return Agent.model_validate(cached)
                except Exception:
                    pass

            statement = select(Agent).where(Agent.id == agent_id)
            response = await self.async_session.exec(statement)
            agent = response.one_or_none()
            if agent:
                self._set_cache(cache_key, agent.model_dump())
            return agent
        except Exception as e:
            logger.exception(f"Error getting agent {agent_id}. {e}")
            raise RepositoryError(f"Error getting agent {agent_id}", status_code=500)

    async def get_agent_by_alias(self, alias: str, workspace_id: UUID) -> Agent | None:
        try:
            cache_key = self._generate_cache_key(
                "get_agent_by_alias", alias=alias, workspace_id=str(workspace_id)
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return Agent.model_validate(cached)
                except Exception:
                    pass

            statement = select(Agent).where(
                Agent.alias == alias, Agent.workspace_id == workspace_id
            )
            response = await self.async_session.exec(statement)
            agent = response.one_or_none()
            if agent:
                self._set_cache(cache_key, agent.model_dump())
                by_id_key = self._generate_cache_key(
                    "get_agent", agent_id=str(agent.id)
                )
                self._set_cache(by_id_key, agent.model_dump())
            return agent
        except Exception as e:
            logger.exception(
                f"Error getting agent by alias {alias} for workspace {workspace_id}. {e}"
            )
            raise RepositoryError(
                f"Error getting agent by alias {alias} for workspace {workspace_id}",
                status_code=500,
            )

    async def get_all_agent_connections(
        self, agents_ids: list[UUID]
    ) -> dict[UUID, list[Connection]]:
        """Get all connections associated with agents using the many-to-many relationship.

        Returns a dictionary mapping agent IDs to their associated connections.
        """
        try:
            # TODO: Cache logic commented out due to bugs - needs investigation
            # # Stable key from sorted agent IDs
            # sorted_ids = sorted([str(aid) for aid in agents_ids])
            # cache_key = self._generate_cache_key(
            #     "get_all_agent_connections", agent_ids=",".join(sorted_ids)
            # )
            # cached = self._get_from_cache(cache_key)
            # if cached:
            #     try:
            #         result: dict[UUID, list[Connection]] = {}
            #         for k, v in cached.items():
            #             result[UUID(k)] = [Connection.model_validate(c) for c in v]
            #         return result
            #     except Exception:
            #         pass

            statement = (
                select(AgentConnection, Connection)
                .join(Connection, col(Connection.id) == AgentConnection.conn_id)
                .where(col(AgentConnection.agent_id).in_(agents_ids))
            )
            query_result = await self.async_session.exec(statement)

            # Group connections by agent ID
            agent_connections: dict[UUID, list[Connection]] = {}
            for agent_conn, connection in query_result:
                if agent_conn.agent_id not in agent_connections:
                    agent_connections[agent_conn.agent_id] = []
                agent_connections[agent_conn.agent_id].append(connection)

            # TODO: Cache logic commented out due to bugs - needs investigation
            # # Cache as str-keyed map with serialized connections
            # serializable_map: dict[str, list[dict]] = {}
            # for k, v in agent_connections.items():
            #     serializable_map[str(k)] = [c.model_dump() for c in v]
            # self._set_cache(cache_key, serializable_map)

            return agent_connections
        except Exception as e:
            logger.exception(
                f"Error getting all agent connections for agents {agents_ids}. {e}"
            )
            raise RepositoryError(
                f"Error getting all agent connections for agents {agents_ids}", 500
            )

    async def create_agent(self, agent: Agent) -> Agent:
        try:
            self.async_session.add(agent)
            await self.async_session.commit()
            await self.async_session.refresh(agent)
            # Invalidate and warm caches
            self._invalidate_model_cache(agent.id)
            by_id_key = self._generate_cache_key("get_agent", agent_id=str(agent.id))
            self._set_cache(by_id_key, agent.model_dump())
            return agent
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Error creating agent {agent}. {e}")
            raise RepositoryError(f"Error creating agent {agent}", 500)

    async def update_agent(
        self,
        agent_id: UUID,
        is_active: bool | None = None,
        instructions: str | None = None,
    ) -> Agent:
        try:
            statement = select(Agent).where(Agent.id == agent_id)
            result = await self.async_session.exec(statement)
            agent = result.one()
            if is_active is not None:
                agent.is_active = is_active
            if instructions is not None:
                agent.instructions = instructions
            await self.async_session.commit()
            await self.async_session.refresh(agent)
            # Invalidate and warm caches
            self._invalidate_model_cache(agent.id)
            by_id_key = self._generate_cache_key("get_agent", agent_id=str(agent.id))
            self._set_cache(by_id_key, agent.model_dump())
            return agent
        except Exception as e:
            await self.async_session.rollback()
            logger.exception(f"Error updating agent {agent_id}. {e}")
            raise RepositoryError(f"Error updating agent {agent_id}", 500)

    async def agent_exists(self, agent_name: str, workspace_id: UUID) -> bool:
        """Check if an agent with the given name already exists in the workspace."""
        cache_key = self._generate_cache_key(
            "agent_exists", agent_name=agent_name, workspace_id=str(workspace_id)
        )
        cached = self._get_from_cache(cache_key)
        if isinstance(cached, bool):
            return cached

        statement = select(Agent).where(
            Agent.title == agent_name,
            Agent.workspace_id == workspace_id,
            Agent.is_deleted == False,
        )
        result = await self.async_session.exec(statement)
        exists = result.first() is not None
        self._set_cache(cache_key, exists)
        return exists
