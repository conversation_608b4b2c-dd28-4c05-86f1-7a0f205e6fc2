import uuid
from datetime import UTC, datetime

from sqlalchemy import func, or_, select, update
from sqlalchemy.sql import operators
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import <PERSON><PERSON>, <PERSON><PERSON><PERSON>everity, <PERSON>ert<PERSON>tatus, PaginationMeta
from app.repositories.base import BaseRepository
from app.schemas.alert import <PERSON><PERSON><PERSON><PERSON>, AlertList, AlertResponse, AlertUpdate


class AlertRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(
        self,
        async_session: AsyncSession,
        cache_enabled: bool | None = None,
    ):
        super().__init__(Alert, cache_enabled=cache_enabled)
        self.async_session = async_session

    async def create_alert(self, workspace_id: uuid.UUID, data: AlertCreate) -> Alert:
        """Create a new alert."""
        alert = Alert(
            **data.model_dump(), workspace_id=workspace_id, status=AlertStatus.OPEN
        )
        self.async_session.add(alert)
        await self.async_session.commit()
        await self.async_session.refresh(alert)

        # Invalidate cache
        self._invalidate_model_cache()

        return alert

    async def get_by_id_and_workspace(
        self, alert_id: uuid.UUID, workspace_id: uuid.UUID
    ) -> Alert | None:
        """Get an alert by ID and workspace."""
        cache_key = self._generate_cache_key(
            "get_by_id_and_workspace",
            alert_id=str(alert_id),
            workspace_id=str(workspace_id),
        )
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"Cache hit for alert: {cache_key}")
            return Alert(**cached_result)

        query = select(Alert).where(
            Alert.id == alert_id, Alert.workspace_id == workspace_id
        )
        result = await self.async_session.execute(query)
        alert = result.scalar_one_or_none()

        if alert:
            self._set_cache(cache_key, alert.model_dump())

        return alert

    async def list_alerts_with_pagination(
        self,
        workspace_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        severity: AlertSeverity | None = None,
        status: AlertStatus | None = None,
        search: str | None = None,
        sort_by: str = "created_at",
        sort_desc: bool = True,
    ) -> AlertList:
        """Get alerts with pagination, filters, and search."""
        cache_key = self._generate_cache_key(
            "list_alerts_with_pagination",
            workspace_id=str(workspace_id),
            skip=skip,
            limit=limit,
            severity=severity.value if severity else None,
            status=status.value if status else None,
            search=search,
            sort_by=sort_by,
            sort_desc=sort_desc,
        )
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"Cache hit for alerts list: {cache_key}")
            return AlertList(**cached_result)

        query = select(Alert).where(Alert.workspace_id == workspace_id)

        if severity:
            query = query.where(Alert.severity == severity)
        if status:
            query = query.where(Alert.status == status)
        if search:
            search_term = f"%{search.lower()}%"
            query = query.where(
                or_(
                    operators.ilike_op(Alert.title, search_term),
                    operators.ilike_op(Alert.description, search_term),
                )
            )

        # Add sorting
        sort_column = getattr(Alert, sort_by)
        query = query.order_by(sort_column.desc() if sort_desc else sort_column)

        # Get total count
        total = await self.async_session.execute(
            select(func.count()).select_from(query)
        )
        total_items = total.scalar() or 0

        # Apply pagination
        paginated_query = query.offset(skip).limit(limit)
        result = await self.async_session.execute(paginated_query)
        alerts = result.scalars().all()

        # Create pagination metadata
        page = (skip // limit) + 1 if limit > 0 else 1
        total_pages = (total_items + limit - 1) // limit if limit > 0 else 1

        pagination_meta = PaginationMeta(
            page=page,
            take=limit,
            total_items=total_items,
            total_pages=total_pages,
            has_previous=page > 1,
            has_next=page < total_pages,
            start_index=skip + 1 if total_items > 0 else 0,
            end_index=min(skip + limit, total_items),
        )

        # Convert Alert objects to AlertResponse objects
        alert_responses = [AlertResponse.model_validate(alert) for alert in alerts]

        result_list = AlertList(data=alert_responses, meta=pagination_meta)

        # Cache the result
        self._set_cache(cache_key, result_list.model_dump())

        return result_list

    async def update_alert(
        self,
        alert_id: uuid.UUID,
        workspace_id: uuid.UUID,
        data: AlertUpdate,
    ) -> Alert | None:
        """Update an alert."""
        alert = await self.get_by_id_and_workspace(alert_id, workspace_id)
        if not alert:
            return None

        update_data = data.model_dump(exclude_unset=True)

        for key, value in update_data.items():
            setattr(alert, key, value)

        alert.updated_at = datetime.now(UTC)
        await self.async_session.commit()
        await self.async_session.refresh(alert)

        # Invalidate cache
        self._invalidate_model_cache(alert_id)

        return alert

    async def update_status(
        self, alert_id: uuid.UUID, workspace_id: uuid.UUID, status: AlertStatus
    ) -> Alert | None:
        """Update alert status."""
        alert = await self.get_by_id_and_workspace(alert_id, workspace_id)
        if not alert:
            return None

        alert.status = status
        alert.updated_at = datetime.now(UTC)
        await self.async_session.commit()
        await self.async_session.refresh(alert)

        # Invalidate cache
        self._invalidate_model_cache(alert_id)

        return alert

    async def mark_all_acknowledged(self, workspace_id: uuid.UUID) -> dict:
        """Mark all open alerts as acknowledged for the workspace."""
        # First, count how many alerts will be updated
        count_query = select(func.count()).select_from(
            select(Alert).where(
                Alert.workspace_id == workspace_id, Alert.status == AlertStatus.OPEN
            )
        )
        count_result = await self.async_session.execute(count_query)
        count = count_result.scalar() or 0

        # Update all open alerts to acknowledged
        update_query = (
            update(Alert)
            .where(Alert.workspace_id == workspace_id)
            .where(Alert.status == AlertStatus.OPEN)
            .values(status=AlertStatus.ACKNOWLEDGED, updated_at=datetime.now(UTC))
        )

        await self.async_session.execute(update_query)
        await self.async_session.commit()

        # Invalidate cache for this workspace
        pattern = f"alert:*workspace_id='{workspace_id}'*"
        self._delete_cache_pattern(pattern)

        return {"message": f"Marked {count} alerts as acknowledged"}

    async def delete_alert(self, alert_id: uuid.UUID, workspace_id: uuid.UUID) -> bool:
        """Delete an alert."""
        alert = await self.get_by_id_and_workspace(alert_id, workspace_id)
        if not alert:
            return False

        await self.async_session.delete(alert)
        await self.async_session.commit()

        # Invalidate cache
        self._invalidate_model_cache(alert_id)

        return True

    async def get_status_summary(self, workspace_id: uuid.UUID) -> dict:
        """Get summary of alerts by status for the last 30 days."""
        cache_key = self._generate_cache_key(
            "get_status_summary", workspace_id=str(workspace_id)
        )
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"Cache hit for alert status summary: {cache_key}")
            return cached_result

        # Build query to count alerts by status
        query = (
            select(Alert.status, func.count(Alert.id).label("count"))
            .where(Alert.workspace_id == workspace_id)
            .group_by(Alert.status)
        )

        result = await self.async_session.execute(query)
        status_counts = {status.value: count for status, count in result.all()}

        # Make sure all statuses are represented, even if count is 0
        for status in AlertStatus.__members__.values():
            if status.value not in status_counts:
                status_counts[status.value] = 0

        # Get total count
        total_query = select(func.count(Alert.id)).where(
            Alert.workspace_id == workspace_id
        )
        total_result = await self.async_session.execute(total_query)
        total = total_result.scalar() or 0

        summary = {"status_counts": status_counts, "total": total}

        # Cache the result
        self._set_cache(cache_key, summary)

        return summary
