from uuid import UUID

from sqlalchemy.orm import selectinload
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import BuiltInTool, WorkspaceBuiltInTool
from app.repositories.base import BaseRepository


class BuiltInToolRepository(BaseRepository):
    # TODO: There is a bug when turn cache on, the workspace builtin tools are not returned
    CACHE_ENABLED = False
    CACHE_TTL = 300

    def __init__(self, session: AsyncSession):
        super().__init__(WorkspaceBuiltInTool)
        self.session = session

    async def get_workspace_builtin_tools(
        self, workspace_id: UUID
    ) -> list[WorkspaceBuiltInTool]:
        """Get all built-in tools associated with a workspace."""
        try:
            cache_key = self._generate_cache_key(
                "get_workspace_builtin_tools", workspace_id=str(workspace_id)
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return [WorkspaceBuiltInTool(**item) for item in cached]
                except Exception:
                    pass
            # Create a query to select WorkspaceBuiltInTool with related BuiltInTool
            statements = (
                select(WorkspaceBuiltInTool)
                .where(WorkspaceBuiltInTool.workspace_id == workspace_id)
                .options(selectinload(WorkspaceBuiltInTool.builtin_tool))  # type: ignore[arg-type]
            )

            # Execute the query
            result = await self.session.exec(statements)
            tools = list(result.all())
            # Cache serialized form
            self._set_cache(cache_key, [tool.model_dump() for tool in tools])
            return tools
        except Exception as e:
            logger.exception(f"Error getting workspace builtin tools: {e}")
            raise RepositoryError(
                status_code=500,
                message="Failed to get workspace builtin tools.",
            )

    async def get_workspace_builtin_tool(
        self, workspace_id: UUID, workspace_builtin_tool_id: UUID
    ) -> WorkspaceBuiltInTool | None:
        """Get a specific built-in tool by ID for a workspace."""
        try:
            cache_key = self._generate_cache_key(
                "get_workspace_builtin_tool",
                workspace_id=str(workspace_id),
                workspace_builtin_tool_id=str(workspace_builtin_tool_id),
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return WorkspaceBuiltInTool(**cached)
                except Exception:
                    pass
            query = (
                select(WorkspaceBuiltInTool)
                .where(
                    WorkspaceBuiltInTool.workspace_id == workspace_id,
                    WorkspaceBuiltInTool.id == workspace_builtin_tool_id,
                )
                .options(selectinload(WorkspaceBuiltInTool.builtin_tool))  # type: ignore[arg-type]
            )
            result = await self.session.exec(query)
            obj = result.first()
            if obj:
                self._set_cache(cache_key, obj.model_dump())
            return obj
        except Exception as e:
            logger.exception(f"Error getting workspace builtin tool: {e}")
            raise RepositoryError(
                status_code=500,
                message="Failed to get workspace builtin tool.",
            )

    async def get_workspace_builtin_tools_by_ids(
        self, workspace_id: UUID, tool_ids: list[UUID]
    ) -> list[WorkspaceBuiltInTool]:
        """Get workspace builtin tools by IDs for validation."""
        try:
            sorted_ids = sorted(str(tid) for tid in tool_ids)
            cache_key = self._generate_cache_key(
                "get_workspace_builtin_tools_by_ids",
                workspace_id=str(workspace_id),
                tool_ids=",".join(sorted_ids),
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return [WorkspaceBuiltInTool(**item) for item in cached]
                except Exception:
                    pass
            stmt = (
                select(WorkspaceBuiltInTool)
                .where(
                    WorkspaceBuiltInTool.workspace_id == workspace_id,
                    col(WorkspaceBuiltInTool.id).in_(tool_ids),
                )
                .options(selectinload(WorkspaceBuiltInTool.builtin_tool))  # type: ignore[arg-type]
            )
            result = await self.session.exec(stmt)
            tools = list(result.all())
            self._set_cache(cache_key, [tool.model_dump() for tool in tools])
            return tools
        except Exception as e:
            logger.exception(f"Error getting workspace builtin tools by IDs: {e}")
            raise RepositoryError(
                status_code=500,
                message="Failed to get workspace builtin tools by IDs.",
            )

    async def update_workspace_builtin_tool(
        self,
        workspace_id: UUID,
        workspace_builtin_tool_id: UUID,
        required_permission: bool,
    ) -> bool | None:
        """Update a built-in tool's active status and permission requirement for a workspace.

        Args:
            workspace_builtin_tool_id: ID of the workspace built-in tool to update
            workspace_id: ID of the workspace
            required_permission: Whether the tool requires permission (optional)

        Returns:
            True if update was successful, False if tool not found
        """
        try:
            workspace_builtin_tool = await self.get_workspace_builtin_tool(
                workspace_id, workspace_builtin_tool_id
            )
            if not workspace_builtin_tool:
                raise RepositoryError(
                    status_code=404,
                    message="Workspace builtin tool not found.",
                )

            workspace_builtin_tool.required_permission = required_permission

            self.session.add(workspace_builtin_tool)
            await self.session.commit()
            await self.session.refresh(workspace_builtin_tool)
            # Invalidate caches for this model and warm key lookups
            self._invalidate_model_cache(workspace_builtin_tool.id)
            by_id_key = self._generate_cache_key(
                "get_workspace_builtin_tool",
                workspace_id=str(workspace_id),
                workspace_builtin_tool_id=str(workspace_builtin_tool_id),
            )
            self._set_cache(by_id_key, workspace_builtin_tool.model_dump())
            return True
        except Exception as e:
            logger.exception(f"Error updating workspace builtin tool: {e}")
            await self.session.rollback()
            raise RepositoryError(
                status_code=500,
                message="Failed to update workspace builtin tool.",
            )

    async def init_default_workspace_builtin_tools(self, workspace_id: UUID) -> None:
        """Initialize all builtin tools for a workspace.

        Args:
            workspace_id: ID of the workspace to initialize tools for
        """
        try:
            # Get all available builtin tools
            all_builtin_tools_result = await self.session.exec(select(BuiltInTool))
            all_builtin_tools = list(all_builtin_tools_result.all())

            if not all_builtin_tools:
                logger.info("No builtin tools found in system")
                return

            # Get existing workspace builtin tools
            existing_tools_result = await self.session.exec(
                select(WorkspaceBuiltInTool).where(
                    WorkspaceBuiltInTool.workspace_id == workspace_id
                )
            )
            existing_tools = list(existing_tools_result.all())
            existing_tool_ids = {tool.builtin_tool_id for tool in existing_tools}

            # Create missing workspace builtin tools
            new_workspace_tools = []
            for builtin_tool in all_builtin_tools:
                if builtin_tool.id not in existing_tool_ids:
                    workspace_tool = WorkspaceBuiltInTool(
                        workspace_id=workspace_id,
                        builtin_tool_id=builtin_tool.id,
                        required_permission=builtin_tool.default_required_permission,
                    )
                    new_workspace_tools.append(workspace_tool)
                    self.session.add(workspace_tool)

            # Commit new tools
            if new_workspace_tools:
                await self.session.commit()
                # Invalidate caches impacted by this initialization
                self._invalidate_model_cache()

        except Exception as e:
            logger.exception(f"Failed to initialize workspace builtin tools: {e}")
            await self.session.rollback()
            raise RepositoryError(
                status_code=500,
                message="Failed to initialize workspace builtin tools.",
            )
