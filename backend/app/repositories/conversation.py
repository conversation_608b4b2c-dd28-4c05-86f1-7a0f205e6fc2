from uuid import UUI<PERSON>

from fastapi import status
from sqlalchemy import func
from sqlalchemy.orm import selectinload
from sqlmodel import Session, col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.conversation_exceptions import (
    ConversationAccessDenied,
    ConversationNotFound,
    ConversationRepositoryError,
    ConversationShareNotFound,
)
from app.logger import logger
from app.models import (
    Agent,
    Conversation,
    Message,
    MessageAttachmentPublic,
    MessageCheckpoint,
    MessageComponent,
    MessageComponentType,
    MessageDisplayComponentPublic,
    MessagePublic,
    MessagePublicList,
    MessageThinkingComponentPublic,
    MessageToolComponentPublic,
)
from app.models.dashboards import Dashboard
from app.models.reports import Report
from app.repositories.base import BaseRepository


class ConversationRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(
        self, async_session: AsyncSession | None = None, session: Session | None = None
    ):
        super().__init__(Conversation)
        self.async_session = async_session
        self.session = session

    def _require_async_session(self) -> AsyncSession:
        if self.async_session is None:
            raise ConversationRepositoryError(detail="Async session not initialized")
        return self.async_session

    def _require_session(self) -> Session:
        if self.session is None:
            raise ConversationRepositoryError(detail="Session not initialized")
        return self.session

    async def check_conversation_permission(
        self,
        agent_id: UUID,
        workspace_id: UUID,
    ) -> bool:
        """Check if user has permission to create conversation in workspace given agent_id and workspace_id."""
        async_session = self._require_async_session()
        agent = await async_session.get(Agent, agent_id)
        if agent is None or agent.workspace_id != workspace_id:
            raise ConversationAccessDenied(
                conversation_id=str(agent_id), user_id=str(workspace_id)
            )

        return True

    def get_conversation(self, conversation_id: UUID) -> Conversation | None:
        session = self._require_session()
        # Always return a session-bound instance to avoid transient objects
        obj = session.get(Conversation, conversation_id)
        if obj:
            cache_key = self._generate_cache_key(
                "get_conversation", conversation_id=str(conversation_id)
            )
            self._set_cache(cache_key, obj.model_dump())
        return obj

    async def async_get_conversation(self, conversation_id: UUID) -> Conversation:
        async_session = self._require_async_session()
        # Always return a session-bound instance to avoid transient objects
        query = (
            select(Conversation)
            .where(Conversation.id == conversation_id)
            .options(
                selectinload(Conversation.agent),  # type: ignore[arg-type]
                selectinload(Conversation.resource),  # type: ignore[arg-type]
            )
        )
        result = await async_session.exec(query)
        conv = result.first()
        if not conv:
            raise ConversationNotFound(conversation_id=str(conversation_id))
        cache_key = self._generate_cache_key(
            "async_get_conversation", conversation_id=str(conversation_id)
        )
        self._set_cache(cache_key, conv.model_dump())
        return conv

    async def get_conversations(
        self,
        agent_id: UUID,
        workspace_id: UUID,
        resource_id: UUID | None = None,
        search: str | None = None,
        skip: int | None = None,
        limit: int | None = None,
    ) -> tuple[list[Conversation], int]:
        """Get list of conversations with filtering and pagination."""
        try:
            await self.check_conversation_permission(agent_id, workspace_id)

            query = (
                select(Conversation)
                .where(Conversation.is_deleted == False)
                .where(Conversation.agent_id == agent_id)
            )

            if resource_id:
                query = query.where(Conversation.resource_id == resource_id)

            if search:
                query = query.where(col(Conversation.name).ilike(f"%{search}%"))

            count_query = select(func.count()).select_from(query.subquery())
            async_session = self._require_async_session()
            total_count = await async_session.exec(count_query)
            total_count = total_count.first()

            query = query.order_by(col(Conversation.created_at).desc())
            if skip is not None:
                query = query.offset(skip)
            if limit is not None:
                query = query.limit(limit)
            if resource_id:
                query = query.options(selectinload(Conversation.resource))  # type: ignore[arg-type]

            result = await async_session.exec(query)
            conversations = result.all()

            return list(conversations), int(total_count or 0)
        except Exception as e:
            logger.exception(f"Failed to get conversations: {e}")
            raise ConversationRepositoryError(detail="Failed to get conversations")

    async def get_conversation_history(
        self,
        conversation_id: UUID,
        workspace_id: UUID,
    ) -> MessagePublicList:
        """Get conversation history with pagination."""
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=str(conversation_id))

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            # Optimized query with batch loading for better performance
            query = (
                select(Message)
                .where(Message.conversation_id == conversation_id)
                .where(Message.is_deleted == False)
                .options(
                    selectinload(Message.components).selectinload(  # type: ignore[arg-type]
                        MessageComponent.tool_component  # type: ignore[arg-type]
                    ),
                    selectinload(Message.components).selectinload(  # type: ignore[arg-type]
                        MessageComponent.display_component  # type: ignore[arg-type]
                    ),
                    selectinload(Message.components).selectinload(  # type: ignore[arg-type]
                        MessageComponent.thinking_component  # type: ignore[arg-type]
                    ),
                    selectinload(Message.attachments),  # type: ignore[arg-type]
                )
            )

            count_query = select(func.count()).select_from(
                select(Message)
                .where(
                    Message.conversation_id == conversation_id,
                    Message.is_deleted == False,
                )
                .subquery()
            )
            async_session = self._require_async_session()
            total_count = await async_session.exec(count_query)
            total_count = total_count.first()

            query = query.order_by(col(Message.created_at).asc())

            result = await async_session.exec(query)
            messages = result.all()

            # Convert Message objects to MessagePublic schema
            message_publics = []
            for message in messages:
                tool_calls = []
                display_components = []
                thinking_components = []
                if message.components:
                    for component in sorted(
                        message.components, key=lambda x: x.position
                    ):
                        if (
                            component.tool_component
                            and component.type == MessageComponentType.TOOL
                        ):
                            tool_component = component.tool_component
                            tool_component = MessageToolComponentPublic(
                                **tool_component.model_dump(),
                                position=component.position,
                            )
                            tool_calls.append(tool_component)
                        elif (
                            component.display_component
                            and component.type == MessageComponentType.DISPLAY
                        ):
                            display_component = MessageDisplayComponentPublic(
                                **component.display_component.model_dump(),
                                position=component.position,
                            )
                            display_components.append(display_component)
                        elif (
                            component.thinking_component
                            and component.type == MessageComponentType.THINKING
                        ):
                            thinking_component = component.thinking_component
                            thinking_component = MessageThinkingComponentPublic(
                                **thinking_component.model_dump(),
                                position=component.position,
                            )
                            thinking_components.append(thinking_component)

                attachments = []
                if message.attachments:
                    for att in message.attachments:
                        attachments.append(MessageAttachmentPublic(**att.model_dump()))

                message_public = MessagePublic(
                    id=message.id,
                    content=message.content,
                    role=message.role,
                    is_interrupt=message.is_interrupt,
                    interrupt_message=message.interrupt_message,
                    interrupt_reasoning=message.interrupt_reasoning,
                    tool_calls=tool_calls,
                    display_components=display_components,
                    thinking_components=thinking_components,
                    attachments=attachments,
                    created_at=message.created_at,
                )
                message_publics.append(message_public)

            # Check for reports and dashboards
            has_report = await self._check_conversation_has_report(conversation_id)
            has_dashboard = await self._check_conversation_has_dashboard(
                conversation_id
            )

            return MessagePublicList(
                messages=message_publics,
                resource_id=conversation.resource_id,
                has_report=has_report,
                has_dashboard=has_dashboard,
                total=total_count or 0,
            )

        except Exception as e:
            logger.exception(f"Failed to get conversation history: {e}")
            raise ConversationRepositoryError(
                detail="Failed to get conversation history",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    async def create_conversation(
        self,
        agent_id: UUID,
        workspace_id: UUID,
        resource_id: UUID | None = None,
    ) -> Conversation:
        """Create a new conversation."""
        try:
            await self.check_conversation_permission(agent_id, workspace_id)

            conversation = Conversation(agent_id=agent_id, resource_id=resource_id)

            async_session = self._require_async_session()
            async_session.add(conversation)
            await async_session.commit()
            await async_session.refresh(conversation)
            # Invalidate caches for Conversation
            self._invalidate_model_cache(conversation.id)
            return conversation
        except Exception as e:
            async_session = self._require_async_session()
            await async_session.rollback()
            logger.exception(f"Failed to create conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to create conversation")

    async def rename_conversation(
        self,
        conversation_id: UUID,
        workspace_id: UUID,
        name: str,
    ) -> Conversation:
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=str(conversation_id))

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            conversation.name = name
            async_session = self._require_async_session()
            await async_session.commit()
            await async_session.refresh(conversation)
            self._invalidate_model_cache(conversation.id)

            return conversation
        except Exception as e:
            async_session = self._require_async_session()
            await async_session.rollback()
            logger.exception(f"Failed to rename conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to rename conversation")

    async def delete_conversation(
        self,
        workspace_id: UUID,
        conversation_id: UUID,
    ) -> None:
        try:
            conversation = await self.async_get_conversation(conversation_id)
            if not conversation:
                raise ConversationNotFound(conversation_id=str(conversation_id))

            await self.check_conversation_permission(
                conversation.agent_id, workspace_id
            )

            conversation.is_deleted = True
            async_session = self._require_async_session()
            await async_session.commit()
            self._invalidate_model_cache(conversation.id)
        except Exception as e:
            async_session = self._require_async_session()
            await async_session.rollback()
            logger.exception(f"Failed to delete conversation: {e}")
            raise ConversationRepositoryError(detail="Failed to delete conversation")

    async def get_conversation_by_share_id(self, share_id: UUID) -> Conversation:
        """Get conversation by share ID."""
        try:
            query = select(Conversation).filter(
                col(Conversation.share_id) == share_id,
                col(Conversation.is_shared) == True,
                col(Conversation.is_deleted) == False,
            )
            # Cache lookup by share_id
            cache_key = self._generate_cache_key(
                "get_conversation_by_share_id", share_id=str(share_id)
            )
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return Conversation(**cached)
                except Exception:
                    pass
            async_session = self._require_async_session()
            result = await async_session.exec(query)
            conversation = result.first()
            if not conversation:
                raise ConversationShareNotFound(share_id=str(share_id))
            self._set_cache(cache_key, conversation.model_dump())
            return conversation
        except Exception as e:
            logger.exception(f"Failed to get conversation by share ID: {e}")
            raise ConversationRepositoryError(
                detail="Failed to get conversation by share ID"
            )

    async def get_last_assistant_message(self, conversation_id: UUID) -> Message | None:
        # Cache by conversation_id
        cache_key = self._generate_cache_key(
            "get_last_assistant_message", conversation_id=str(conversation_id)
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return Message(**cached)
            except Exception:
                pass

        query = (
            select(Message)
            .where(Message.conversation_id == conversation_id)
            .where(Message.is_deleted == False)
            .where(Message.role != "user")
            .order_by(col(Message.created_at).desc())
            .limit(1)
        )
        async_session = self._require_async_session()
        result = await async_session.exec(query)
        msg = result.first()
        if msg:
            self._set_cache(cache_key, msg.model_dump())
        return msg

    async def get_checkpoint_by_message_id(
        self, message_id: UUID
    ) -> MessageCheckpoint | None:
        # Cache by message_id
        cache_key = self._generate_cache_key(
            "get_checkpoint_by_message_id", message_id=str(message_id)
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return MessageCheckpoint(**cached)
            except Exception:
                pass

        query = select(MessageCheckpoint).where(
            MessageCheckpoint.message_id == message_id
        )
        async_session = self._require_async_session()
        result = await async_session.exec(query)
        checkpoint = result.first()
        if checkpoint:
            self._set_cache(cache_key, checkpoint.model_dump())
        return checkpoint

    async def get_latest_user_message(self, conversation_id: UUID) -> Message | None:
        # Cache by conversation_id
        cache_key = self._generate_cache_key(
            "get_latest_user_message", conversation_id=str(conversation_id)
        )
        cached = self._get_from_cache(cache_key)
        if cached:
            try:
                return Message(**cached)
            except Exception:
                pass

        query = (
            select(Message)
            .where(Message.is_deleted == False)
            .where(Message.conversation_id == conversation_id)
            .where(Message.role == "user")
            .order_by(col(Message.created_at).desc())
            .limit(1)
        )
        async_session = self._require_async_session()
        result = await async_session.exec(query)
        msg = result.first()
        if msg:
            self._set_cache(cache_key, msg.model_dump())
        return msg

    async def attach_resource_to_conversation(
        self, conversation_id: UUID, resource_id: UUID
    ) -> Conversation:
        """Attach a resource to a conversation if it doesn't already have one."""
        conversation = await self.async_get_conversation(conversation_id)
        if not conversation:
            raise ValueError("Conversation not found")

        # Only attach resource if conversation doesn't already have one
        if conversation.resource_id is None:
            conversation.resource_id = resource_id
            async_session = self._require_async_session()
            await async_session.commit()
            await async_session.refresh(conversation)
            self._invalidate_model_cache(conversation.id)

        return conversation

    async def _check_conversation_has_report(self, conversation_id: UUID) -> bool:
        """Check if there are reports for the given conversation_id."""
        report_query = select(Report).where(Report.conversation_id == conversation_id)
        async_session = self._require_async_session()
        report_result = await async_session.exec(report_query)
        return report_result.first() is not None

    async def _check_conversation_has_dashboard(self, conversation_id: UUID) -> bool:
        """Check if there are dashboards for the given conversation_id."""
        dashboard_query = select(Dashboard).where(
            Dashboard.conversation_id == conversation_id
        )
        async_session = self._require_async_session()
        dashboard_result = await async_session.exec(dashboard_query)
        return dashboard_result.first() is not None
