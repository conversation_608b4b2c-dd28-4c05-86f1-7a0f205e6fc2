from datetime import UTC, datetime, timedelta
from uuid import UUID

from sqlalchemy import desc, func, or_
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.kb_exceptions import (
    InvalidKBOperation,
    KBNotFound,
    KBOwnershipRequired,
)
from app.logger import logger
from app.models import (
    KB,
    AsyncTaskStatus,
    DocumentKB,
    DocumentKBCreate,
    DocumentKBRead,
    DocumentsKBRead,
    DocumentType,
    KBCreate,
    KBsPublic,
    KBUpdate,
    KBUsageMode,
    PaginationMeta,
)
from app.models.knowledge_bases import KBPublic
from app.schemas.kb import DocumentOverviewResponse, KBOverviewResponse


class KBRepository:
    def __init__(self, session: AsyncSession) -> None:
        self.session = session

    async def create(
        self,
        kb_create: KBCreate,
        user_id: UUID,
        workspace_id: UUID,
    ) -> KB:
        try:
            kb_data = kb_create.model_dump()
            kb_data["owner_id"] = user_id
            kb_data["workspace_id"] = workspace_id
            kb_data["allowed_users"] = [user_id]

            kb = KB(**kb_data)
            self.session.add(kb)
            await self.session.commit()
            await self.session.refresh(kb)
            return kb
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error creating KB: {str(e)}")
            raise InvalidKBOperation(f"Failed to create knowledge base: {str(e)}")

    async def get(self, kb_id: UUID, user_id: UUID, workspace_id: UUID) -> KB:
        """
        Get a knowledge base by ID.
        The user must be in the allowed users list or be the owner of the knowledge base.
        """
        try:
            query = select(KB).where(
                KB.id == kb_id,
                KB.is_deleted == False,
                KB.workspace_id == workspace_id,
                func.array_to_string(KB.allowed_users, ",").like(f"%{user_id}%"),
            )
            result = await self.session.exec(query)
            kb = result.one_or_none()
            if not kb:
                raise KBNotFound(str(kb_id))
            return kb
        except KBNotFound:
            raise
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error getting KB {kb_id}: {str(e)}")
            raise InvalidKBOperation(f"Failed to retrieve knowledge base: {str(e)}")

    async def get_kbs(
        self,
        workspace_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 50,
        mode: KBUsageMode | None = None,
        search: str | None = None,
        access_level: str | None = None,
    ) -> KBsPublic:
        try:
            query = select(KB).where(
                # The owner is the first user to be added to the allowed users
                func.array_to_string(KB.allowed_users, ",").like(f"%{user_id}%"),
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
            )

            # Add search functionality
            if search:
                search_condition = or_(
                    func.lower(KB.title).like(f"%{search.lower()}%"),
                    func.lower(KB.description).like(f"%{search.lower()}%"),
                )
                query = query.where(search_condition)

            # Add access level filter
            if access_level:
                query = query.where(KB.access_level == access_level)

            # Add usage mode filter
            if mode:
                query = query.where(KB.usage_mode == mode)

            # Add default ordering by created_at descending (newest first)
            query = query.order_by(desc(col(KB.created_at)))

            count_query = select(func.count()).select_from(query.subquery())
            query = query.offset(skip).limit(limit)

            result = await self.session.exec(query)
            result = result.all()

            count = await self.session.exec(count_query)
            total_items = count.one()

            # Create pagination metadata
            page = (skip // limit) + 1 if limit > 0 else 1
            total_pages = (total_items + limit - 1) // limit if limit > 0 else 1

            pagination_meta = PaginationMeta(
                page=page,
                take=limit,
                total_items=total_items,
                total_pages=total_pages,
                has_previous=page > 1,
                has_next=page < total_pages,
                start_index=skip + 1 if total_items > 0 else 0,
                end_index=min(skip + limit, total_items),
            )

            return KBsPublic(
                data=[KBPublic(**kb.model_dump()) for kb in result],
                meta=pagination_meta,
            )
        except Exception as e:
            logger.exception(f"Error getting knowledge bases: {str(e)}")
            await self.session.rollback()
            raise InvalidKBOperation(f"Failed to retrieve knowledge bases: {str(e)}")

    async def update(
        self,
        kb_id: UUID,
        kb_update: KBUpdate,
        user_id: UUID,
        workspace_id: UUID,
    ) -> KB:
        """
        Update a knowledge base.
        The user must be the owner of the knowledge base.
        """
        try:
            kb = await self.get(kb_id, user_id, workspace_id)
            if kb.owner_id != user_id:
                raise KBOwnershipRequired(str(kb_id))

            # Validate that the user is not removing themselves from allowed_users
            update_data = kb_update.model_dump()
            if (
                "allowed_users" in update_data
                and update_data["allowed_users"] is not None
            ):
                new_allowed_users = update_data["allowed_users"]
                # Check if the current user (owner) is trying to remove themselves
                if user_id not in new_allowed_users:
                    raise InvalidKBOperation(
                        "You cannot remove yourself from the allowed users list"
                    )

            for key, value in update_data.items():
                if value is not None:
                    setattr(kb, key, value)

            await self.session.commit()
            await self.session.refresh(kb)
            return kb
        except (KBNotFound, KBOwnershipRequired, InvalidKBOperation):
            raise
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error updating KB {kb_id}: {str(e)}")
            raise InvalidKBOperation(f"Failed to update knowledge base: {str(e)}")

    async def delete(self, kb_id: UUID, user_id: UUID, workspace_id: UUID) -> bool:
        """
        Delete a knowledge base.
        The user must be the owner of the knowledge base.
        """
        try:
            kb = await self.get(kb_id, user_id, workspace_id)
            if kb.owner_id != user_id:
                raise KBOwnershipRequired(str(kb_id))

            kb.is_deleted = True
            await self.session.commit()
            return True
        except (KBNotFound, KBOwnershipRequired):
            raise
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error deleting knowledge base: {str(e)}")
            raise InvalidKBOperation(f"Failed to delete knowledge base: {str(e)}")

    async def create_documents(
        self, document_creates: list[DocumentKBCreate]
    ) -> list[DocumentKB]:
        try:
            documents = [
                DocumentKB.model_validate(document_create)
                for document_create in document_creates
            ]
            self.session.add_all(documents)
            await self.session.commit()
            for document in documents:
                await self.session.refresh(document)
            return documents
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error creating documents with error: {e}")
            raise InvalidKBOperation("Failed to create documents")

    async def get_document(self, kb_id: UUID, document_id: str) -> DocumentKB | None:
        """
        Get a single document by ID.
        Returns None if document not found or doesn't belong to the specified KB.
        """
        try:
            query = select(DocumentKB).where(
                DocumentKB.id == document_id,
                DocumentKB.kb_id == kb_id,
                DocumentKB.is_deleted == False,
            )
            result = await self.session.exec(query)
            return result.one_or_none()
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error retrieving document {document_id}: {e}")
            raise InvalidKBOperation("Failed to retrieve document")

    async def get_documents(
        self, kb_id: UUID, doc_ids: list[str], batch_size: int = 1000
    ) -> list[DocumentKB]:
        """
        Get multiple documents by IDs with batch processing support.

        Args:
            kb_id: Knowledge base ID
            doc_ids: List of document IDs to retrieve
            batch_size: Maximum number of IDs per database query (default: 1000)

        Returns:
            List of DocumentKB objects

        Raises:
            InvalidKBOperation: If there's an error retrieving documents
        """
        if not doc_ids:
            return []

        try:
            all_documents = []

            # Process document IDs in batches to avoid query size limits
            for i in range(0, len(doc_ids), batch_size):
                batch_doc_ids = doc_ids[i : i + batch_size]

                query = select(DocumentKB).where(
                    DocumentKB.kb_id == kb_id,
                    col(DocumentKB.id).in_(batch_doc_ids),  # type: ignore
                    DocumentKB.is_deleted == False,
                )

                result = await self.session.exec(query)
                batch_documents = result.all()  # type: ignore
                all_documents.extend(batch_documents)

                logger.info(
                    f"Retrieved batch {i // batch_size + 1} of documents: "
                    f"{len(batch_documents)} found for {len(batch_doc_ids)} requested"
                )

            logger.info(
                f"Total documents retrieved: {len(all_documents)} out of {len(doc_ids)} requested"
            )
            return all_documents

        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error retrieving documents with error: {e}")
            raise InvalidKBOperation("Failed to retrieve documents")

    async def update_documents(self, documents: list[DocumentKB]) -> list[DocumentKB]:
        try:
            for document in documents:
                self.session.add(document)
            await self.session.commit()
            for document in documents:
                await self.session.refresh(document)
            return documents
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error updating documents with error: {e}")
            raise InvalidKBOperation("Failed to update documents")

    async def list_documents(
        self,
        kb_id: UUID,
        skip: int = 0,
        limit: int = 50,
        search: str | None = None,
        doc_type: DocumentType | None = None,
    ) -> DocumentsKBRead:
        try:
            # Flat pagination: treat all documents equally regardless of hierarchy
            base_query = (
                select(DocumentKB)
                .where(
                    DocumentKB.kb_id == kb_id,
                    DocumentKB.is_deleted == False,
                )
                .order_by(desc(col(DocumentKB.created_at)))  # Consistent ordering
            )

            # Create count query before applying search filter
            count_query = select(func.count()).select_from(base_query.subquery())

            if search:
                search_condition = func.lower(DocumentKB.name).like(
                    f"%{search.lower()}%"
                )
                base_query = base_query.where(search_condition)
                # Update count query to include search filter
                count_query = select(func.count()).select_from(base_query.subquery())

            if doc_type:
                base_query = base_query.where(DocumentKB.type == doc_type)  # type: ignore
                count_query = select(func.count()).select_from(base_query.subquery())

            # Apply pagination to all documents
            paginated_query = base_query.offset(skip).limit(limit)

            result = await self.session.exec(paginated_query)
            documents = result.all()

            count_result = await self.session.exec(count_query)
            count = count_result.one()

            # Convert to DocumentKBRead and set children to empty list for flat pagination
            documents_read = []
            for doc in documents:
                doc_dict = doc.model_dump()
                doc_dict[
                    "children"
                ] = []  # Set children to empty list for flat pagination
                documents_read.append(DocumentKBRead(**doc_dict))

            # Create pagination metadata
            page = (skip // limit) + 1 if limit > 0 else 1
            total_pages = (count + limit - 1) // limit if limit > 0 else 1

            pagination_meta = PaginationMeta(
                page=page,
                take=limit,
                total_items=count,
                total_pages=total_pages,
                has_previous=page > 1,
                has_next=page < total_pages,
                start_index=skip + 1 if count > 0 else 0,
                end_index=min(skip + limit, count),
            )

            # Debug logging
            logger.info(
                f"KB {kb_id} pagination: skip={skip}, limit={limit}, returned={len(documents_read)}, total_count={count}"
            )

            return DocumentsKBRead(data=documents_read, meta=pagination_meta)
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error listing documents with error: {e}")
            raise InvalidKBOperation("Failed to list documents")

    async def delete_document(self, document_id: str) -> None:
        try:
            statement = select(DocumentKB).where(DocumentKB.id == document_id)
            result = await self.session.exec(statement)
            document = result.one_or_none()
            if document:
                await self.session.delete(document)
                await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error deleting document with error: {e}")
            raise InvalidKBOperation("Failed to delete document")

    async def soft_delete_document(self, document_id: str) -> None:
        try:
            statement = select(DocumentKB).where(DocumentKB.id == document_id)
            result = await self.session.exec(statement)
            document = result.one_or_none()
            if document:
                document.is_deleted = True
                await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error soft deleting document with error: {e}")
            raise InvalidKBOperation("Failed to soft delete document")

    async def update_kb_statistics(self, kb_id: UUID) -> None:
        """
        Update the total_document and total_size fields for a knowledge base
        based on its active documents.
        """
        try:
            # Calculate document count and total size
            query = select(
                func.count(col(DocumentKB.id)).label("doc_count"),
                func.coalesce(func.sum(DocumentKB.file_size), 0).label("total_size"),
            ).where(
                DocumentKB.kb_id == kb_id,
                DocumentKB.is_deleted == False,
            )

            result = await self.session.exec(query)
            stats = result.one()

            # Update the KB with new statistics
            kb_query = select(KB).where(KB.id == kb_id)
            kb_result = await self.session.exec(kb_query)
            kb = kb_result.one_or_none()

            if kb:
                kb.total_document = int(stats[0])  # doc_count
                kb.total_size = int(stats[1])  # total_size
                await self.session.commit()
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error updating KB statistics for {kb_id}: {str(e)}")
            raise InvalidKBOperation(f"Failed to update KB statistics: {str(e)}")

    async def get_kb_overview(self, workspace_id: UUID) -> KBOverviewResponse:
        """
        Get overview statistics for all knowledge bases in a workspace.
        """
        try:
            # Get KB statistics
            kb_query = select(
                func.count(col(KB.id)).label("total_kbs"),
                func.coalesce(func.sum(KB.total_document), 0).label("total_docs"),
                func.coalesce(func.sum(KB.total_size), 0).label("total_size"),
                func.count().filter(col(KB.total_document) > 0).label("active_kbs"),
            ).where(
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
            )

            result = await self.session.exec(kb_query)
            stats = result.one()

            return KBOverviewResponse(
                total_knowledge_bases=int(stats[0]),  # total_kbs
                total_documents=int(stats[1]),  # total_docs
                total_size_bytes=int(stats[2]),  # total_size
                active_kbs_count=int(stats[3]),  # active_kbs
            )
        except Exception as e:
            await self.session.rollback()
            logger.exception(
                f"Error getting KB overview for workspace {workspace_id}: {str(e)}"
            )
            raise InvalidKBOperation(f"Failed to get KB overview: {str(e)}")

    async def get_document_overview(
        self, workspace_id: UUID, kb_id: UUID | None = None
    ) -> DocumentOverviewResponse:
        """
        Get overview statistics for documents in a workspace, optionally filtered by KB.
        """
        try:
            # Build base where conditions
            base_conditions = [
                KB.workspace_id == workspace_id,
                KB.is_deleted == False,
                DocumentKB.is_deleted == False,
            ]

            # Filter by specific KB if provided
            if kb_id:
                base_conditions.append(DocumentKB.kb_id == kb_id)

            # Total processed documents (COMPLETED status)
            processed_query = (
                select(func.count(col(DocumentKB.id)))
                .join(KB, col(DocumentKB.kb_id) == col(KB.id))
                .where(
                    *base_conditions,
                    DocumentKB.embed_status == AsyncTaskStatus.COMPLETED,
                )
            )

            # Total documents
            total_query = (
                select(func.count(col(DocumentKB.id)))
                .join(KB, col(DocumentKB.kb_id) == col(KB.id))
                .where(*base_conditions)
            )

            # Documents by type
            type_query = (
                select(
                    DocumentKB.type,  # type: ignore
                    func.count(col(DocumentKB.id)).label("count"),
                )
                .join(KB, col(DocumentKB.kb_id) == col(KB.id))
                .where(*base_conditions)
                .group_by(DocumentKB.type)  # type: ignore
            )

            # Recent activity (last 7 days)
            seven_days_ago = datetime.now(UTC) - timedelta(days=7)
            recent_query = (
                select(func.count(col(DocumentKB.id)))
                .join(KB, col(DocumentKB.kb_id) == col(KB.id))
                .where(*base_conditions, DocumentKB.created_at >= seven_days_ago)
            )

            # Execute queries
            processed_result = await self.session.exec(processed_query)
            total_result = await self.session.exec(total_query)
            type_result = await self.session.exec(type_query)
            recent_result = await self.session.exec(recent_query)

            processed_count = processed_result.one()
            total_count = total_result.one()
            type_counts = type_result.all()
            recent_count = recent_result.one()

            # Calculate success rate
            success_rate = (
                (processed_count / total_count * 100) if total_count > 0 else 0.0
            )

            # Build type breakdown
            documents_by_type = {}
            for doc_type, count in type_counts:
                documents_by_type[doc_type] = int(count)

            return DocumentOverviewResponse(
                total_documents_processed=int(processed_count),
                processing_success_rate=round(success_rate, 2),
                documents_by_type=documents_by_type,
                recent_activity_count=int(recent_count),
            )
        except Exception as e:
            await self.session.rollback()
            logger.exception(
                f"Error getting document overview for workspace {workspace_id}: {str(e)}"
            )
            raise InvalidKBOperation(f"Failed to get document overview: {str(e)}")
