from uuid import UUID

from sqlmodel import Session

from app.exceptions.message_component_exceptions import MessageComponentError
from app.logger import logger
from app.models.messages import (
    ChartType,
    MessageComponent,
    MessageComponentType,
    MessageDisplayComponent,
    MessageDisplayComponentType,
    MessageThinkingComponent,
    MessageToolComponent,
)
from app.repositories.base import BaseRepository


class MessageComponentRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(self, session: Session):
        super().__init__(MessageComponent)
        self.session = session

    def create_message_component(
        self,
        message_id: UUID,
        component_type: MessageComponentType,
        position: int = 0,
    ) -> MessageComponent:
        """Create a basic message component"""
        try:
            component = MessageComponent(
                message_id=message_id,
                type=component_type,
                position=position,
            )
            self.session.add(component)
            self.session.commit()
            self.session.refresh(component)
            # Invalidate caches for message components
            self._invalidate_model_cache(component.id)
            return component
        except Exception as e:
            error_message = "Failed to create message component"
            logger.exception(f"{error_message}: {e}")
            self.session.rollback()
            raise MessageComponentError(detail=error_message, status_code=500)

    def create_message_component_with_tool(
        self,
        message_id: UUID,
        position: int = 0,
        tool_name: str | None = None,
        tool_input: dict | None = None,
        tool_output: str | None = None,
        tool_reasoning: str | None = None,
        tool_runtime: float = 0.0,
    ) -> MessageComponent:
        """Create a message component with associated tool component"""
        try:
            # Create the main message component
            component = MessageComponent(
                message_id=message_id,
                type=MessageComponentType.TOOL,
                position=position,
            )
            self.session.add(component)
            self.session.flush()  # Flush to get the component ID

            # Create the tool component
            tool_component = MessageToolComponent(
                message_component_id=component.id,
                tool_name=tool_name,
                tool_input=tool_input,
                tool_output=tool_output,
                tool_reasoning=tool_reasoning,
                tool_runtime=tool_runtime,
                is_completed=False,
            )
            self.session.add(tool_component)
            self.session.flush()  # Flush to get the tool component ID
            self.session.add(component)

            self.session.commit()
            self.session.refresh(component)
            # Invalidate caches for message components
            self._invalidate_model_cache(component.id)
            return component
        except Exception as e:
            error_message = "Failed to create message component with tool"
            logger.exception(f"{error_message}: {e}")
            self.session.rollback()
            raise MessageComponentError(detail=error_message, status_code=500)

    def create_message_component_with_thinking(
        self,
        message_id: UUID,
        position: int = 0,
        content: str | None = None,
        thinking_type: str = "tool_brief",
    ) -> MessageComponent:
        """Create a message component with associated thinking component"""
        try:
            # Create the main message component
            component = MessageComponent(
                message_id=message_id,
                type=MessageComponentType.THINKING,
                position=position,
            )
            self.session.add(component)
            self.session.flush()  # Flush to get the component ID

            # Create the thinking component
            thinking_component = MessageThinkingComponent(
                message_component_id=component.id,
                content=content or "",
                thinking_type=thinking_type,
            )
            self.session.add(thinking_component)
            self.session.flush()  # Flush to get the thinking component ID

            self.session.commit()
            self.session.refresh(component)
            # Invalidate caches for message components
            self._invalidate_model_cache(component.id)
            return component
        except Exception as e:
            error_message = "Failed to create message component with thinking"
            logger.exception(f"{error_message}: {e}")
            self.session.rollback()
            raise MessageComponentError(detail=error_message, status_code=500)

    def create_message_component_with_display(
        self,
        message_id: UUID,
        position: int,
        display_type: MessageDisplayComponentType,
        data: dict,
        title: str,
        description: str,
        chart_type: ChartType | None = None,
    ) -> MessageComponent:
        """Create a message component with associated display component"""
        try:
            # Create the main message component
            component = MessageComponent(
                message_id=message_id,
                type=MessageComponentType.DISPLAY,
                position=position,
            )
            self.session.add(component)
            self.session.flush()  # Flush to get the component ID

            # Create the display component
            display_component = MessageDisplayComponent(
                message_component_id=component.id,
                type=display_type,
                chart_type=chart_type,
                title=title,
                description=description,
                data=data or {},
            )
            self.session.add(display_component)
            self.session.flush()  # Flush to get the display component ID
            self.session.add(component)

            self.session.commit()
            self.session.refresh(component)
            # Invalidate caches for message components
            self._invalidate_model_cache(component.id)
            return component
        except Exception as e:
            error_message = "Failed to create message component with display"
            logger.exception(f"{error_message}: {e}")
            self.session.rollback()
            raise MessageComponentError(detail=error_message, status_code=500)

    def update_tool_component(
        self,
        message_component_id: UUID,
        tool_output: str,
        tool_reasoning: str,
        tool_runtime: float,
    ) -> MessageComponent:
        """Update a tool component"""
        try:
            # Get the component and associated tool component
            component = self.session.get(MessageComponent, message_component_id)
            if not component or not component.tool_component:
                raise MessageComponentError(
                    detail=f"Tool component {message_component_id} not found",
                    status_code=404,
                )

            # Update the tool component fields
            component.tool_component.tool_output = tool_output
            component.tool_component.tool_reasoning = tool_reasoning
            component.tool_component.tool_runtime = tool_runtime
            component.tool_component.is_completed = True
            self.session.add(component)
            self.session.commit()
            self.session.refresh(component)
            # Invalidate caches for message components
            self._invalidate_model_cache(component.id)
            return component
        except Exception as e:
            error_message = "Failed to update tool component"
            logger.exception(f"{error_message}: {e}")
            self.session.rollback()
            raise MessageComponentError(detail=error_message, status_code=500)
