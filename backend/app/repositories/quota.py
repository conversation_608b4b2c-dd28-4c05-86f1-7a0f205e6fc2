from datetime import UTC, datetime
from typing import Any
from uuid import UUID

from sqlalchemy import text
from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.exceptions.quota_exceptions import InsufficientQuotaError, QuotaUpdateError
from app.logger import logger
from app.models import UsageQuota
from app.repositories.base import BaseRepository


class QuotaRepository(BaseRepository):
    CACHE_ENABLED = False
    CACHE_TTL = 300

    def __init__(self, async_session: AsyncSession):
        super().__init__(UsageQuota)
        self.async_session = async_session

    async def get_user_quota(self, user_id: UUID) -> UsageQuota | None:
        """
        Get usage quota for a user.

        Args:
            user_id: ID of the user

        Returns:
            UsageQuota object for the user or None if not found
        """
        try:
            cache_key = self._generate_cache_key("get_user_quota", user_id=str(user_id))
            cached = self._get_from_cache(cache_key)
            if cached:
                try:
                    return UsageQuota.model_validate(cached)
                except Exception:
                    pass

            stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
            result = await self.async_session.exec(stmt)
            quota = result.first()

            if quota:
                self._set_cache(cache_key, quota.model_dump())

            return quota

        except Exception as e:
            logger.error(f"Error getting quota for user {user_id}: {str(e)}")
            raise RepositoryError(
                f"Error getting quota for user {user_id}", status_code=500
            )

    async def create_user_quota(self, user_id: UUID) -> UsageQuota:
        """
        Create a new UsageQuota record for a user.

        Args:
            user_id: ID of the user

        Returns:
            UsageQuota object for the user

        Raises:
            RepositoryError: If quota creation fails
        """
        try:
            # Check if quota already exists
            existing_quota = await self.get_user_quota(user_id)
            if existing_quota:
                logger.warning(f"UsageQuota already exists for user {user_id}")
                return existing_quota

            # Create new quota with default values
            new_quota = UsageQuota(
                user_id=user_id,
                credit_used=0,
                quota_used_tokens=0,
                daily_credit_used=0,
                workspaces_used=0,
                members_used=0,
                scheduled_tasks_used=0,
                kb_storage_used=0.0,
            )

            self.async_session.add(new_quota)
            await self.async_session.commit()
            await self.async_session.refresh(new_quota)

            # Invalidate cache and warm with new data
            self._invalidate_model_cache(user_id)
            cache_key = self._generate_cache_key("get_user_quota", user_id=str(user_id))
            self._set_cache(cache_key, new_quota.model_dump())

            logger.info(f"Created UsageQuota record for user {user_id}")
            return new_quota

        except Exception as e:
            await self.async_session.rollback()
            logger.error(f"Failed to create quota for user {user_id}: {str(e)}")
            raise RepositoryError(
                f"Failed to create user quota: {str(e)}", status_code=500
            )

    async def update_quota_usage(
        self, user_id: UUID, updates: dict[str, Any]
    ) -> UsageQuota:
        """
        Update quota usage fields for a user.

        Args:
            user_id: ID of the user
            updates: Dictionary of field updates

        Returns:
            Updated UsageQuota object

        Raises:
            RepositoryError: If quota update fails
        """
        try:
            quota = await self.get_user_quota(user_id)
            if not quota:
                raise RepositoryError(
                    f"UsageQuota record not found for user {user_id}", status_code=404
                )

            # Apply updates
            for field, value in updates.items():
                if hasattr(quota, field):
                    setattr(quota, field, value)
                else:
                    logger.warning(f"Unknown quota field: {field}")

            await self.async_session.commit()
            await self.async_session.refresh(quota)

            # Invalidate and warm cache
            self._invalidate_model_cache(user_id)
            cache_key = self._generate_cache_key("get_user_quota", user_id=str(user_id))
            self._set_cache(cache_key, quota.model_dump())

            return quota

        except Exception as e:
            await self.async_session.rollback()
            logger.error(f"Failed to update quota for user {user_id}: {str(e)}")
            raise RepositoryError(
                f"Failed to update user quota: {str(e)}", status_code=500
            )

    async def deduct_quota_atomic(
        self, user_id: UUID, resource_type: str, amount: int, limit: int
    ) -> UsageQuota:
        """
        Atomically deduct quota with proper database locking.

        This method uses SELECT FOR UPDATE to ensure atomic quota deduction
        and prevent race conditions when multiple requests try to deduct
        quota simultaneously.

        Args:
            user_id: ID of the user
            resource_type: Type of resource ('messages', 'tokens', 'daily_messages')
            amount: Amount to deduct
            limit: Maximum allowed limit for this resource

        Returns:
            Updated UsageQuota object

        Raises:
            InsufficientQuotaError: If user doesn't have enough quota
            QuotaUpdateError: If quota update fails
        """
        try:
            # Use SELECT FOR UPDATE for atomic operation
            stmt = (
                select(UsageQuota)
                .where(UsageQuota.user_id == user_id)
                .with_for_update()
            )
            result = await self.async_session.exec(stmt)
            quota = result.first()

            if not quota:
                raise QuotaUpdateError(
                    f"UsageQuota record not found for user {user_id}"
                )

            # Get current usage based on resource type and check quota BEFORE increment
            if resource_type == "messages":
                current_used = quota.credit_used
                if current_used + amount > limit:
                    raise InsufficientQuotaError(
                        f"Insufficient {resource_type} quota. Used: {current_used}, "
                        f"Requested: {amount}, Available: {limit - current_used}"
                    )
                quota.credit_used = current_used + amount

            elif resource_type == "tokens":
                current_used = quota.quota_used_tokens
                if current_used + amount > limit:
                    raise InsufficientQuotaError(
                        f"Insufficient {resource_type} quota. Used: {current_used}, "
                        f"Requested: {amount}, Available: {limit - current_used}"
                    )
                quota.quota_used_tokens = current_used + amount

            elif resource_type == "daily_messages":
                current_used = quota.daily_credit_used
                if current_used + amount > limit:
                    raise InsufficientQuotaError(
                        f"Insufficient {resource_type} quota. Used: {current_used}, "
                        f"Requested: {amount}, Available: {limit - current_used}"
                    )
                # Log current values before update to debug constraint violation
                logger.info(
                    f"Before daily_messages update for user {user_id}: "
                    f"daily_credit_used={current_used}, credit_used={quota.credit_used}, "
                    f"new_daily_total={current_used + amount}, limit={limit}"
                )
                quota.daily_credit_used = current_used + amount

            else:
                raise QuotaUpdateError(f"Unsupported resource type: {resource_type}")

            # Update timestamp
            quota.updated_at = datetime.now(UTC)

            await self.async_session.commit()
            await self.async_session.refresh(quota)

            # Invalidate cache
            self._invalidate_model_cache(user_id)
            cache_key = self._generate_cache_key("get_user_quota", user_id=str(user_id))
            self._set_cache(cache_key, quota.model_dump())

            logger.info(
                f"Successfully deducted {amount} {resource_type} from user {user_id}"
            )
            return quota

        except InsufficientQuotaError:
            # Re-raise quota errors without wrapping
            raise
        except Exception as e:
            await self.async_session.rollback()
            logger.error(
                f"Failed to atomically deduct quota for user {user_id}: {str(e)}"
            )
            raise QuotaUpdateError(f"Failed to deduct quota: {str(e)}")

    async def check_quota_availability(
        self, user_id: UUID, resource_type: str, amount: int, limit: int
    ) -> bool:
        """
        Check if user has sufficient quota available without deducting.

        Args:
            user_id: ID of the user
            resource_type: Type of resource ('messages', 'tokens', 'daily_messages')
            amount: Amount to check
            limit: Maximum allowed limit for this resource

        Returns:
            True if user has sufficient quota, False otherwise
        """
        try:
            quota = await self.get_user_quota(user_id)
            if not quota:
                return False

            if resource_type == "messages":
                return quota.credit_used + amount <= limit

            elif resource_type == "tokens":
                return quota.quota_used_tokens + amount <= limit

            elif resource_type == "daily_messages":
                return quota.daily_credit_used + amount <= limit

            return False

        except Exception as e:
            logger.error(
                f"Failed to check quota availability for user {user_id}: {str(e)}"
            )
            return False

    async def check_resource_availability(
        self, user_id: UUID, resource_type: str, amount: float, limit: float
    ) -> bool:
        """
        Check if user has sufficient resource quota available without deducting.

        Args:
            user_id: ID of the user
            resource_type: Type of resource ('workspaces', 'members', 'scheduled_tasks', 'kb_storage')
            amount: Amount to check
            limit: Maximum allowed limit for this resource

        Returns:
            True if user has sufficient quota, False otherwise
        """
        try:
            quota = await self.get_user_quota(user_id)
            if not quota:
                return False

            # Map resource types to fields
            resource_config = {
                "workspaces": "workspaces_used",
                "members": "members_used",
                "scheduled_tasks": "scheduled_tasks_used",
                "kb_storage": "kb_storage_used",
            }

            if resource_type not in resource_config:
                return False

            field_name, default_limit = resource_config[resource_type]
            current_used = getattr(quota, field_name)

            return current_used + amount <= limit

        except Exception as e:
            logger.error(
                f"Failed to check resource availability for user {user_id}: {str(e)}"
            )
            return False

    async def reset_user_quota_timestamps(
        self, user_id: UUID, reset_at: datetime
    ) -> UsageQuota:
        """
        Reset quota timestamps for a user.

        Args:
            user_id: ID of the user
            reset_at: Reset timestamp

        Returns:
            Updated UsageQuota object

        Raises:
            RepositoryError: If quota reset fails
        """
        try:
            updates = {
                "credit_used": 0,
                "quota_used_tokens": 0,
                "reset_at": reset_at,
            }
            return await self.update_quota_usage(user_id, updates)

        except Exception as e:
            logger.error(
                f"Failed to reset quota timestamps for user {user_id}: {str(e)}"
            )
            raise RepositoryError(
                f"Failed to reset user quota timestamps: {str(e)}", status_code=500
            )

    async def batch_reset_daily_quotas(self) -> int:
        """
        Reset daily message quota for all users using batch operation.

        Returns:
            Number of users whose quotas were reset

        Raises:
            RepositoryError: If batch reset fails
        """
        try:
            current_time = datetime.now(UTC)

            stmt = text(
                "UPDATE usage_quota SET daily_credit_used = 0, daily_credits_reset_at = :reset_time"
            )
            result = await self.async_session.execute(
                stmt, {"reset_time": current_time}
            )
            await self.async_session.commit()

            reset_count = getattr(result, "rowcount", 0)

            # Invalidate all quota caches since we did a batch update
            self._delete_cache_pattern("usagequota:*")

            logger.info(f"Reset daily quotas for {reset_count} users at {current_time}")
            return reset_count

        except Exception as e:
            await self.async_session.rollback()
            logger.error(f"Failed to batch reset daily quotas: {str(e)}")
            raise RepositoryError(
                f"Failed to batch reset daily quotas: {str(e)}", status_code=500
            )

    async def get_or_create_user_quota(self, user_id: UUID) -> UsageQuota:
        """
        Get existing quota or create a new one if it doesn't exist.

        Args:
            user_id: ID of the user

        Returns:
            UsageQuota object for the user

        Raises:
            RepositoryError: If operation fails
        """
        try:
            quota = await self.get_user_quota(user_id)
            if not quota:
                quota = await self.create_user_quota(user_id)
            return quota

        except Exception as e:
            logger.error(f"Failed to get or create quota for user {user_id}: {str(e)}")
            raise RepositoryError(
                f"Failed to get or create user quota: {str(e)}", status_code=500
            )

    async def deduct_resource_atomic(
        self, user_id: UUID, resource_type: str, amount: float, limit: float
    ) -> UsageQuota:
        """
        Atomically deduct resource quota with proper database locking.

        This method uses SELECT FOR UPDATE to ensure atomic resource deduction
        and prevent race conditions when multiple requests try to deduct
        resource quota simultaneously.

        Args:
            user_id: ID of the user
            resource_type: Type of resource ('workspaces', 'members', 'scheduled_tasks', 'kb_storage')
            amount: Amount to deduct
            limit: Maximum allowed limit for this resource

        Returns:
            Updated UsageQuota object

        Raises:
            InsufficientQuotaError: If user doesn't have enough quota
            QuotaUpdateError: If deduction fails
        """
        try:
            # Use SELECT FOR UPDATE for atomic operation
            stmt = (
                select(UsageQuota)
                .where(UsageQuota.user_id == user_id)
                .with_for_update()
            )
            result = await self.async_session.exec(stmt)
            quota = result.first()

            if not quota:
                raise QuotaUpdateError(
                    f"UsageQuota record not found for user {user_id}"
                )

            # Map resource types to fields and default limits
            resource_config = {
                "workspaces": ("workspaces_used", 1),
                "members": ("members_used", 1),
                "scheduled_tasks": ("scheduled_tasks_used", 0),
                "kb_storage": ("kb_storage_used", 0.0),
            }

            if resource_type not in resource_config:
                raise QuotaUpdateError(f"Unsupported resource type: {resource_type}")

            field_name, default_limit = resource_config[resource_type]
            current_used = getattr(quota, field_name)

            # Check if user has sufficient quota BEFORE increment
            if current_used + amount > limit:
                raise InsufficientQuotaError(
                    f"Insufficient {resource_type} quota. "
                    f"Used: {current_used}, Requested: {amount}, Limit: {limit}"
                )

            # Deduct the resource
            setattr(quota, field_name, current_used + amount)

            # Update timestamp
            quota.updated_at = datetime.now(UTC)

            await self.async_session.commit()
            await self.async_session.refresh(quota)

            # Invalidate cache
            self._invalidate_model_cache(user_id)
            cache_key = self._generate_cache_key("get_user_quota", user_id=str(user_id))
            self._set_cache(cache_key, quota.model_dump())

            logger.info(
                f"Successfully deducted {amount} {resource_type} from user {user_id}"
            )
            return quota

        except InsufficientQuotaError:
            # Re-raise quota errors without wrapping
            raise
        except Exception as e:
            await self.async_session.rollback()
            logger.error(
                f"Failed to atomically deduct resource for user {user_id}: {str(e)}"
            )
            raise QuotaUpdateError(f"Failed to deduct resource: {str(e)}")
