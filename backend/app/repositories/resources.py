import uuid
from datetime import UTC, datetime

from sqlmodel import Session, col, desc, func, nullslast, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import RepositoryError
from app.logger import logger
from app.models import (
    PaginationMeta,
    Recommendation,
    RecommendationStatus,
    Resource,
    ResourceDetail,
    ResourcePublic,
    ResourceSavingsInfo,
    ResourcesPublic,
    ResourceStatistics,
    ResourceStatusCount,
    ResourceTypeCount,
)
from app.repositories.base import BaseRepository


class ResourceRepository(BaseRepository):
    CACHE_ENABLED = True
    CACHE_TTL = 300

    def __init__(
        self,
        session: AsyncSession,
        cache_enabled: bool | None = None,
    ):
        super().__init__(Resource, cache_enabled=cache_enabled)
        self.session = session

    async def delete_by_id_and_workspace(
        self, resource_id: str | uuid.UUID, workspace_id: uuid.UUID
    ) -> bool:
        """Delete a specific resource by ID within a workspace.

        Returns True if deleted, False if not found or workspace mismatch.
        """
        try:
            # Verify the resource exists and belongs to the workspace
            resource_query = select(Resource).where(
                (Resource.id == resource_id) & (Resource.workspace_id == workspace_id)
            )
            resource_result = await self.session.exec(resource_query)
            resource = resource_result.first()

            if not resource:
                return False

            await self.session.delete(resource)
            await self.session.commit()

            # Invalidate related caches for this workspace
            await self.invalidate_cache(workspace_id)
            return True
        except Exception as e:
            await self.session.rollback()
            logger.exception(f"Error deleting resource by ID {resource_id}: {e}")
            raise RepositoryError(
                f"Unable to delete resource: {str(e)}", status_code=500
            )

    async def get_resources_with_pagination(
        self,
        workspace_id: uuid.UUID,
        skip: int = 0,
        limit: int = 10,
        name: str | None = None,
        resource_type: list[str] | None = None,
        status: list[str] | None = None,
        region: list[str] | None = None,
        category: list[str] | None = None,
    ) -> ResourcesPublic:
        """Get resources with pagination and caching"""
        try:
            # Try to get from cache first
            cache_key = self._generate_cache_key(
                "get_resources_with_pagination",
                workspace_id=str(workspace_id),
                skip=skip,
                limit=limit,
                name=name,
                resource_type=resource_type or [],
                status=status or [],
                region=region or [],
                category=category or [],
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"Cache hit for resources query: {cache_key}")
                return ResourcesPublic(**cached_result)

            # Cache miss, query database
            logger.info(f"Cache miss for resources query: {cache_key}")

            # Use the same query pattern as the original working code
            # Subquery to calculate total potential savings for each resource
            subq = (
                select(
                    col(Recommendation.resource_id).label("resource_id"),
                    func.coalesce(
                        func.sum(col(Recommendation.potential_savings)),  # type: ignore[attr-defined]
                        0,
                    ).label("total_savings"),
                )
                .join(Resource, col(Resource.id) == Recommendation.resource_id)
                .where(
                    (Recommendation.status == RecommendationStatus.PENDING)
                    & (Resource.workspace_id == workspace_id)
                )
                .group_by(col(Recommendation.resource_id))
                .subquery()
            )

            # Base query
            base_query = (
                select(Resource.id, subq.c.total_savings)
                .join(subq, col(Resource.id) == subq.c.resource_id, isouter=True)
                .where(Resource.workspace_id == workspace_id)
            )

            # Apply filters
            if name:
                base_query = base_query.where(col(Resource.name).ilike(f"%{name}%"))
            if resource_type:
                base_query = base_query.where(col(Resource.type).in_(resource_type))
            if status:
                base_query = base_query.where(col(Resource.status).in_(status))
            if region:
                base_query = base_query.where(col(Resource.region).in_(region))
            if category:
                base_query = base_query.where(col(Resource.category).in_(category))

            # Count query
            count_query = select(func.count()).select_from(base_query.subquery())

            # Main query with ordering and pagination
            main_query = (
                base_query.order_by(nullslast(desc(subq.c.total_savings)))
                .offset(skip)
                .limit(limit)
            )

            # Execute queries
            count_result = await self.session.exec(count_query)
            total_items = count_result.one()

            results = await self.session.exec(main_query)
            result_data = results.all()

            # Extract resource IDs and their savings
            sorted_resource_ids = [resource_id for resource_id, _ in result_data]

            # Fetch full Resource objects
            resources_query = select(Resource).where(
                col(Resource.id).in_(sorted_resource_ids)
            )
            resources_result = await self.session.exec(resources_query)
            resources = resources_result.all()

            # Sort resources to match the order of sorted_resource_ids
            resources_dict = {r.id: r for r in resources}
            sorted_resources = [resources_dict[id] for id in sorted_resource_ids]

            # Calculate total recommendations for each resource
            recommendations_query = (
                select(
                    Recommendation.resource_id,
                    func.count(col(Recommendation.id)).label("total_recommendations"),
                    func.coalesce(
                        func.sum(col(Recommendation.potential_savings)),  # type: ignore[attr-defined]
                        0,
                    ).label("total_savings"),
                )
                .where(
                    (col(Recommendation.resource_id).in_(sorted_resource_ids))
                    & (Recommendation.status == RecommendationStatus.PENDING)
                )
                .group_by(col(Recommendation.resource_id))
            )

            recommendations_result = await self.session.exec(recommendations_query)
            recommendations_data = recommendations_result.all()

            # Create recommendation metadata lookup
            rec_metadata: dict[uuid.UUID, dict[str, float | int]] = {}
            for rec in recommendations_data:
                resource_id_value, total_recommendations_value, total_savings_value = (
                    rec
                )
                if resource_id_value is None:
                    continue
                rec_metadata[resource_id_value] = {
                    "total_recommendation": int(total_recommendations_value),
                    "total_potential_saving": float(total_savings_value or 0),
                }

            # Convert to ResourcePublic with calculated fields
            resource_publics = []
            for resource in sorted_resources:
                # Get metadata for this resource
                metadata = rec_metadata.get(
                    resource.id,
                    {
                        "total_recommendation": 0,
                        "total_potential_saving": 0.0,
                    },
                )

                resource_public = ResourcePublic(
                    **resource.model_dump(exclude={"updated_at"}),
                    total_recommendation=int(metadata["total_recommendation"]),
                    total_potential_saving=metadata["total_potential_saving"],
                    updated_at=resource.updated_at,
                )
                resource_publics.append(resource_public)

            # Create pagination metadata
            page = (skip // limit) + 1 if limit > 0 else 1
            total_pages = (total_items + limit - 1) // limit if limit > 0 else 1

            pagination_meta = PaginationMeta(
                page=page,
                take=limit,
                total_items=total_items,
                total_pages=total_pages,
                has_previous=page > 1,
                has_next=page < total_pages,
                start_index=skip + 1 if total_items > 0 else 0,
                end_index=min(skip + limit, total_items),
            )

            result = ResourcesPublic(
                data=resource_publics,
                meta=pagination_meta,
            )

            # Cache the result
            self._set_cache(cache_key, result.model_dump())

            return result

        except Exception as e:
            logger.exception(f"Error getting resources: {e}")
            raise RepositoryError(
                f"Unable to retrieve resources: {str(e)}", status_code=500
            )

    async def invalidate_cache(self, workspace_id: uuid.UUID) -> None:
        """Invalidate cache for workspace resources"""
        try:
            # Invalidate only caches for this workspace for resource queries/statistics
            deleted_count = 0
            patterns = [
                f"resource:get_resources_with_pagination:workspace_id='{workspace_id}'*",
                f"resource:get_resource_statistics:workspace_id='{workspace_id}'*",
                f"resource:get_by_id_and_workspace:workspace_id='{workspace_id}'*",
            ]
            for pattern in patterns:
                deleted_count += int(self._delete_cache_pattern(pattern))
            logger.info(
                f"Invalidated {deleted_count} cache entries for workspace {workspace_id}"
            )
        except Exception as e:
            logger.error(f"Error invalidating cache for workspace {workspace_id}: {e}")

    async def get_by_id_and_workspace(
        self, resource_id: str, workspace_id: uuid.UUID
    ) -> ResourceDetail | None:
        """Get a specific resource by ID and workspace"""
        try:
            # Try cache first
            cache_key = self._generate_cache_key(
                "get_by_id_and_workspace",
                resource_id=str(resource_id),
                workspace_id=str(workspace_id),
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"Cache hit for resource detail: {cache_key}")
                return ResourceDetail(**cached_result)

            # Query for the resource
            resource_query = select(Resource).where(
                (Resource.id == resource_id) & (Resource.workspace_id == workspace_id)
            )
            resource_result = await self.session.exec(resource_query)
            resource = resource_result.first()

            if not resource:
                return None

            # Get recommendation metadata for this resource
            recommendations_query = select(
                func.count(col(Recommendation.id)).label("total_recommendations"),
                func.coalesce(func.sum(col(Recommendation.potential_savings)), 0).label(  # type: ignore[attr-defined]
                    "total_savings"
                ),
            ).where(
                (Recommendation.resource_id == resource_id)
                & (Recommendation.status == RecommendationStatus.PENDING)
            )

            recommendations_result = await self.session.exec(recommendations_query)
            rec_data = recommendations_result.first()

            if rec_data:
                total_recommendations = int(rec_data[0])
                total_savings = float(rec_data[1] or 0)
            else:
                total_recommendations = 0
                total_savings = 0.0

            # Convert to ResourceDetail with calculated fields
            resource_detail = ResourceDetail(
                id=resource.id,
                name=resource.name,
                type=resource.type,
                region=resource.region,
                status=resource.status,
                total_recommendation=total_recommendations,
                total_potential_saving=total_savings,
            )

            # Cache the detail for subsequent reads
            self._set_cache(cache_key, resource_detail.model_dump())

            return resource_detail

        except Exception as e:
            logger.exception(f"Error getting resource by ID {resource_id}: {e}")
            raise RepositoryError(
                f"Unable to retrieve resource: {str(e)}", status_code=500
            )

    async def get_resource_statistics(
        self, workspace_id: uuid.UUID
    ) -> ResourceStatistics:
        """Get comprehensive resource statistics for the workspace"""
        try:
            # Generate cache key for statistics
            cache_key = self._generate_cache_key(
                "get_resource_statistics", workspace_id=str(workspace_id)
            )
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                logger.info(f"Cache hit for resource statistics: {cache_key}")
                return ResourceStatistics(**cached_result)

            logger.info(f"Cache miss for resource statistics: {cache_key}")

            # 1. Get status counts
            status_count_query = (
                select(Resource.status, func.count(col(Resource.id)).label("count"))
                .where(Resource.workspace_id == workspace_id)
                .group_by(Resource.status)
            )
            status_results = await self.session.exec(status_count_query)
            status_counts = [
                ResourceStatusCount(status=row[0], count=row[1])
                for row in status_results.all()
            ]

            # 2. Get total resources count
            total_count_query = select(func.count(col(Resource.id))).where(
                Resource.workspace_id == workspace_id
            )
            total_count_result = await self.session.exec(total_count_query)
            total_resources = total_count_result.one()

            # 3. Get top resource types (top 3)
            type_count_query = (
                select(Resource.type, func.count(col(Resource.id)).label("count"))
                .where(Resource.workspace_id == workspace_id)
                .group_by(Resource.type)
                .order_by(desc(func.count(col(Resource.id))))
                .limit(3)
            )
            type_results = await self.session.exec(type_count_query)
            top_resource_types = [
                ResourceTypeCount(resource_type=row[0], count=row[1])
                for row in type_results.all()
            ]

            # 4. Get potential monthly savings
            savings_query = (
                select(
                    func.coalesce(
                        func.sum(col(Recommendation.potential_savings)),  # type: ignore[attr-defined]
                        0,  # type: ignore[attr-defined]
                    ).label("total_savings"),
                    func.count(func.distinct(Recommendation.resource_id)).label(
                        "resources_with_savings"
                    ),
                )
                .join(Resource, col(Resource.id) == Recommendation.resource_id)
                .where(
                    (Resource.workspace_id == workspace_id)
                    & (Recommendation.status == RecommendationStatus.PENDING)
                    & (col(Recommendation.potential_savings) > 0)  # type: ignore[attr-defined]
                )
            )
            savings_result = await self.session.exec(savings_query)
            savings_data = savings_result.first()

            potential_monthly_savings = ResourceSavingsInfo(
                total_savings=float(savings_data[0])
                if savings_data and savings_data[0]
                else 0.0,
                resources_with_savings=int(savings_data[1])
                if savings_data and savings_data[1]
                else 0,
            )

            # Create the statistics response
            statistics = ResourceStatistics(
                status_counts=status_counts,
                total_resources=total_resources,
                top_resource_types=top_resource_types,
                potential_monthly_savings=potential_monthly_savings,
            )

            # Cache the result for 5 minutes
            self._set_cache(cache_key, statistics.model_dump())

            return statistics

        except Exception as e:
            logger.exception(f"Error getting resource statistics: {e}")
            raise RepositoryError(
                f"Unable to retrieve resource statistics: {str(e)}", status_code=500
            )

    @staticmethod
    def create_or_update(sync_session: Session, resource: Resource) -> Resource:
        db_resource = sync_session.exec(
            select(Resource).where(
                Resource.resource_id == resource.resource_id,
                Resource.workspace_id == resource.workspace_id,
            )
        ).first()

        if db_resource:
            for key, value in resource.model_dump(exclude_unset=True).items():
                setattr(db_resource, key, value)
            db_resource.updated_at = datetime.now(UTC)
        else:
            db_resource = resource
            sync_session.add(db_resource)

        # NOTE: Future improvement - Consider moving cache invalidation to service layer
        # or making this method non-static to access repository cache methods.
        # Current approach directly accesses Redis which breaks encapsulation.
        from app.core.redis.redis_manager import RedisManager

        try:
            redis_manager = RedisManager()
            if not redis_manager:
                raise Exception("Redis client not initialized")
            # Delete all cache keys for this workspace using safe helper
            pattern = f"resource:*workspace_id='{resource.workspace_id}'*"
            redis_manager.delete_pattern(pattern)
        except Exception as e:
            logger.warning(
                f"Failed to clear cache for workspace {resource.workspace_id}: {e}"
            )

        return db_resource
