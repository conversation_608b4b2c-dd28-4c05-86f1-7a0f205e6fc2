import uuid

from sqlalchemy import func
from sqlmodel import and_, col, or_, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser
from app.models.shortcuts import (
    Shortcut,
    ShortcutCreate,
    ShortcutPublic,
    ShortcutUpdate,
    ShortcutsPublic,
)
from app.models.shared import PaginationMeta
from app.repositories.base import BaseRepository


class ShortcutRepository(BaseRepository):
    def __init__(self, session: AsyncSession):
        super().__init__(Shortcut)
        self.session = session

    async def list_by_workspace(
        self,
        workspace_id: uuid.UUID,
        user_id: uuid.UUID,
        include_defaults: bool = True,
        search: str | None = None,
        offset: int = 0,
        limit: int = 20,
    ) -> ShortcutsPublic:
        query = select(Shortcut)

        # Workspace filtering
        conditions: list[bool] = [Shortcut.workspace_id == workspace_id]

        # User access: own shortcuts + defaults if requested
        if include_defaults:
            conditions.append(
                or_(
                    col(Shortcut.user_id) == user_id,
                    Shortcut.is_default,  # type: ignore[arg-type]
                )
            )
        else:
            conditions.append(Shortcut.user_id == user_id)

        query = query.where(and_(*conditions))

        # Search filtering
        if search:
            search_filter = f"%{search.lower()}%"
            search_conditions = [
                col(Shortcut.title).ilike(search_filter),
                col(Shortcut.slug).ilike(search_filter),
                col(Shortcut.category).ilike(search_filter),
                col(Shortcut.content).ilike(search_filter),
            ]
            query = query.where(or_(*search_conditions))

        # Get total count for pagination (without pagination clauses)
        count_query = select(func.count(func.distinct(Shortcut.id))).where(
            and_(*conditions)
        )

        # Apply search filtering to count query if present
        if search:
            search_filter = f"%{search.lower()}%"
            search_conditions = [
                col(Shortcut.title).ilike(search_filter),
                col(Shortcut.slug).ilike(search_filter),
                col(Shortcut.category).ilike(search_filter),
                col(Shortcut.content).ilike(search_filter),
            ]
            count_query = count_query.where(or_(*search_conditions))

        count_result = await self.session.execute(count_query)
        total_items = count_result.scalar() or 0

        # Apply pagination and ordering
        query = (
            query.order_by(
                col(Shortcut.is_default).desc(),
                col(Shortcut.updated_at).desc(),
            )
            .offset(offset)
            .limit(limit)
        )

        result = await self.session.exec(query)
        shortcuts = result.all()

        # Calculate pagination metadata
        page = (offset // limit) + 1
        total_pages = (total_items + limit - 1) // limit

        pagination_meta = PaginationMeta(
            page=page,
            take=limit,
            total_items=total_items,
            total_pages=total_pages,
            has_previous=page > 1,
            has_next=page < total_pages,
            start_index=offset + 1 if total_items > 0 else 0,
            end_index=min(offset + limit, total_items),
        )

        return ShortcutsPublic(
            data=[ShortcutPublic.model_validate(shortcut) for shortcut in shortcuts],
            meta=pagination_meta,
        )

    async def get_by_slug(self, workspace_id: uuid.UUID, slug: str) -> Shortcut | None:
        query = select(Shortcut).where(
            and_(
                Shortcut.workspace_id == workspace_id,
                Shortcut.slug == slug,
            )
        )
        result = await self.session.exec(query)
        return result.one_or_none()

    async def is_slug_taken(
        self, workspace_id: uuid.UUID, slug: str, exclude_id: uuid.UUID | None = None
    ) -> bool:
        query = select(Shortcut).where(
            and_(
                Shortcut.workspace_id == workspace_id,
                Shortcut.slug == slug,
            )
        )

        if exclude_id:
            query = query.where(Shortcut.id != exclude_id)

        result = await self.session.exec(query)
        return result.one_or_none() is not None

    async def create_shortcut(
        self, data: ShortcutCreate, user_id: uuid.UUID, workspace_id: uuid.UUID
    ) -> Shortcut:
        shortcut = Shortcut(
            title=data.title,
            slug=data.slug,
            content=data.content,
            category=data.category,
            is_default=data.is_default,
            user_id=user_id,
            workspace_id=workspace_id,
        )

        self.session.add(shortcut)
        await self.session.commit()
        await self.session.refresh(shortcut)
        return shortcut

    async def get_by_id_with_access(
        self, shortcut_id: uuid.UUID, current_user: CurrentUser
    ) -> Shortcut | None:
        query = select(Shortcut).where(
            and_(
                Shortcut.id == shortcut_id,
                Shortcut.workspace_id == current_user.current_workspace_id,
                or_(
                    Shortcut.user_id == current_user.id,
                    Shortcut.is_default,
                ),
            )
        )
        result = await self.session.exec(query)
        return result.one_or_none()

    async def update_shortcut(
        self, shortcut_id: uuid.UUID, data: ShortcutUpdate, current_user: CurrentUser
    ) -> Shortcut | None:
        shortcut = await self.get_by_id_with_access(shortcut_id, current_user)
        if not shortcut:
            return None

        # Update fields
        if data.title is not None:
            shortcut.title = data.title
        if data.slug is not None:
            shortcut.slug = data.slug
        if data.content is not None:
            shortcut.content = data.content
        if data.category is not None:
            shortcut.category = data.category
        if data.is_default is not None:
            shortcut.is_default = data.is_default

        await self.session.commit()
        await self.session.refresh(shortcut)
        return shortcut

    async def delete_shortcut(
        self, shortcut_id: uuid.UUID, current_user: CurrentUser
    ) -> bool:
        shortcut = await self.get_by_id_with_access(shortcut_id, current_user)
        if not shortcut:
            return False

        await self.session.delete(shortcut)
        await self.session.commit()
        return True
