from typing import Annotated
from pydantic import BaseModel, Field


class CreditResponse(BaseModel):
    """
    Schema for credit quota response.

    Returns focused credit quota data for UI components that only need
    to display credit availability.
    """

    premium_credits_remaining: Annotated[
        int,
        Field(
            title="Premium Credits Remaining",
            description="Number of premium credits remaining in the current period",
            ge=0,
        ),
    ]
    daily_credits_remaining: Annotated[
        int,
        Field(
            title="Daily Credits Remaining",
            description="Number of daily credits remaining for today",
            ge=0,
        ),
    ]
    total_credits_remaining: Annotated[
        int,
        Field(
            title="Total Credits Remaining",
            description="Total credits available (daily + premium)",
            ge=0,
        ),
    ]

    class Config:
        json_schema_extra = {
            "example": {
                "premium_credits_remaining": 50,
                "daily_credits_remaining": 25,
                "total_credits_remaining": 75,
            }
        }


class DashboardOverviewResponse(BaseModel):
    """
    Schema for dashboard overview response.

    Returns comprehensive quota and resource usage data for
    dashboard displays that need full visibility.
    """

    # Workspace quotas
    workspaces_used: Annotated[
        int,
        Field(
            title="Workspaces Used",
            description="Number of workspaces currently in use",
            ge=0,
        ),
    ]
    workspaces_limit: Annotated[
        int,
        Field(
            title="Workspaces Limit",
            description="Maximum number of workspaces allowed",
            ge=-1,  # -1 represents unlimited
        ),
    ]
    workspaces_remaining: Annotated[
        int,
        Field(
            title="Workspaces Remaining",
            description="Number of additional workspaces that can be created",
            ge=0,
        ),
    ]

    # Member quotas
    members_used: Annotated[
        int,
        Field(
            title="Members Used",
            description="Number of workspace members currently active",
            ge=0,
        ),
    ]
    members_limit: Annotated[
        int,
        Field(
            title="Members Limit",
            description="Maximum number of members allowed across all workspaces",
            ge=-1,  # -1 represents unlimited
        ),
    ]
    members_remaining: Annotated[
        int,
        Field(
            title="Members Remaining",
            description="Number of additional members that can be added",
            ge=0,
        ),
    ]

    # Scheduled task quotas
    scheduled_tasks_used: Annotated[
        int,
        Field(
            title="Scheduled Tasks Used",
            description="Number of scheduled tasks currently active",
            ge=0,
        ),
    ]
    scheduled_tasks_limit: Annotated[
        int,
        Field(
            title="Scheduled Tasks Limit",
            description="Maximum number of scheduled tasks allowed",
            ge=-1,  # -1 represents unlimited
        ),
    ]
    scheduled_tasks_remaining: Annotated[
        int,
        Field(
            title="Scheduled Tasks Remaining",
            description="Number of additional scheduled tasks that can be created",
            ge=0,
        ),
    ]

    # Knowledge base storage quotas
    kb_storage_used: Annotated[
        float,
        Field(
            title="KB Storage Used",
            description="Knowledge base storage used in GB",
            ge=0.0,
        ),
    ]
    kb_storage_limit: Annotated[
        float,
        Field(
            title="KB Storage Limit",
            description="Maximum knowledge base storage allowed in GB",
            ge=-1.0,  # -1 represents unlimited
        ),
    ]
    kb_storage_remaining: Annotated[
        float,
        Field(
            title="KB Storage Remaining",
            description="Additional knowledge base storage available in GB",
            ge=0.0,
        ),
    ]

    # Premium message quotas
    premium_messages_used: Annotated[
        int,
        Field(
            title="Premium Messages Used",
            description="Number of premium messages used in the current period",
            ge=0,
        ),
    ]
    premium_messages_limit: Annotated[
        int,
        Field(
            title="Premium Messages Limit",
            description="Maximum premium messages allowed per period",
            ge=-1,  # -1 represents unlimited
        ),
    ]
    premium_messages_remaining: Annotated[
        int,
        Field(
            title="Premium Messages Remaining",
            description="Number of premium messages remaining",
            ge=0,
        ),
    ]

    # Usage percentage
    premium_usage_percentage: Annotated[
        float,
        Field(
            title="Premium Usage Percentage",
            description="Percentage of premium messages used",
            ge=0.0,
            le=100.0,
        ),
    ]

    class Config:
        json_schema_extra = {
            "example": {
                "workspaces_used": 3,
                "workspaces_limit": 10,
                "workspaces_remaining": 7,
                "members_used": 5,
                "members_limit": 20,
                "members_remaining": 15,
                "scheduled_tasks_used": 12,
                "scheduled_tasks_limit": 50,
                "scheduled_tasks_remaining": 38,
                "kb_storage_used": 2.5,
                "kb_storage_limit": 10.0,
                "kb_storage_remaining": 7.5,
                "premium_messages_used": 50,
                "premium_messages_limit": 100,
                "premium_messages_remaining": 50,
                "premium_usage_percentage": 50.0,
            }
        }
