import asyncio
import uuid

from langchain_core.runnables import RunnableConfig
from langgraph.graph.state import CompiledStateGraph  # type: ignore
from pydantic import BaseModel
from slugify import slugify

from app.api.deps import SessionAsyncDep, SessionDep
from app.core.config import settings
from app.core.langfuse import create_langfuse_handler
from app.exceptions.legacy import ServiceError
from app.logger import logger
from app.models import (
    Agent,
    BuiltinConnectionType,
    ConnectionType,
    Conversation,
    KBUsageMode,
    Message,
    MessageAttachment,
)
from app.modules.connectors.connection_client.mcp_manager_client import MCPManagerClient
from app.modules.multi_agents.agents import AgentFactory
from app.modules.multi_agents.config import (
    AgentConfig,
    MultiAgentConfig,
    ToolConfig,
)
from app.modules.multi_agents.core import ToolManager
from app.modules.multi_agents.core.utils import load_chat_model
from app.modules.multi_agents.tools.kb import parse_document_mention
from app.repositories import (
    AgentContextRepository,
    AgentRepository,
    BuiltInToolRepository,
    ConversationRepository,
    KBRepository,
    MessageAttachmentRepository,
    MessageRepository,
    RecommendationRepository,
    WorkspaceRepository,
)
from app.schemas.chat import (
    ChatServiceContext,
    WorkspaceConfigSchema,
)
from app.services import AgentBuiltinToolService
from app.services.attachment_service import AttachmentService
from app.services.builtin_conn_service import BuiltinConnectionService
from app.services.quota import MessageQuotaService
from app.services.token_usage_service import TokenUsageService

from .component_handler import ComponentHandler
from .message_handler import MessageHandler
from .stream_handler import StreamHandler


class AgentBaseService:
    def __init__(self, session: SessionDep, async_session: SessionAsyncDep):
        self.session = session
        self._async_session = async_session

        self.agent_builtin_tool_service = AgentBuiltinToolService(self._async_session)

        # Initialize handlers
        self.message_handler = MessageHandler(self.session, self._async_session)
        self.component_handler = ComponentHandler(self.session)
        self.token_usage_service = TokenUsageService(self._async_session)
        self.quota_service = MessageQuotaService(self._async_session)
        self.stream_handler = StreamHandler(
            self.session,
            self._async_session,
            component_handler=self.component_handler,
            message_handler=self.message_handler,
            token_usage_service=self.token_usage_service,
            quota_service=self.quota_service,
        )
        self.attachment_service = AttachmentService(self._async_session)

        # Initialize repositories
        self.conversation_repository = ConversationRepository(
            async_session=self._async_session, session=self.session
        )
        self.message_repository = MessageRepository(
            session=self.session, async_session=self._async_session
        )
        self.message_attachment_repository = MessageAttachmentRepository(
            self._async_session
        )
        self.agent_repository = AgentRepository(async_session=self._async_session)
        self.builtin_tool_repository = BuiltInToolRepository(self._async_session)
        self.agent_context_repository = AgentContextRepository(self._async_session)
        self.recommendation_repository = RecommendationRepository(self._async_session)

        self.builtin_conn_service = BuiltinConnectionService(self._async_session)
        self.mcp_manager_client = MCPManagerClient()
        self.tool_manager = ToolManager()

    async def _prepare_agent_config(self, conversation: Conversation):
        """Prepare agent configuration with workspace and context settings."""
        agents_config = MultiAgentConfig()

        # Get resource context if available
        if conversation.resource_id and conversation.resource:
            # Get recommendations for this resource
            try:
                recommendations = await self.recommendation_repository.get_recommendations_by_resource_id(
                    conversation.resource_id
                )
                recommendations_context = (
                    self.recommendation_repository.format_recommendations_for_context(
                        recommendations
                    )
                )
            except Exception as e:
                logger.warning(
                    f"Failed to fetch recommendations for resource {conversation.resource_id}: {e}"
                )
                recommendations_context = ""

            # Get base resource context with recommendations
            resource_context = conversation.resource.get_resource_context(
                recommendations_context
            )
            resource_context = (
                f"<resource_context>{resource_context}</resource_context>"
            )
        else:
            resource_context = ""

        # 1. Get all agents
        agents = await self.agent_repository.get_agents(conversation.agent.workspace_id)
        agent_id_to_name = {agent.id: agent.alias for agent in agents}

        # 2. Update the agents config
        agents_config = await self.update_agents_config(
            agents_config, resource_context, agents
        )

        # 3. Update the connections config
        agents_config = await self.update_connections_config(
            agents_config, agent_id_to_name, agents
        )

        # 4. Update the tools config
        agents_config = await self.update_builtin_tools_config(
            agents_config, agent_id_to_name
        )
        return agents_config

    async def update_agents_config(
        self,
        agents_config: MultiAgentConfig,
        resource_context: str,
        agents: list[Agent],
    ) -> MultiAgentConfig:
        """Update the agents config for the agents config."""
        for agent in agents:
            if agent_context := self.agent_context_repository.get_by_agent_id(agent.id):
                agent_context = f"<agent_context>{agent_context}</agent_context>"
            else:
                agent_context = ""

            agents_config.agents[agent.alias] = AgentConfig(
                role=agent.role,
                goal=agent.goal,
                instructions=agent.instructions or "",
                resource_context=resource_context,
                agent_id=str(agent.id),
                agent_context=agent_context,
                tool_config=ToolConfig(
                    builtin=[],
                    tool_permissions=[],
                ),
                region_constraints="",
                mcp_connections=[],
            )
        agents_config.active_agents = [
            agent.alias for agent in agents if agent.is_active
        ]
        return agents_config

    async def update_connections_config(
        self,
        agents_config: MultiAgentConfig,
        agent_id_to_name: dict[uuid.UUID, str],
        agents: list[Agent],
    ) -> MultiAgentConfig:
        """Update the connections config for the agents config."""
        # Get the connections
        agent_ids = list(agent_id_to_name.keys())
        agent_connections = await self.agent_repository.get_all_agent_connections(
            agent_ids
        )
        active_agent_ids = [agent.id for agent in agents if agent.is_active]

        all_mcp_connections = []
        all_connections = {}
        seen_mcp_connections = set()

        # Update the connections config for the agents
        for agent_id, connections in agent_connections.items():
            if agent_id not in agent_id_to_name:
                continue

            # if agent is not active, skip
            if agent_id not in active_agent_ids:
                continue

            agent_name = agent_id_to_name[agent_id]
            agent_config = agents_config.agents[agent_name]

            for connection in connections:
                connection_cached_tools: list[dict[str, str | bool]] = connection.tools
                connection_cached_tools_list = [
                    str(tool["name"]) for tool in connection_cached_tools
                ]
                connection_cached_tools_permissions = [
                    str(tool["name"])
                    for tool in connection_cached_tools
                    if bool(tool["is_required_permission"])
                ]
                connection_cached_tools_enabled = [
                    str(tool["name"])
                    for tool in connection_cached_tools
                    if bool(tool["is_enabled"])
                ]

                if connection.connection_type == ConnectionType.MCP:
                    if agent_config.mcp_connections is not None:
                        agent_config.mcp_connections.append(connection.name)
                    else:
                        agent_config.mcp_connections = [connection.name]
                    agent_config.tool_config.tool_permissions.extend(
                        connection_cached_tools_permissions
                    )
                    connection_key = f"{connection.name}_{connection.prefix}"
                    if connection_key not in seen_mcp_connections:
                        all_mcp_connections.append(connection)
                        seen_mcp_connections.add(connection_key)
                elif connection.connection_type == ConnectionType.BUILTIN and (
                    connection.builtin_connection_type == BuiltinConnectionType.CLOUD
                    or connection.builtin_connection_type
                    == BuiltinConnectionType.ORCHESTRATION
                ):
                    if connection.name not in all_connections:
                        connection_tools = await self.tool_manager.get_console_tools(
                            connection_cached_tools_list
                        )
                        filtered_connection_tools = []
                        for connection_tool in connection_tools:
                            if connection_tool.name in connection_cached_tools_enabled:
                                filtered_connection_tools.append(connection_tool)
                        all_connections[connection.name] = filtered_connection_tools

                    if agent_config.connections is not None:
                        agent_config.connections.append(connection.name)
                    else:
                        agent_config.connections = [connection.name]
                    agent_config.tool_config.tool_permissions.extend(
                        connection_cached_tools_permissions
                    )
                elif (
                    connection.connection_type == ConnectionType.BUILTIN
                    and connection.builtin_connection_type
                    != BuiltinConnectionType.CLOUD
                ):
                    if connection.name not in all_connections:
                        connection_tools = (
                            await self.mcp_manager_client.get_langchain_tools(
                                connection
                            )
                        )
                        filtered_connection_tools = []
                        for connection_tool in connection_tools:
                            if connection_tool.name in connection_cached_tools_enabled:
                                filtered_connection_tools.append(connection_tool)
                        all_connections[connection.name] = filtered_connection_tools

                    if agent_config.connections is not None:
                        agent_config.connections.append(connection.name)
                    else:
                        agent_config.connections = [connection.name]
                    agent_config.tool_config.tool_permissions.extend(
                        connection_cached_tools_permissions
                    )

        # Update the global connections list
        agents_config.all_mcp_connections = all_mcp_connections
        agents_config.all_connections = all_connections
        return agents_config

    async def update_builtin_tools_config(
        self,
        agents_config: MultiAgentConfig,
        agent_id_to_name: dict[uuid.UUID, str],
    ) -> MultiAgentConfig:
        """Update the builtin tools config for the agents config"""
        agent_ids = list(agent_id_to_name.keys())
        # Get agent builtin tools with permissions using the service
        agent_builtin_tools_response = (
            await self.agent_builtin_tool_service.get_agents_builtin_tools(agent_ids)
        )

        # Update the tools config for the agents
        for agent_tool in agent_builtin_tools_response:
            agent_id = agent_tool.agent_id
            tools = agent_tool.tools

            if agent_id not in agent_id_to_name:
                continue

            agent_name = agent_id_to_name[agent_id]
            agent_config = agents_config.agents[agent_name]
            filtered_tools = [tool for tool in tools if tool.is_active]
            tool_names = [tool.name for tool in filtered_tools] + [
                "group_chat"
            ]  # Group chat is always active and hidden from the user
            tool_permissions = [
                tool.name for tool in filtered_tools if tool.required_permission
            ]

            # Update builtin tools
            agent_config.tool_config.builtin = tool_names

            # Add tool names to permissions, avoiding duplicates
            agent_config.tool_config.tool_permissions = list(
                set(agent_config.tool_config.tool_permissions + tool_permissions)
            )

        return agents_config

    async def get_kb_config(
        self, workspace_id: uuid.UUID, user_id: uuid.UUID, message_content: str
    ):
        """Get the kb config for the conversation."""
        # Get all kb_ids
        kb_repo = KBRepository(self._async_session)
        all_kbs = await kb_repo.get_kbs(
            workspace_id=workspace_id,
            user_id=user_id,
        )
        all_kbs = all_kbs.data if all_kbs else []

        always_kbs = [kb for kb in all_kbs if kb.usage_mode == KBUsageMode.ALWAYS]

        if len(always_kbs) == 0:
            kb_prompt = "<knowledge_base_context>User has no always knowledge bases configured, please only call the search_knowledge_base tool if user mention a #kbs/kb-name in their prompt.</knowledge_base_context>"
        else:
            kb_prompt = "<knowledge_base_context>User has always knowledge bases configured, please use the search_knowledge_base tool to get information from the knowledge bases if needed.\n"
            kb_prompt += "\n".join(
                [
                    f"Title: {kb.title} - Description: {kb.description}"
                    for kb in always_kbs
                ]
            )
            kb_prompt += "</knowledge_base_context>"

        # Extract KB references from message content more efficiently
        kbs_mentioned = []
        slugified_names = parse_document_mention(message_content)
        if slugified_names:
            kbs_mentioned = [
                kb.id for kb in all_kbs if slugify(kb.title) in slugified_names
            ]

        # Ensure we only pass KB IDs, not KB objects
        always_kb_ids = [kb.id for kb in always_kbs]
        available_kbs_for_search = always_kb_ids + kbs_mentioned

        return {
            "available_kbs_for_search": available_kbs_for_search,
            "kb_prompt": kb_prompt,
        }

    async def _prepare_attachment_content(
        self, attachment_records: list[MessageAttachment] | None = None
    ) -> list[dict] | None:
        if not attachment_records:
            return None

        # Get async attachment service and process attachments into multimodal content
        attachment_content = (
            await self.attachment_service.process_attachments_for_agent(
                attachment_records
            )
        )

        return attachment_content

    async def _prepare_execution_context(
        self,
        ctx: ChatServiceContext,
    ) -> tuple[CompiledStateGraph, ChatServiceContext]:
        """Unified method that prepares both message and conversation configuration."""

        # Initialize context variables with defaults
        need_title_generation = False
        attachment_records = None
        user_message_db = None
        response_message = None

        workspace_id = ctx.conversation.agent.workspace_id

        # Handle resume vs new message workflow
        if ctx.resume:
            # Resume workflow: get existing messages
            user_message_db = self.message_repository.get_last_user_message(
                ctx.conversation.id
            )
            if not user_message_db:
                logger.error("No previous message found to resume from")
                raise ServiceError(
                    message="No previous message found to resume from", status_code=400
                )

            response_message = self.message_repository.get_last_interrupted_message(
                ctx.conversation.id
            )

            if not response_message:
                logger.error("No interrupted message found to resume")
                raise ServiceError(
                    message="No interrupted message found to resume", status_code=404
                )

            # Update interrupted message status
            response_message.is_interrupt = False
            logger.info(f"Updated interrupted message: {response_message}")
            self.message_repository.update_message(response_message)
        else:
            # New message workflow: create user message and handle attachments
            user_message_db = self.message_repository.create_message(
                Message(
                    conversation_id=ctx.conversation.id,
                    content=ctx.user_prompt,
                    role="user",
                )
            )

            # Process attachments if provided
            if ctx.attachment_ids:
                attachment_records = (
                    await self.message_attachment_repository.attach_files_to_message(
                        user_message_db.id, ctx.attachment_ids
                    )
                )

            # Check if title generation is needed (only for first user message)
            user_message_count = self.message_repository.get_user_message_count(
                ctx.conversation.id
            )
            need_title_generation = user_message_count == 1

            # Create response message (agent message)
            response_message = self.message_handler.create_message(
                ctx.conversation.id,
                "",
                "assistant",  # Populate later
            )

        # Get credentials and kb config
        cloud_credentials = (
            await self.builtin_conn_service.get_cloud_connection_credentials(
                workspace_id
            )
        )
        k8s_credentials = (
            await self.builtin_conn_service.get_k8s_connection_credentials(workspace_id)
        )
        kb_config = await self.get_kb_config(workspace_id, ctx.user_id, ctx.user_prompt)

        # Get pre-compiled graph
        graph: CompiledStateGraph | None = AgentFactory.get_pre_compiled_graph(
            ctx.graph_name
        )
        if graph is None:
            raise ServiceError(message="Graph not found", status_code=404)

        # Prepare attachment content
        attachment_content = await self._prepare_attachment_content(attachment_records)

        agents_config = await self._prepare_agent_config(ctx.conversation)

        # Get owner id
        ctx.owner_id = await WorkspaceRepository(
            self._async_session
        ).get_workspace_owner_id(workspace_id)

        # Update context with all prepared data (depends on attachment_records)
        ctx.attachment_records = attachment_records
        ctx.response_message = response_message
        ctx.workspace_config = WorkspaceConfigSchema(
            workspace_id=workspace_id,
            provider=ctx.conversation.agent.workspace.provider,
            aws_access_key_id=cloud_credentials.get("AWS_ACCESS_KEY_ID")
            if cloud_credentials
            else None,
            aws_secret_access_key=cloud_credentials.get("AWS_SECRET_ACCESS_KEY")
            if cloud_credentials
            else None,
            aws_default_region=cloud_credentials.get("AWS_DEFAULT_REGION")
            if cloud_credentials
            else None,
            gcp_key=cloud_credentials.get("GOOGLE_SERVICE_ACCOUNT_KEY")
            if cloud_credentials
            else None,
            azure_username=cloud_credentials.get("APP_ID")
            if cloud_credentials
            else None,
            azure_password=cloud_credentials.get("CLIENT_SECRET")
            if cloud_credentials
            else None,
            azure_tenant=cloud_credentials.get("TENANT_ID")
            if cloud_credentials
            else None,
            kubeconfig=k8s_credentials.get("KUBECONFIG") if k8s_credentials else None,
        )
        ctx.agents_config = agents_config
        ctx.available_kbs_for_search = kb_config["available_kbs_for_search"]
        ctx.kb_prompt = kb_config["kb_prompt"]
        ctx.attachment_content = attachment_content
        ctx.tool_manager = ToolManager(
            mcp_servers=ctx.agents_config.all_mcp_connections,
            connections_tools=ctx.agents_config.all_connections,
        )

        # Generate a title after the assistant response if needed and save it
        if need_title_generation:
            _ = asyncio.create_task(
                self._generate_title_save_conversation(
                    ctx.user_prompt,
                    ctx.conversation.id,
                    workspace_id,
                )
            )

        return graph, ctx

    async def _generate_title_save_conversation(
        self, user_prompt: str, conversation_id: uuid.UUID, workspace_id: uuid.UUID
    ):
        """Optimized title generation using only the first user message."""
        try:

            class TitleExtraction(BaseModel):
                title: str

            prompt = """Generate a short (3 to 4 words), descriptive title for this conversation:
User: {user_message}

The title should be concise, relevant, and capture the main topic or intent."""

            model = load_chat_model(settings.TITLE_GENERATION_MODEL)

            config = RunnableConfig(
                run_name="Generate Conversation Title",
                callbacks=[create_langfuse_handler()],
            )

            response = await model.with_structured_output(TitleExtraction).ainvoke(
                [{"role": "user", "content": prompt.format(user_message=user_prompt)}],
                config=config,
            )

            response = TitleExtraction.model_validate(response)

            await self.conversation_repository.rename_conversation(
                conversation_id=conversation_id,
                workspace_id=workspace_id,
                name=response.title,
            )

            logger.info(
                f"Generated title '{response.title}' for conversation {conversation_id}"
            )

        except Exception as e:
            logger.error(
                f"Failed to generate title for conversation {conversation_id}: {str(e)}"
            )
