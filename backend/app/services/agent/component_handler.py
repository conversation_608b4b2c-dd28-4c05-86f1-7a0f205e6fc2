from sqlmodel import Session

from app.logger import logger
from app.models import (
    ChartType,
    MessageDisplayComponentType,
    RecommendationStatus,
)
from app.repositories.message_component import MessageComponentRepository
from app.schemas.chat import ChatExecutionContext


class ComponentHandler:
    def __init__(self, session: Session):
        self.session = session
        self.message_component_repository = MessageComponentRepository(session)

    def _format_savings(self, savings: float | None) -> str:
        if savings is None:
            return "None"
        try:
            return f"${float(savings):.2f}"
        except (ValueError, TypeError):
            return "None"

    def process_chart_component(
        self, execution_ctx: ChatExecutionContext, chart_data: dict
    ) -> ChatExecutionContext:
        chart_config = chart_data["chart_data"]

        # Create and store the chart component with proper position including all new fields
        chart_data = {
            "labels": chart_config.get(
                "categories", []
            ),  # Map categories to labels for frontend compatibility
            "datasets": chart_config.get("datasets", []),
        }

        # Add Sankey-specific data if present
        if "sankey_nodes" in chart_config and "sankey_links" in chart_config:
            chart_data["sankey_nodes"] = chart_config["sankey_nodes"]
            chart_data["sankey_links"] = chart_config["sankey_links"]

        # Add axis configuration if present
        if "x_axis" in chart_config:
            chart_data["x_axis"] = chart_config["x_axis"]
        if "y_axis" in chart_config:
            chart_data["y_axis"] = chart_config["y_axis"]

        # Add display options if present
        if any(
            key in chart_config
            for key in [
                "show_legend",
                "show_grid",
                "currency_format",
                "percentage_format",
            ]
        ):
            chart_data["display_options"] = {
                "show_legend": chart_config.get("show_legend", True),
                "show_grid": chart_config.get("show_grid", True),
                "currency_format": chart_config.get("currency_format", False),
                "percentage_format": chart_config.get("percentage_format", False),
            }

        self.message_component_repository.create_message_component_with_display(
            message_id=execution_ctx.current_message.id,
            position=execution_ctx.current_message.components[-1].position + 1
            if execution_ctx.current_message.components
            else 0,
            display_type=MessageDisplayComponentType.CHART,
            data=chart_data,
            title=chart_config["title"],
            description=chart_config["description"],
            chart_type=ChartType[chart_config["chart_type"].upper()],
        )

        self.session.refresh(execution_ctx.current_message)
        execution_ctx = ChatExecutionContext.new_context(execution_ctx.current_message)

        return execution_ctx

    def save_recommendations_as_components(
        self,
        execution_ctx: ChatExecutionContext,
        recommendations_data: list[dict],
        additional_metadata: dict | None = None,
    ) -> ChatExecutionContext:
        try:
            # Create summary table component with optimized data structure
            summary_data = {
                "headers": [
                    "Type",
                    "Title",
                    "Description",
                    "Monthly Savings ($)",
                    "Effort",
                    "Risk",
                    "Status",
                ],
                "rows": [
                    [
                        rec["type"],
                        rec["title"],
                        rec["description"],
                        self._format_savings(rec.get("potential_savings")),
                        rec["effort"],
                        rec["risk"],
                        RecommendationStatus.PENDING.value,
                    ]
                    for rec in recommendations_data
                ],
            }

            # Create message component with display component
            self.message_component_repository.create_message_component_with_display(
                message_id=execution_ctx.current_message.id,
                position=execution_ctx.current_message.components[-1].position + 1
                if execution_ctx.current_message.components
                else 0,
                display_type=MessageDisplayComponentType.TABLE,
                data=summary_data,
                title="Recommendations Summary",
                description="Overview of cost optimization recommendations",
            )

            # Update message metadata efficiently
            recommendations_text = ["Here are the generated recommendations:\n"]
            recommendations_text.extend(
                f"- {rec['title']}\n  Type: {rec['type']}\n  Potential Savings: {self._format_savings(rec.get('potential_savings'))}\n  Effort: {rec['effort']}\n  Risk: {rec['risk']}\n  Description: {rec['description']}\n"
                for rec in recommendations_data
            )

            # Combine all metadata updates in one operation
            metadata_updates = {
                **(execution_ctx.current_message.message_metadata or {}),
                "recommendations_text": "".join(recommendations_text),
            }

            # Add any additional metadata if provided
            if additional_metadata:
                metadata_updates.update(additional_metadata)

            # Update message metadata and type in a single operation
            execution_ctx.current_message.message_metadata = metadata_updates

            # Commit changes in a single transaction
            self.session.add(execution_ctx.current_message)
            self.session.commit()
            self.session.refresh(execution_ctx.current_message)

            execution_ctx = ChatExecutionContext.new_context(
                execution_ctx.current_message
            )

            return execution_ctx

        except Exception as e:
            logger.error(f"Error creating recommendation components: {str(e)}")
            self.session.rollback()
            raise
