"""Functional programming utilities for agent services."""

import inspect
from typing import Any, Callable


def pipe(*functions: Callable) -> Callable:
    """Functional pipeline composition.

    Args:
        *functions: Functions to compose in order

    Returns:
        Composed function that applies all functions in sequence
    """

    def composed(value: Any) -> Any:
        for func in functions:
            if func is None:
                continue
            value = func(value)
        return value

    return composed


async def async_pipe(value: Any, *functions: Callable) -> Any:
    """Async functional pipeline composition.

    Args:
        value: Initial value to transform
        *functions: Functions to apply in sequence

    Returns:
        Final transformed value
    """
    for func in functions:
        if func is None:
            continue
        if callable(func) and hasattr(func.__call__, "__code__"):
            if inspect.iscoroutinefunction(func):
                value = await func(value)
            else:
                value = func(value)
        else:
            value = func(value)
    return value


def filter_result(result: Any) -> Any:
    """Filter pipeline result to extract meaningful data.

    Args:
        result: Result from pipeline processing

    Returns:
        Extracted meaningful data
    """
    if isinstance(result, tuple) and len(result) == 2:
        _, output = result
        return output
    return result
