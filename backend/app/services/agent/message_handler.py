import uuid

from langgraph.graph.state import RunnableConfig  # type: ignore
from sqlmodel import Session, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import Message, MessageCheckpoint
from app.modules.multi_agents.agents import AgentFactory
from app.repositories.conversation import ConversationRepository
from app.services.notification_service import NotificationService


class MessageHandler:
    def __init__(self, session: Session, async_session: AsyncSession):
        self.session = session
        self.notification_service = NotificationService(session)
        self.conversation_repo = ConversationRepository(async_session=async_session)

    def create_message(
        self, conversation_id: uuid.UUID, content: str, role: str
    ) -> Message:
        message = Message(conversation_id=conversation_id, content=content, role=role)
        self.session.add(message)
        self.session.commit()
        self.session.refresh(message)
        return message

    def update_message_namespace(self, message_id: uuid.UUID, namespace: str):
        message = self.session.get(Message, message_id)
        if not message:
            raise ValueError(f"Message with id {message_id} not found")
        message.role = namespace
        self.session.add(message)
        self.session.commit()
        self.session.refresh(message)
        return message

    def handle_interrupt(
        self,
        response_message: Message,
        interrupt_message: str,
        interrupt_reasoning: str,
    ):
        response_message.is_interrupt = True
        response_message.interrupt_message = interrupt_message
        response_message.interrupt_reasoning = interrupt_reasoning
        self.session.add(response_message)
        self.session.commit()

        # Create a notification for the interrupt message
        conversation = response_message.conversation
        if conversation and conversation.agent and conversation.agent.workspace:
            # Create action URL with agent ID and conversation ID
            action_url = (
                f"/agents/{conversation.agent.id}?conversation={conversation.id}"
            )

            notification_result = (
                self.notification_service.create_interrupt_notification(
                    user_id=conversation.agent.workspace.owner_id,
                    interrupt_message=interrupt_message,
                    conversation_id=conversation.id,
                    requires_action=True,
                    action_url=action_url,
                    message_id=response_message.id,
                )
            )

            logger.info(
                f"Scheduled interrupt notification task: {notification_result['task_id']}"
            )

    def cleanup_on_error(
        self, response_message: Message | None, user_message: Message | None
    ):
        if response_message and response_message.id:
            self.session.delete(response_message)
            self.session.commit()

        if user_message:
            self.session.delete(user_message)
            self.session.commit()

    def update_message_checkpoint(
        self,
        message: Message,
        start_checkpoint_id: str | None,
        end_checkpoint_id: str | None,
    ):
        if not message or message.role == "user":
            return

        # Get existing checkpoint or create new one
        checkpoint = self.session.exec(
            select(MessageCheckpoint).where(MessageCheckpoint.message_id == message.id)
        ).first()

        if not checkpoint:
            checkpoint = MessageCheckpoint(
                message_id=message.id, start_checkpoint_id=None, end_checkpoint_id=None
            )

        # Update checkpoint IDs if available - handle start and end independently
        if start_checkpoint_id:
            checkpoint.start_checkpoint_id = start_checkpoint_id
        if end_checkpoint_id:
            checkpoint.end_checkpoint_id = end_checkpoint_id

        self.session.add(checkpoint)
        self.session.commit()

    async def get_latest_message_plans(self, thread_id: str) -> list[dict]:
        """Retrieve plan from checkpoint data."""
        try:
            config = RunnableConfig(configurable={"thread_id": thread_id})

            graph = AgentFactory.get_pre_compiled_graph("coordinator_agent")
            if not graph:
                return []

            state_snapshot = await graph.aget_state(config, subgraphs=True)

            # Get the actual state values from the snapshot
            if (
                not state_snapshot
                or not hasattr(state_snapshot, "values")
                or not state_snapshot.values
            ):
                logger.warning(f"No state found for thread_id: {thread_id}")
                return []

            global_state = state_snapshot.values

            # Debug: log the state structure
            logger.debug(
                f"Global state keys: {list(global_state.keys()) if isinstance(global_state, dict) else 'Not a dict'}"
            )

            # Access plan_manager from the state values (it's a dict, not an object)
            if (
                isinstance(global_state, dict)
                and "plan_manager" in global_state
                and global_state["plan_manager"]
            ):
                plan_managers = global_state["plan_manager"]
            else:
                logger.debug("No plan_manager found in global state")
                return []

            plans = []
            if "instance_states" in global_state:
                for agent_id, _ in global_state["instance_states"].items():
                    plan_manager = plan_managers[agent_id]
                    if plan_manager:
                        # Handle both old dict format and new PlanManager object format
                        if hasattr(plan_manager, "get_plan_dict"):
                            plans.append(
                                {"agent_id": agent_id, **plan_manager.get_plan_dict()}
                            )
                        elif isinstance(plan_manager, dict):
                            # Legacy format - plan_manager is already a dict
                            plans.append({"agent_id": agent_id, **plan_manager})
            plans.sort(key=lambda x: x["updated_at"], reverse=True)
            return plans
        except Exception as e:
            logger.error(f"Error in checkpoint retrieval: {e}")
            raise e
