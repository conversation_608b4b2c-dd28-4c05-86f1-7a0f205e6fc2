import json
from typing import Any

from langchain_core.runnables.schema import (
    CustomStreamEvent,
    EventData,
    StandardStreamEvent,
)
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.agent_service_exceptions import AgentServiceQuotaExceededError
from app.logger import logger
from app.repositories.message_component import MessageComponentRepository
from app.schemas.chat import ChatExecutionContext, ChatServiceContext, TokenUsage
from app.tasks.token_handler_tasks import handle_message_completion

from .message_handler import MessageHandler


class StreamHandler:
    def __init__(
        self,
        session: Session,
        async_session: AsyncSession,
        component_handler=None,
        message_handler=None,
        token_usage_service=None,
        quota_service=None,
    ):
        self.session = session
        self.message_handler = message_handler or MessageHandler(
            self.session, async_session
        )
        self.component_handler = component_handler
        self.token_usage_service = token_usage_service
        self.quota_service = quota_service
        self.message_component_repository = MessageComponentRepository(self.session)
        self.ignore_tool_streams = [
            "planning",
            "RolePlayingOutput",
            "create_memory",
            "visualize",
        ]
        self.ignore_agent_streams = [
            "recommendation",
            "dashboard",
            "visualize",
            "report",
            "create_memory",
        ]
        self.tool_streams = {}

    def _handle_chat_stream(
        self,
        event_data: EventData | Any,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle chat model streaming events."""
        content = event_data["chunk"].content
        result = None
        response_text = execution_ctx.response_text
        response_text_pointer = execution_ctx.response_text_pointer
        cached_tool_reasoning = execution_ctx.cached_tool_reasoning

        if isinstance(content, list):
            for chunk in content:
                if "type" in chunk:
                    if chunk["type"] == "text":
                        response_text += chunk["text"]
        else:
            response_text += content

        process_text = response_text[response_text_pointer:]
        if "<tool_brief>" in process_text and "</tool_brief>" in process_text:
            tool_brief_start = process_text.find("<tool_brief>")
            tool_brief_end = process_text.find("</tool_brief>")
            if tool_brief_start != -1 and tool_brief_end != -1:
                tool_brief_text = process_text[
                    tool_brief_start + len("<tool_brief>") : tool_brief_end
                ].strip()

                # Create MessageComponent for tool_brief content
                try:
                    position = (
                        execution_ctx.current_message.components[-1].position + 1
                        if execution_ctx.current_message.components
                        else 0
                    )
                    self.message_component_repository.create_message_component_with_thinking(
                        message_id=execution_ctx.current_message.id,
                        position=position,
                        content=tool_brief_text,
                        thinking_type="tool_brief",
                    )
                    self.session.refresh(execution_ctx.current_message)
                except Exception as e:
                    logger.warning(
                        f"Failed to create thinking component for tool_brief: {e}"
                    )

                result = {"type": "thinking", "content": tool_brief_text + "..."}
                response_text_pointer = (
                    response_text_pointer + tool_brief_end + len("</tool_brief>")
                )

        process_text = response_text[response_text_pointer:]
        if "<tool_reasoning>" in response_text and "</tool_reasoning>" in response_text:
            tool_reasoning_start = response_text.find("<tool_reasoning>")
            tool_reasoning_end = response_text.find("</tool_reasoning>")
            if tool_reasoning_start != -1 and tool_reasoning_end != -1:
                tool_reasoning_text = response_text[
                    tool_reasoning_start + len("<tool_reasoning>") : tool_reasoning_end
                ].strip()
                cached_tool_reasoning = tool_reasoning_text
                response_text_pointer = (
                    response_text_pointer
                    + tool_reasoning_end
                    + len("</tool_reasoning>")
                )

        execution_ctx.cached_tool_reasoning = cached_tool_reasoning
        execution_ctx.response_text = response_text
        execution_ctx.response_text_pointer = response_text_pointer

        return execution_ctx, result

    def _handle_tool_use_streaming(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle streaming tool use chunks with enhanced phases."""
        tool_call_chunk = event["data"]["chunk"].tool_call_chunks[0]
        tool_name = tool_call_chunk.get("name")
        tool_call_id = tool_call_chunk.get("id")
        tool_call_index = tool_call_chunk.get("index")
        partial_args = tool_call_chunk.get("args", "")
        conversation_id = execution_ctx.current_message.conversation_id

        if (
            tool_name in self.ignore_tool_streams
            or tool_name in self.ignore_agent_streams
        ):
            return execution_ctx, None

        # Has tool call id -> new tool call
        if tool_call_id:
            tool_stream = {
                "name": tool_name,
                "id": tool_call_id,
                "index": tool_call_index,
                "accumulated_input": partial_args,
            }

            if self.tool_streams.get(conversation_id):
                self.tool_streams[conversation_id][tool_call_id] = tool_stream
            else:
                self.tool_streams[conversation_id] = {tool_call_id: tool_stream}

            return (
                execution_ctx,
                {
                    "type": "fs_s",
                    "name": tool_stream["name"],
                    "id": tool_stream["id"],
                    "reasoning": execution_ctx.cached_tool_reasoning or "",
                    "partial_args": partial_args,
                },
            )
        else:
            if self.tool_streams.get(conversation_id):
                # No tool call id -> update existing tool call
                # Get all tool streams for this conversation and sort by index to get the latest
                tool_streams = list(self.tool_streams[conversation_id].values())
                tool_streams.sort(key=lambda x: x.get("index", 0))
                tool_stream = tool_streams[-1]
                if partial_args:
                    # Initialize accumulated_input as empty string if it's None
                    if tool_stream["accumulated_input"] is None:
                        tool_stream["accumulated_input"] = ""
                    tool_stream["accumulated_input"] += partial_args
                    return (
                        execution_ctx,
                        {
                            "type": "fs_u",
                            "partial_args": partial_args,
                        },
                    )
            return execution_ctx, None

    def get_interrupt_message(self, debug_data: dict[str, Any]) -> str | None:
        """Check for interrupts in debug data."""
        # Handle regular interrupts
        if (
            debug_data["type"] == "task_result"
            and len(debug_data["payload"]["interrupts"]) > 0
        ):
            interrupt_message = debug_data["payload"]["interrupts"][-1]["value"]
            return interrupt_message
        return None

    def handle_token_usage(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> ChatExecutionContext:
        if event["event"] == "on_chat_model_stream":
            usage_metadata = event["data"]["chunk"].usage_metadata
            if usage_metadata:
                input_tokens = usage_metadata["input_tokens"]
                output_tokens = usage_metadata["output_tokens"]
                total_tokens = usage_metadata["total_tokens"]
                input_token_details = usage_metadata["input_token_details"]
                output_token_details = (
                    usage_metadata["output_token_details"]
                    if "output_token_details" in usage_metadata
                    else {}
                )

                token_usage = TokenUsage(
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                    total_tokens=total_tokens,
                    input_token_details=input_token_details,
                    output_token_details=output_token_details,
                )
                execution_ctx.token_usage = token_usage

        return execution_ctx

    def handle_chat_model_stream(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle chat model stream events with token usage and content processing."""
        execution_ctx = self.handle_token_usage(event, execution_ctx)

        if event["metadata"].get("langgraph_node", "") == "summarize":
            return execution_ctx, None

        if event["data"]["chunk"].tool_call_chunks:
            execution_ctx, result = self._handle_tool_use_streaming(
                event=event,
                execution_ctx=execution_ctx,
            )
            return execution_ctx, result
        else:
            execution_ctx, result = self._handle_chat_stream(
                event["data"], execution_ctx
            )
            return execution_ctx, result

    def handle_tool_start(
        self,
        event: CustomStreamEvent | StandardStreamEvent | dict[str, Any],
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle tool start events."""
        tool_name = event["name"]
        tool_call_id = event["metadata"]["tool_call_id"]
        cached_tool_reasoning = execution_ctx.cached_tool_reasoning

        # Skip ignored tools
        if tool_name in self.ignore_tool_streams:
            return execution_ctx, None

        tool_input: dict = event["data"].get("input")
        position = (
            execution_ctx.current_message.components[-1].position + 1
            if execution_ctx.current_message.components
            else 0
        )

        if isinstance(tool_input, str):
            try:
                tool_input = json.loads(tool_input)
            except json.JSONDecodeError:
                tool_input = {"input": tool_input}

        self.message_component_repository.create_message_component_with_tool(
            message_id=execution_ctx.current_message.id,
            position=position,
            tool_name=event["name"],
            tool_input=tool_input,
            tool_output="",
            tool_reasoning=cached_tool_reasoning or "",
            tool_runtime=0.0,
        )
        self.session.refresh(execution_ctx.current_message)

        return (
            execution_ctx,
            {
                "type": "fs_e",
                "name": event["name"],
                "id": tool_call_id,
                "reasoning": cached_tool_reasoning or "",
                "partial_args": tool_input,
            },
        )

    def handle_tool_end(
        self,
        event: CustomStreamEvent | StandardStreamEvent | dict[str, Any],
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle tool end events."""
        tool_name = event["name"]
        conversation_id = execution_ctx.current_message.conversation_id
        cached_tool_reasoning = execution_ctx.cached_tool_reasoning
        tool_call_id = event["metadata"]["tool_call_id"]

        # Skip ignored tools
        if tool_name in self.ignore_tool_streams:
            return execution_ctx, None

        try:
            observation = json.loads(event["data"].get("output", ""))
        except Exception:
            observation = event["data"].get("output")

        self.message_component_repository.update_tool_component(
            message_component_id=execution_ctx.current_message.components[-1].id,
            tool_output=json.dumps(observation)
            if isinstance(observation, dict)
            else str(observation),
            tool_reasoning=cached_tool_reasoning or "",
            tool_runtime=0.0,
        )
        self.session.refresh(execution_ctx.current_message)

        execution_ctx = ChatExecutionContext.new_context(
            execution_ctx.current_message,
            preserve_token_usage=execution_ctx.token_usage,
        )
        execution_ctx.cached_tool_reasoning = cached_tool_reasoning

        # Remove tool call from tool streams
        if conversation_id in self.tool_streams:
            tool_stream = self.tool_streams[conversation_id].get(tool_call_id)
            if tool_stream:
                del self.tool_streams[conversation_id][tool_call_id]
            if self.tool_streams[conversation_id] == {}:
                del self.tool_streams[conversation_id]

        return (
            execution_ctx,
            {
                "type": "fs_r",
                "name": event["name"],
                "id": tool_call_id,
                "output": observation,
            },
        )

    def handle_chain_stream_debug(
        self,
        event_data: dict[str, Any],
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle debug events in the chain stream.

        Returns:
            tuple containing (first_checkpoint_id, last_checkpoint_id, interrupt_result)
        """
        if event_data["type"] == "checkpoint":
            if execution_ctx.first_checkpoint_id is None and not ctx.resume:
                execution_ctx.first_checkpoint_id = event_data["payload"]["config"][
                    "configurable"
                ]["checkpoint_id"]

            execution_ctx.last_checkpoint_id = event_data["payload"]["config"][
                "configurable"
            ]["checkpoint_id"]

        interrupt_message = self.get_interrupt_message(event_data)
        interrupt_response = (
            json.loads(interrupt_message) if interrupt_message else None
        )
        if interrupt_response:
            interrupt_result = {
                "type": "interrupt",
                "namespace": execution_ctx.current_message.role,
                "content": interrupt_response,
            }

            self.message_handler.handle_interrupt(
                response_message=execution_ctx.current_message,
                interrupt_message=interrupt_message or "",
                interrupt_reasoning=execution_ctx.cached_tool_reasoning or "",
            )
        else:
            interrupt_result = None

        return execution_ctx, interrupt_result

    async def handle_event(
        self,
        event: CustomStreamEvent | StandardStreamEvent,
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext | None = None,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Central event dispatcher for all stream events."""
        event_type = event["event"]
        if event_type == "on_chain_stream" and ctx is not None:
            return await self.handle_chain_stream_event(event, execution_ctx, ctx)
        elif event_type == "on_chat_model_stream":
            return self.handle_chat_model_stream(event, execution_ctx)
        elif event_type == "on_tool_start":
            return self.handle_tool_start(event, execution_ctx)
        elif event_type == "on_tool_end":
            return self.handle_tool_end(event, execution_ctx)

        return execution_ctx, None

    async def handle_chain_stream_event(
        self,
        event: CustomStreamEvent | StandardStreamEvent | dict[str, Any],
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Process on_chain_stream events."""
        if not isinstance(event["data"]["chunk"], tuple):
            return execution_ctx, None

        event_data = event["data"]["chunk"][2]
        event_type = event["data"]["chunk"][1]

        if event_type == "debug":
            execution_ctx, interrupt_result = self.handle_chain_stream_debug(
                event_data,
                execution_ctx,
                ctx,
            )

            if interrupt_result:
                handle_message_completion.delay(  # type: ignore
                    message_id=str(execution_ctx.current_message.id),
                    token_usage=execution_ctx.token_usage.model_dump(),
                    workspace_id=str(ctx.workspace_id),
                )
                return execution_ctx, interrupt_result

        elif event_type == "custom":
            return await self.handle_custom_event(event_data, execution_ctx, ctx)

        return execution_ctx, None

    async def handle_custom_event(
        self,
        event_data: dict[str, Any],
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle custom event types within chain stream events."""
        if event_data["type"] == "on_agent_start":
            try:
                (
                    execution_ctx,
                    result,
                ) = await self.handle_namespace_change(
                    updated_namespace=event_data["content"],
                    execution_ctx=execution_ctx,
                    ctx=ctx,
                )
            except AgentServiceQuotaExceededError as e:
                return execution_ctx, {
                    "type": "error",
                    "content": str(e),
                    "message_id": None,
                }

            return execution_ctx, result

        elif event_data["type"] == "on_recommendation_generation_response":
            return self.handle_recommendation_response(event_data, execution_ctx)

        elif event_data.get("type") == "on_chart_generation_response":
            return self.handle_chart_generation_response(event_data, execution_ctx)

        elif event_data.get("type") == "on_report_generation_response":
            report_data = event_data.get("content")
            if report_data:
                return execution_ctx, {
                    "type": "on_report_generation_response",
                    "content": report_data,
                }

        elif event_data.get("type") == "on_dashboard_generation_response":
            dashboard_data = event_data.get("content")
            if dashboard_data:
                return execution_ctx, {
                    "type": "on_dashboard_generation_response",
                    "content": dashboard_data,
                }

        elif event_data.get("type") == "planning":
            return execution_ctx, event_data

        return execution_ctx, None

    def handle_recommendation_response(
        self,
        event_data: dict[str, Any],
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle recommendation generation response."""
        # Extract metadata for recommendations
        additional_metadata = {}
        if "metadata" in event_data and "resource_id" in event_data["metadata"]:
            additional_metadata["resource_id"] = event_data["metadata"]["resource_id"]

        # Save and stream recommendations in a single operation with metadata
        # TODO: Move to async task
        if not self.component_handler:
            logger.error(
                "Component handler not available for recommendation processing"
            )
            return execution_ctx, None

        execution_ctx = self.component_handler.save_recommendations_as_components(
            execution_ctx=execution_ctx,
            recommendations_data=event_data["content"],
            additional_metadata=additional_metadata,
        )
        # Get the latest component and display component
        latest_component = execution_ctx.current_message.components[-1]
        display_component = latest_component.display_component

        if not display_component:
            logger.warning("No display component found")
            return execution_ctx, None

        # Stream components efficiently
        return execution_ctx, {
            "type": "display_component",
            "content": {
                "id": str(display_component.id),
                "type": display_component.type.value,
                "chart_type": None,
                "title": display_component.title,
                "description": display_component.description,
                "data": display_component.data,
                "position": latest_component.position,
                "created_at": int(display_component.created_at.timestamp()),
            },
            "position": latest_component.position,
        }

    def handle_chart_generation_response(
        self,
        event_data: dict[str, Any],
        execution_ctx: ChatExecutionContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle chart generation response."""
        # Handle custom chart component stream
        chart_data = event_data.get("content")

        if not chart_data:
            logger.warning("No chart data received in display component event")
            return execution_ctx, None

        try:
            # Process chart component
            if isinstance(chart_data, dict) and "chart_data" in chart_data:
                if not self.component_handler:
                    logger.error("Component handler not available for chart processing")
                    return execution_ctx, None

                execution_ctx = self.component_handler.process_chart_component(
                    execution_ctx=execution_ctx,
                    chart_data=chart_data,
                )
                latest_component = execution_ctx.current_message.components[-1]
                display_component = latest_component.display_component

                if display_component:
                    # Stream the component
                    return execution_ctx, {
                        "type": "display_component",
                        "content": {
                            "id": str(display_component.id),
                            "type": display_component.type.value,
                            "chart_type": display_component.chart_type.value
                            if display_component.chart_type
                            else None,
                            "title": display_component.title,
                            "description": display_component.description,
                            "data": display_component.data,
                            "position": latest_component.position,
                            "created_at": int(display_component.created_at.timestamp()),
                        },
                        "position": latest_component.position,
                    }
        except Exception as e:
            logger.exception(f"Error processing chart component: {str(e)}")

        return execution_ctx, None

    async def handle_namespace_change(
        self,
        updated_namespace: str,
        execution_ctx: ChatExecutionContext,
        ctx: ChatServiceContext,
    ) -> tuple[ChatExecutionContext, dict[str, Any] | None]:
        """Handle namespace changes in the event stream.

        Args:
            execution_ctx (ChatExecutionContext): Execution context
            ctx (ChatServiceContext): Chat service context

        Returns:
            NamespaceChangeOutput: Output schema for namespace change event
        """
        if execution_ctx.current_message.role == updated_namespace:
            return execution_ctx, None

        if execution_ctx.current_message.role == "assistant":
            execution_ctx.current_message = (
                self.message_handler.update_message_namespace(
                    execution_ctx.current_message.id, updated_namespace
                )
            )
        else:
            # Namespace change
            self.message_handler.update_message_checkpoint(
                message=execution_ctx.current_message,
                start_checkpoint_id=None,
                end_checkpoint_id=execution_ctx.last_checkpoint_id,
            )
            handle_message_completion.delay(  # type: ignore
                message_id=str(execution_ctx.current_message.id),
                token_usage=execution_ctx.token_usage.model_dump(),
                workspace_id=str(ctx.workspace_id),
            )

            # Check if out of quota
            if self.quota_service and await self.quota_service.check_out_of_messages(
                ctx.owner_id
            ):
                raise AgentServiceQuotaExceededError(
                    detail="Out of quota",
                    status_code=403,
                )

            # Update execution context
            execution_ctx.current_message = self.message_handler.create_message(
                conversation_id=ctx.conversation.id,
                content="",
                role=updated_namespace,
            )
            execution_ctx = ChatExecutionContext.new_context(
                execution_ctx.current_message,
                preserve_token_usage=execution_ctx.token_usage,
            )

            # Update token usage
            handle_message_completion.delay(  # type: ignore
                message_id=str(execution_ctx.current_message.id),
                token_usage=execution_ctx.token_usage.model_dump(),
                workspace_id=str(ctx.workspace_id),
            )
        return (
            execution_ctx,
            {
                "type": "message_id",
                "message_id": str(execution_ctx.current_message.id),
                "namespace": execution_ctx.current_message.role,
            },
        )
