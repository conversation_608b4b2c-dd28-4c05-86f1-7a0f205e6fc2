"""
Stream persistence implementation for SSE with Redis Queue support.
Phase 1 implementation with asyncio.create_task() background processing.
"""

import asyncio
import datetime
import json
import uuid
from datetime import UTC
from typing import AsyncGenerator

from langgraph.graph.state import RunnableConfig  # pyright: ignore[reportMissingTypeStubs]
from sqlmodel import Session

from app.core.db import engine
from app.core.redis.redis_manager import RedisManager
from app.logger import logger
from app.schemas.chat import ChatServiceContext
from app.services.agent import AgentService
from app.exceptions.agent_service_exceptions import AgentServiceQuotaExceededError

# Global task manager to track active background tasks
_active_tasks: dict[str, asyncio.Task] = {}


async def process_and_queue_stream(
    conversation_id: str, ctx: ChatServiceContext, redis_manager: RedisManager
) -> None:
    """
    Runs the actual agent execution and queues all events to Redis.
    This continues even if client disconnects.
    Uses asyncio.create_task() for background processing (will use Celery in future).
    """

    # Get next position for this conversation
    current_meta = redis_manager.get_stream_status(conversation_id)
    if current_meta:
        position = current_meta.get("last_stream_position", 0) + 1
    else:
        position = 1

    # Store user message in Redis as a special event for reconnection context
    # This ensures complete conversation history is available
    if ctx.user_prompt and ctx.user_prompt.strip():
        position += 1

        # Create a special user_message event for frontend parsing
        user_message_event = {
            "event": {
                "type": "user_message",
                "content": ctx.user_prompt,
                "user_id": str(ctx.user_id),
                "timestamp": datetime.datetime.now(UTC).isoformat(),
                "namespace": "user",
            },
            "position": position,
            "message_id": f"user_{uuid.uuid4()}",  # Special user message ID
        }

        # Queue user message event for reconnection
        redis_manager.queue_stream_event(
            conversation_id, user_message_event, user_message_event["message_id"]
        )
        redis_manager.update_stream_position(conversation_id, position)

        logger.info(
            f"Stored user message event in Redis for conversation {conversation_id}"
        )

    # Generate message ID for this streaming session
    message_id = str(uuid.uuid4())

    # Initialize stream metadata with message boundaries
    redis_manager.start_new_message_stream(conversation_id, message_id, position)

    try:
        # Create new database sessions for background task using proper context managers
        # This prevents session lifecycle conflicts with the HTTP request
        from sqlalchemy.orm import configure_mappers
        from sqlmodel.ext.asyncio.session import AsyncSession

        from app.core.db import async_engine

        # Ensure all SQLAlchemy mappers are configured before background task
        configure_mappers()

        # Use proper session creation pattern for background tasks
        async_session = AsyncSession(async_engine, expire_on_commit=False)
        session = Session(engine)

        try:
            # Create new context with background task sessions
            background_ctx = ChatServiceContext(
                **{
                    k: v
                    for k, v in ctx.__dict__.items()
                    if k not in ["session", "async_session"]
                },
                # Runtime context - use new sessions
                session=session,
                async_session=async_session,
            )

            # Create agent service for processing
            autonomous_agent_service = AgentService(session, async_session)

            # Process the agent execution
            try:
                async for event in autonomous_agent_service.execute(background_ctx):
                    position += 1

                    # Queue each event to Redis with timestamp, position, and message context
                    event_data = {
                        "event": event,
                        "position": position,
                        "message_id": message_id,
                    }

                    redis_manager.queue_stream_event(
                        conversation_id, event_data, message_id
                    )

                    # Update stream position in metadata
                    redis_manager.update_stream_position(conversation_id, position)

                    # Publish to real-time channel
                    redis_manager.publish_stream_event(conversation_id, event_data)
            except AgentServiceQuotaExceededError as quota_error:
                logger.error(
                    f"Quota exceeded in background stream processing {conversation_id}: {quota_error}"
                )
                # Queue specific quota error event
                position += 1
                quota_error_event = {
                    "event": {
                        "type": "error",
                        "content": str(quota_error),
                        "error_type": "quota_exceeded",
                        "status_code": quota_error.status_code,
                    },
                    "position": position,
                    "message_id": message_id,
                }
                redis_manager.queue_stream_event(
                    conversation_id, quota_error_event, message_id
                )
                redis_manager.set_stream_status(
                    conversation_id, {"stream_status": "quota_exceeded"}
                )
                return  # Exit early for quota errors
            except Exception as e:
                logger.error(
                    f"Error in background stream processing {conversation_id}: {e}"
                )
                raise

        finally:
            # Clean up sessions
            try:
                await async_session.close()
                session.close()
            except Exception as cleanup_error:
                logger.error(f"Error cleaning up sessions: {cleanup_error}")

    except AgentServiceQuotaExceededError as quota_error:
        # Queue specific quota error event
        position += 1
        quota_error_event = {
            "event": {
                "type": "error",
                "content": str(quota_error),
                "error_type": "quota_exceeded",
                "status_code": quota_error.status_code,
            },
            "position": position,
            "message_id": message_id,
        }
        redis_manager.queue_stream_event(conversation_id, quota_error_event, message_id)
        redis_manager.set_stream_status(
            conversation_id, {"stream_status": "quota_exceeded"}
        )
        logger.error(
            f"Quota exceeded in background stream processing {conversation_id}: {quota_error}"
        )
    except Exception as e:
        # Queue error event
        position += 1
        error_event = {
            "event": {"type": "error", "content": str(e)},
            "position": position,
            "message_id": message_id,
        }
        redis_manager.queue_stream_event(conversation_id, error_event, message_id)
        redis_manager.set_stream_status(conversation_id, {"stream_status": "error"})
        logger.exception(
            f"Error in background stream processing {conversation_id}: {e}"
        )

    finally:
        # Mark conversation as stream complete
        position += 1
        complete_event = {
            "event": {"type": "complete"},
            "position": position,
            "message_id": message_id,
        }
        redis_manager.queue_stream_event(conversation_id, complete_event, message_id)

        # Complete the current message and mark stream as inactive
        redis_manager.complete_current_message(conversation_id)
        logger.info(f"Stream processing completed for conversation {conversation_id}")


async def stream_from_redis_queue(
    conversation_id: str,
    redis_manager: RedisManager,
    from_position: int = 0,
    current_message_only: bool = False,
    full_conversation_history: bool = False,
) -> AsyncGenerator[str, None]:
    """
    Streams events from Redis queue to client.
    Handles both historical events and real-time events.

    Args:
        conversation_id: The conversation to stream
        redis_manager: Redis manager instance
        from_position: Position to start streaming from (0 for all events)
        current_message_only: If True, only stream events from current active message
        full_conversation_history: If True, stream complete conversation history for reconnection
    """
    client_id = f"client_{conversation_id}_{datetime.datetime.now(UTC)}"

    try:
        # Get events based on mode
        if full_conversation_history:
            # For reconnection with full context - get ALL conversation events from Redis
            historical_events = redis_manager.get_conversation_history_events(
                conversation_id
            )
        elif current_message_only:
            # For reconnection - get all events from current active message
            historical_events = redis_manager.get_current_message_events(
                conversation_id
            )
        else:
            # For new streams - only get events from current active message (if any)
            # This prevents showing old completed message events
            stream_status = redis_manager.get_stream_status(conversation_id)
            if stream_status and stream_status.get("is_streaming_active"):
                # There's an active stream, get only current message events
                historical_events = redis_manager.get_current_message_events(
                    conversation_id
                )
            else:
                # No active stream, start fresh (no historical events)
                historical_events = []

        # Send historical events
        for event_data in historical_events or []:
            yield f"data: {json.dumps(event_data)}\n\n"

        # Calculate last position from historical events
        if historical_events:
            last_position = max(event.get("position", 0) for event in historical_events)
        else:
            last_position = from_position

        # Listen for new events in real-time
        while True:
            # Poll for new events every 300ms
            await asyncio.sleep(0.3)

            # Get stream status to check current message ID
            stream_status = redis_manager.get_stream_status(conversation_id)
            if not stream_status:
                break

            current_message_id = stream_status.get("current_message_id")

            # Get new events but filter by current message if needed
            if current_message_only or stream_status.get("is_streaming_active"):
                # Only get events from current message to prevent cross-message pollution
                new_events = redis_manager.get_stream_events(
                    conversation_id, last_position
                )
                filtered_events = []

                for event_data in new_events or []:
                    # Only include events from current message
                    if (
                        current_message_id
                        and event_data.get("message_id") == current_message_id
                    ):
                        filtered_events.append(event_data)
                    elif (
                        not current_message_id
                    ):  # No active message, include all new events
                        filtered_events.append(event_data)

                new_events = filtered_events
            else:
                new_events = redis_manager.get_stream_events(
                    conversation_id, last_position
                )
            for event_data in new_events or []:
                yield f"data: {json.dumps(event_data)}\n\n"
                last_position = event_data["position"]

                # Break if complete event
                if event_data["event"]["type"] == "complete":
                    return

            # Check if stream is still active
            if not stream_status.get("is_streaming_active"):
                # Stream completed, send any remaining events and exit
                break

    except asyncio.CancelledError:
        # Client disconnected - this is normal for page navigation
        logger.info(f"Client {client_id} disconnected from stream {conversation_id}")
    except Exception as e:
        logger.error(f"Error in stream_from_redis_queue: {str(e)}")
        # Send error event to client
        error_event = {
            "event": {"type": "error", "content": str(e)},
            "position": 0,
        }
        yield f"data: {json.dumps(error_event)}\n\n"


def create_background_stream_task(
    conversation_id: str, ctx: ChatServiceContext, redis_manager: RedisManager
) -> asyncio.Task:
    """
    Creates and returns a background task for stream processing.
    Handles task coordination to prevent multiple concurrent streams.
    The task continues even if the client disconnects.
    """
    # Cancel any existing background task for this conversation
    existing_task = _active_tasks.get(conversation_id)
    if existing_task and not existing_task.done():
        logger.info(
            f"Canceling existing background task for conversation {conversation_id}"
        )
        existing_task.cancel()

    # Complete any existing active message to clean state before new message
    stream_status = redis_manager.get_stream_status(conversation_id)
    if stream_status and stream_status.get("is_streaming_active"):
        logger.info(
            f"Completing previous active message for conversation {conversation_id}"
        )
        redis_manager.complete_current_message(conversation_id)

    # Optional: Clean up old message events to prevent accumulation
    # Keep more messages for complete conversation history during reconnection
    redis_manager.cleanup_old_message_events(conversation_id, keep_messages=50)

    # Create new task
    task = asyncio.create_task(
        process_and_queue_stream(conversation_id, ctx, redis_manager)
    )

    # Store in active tasks
    _active_tasks[conversation_id] = task

    # Add done callback for cleanup and logging
    def task_done_callback(finished_task: asyncio.Task) -> None:
        # Remove from active tasks
        if (
            conversation_id in _active_tasks
            and _active_tasks[conversation_id] == finished_task
        ):
            del _active_tasks[conversation_id]

        if finished_task.cancelled():
            logger.info(f"Background stream task cancelled for {conversation_id}")
        elif finished_task.exception():
            logger.error(
                f"Background stream task failed for {conversation_id}: {finished_task.exception()}"
            )
        else:
            logger.info(f"Background stream task completed for {conversation_id}")

    task.add_done_callback(task_done_callback)
    return task


async def cancel_background_task(conversation_id: str) -> bool:
    """
    Cancel active background task for a conversation.
    Returns True if task was cancelled, False if no active task.
    """
    existing_task = _active_tasks.get(conversation_id)
    if existing_task and not existing_task.done():
        existing_task.cancel()

        from app.modules.multi_agents.agents.factory import AgentFactory

        graph = AgentFactory.get_pre_compiled_graph("coordinator_agent")
        if graph is None:
            return False

        config = RunnableConfig(configurable={"thread_id": conversation_id})
        state = await graph.aget_state(config, subgraphs=True)
        if state.tasks and state.tasks[0].state:
            await graph.aupdate_state(config=config, values=state.tasks[0].state.values)

        logger.info(f"Cancelled background task for conversation {conversation_id}")
        return True
    return False


def get_active_task_status(conversation_id: str) -> dict:
    """
    Get status of active background task for a conversation.
    """
    task = _active_tasks.get(conversation_id)
    if not task:
        return {"exists": False, "status": "none"}

    return {
        "exists": True,
        "status": "running" if not task.done() else "completed",
        "cancelled": task.cancelled() if task.done() else False,
        "exception": str(task.exception())
        if task.done() and task.exception()
        else None,
    }
