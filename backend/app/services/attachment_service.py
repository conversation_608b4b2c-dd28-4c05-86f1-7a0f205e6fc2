import base64
import io
import tempfile
from datetime import timed<PERSON><PERSON>
from uuid import UUID, uuid4

from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.logger import logger
from app.models import MessageAttachment
from app.repositories.attachment import AttachmentRepository
from app.repositories.object_storage.base import BaseStorageRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.schemas.message_attachment import (
    AttachmentFileInfo,
    AttachmentPresignedUrlResponse,
    PresignedUrlInfo,
    UploadedAttachmentInfo,
)
from app.services.csv_sandbox_service import CsvSandboxService
from app.services.security.attachment_validator import validate_attachment_metadata


class AttachmentService:
    def __init__(self, session: AsyncSession):
        self.session = session
        self.attachment_repo = AttachmentRepository(session)
        self.os_repo: BaseStorageRepository = get_object_storage_repository()

    async def generate_presigned_urls(
        self, files: list[AttachmentFileInfo], user_id: UUID, workspace_id: UUID
    ) -> tuple[AttachmentPresignedUrlResponse | None, str | None]:
        """
        Generate presigned URLs for clients to upload files directly to S3.
        Returns (response, error) where one is None and the other has a value.
        """
        try:
            presigned_urls = []
            for file_info in files:
                # Validate file metadata
                is_valid, error = validate_attachment_metadata(file_info)
                if not is_valid:
                    return None, error

                # Generate a unique storage key
                object_name = f"{workspace_id}/{user_id}/{uuid4()}_{file_info.filename}"

                # Generate presigned URL
                presigned_url = await self.os_repo.get_presigned_put_url(
                    object_name=object_name,
                    bucket_name=settings.ATTACHMENT_BUCKET,
                    expires=3600,  # 1 hour expiration
                    content_type=file_info.content_type,
                )

                presigned_urls.append(
                    PresignedUrlInfo(
                        file_id=file_info.file_id,
                        filename=file_info.filename,
                        storage_key=object_name,
                        presigned_url=presigned_url,
                    )
                )

            return AttachmentPresignedUrlResponse(presigned_urls=presigned_urls), None
        except Exception as e:
            logger.error(f"Error generating presigned URLs: {e}")
            return None, "An unexpected error occurred while generating presigned URLs."

    async def verify_uploads(
        self, uploaded_files: list[UploadedAttachmentInfo]
    ) -> list[UploadedAttachmentInfo]:
        """
        Verify that files have been uploaded to the object storage.
        """
        verified_files = []
        for file_info in uploaded_files:
            if await self.os_repo.file_exists(
                file_info.storage_key, settings.ATTACHMENT_BUCKET
            ):
                verified_files.append(file_info)
            else:
                logger.warning(
                    f"File not found in storage after upload confirmation: {file_info.storage_key}"
                )
        return verified_files

    async def get_download_url(
        self, attachment_id: UUID, user_id: UUID
    ) -> tuple[str | None, str | None]:
        """
        Generate a presigned GET URL for a client to download an attachment.
        Returns (download_url, error) where one is None and the other has a value.
        """
        try:
            attachment = await self.attachment_repo.get_attachment_by_id(attachment_id)
            if not attachment:
                return None, "Attachment not found."

            # Basic permission check (can be expanded)
            if attachment.owner_id != user_id:
                return None, "You do not have permission to access this file."

            download_url = await self.os_repo.get_presigned_url(
                object_name=attachment.storage_key,
                bucket_name=settings.ATTACHMENT_BUCKET,
                expires=timedelta(seconds=3600),  # 1 hour expiration
            )
            return download_url, None
        except Exception as e:
            logger.error(
                f"Error generating download URL for attachment {attachment_id}: {e}"
            )
            return (
                None,
                "An unexpected error occurred while generating the download URL.",
            )

    async def process_attachments_for_agent(
        self,
        attachment_records: list[MessageAttachment],
        conversation_id: UUID | None = None,
    ) -> list[dict]:
        """Process attachments into LangGraph-compatible multimodal content.

        Args:
            attachment_records: List of attachment records to process
            conversation_id: Optional conversation ID for sandbox processing

        Returns:
            List of content dictionaries compatible with LangGraph multimodal messages
        """
        processed_content = []

        for attachment_record in attachment_records:
            try:
                # Check if this is a large CSV that should be processed with sandbox
                logger.info(
                    f"Processing attachment {attachment_record.id}, with type: {attachment_record.file_type}"
                )
                if conversation_id and attachment_record.file_type == "text/csv":
                    csv_service = CsvSandboxService()
                    should_use_sandbox = await csv_service.should_process_with_sandbox(
                        attachment_record
                    )

                    logger.info(f"Should use sandbox: {should_use_sandbox}")

                    if should_use_sandbox:
                        # Process with sandbox database
                        sandbox_content = await self._process_csv_with_sandbox(
                            attachment_record, conversation_id
                        )
                        if sandbox_content:
                            processed_content.append(sandbox_content)
                            continue

                # Process normally
                content_dicts = await self._process_single_attachment(attachment_record)
                processed_content.extend(content_dicts)
            except Exception as e:
                logger.error(f"Error processing attachment {attachment_record.id}: {e}")
                continue

        return processed_content

    async def _process_single_attachment(
        self, attachment_record: MessageAttachment
    ) -> list[dict]:
        """Process a single attachment and return list of content dictionaries.

        Args:
            attachment_record: Single attachment record to process

        Returns:
            List of content dictionaries (list because PDFs can generate multiple images)
        """
        file_category = self._get_file_category(attachment_record.file_type)

        if file_category == "image":
            # Keep images as base64 for vision models
            content_dict = await self._process_image_attachment(attachment_record)
            return [content_dict] if content_dict else []

        elif file_category == "pdf":
            # Convert PDF to images using pdf2image
            content_dicts = await self._process_pdf_attachment(attachment_record)
            return content_dicts

        elif file_category == "text":
            # Process text-based files directly
            content_dict = await self._process_text_attachment(attachment_record)
            return [content_dict] if content_dict else []

        elif file_category == "office":
            # Use MarkItDown for office documents (best for structured docs)
            content_dict = await self._process_document_attachment(attachment_record)
            return [content_dict] if content_dict else []

        elif file_category == "archive":
            # Handle archive files (limited support)
            content_dict = await self._process_archive_attachment(attachment_record)
            return [content_dict] if content_dict else []

        else:
            # Use MarkItDown for other document types
            content_dict = await self._process_document_attachment(attachment_record)
            return [content_dict] if content_dict else []

    def _get_file_category(self, content_type: str) -> str:
        """Categorize file type for processing."""
        if content_type.startswith("image/"):
            return "image"
        elif content_type == "application/pdf":
            return "pdf"
        elif content_type in [
            "text/plain",
            "text/markdown",
            "text/csv",
            "text/rtf",
            "application/json",
            "application/xml",
            "application/yaml",
            "application/x-yaml",
            "text/javascript",
            "text/typescript",
            "application/javascript",
            "application/typescript",
            "text/html",
            "text/css",
            "text/x-python",
            "application/x-python-code",
            "text/x-sh",
            "application/x-shellscript",
        ]:
            return "text"
        elif (
            content_type
            in [
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # .docx
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # .xlsx
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",  # .pptx
                "application/msword",  # .doc
                "application/vnd.ms-excel",  # .xls
                "application/vnd.ms-powerpoint",  # .ppt
                "application/vnd.oasis.opendocument.text",  # .odt
                "application/vnd.oasis.opendocument.spreadsheet",  # .ods
                "application/vnd.oasis.opendocument.presentation",  # .odp
            ]
        ):
            return "office"
        elif content_type in [
            "application/zip",
            "application/x-tar",
            "application/gzip",
            "application/x-7z-compressed",
        ]:
            return "archive"
        else:
            return "document"

    async def _download_attachment_content(self, attachment) -> bytes:
        """Download attachment content from storage."""
        try:
            file_data, _, _, _ = await self.os_repo.get_file(
                object_name=attachment.storage_key,
                bucket_name=settings.ATTACHMENT_BUCKET,
            )
            return file_data.getvalue()
        except Exception as e:
            logger.error(f"Error downloading attachment {attachment.id}: {e}")
            raise

    async def _process_image_attachment(self, attachment) -> dict | None:
        """Process image attachment into multimodal content."""
        try:
            file_data = await self._download_attachment_content(attachment)
            base64_data = base64.b64encode(file_data).decode("utf-8")

            return {
                "type": "image",
                "source_type": "base64",
                "data": base64_data,
                "mime_type": attachment.file_type,
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id),
                },
            }
        except Exception as e:
            logger.error(f"Error processing image attachment {attachment.id}: {e}")
            return None

    async def _process_pdf_attachment(self, attachment) -> list[dict]:
        """Process PDF attachment by converting pages to images."""
        try:
            file_data = await self._download_attachment_content(attachment)
            images = await self._pdf_to_images(file_data)

            content_dicts = []
            for i, img_data in enumerate(images):
                content_dicts.append(
                    {
                        "type": "image",
                        "source_type": "base64",
                        "data": img_data,
                        "mime_type": "image/png",
                        "metadata": {
                            "filename": attachment.filename,
                            "attachment_id": str(attachment.id),
                            "source": f"{attachment.filename}_page_{i + 1}",
                            "page_number": i + 1,
                        },
                    }
                )

            return content_dicts
        except Exception as e:
            logger.error(f"Error processing PDF attachment {attachment.id}: {e}")
            return []

    async def _pdf_to_images(self, pdf_data: bytes) -> list[str]:
        """Convert PDF pages to base64 images using pdf2image."""
        try:
            from pdf2image import convert_from_bytes

            images = convert_from_bytes(pdf_data)
            base64_images = []

            for img in images:
                buffer = io.BytesIO()
                img.save(buffer, format="PNG")
                img_data = base64.b64encode(buffer.getvalue()).decode("utf-8")
                base64_images.append(img_data)

            return base64_images
        except ImportError:
            logger.error("pdf2image not installed. Install with: pip install pdf2image")
            raise
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise

    async def _process_text_attachment(self, attachment) -> dict | None:
        """Process text-based attachment by extracting text content directly."""
        try:
            file_data = await self._download_attachment_content(attachment)

            # Try to decode the file content
            try:
                text_content = file_data.decode("utf-8")
            except UnicodeDecodeError:
                # Fallback for files with different encodings
                try:
                    text_content = file_data.decode("latin-1")
                except UnicodeDecodeError:
                    text_content = f"[Binary file content - {attachment.filename}]"

            return {
                "type": "text",
                "text": f"File: {attachment.filename}\nContent:\n{text_content}",
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id),
                    "file_type": attachment.file_type,
                },
            }
        except Exception as e:
            logger.error(f"Error processing text attachment {attachment.id}: {e}")
            return None

    async def _process_archive_attachment(self, attachment) -> dict | None:
        """Process archive attachment (limited support - just metadata)."""
        try:
            return {
                "type": "text",
                "text": f"Archive File: {attachment.filename}\nNote: Archive files are not extracted for content analysis.",
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id),
                    "file_type": attachment.file_type,
                    "archive_type": "compressed",
                },
            }
        except Exception as e:
            logger.error(f"Error processing archive attachment {attachment.id}: {e}")
            return None

    async def _process_document_attachment(self, attachment) -> dict | None:
        """Process document attachment using MarkItDown for better text extraction."""
        try:
            text_content = await self._extract_text_with_markitdown(attachment)

            return {
                "type": "text",
                "text": f"File: {attachment.filename}\nContent:\n{text_content}",
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id),
                    "file_type": attachment.file_type,
                },
            }
        except Exception as e:
            logger.error(f"Error processing document attachment {attachment.id}: {e}")
            return None

    async def _process_csv_with_sandbox(
        self, attachment: MessageAttachment, conversation_id: UUID
    ) -> dict | None:
        """Process large CSV files using sandbox database."""
        try:
            csv_service = CsvSandboxService()
            table_name = await csv_service.import_csv_to_sandbox(
                attachment, conversation_id
            )

            return {
                "type": "csv_sandbox",
                "text": f"Large CSV File: {attachment.filename}\n\n"
                f"This file has been imported to the sandbox database for efficient analysis.\n"
                f"Table Name: {table_name}\n\n"
                f"You can now use the #query_sandbox tool to analyze this data with SQL queries.\n"
                f"Example: Use 'execute_query' command with SQL like 'SELECT * FROM {table_name} LIMIT 10'",
                "metadata": {
                    "filename": attachment.filename,
                    "attachment_id": str(attachment.id),
                    "file_type": attachment.file_type,
                    "table_name": table_name,
                    "processing_method": "sandbox_database",
                    "conversation_id": str(conversation_id),
                },
            }
        except Exception as e:
            logger.error(f"Error processing CSV with sandbox for {attachment.id}: {e}")
            # Fallback to normal processing
            return None

    async def _extract_text_with_markitdown(self, attachment) -> str:
        """Extract text using MarkItDown for better document conversion."""
        try:
            from markitdown import MarkItDown

            # Download file content
            file_data = await self._download_attachment_content(attachment)

            # Save temporarily and process with MarkItDown
            file_extension = (
                attachment.filename.split(".")[-1]
                if "." in attachment.filename
                else "txt"
            )

            with tempfile.NamedTemporaryFile(
                suffix=f".{file_extension}", delete=False
            ) as tmp_file:
                tmp_file.write(file_data)
                tmp_file.flush()

                try:
                    # Initialize MarkItDown converter
                    md_converter = MarkItDown()

                    # Convert file to markdown
                    result = md_converter.convert(tmp_file.name)

                    return result.text_content
                finally:
                    # Clean up temporary file
                    import os

                    try:
                        os.unlink(tmp_file.name)
                    except Exception as e:
                        logger.exception(e)
                        pass

            return ""  # Return empty string if conversion failed

        except ImportError:
            logger.error(
                "MarkItDown not installed. Install with: pip install 'markitdown[all]'"
            )
            # Fallback to LlamaIndex
            return await self._extract_text_with_llamaindex_fallback(attachment)
        except Exception as e:
            logger.error(
                f"Error extracting text with MarkItDown from {attachment.filename}: {e}"
            )
            # Fallback to LlamaIndex
            return await self._extract_text_with_llamaindex_fallback(attachment)

    async def _extract_text_with_llamaindex_fallback(self, attachment) -> str:
        """Fallback text extraction using LlamaIndex."""
        try:
            from llama_index.core import SimpleDirectoryReader

            # Download file content
            file_data = await self._download_attachment_content(attachment)

            # Save temporarily and process
            file_extension = (
                attachment.filename.split(".")[-1]
                if "." in attachment.filename
                else "txt"
            )
            with tempfile.NamedTemporaryFile(
                suffix=f".{file_extension}", delete=False
            ) as tmp_file:
                tmp_file.write(file_data)
                tmp_file.flush()

                try:
                    reader = SimpleDirectoryReader(input_files=[tmp_file.name])
                    documents = reader.load_data()

                    return "\n".join([doc.text for doc in documents])
                finally:
                    # Clean up temporary file
                    import os

                    try:
                        os.unlink(tmp_file.name)
                    except Exception as e:
                        logger.exception(e)
                        pass

            return ""  # Return empty string if no documents found

        except ImportError:
            logger.error(
                "LlamaIndex not installed. Install with: pip install llama-index"
            )
            # Final fallback to basic text extraction
            return f"Document: {attachment.filename} (text extraction not available)"
        except Exception as e:
            logger.error(f"Error extracting text from {attachment.filename}: {e}")
            return f"Document: {attachment.filename} (error extracting text)"

    @staticmethod
    def format_message_content_for_summary(message_content) -> str:
        """Format message content for summarization, ignoring base64 image data.

        This function properly handles multimodal content by:
        - Keeping text content as-is
        - Replacing base64 images with descriptive placeholders
        - Ignoring actual base64 data to keep summaries clean and readable

        Args:
            message_content: The message content, can be string or list of content parts

        Returns:
            Formatted string suitable for summarization (without base64 data)
        """
        if isinstance(message_content, str):
            return message_content
        elif isinstance(message_content, list):
            # Handle multimodal content (text + images + other attachments)
            formatted_parts = []

            for part in message_content:
                if isinstance(part, dict):
                    content_type = part.get("type", "")

                    if content_type == "text":
                        # Keep text content
                        text_content = part.get("text", "")
                        if text_content:
                            formatted_parts.append(text_content)

                    elif content_type == "image_url":
                        # Handle image_url format - ignore base64 data
                        image_url = part.get("image_url", {})
                        if isinstance(image_url, dict):
                            url = image_url.get("url", "")
                            if url.startswith("data:image"):
                                # Base64 image - use placeholder only
                                formatted_parts.append("[Image attachment]")
                            else:
                                # External image URL - keep URL reference
                                formatted_parts.append(f"[Image: {url}]")
                        else:
                            formatted_parts.append("[Image attachment]")

                    elif content_type == "image":
                        # Handle direct image format - ignore base64 data
                        metadata = part.get("metadata", {})
                        filename = metadata.get("filename", "image")
                        formatted_parts.append(f"[Image: {filename}]")

                    elif content_type in ["document", "file"]:
                        # Handle document attachments
                        metadata = part.get("metadata", {})
                        filename = metadata.get("filename", "document")
                        formatted_parts.append(f"[Document: {filename}]")

                    else:
                        # Handle other content types - avoid including large data fields
                        if "text" in part:
                            formatted_parts.append(part["text"])
                        elif "content" in part and not part.get(
                            "content", ""
                        ).startswith("data:"):
                            formatted_parts.append(str(part["content"]))
                        else:
                            formatted_parts.append(f"[{content_type} content]")

                elif isinstance(part, str):
                    # Direct string content - but avoid base64 data
                    if not part.startswith("data:image") and not part.startswith(
                        "data:application"
                    ):
                        formatted_parts.append(part)
                    else:
                        formatted_parts.append("[Attachment data]")
                else:
                    # Fallback for other types - convert to string but limit length
                    str_part = str(part)
                    if len(str_part) > 100:  # Likely base64 or large data
                        formatted_parts.append("[Large data content]")
                    else:
                        formatted_parts.append(str_part)

            return " ".join(filter(None, formatted_parts))  # Filter out empty strings
        else:
            # Fallback for other content types
            return str(message_content)
