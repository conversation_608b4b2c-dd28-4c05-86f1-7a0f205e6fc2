import copy
import json
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.constants import get_agent_alias_for_connection
from app.exceptions.connection_exceptions import ConnectionServiceError
from app.logger import logger
from app.models import (
    BuiltinConnectionEnvType,
    BuiltinConnectionPublic,
    BuiltinConnectionsPublic,
    BuiltinConnectionType,
    CloudProvider,
    ConnectionBase,
    ConnectionEnvConfig,
    ConnectionToolConfig,
    ConnectionType,
)
from app.repositories.connection import ConnectionRepository
from app.repositories.connection_template import ConnectionTemplateRepository
from app.services.agent_connection_service import AgentConnectionService
from app.services.builtin_conn_test_service import BuiltinConnectionTestService
from app.services.workspace_service import WorkspaceService


class BuiltinConnectionService:
    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.conn_repository = ConnectionRepository(async_session)
        self.connection_template_repository = ConnectionTemplateRepository(
            async_session
        )
        self.workspace_service = WorkspaceService(async_session)
        self.builtin_connection_test_service = BuiltinConnectionTestService(
            async_session
        )

    async def _get_env_masked(self, env: list[dict], mask_env: bool) -> list[dict]:
        return (
            [{"key": env["key"], "value": "", "type": env["type"]} for env in env]
            if mask_env
            else env
        )

    async def get_builtin_connections(
        self,
        workspace_id: UUID,
        user_id: UUID | None = None,
        builtin_connection_types: list[BuiltinConnectionType] | None = None,
        filter_by_provider: bool = False,
        mask_env: bool = True,
    ) -> BuiltinConnectionsPublic:
        """Get all available builtin connections from the default
        workspace."""
        conn_templates = (
            await self.connection_template_repository.get_connection_templates(
                builtin_connection_types=builtin_connection_types
            )
        )

        if filter_by_provider and user_id:
            workspace = await self.workspace_service.get_workspace_details(
                user_id=user_id, workspace_id=workspace_id
            )
            filtered_conn_templates = []
            for conn_template in conn_templates:
                if conn_template.builtin_connection_type != BuiltinConnectionType.CLOUD:
                    filtered_conn_templates.append(conn_template)
                else:
                    if (
                        conn_template.prefix == "aws"
                        and workspace.provider == CloudProvider.AWS
                    ):
                        filtered_conn_templates.append(conn_template)
                    elif (
                        conn_template.prefix == "gcp"
                        and workspace.provider == CloudProvider.GCP
                    ):
                        filtered_conn_templates.append(conn_template)
                    elif (
                        conn_template.prefix == "azure"
                        and workspace.provider == CloudProvider.AZURE
                    ):
                        filtered_conn_templates.append(conn_template)
            conn_templates = filtered_conn_templates

        if conn_templates is None:
            return BuiltinConnectionsPublic(data=[])

        # Get existing connections for this workspace
        existing_builtin_connections = (
            await self.conn_repository.get_connections_by_type(
                workspace_id, ConnectionType.BUILTIN
            )
        )

        # Create a mapping of connection_template_id to connection
        template_to_connection_map = {}
        for connection in existing_builtin_connections:
            if connection.connection_template_id:
                template_to_connection_map[connection.connection_template_id] = (
                    connection
                )

        # Post process conn_templates
        conn_templates_responses = []
        for conn_template in conn_templates:
            # Ensure conn_template.id is loaded in current session to avoid lazy loading issues
            try:
                await self.async_session.refresh(conn_template)
            except Exception as refresh_error:
                logger.warning(
                    f"Failed to refresh conn_template {conn_template}: {refresh_error}"
                )
            existing_connection = template_to_connection_map.get(conn_template.id)

            if existing_connection:
                # Use values from the existing connection
                env_data = (
                    json.loads(existing_connection.env)
                    if existing_connection.env
                    else []
                )
                env_masked = await self._get_env_masked(env_data, mask_env)
                conn_template_response = BuiltinConnectionPublic(
                    id=existing_connection.id,  # Use connection ID, not template ID
                    description=conn_template.description,
                    name=existing_connection.name,
                    prefix=existing_connection.prefix,
                    builtin_connection_type=existing_connection.builtin_connection_type,
                    env=[ConnectionEnvConfig(**env) for env in env_masked],
                    tools=[
                        ConnectionToolConfig(**tool)
                        for tool in existing_connection.tools
                    ],
                    is_connected=True,
                    is_sandbox_supported=existing_connection.connection_template.is_sandbox_supported,
                    connection_env_type=existing_connection.connection_env_type,
                )
            else:
                # Use template values as default
                env_data = (
                    json.loads(conn_template.env)
                    if conn_template.env and isinstance(conn_template.env, str)
                    else []
                )
                env_masked = await self._get_env_masked(env_data, mask_env)
                conn_template_response = BuiltinConnectionPublic(
                    id=conn_template.id,
                    description=conn_template.description,
                    name=conn_template.name,
                    prefix=conn_template.prefix,
                    builtin_connection_type=conn_template.builtin_connection_type,
                    env=[ConnectionEnvConfig(**env) for env in env_masked],
                    tools=[
                        ConnectionToolConfig(**tool) for tool in conn_template.tools
                    ],
                    is_connected=False,
                    is_sandbox_supported=conn_template.is_sandbox_supported,
                    connection_env_type=BuiltinConnectionEnvType.PRODUCTION,
                )
            conn_templates_responses.append(conn_template_response)

        sorted_responses = sorted(conn_templates_responses, key=lambda x: x.name)
        return BuiltinConnectionsPublic(data=sorted_responses)

    async def install_builtin_connection(
        self,
        workspace_id: UUID,
        builtin_connection_id: UUID,
        env: list[dict] | None = None,
        connection_env_type: BuiltinConnectionEnvType = BuiltinConnectionEnvType.PRODUCTION,
    ) -> BuiltinConnectionPublic | None:
        """Install a builtin connection by cloning it to the user's workspace."""
        # Get the builtin connection from default workspace
        conn_template = (
            await self.connection_template_repository.get_connection_template(
                connection_template_id=builtin_connection_id
            )
        )
        if not conn_template:
            raise ConnectionServiceError("Builtin connection not found", 404)

        # Create a new connection based on the builtin template
        conn = ConnectionBase(
            name=conn_template.name,
            prefix=conn_template.prefix,
            connection_type=ConnectionType.BUILTIN,
            builtin_connection_type=conn_template.builtin_connection_type,
            config=conn_template.config,
            env=json.dumps(env),
            tools=conn_template.tools,
            connection_env_type=connection_env_type,
        )

        # Test the connection to get available tools
        conn_status_response = await self.builtin_connection_test_service.connect(
            conn, workspace_id
        )

        conn.tool_schemas = conn_status_response.tool_schemas
        conn.status = conn_status_response.status
        conn.status_message = conn_status_response.status_message
        conn.tools = conn_status_response.tools

        # Create the connection in user's workspace
        conn = await self.conn_repository.create_builtin_connection(
            workspace_id=workspace_id, conn_data=conn, conn_template_id=conn_template.id
        )

        # Auto-attach the connection to the appropriate agent
        await self._auto_attach_connection_to_agent(
            workspace_id=workspace_id,
            connection_type=conn_template.builtin_connection_type.value,
            connection_prefix=conn_template.prefix,
            connection_id=conn.id,
        )

        # Refresh the connection template to ensure all attributes are loaded
        await self.async_session.refresh(conn_template)

        if conn_template.builtin_connection_type == BuiltinConnectionType.CLOUD:
            # Get the cloud provider from the workspace
            workspace_provider = (
                await self.workspace_service.get_workspace_cloud_provider(
                    workspace_id=workspace_id
                )
            )

            # If the workspace provider is not None, update the provider for onboarding
            if workspace_provider is None:
                # Choose the provider based on the cloud prefix
                if conn_template.prefix == "aws":
                    workspace_provider = CloudProvider.AWS
                elif conn_template.prefix == "gcp":
                    workspace_provider = CloudProvider.GCP
                elif conn_template.prefix == "azure":
                    workspace_provider = CloudProvider.AZURE
                else:
                    raise ConnectionServiceError("Invalid cloud prefix", 400)

                # Update the workspace provider
                await self.workspace_service.update_workspace_cloud_provider(
                    workspace_id=workspace_id,
                    cloud_provider=workspace_provider,
                )

        # Refresh the connection to ensure all attributes are loaded
        await self.async_session.refresh(conn)
        await self.async_session.refresh(conn_template)

        env_masked = await self._get_env_masked(env, True) if env else []
        return BuiltinConnectionPublic(
            id=conn.id,
            name=conn.name,
            prefix=conn.prefix,
            description=conn.connection_template.description
            if conn.connection_template
            else "",
            builtin_connection_type=conn.builtin_connection_type,
            env=[ConnectionEnvConfig(**env) for env in env_masked],
            tools=[ConnectionToolConfig(**tool) for tool in conn.tools],
            is_connected=True,
            is_sandbox_supported=conn_template.is_sandbox_supported,
            connection_env_type=conn.connection_env_type,
        )

    async def update_builtin_connection(
        self,
        workspace_id: UUID,
        builtin_connection_id: UUID,
        env: list[dict] | None = None,
    ) -> BuiltinConnectionPublic | None:
        """Update a builtin connection by cloning it to the user's workspace."""
        # Get the builtin connection from default workspace
        conn = await self.conn_repository.get_connection(
            workspace_id=workspace_id,
            conn_id=builtin_connection_id,
        )
        if not conn:
            raise ConnectionServiceError("Builtin connection not found", 404)

        # Update the connection
        conn.env = json.dumps(env)

        # Test the connection to get available tools
        conn_status_response = await self.builtin_connection_test_service.connect(
            conn, workspace_id
        )

        conn.tool_schemas = conn_status_response.tool_schemas
        conn.status = conn_status_response.status
        conn.status_message = conn_status_response.status_message
        conn.tools = conn_status_response.tools

        # Update the connection in user's workspace
        conn = await self.conn_repository.update_builtin_connection(conn=conn)

        env_masked = await self._get_env_masked(env, True) if env else []
        return BuiltinConnectionPublic(
            id=conn.id,
            name=conn.name,
            prefix=conn.prefix,
            description=conn.connection_template.description
            if conn.connection_template
            else "",
            builtin_connection_type=conn.builtin_connection_type,
            env=[ConnectionEnvConfig(**env) for env in env_masked],
            tools=[ConnectionToolConfig(**tool) for tool in conn.tools],
            is_connected=True,
            is_sandbox_supported=conn.connection_template.is_sandbox_supported,
            connection_env_type=conn.connection_env_type,
        )

    async def delete_builtin_connection(
        self,
        workspace_id: UUID,
        builtin_connection_id: UUID,
    ) -> None:
        """Delete a builtin connection from the user's workspace."""
        return await self.conn_repository.delete_builtin_connection(
            workspace_id=workspace_id,
            builtin_connection_id=builtin_connection_id,
        )

    async def update_builtin_connection_tool(
        self,
        workspace_id: UUID,
        connection_id: UUID,
        tool_name: str,
        is_enabled: bool,
        is_required_permission: bool,
    ) -> BuiltinConnectionPublic | None:
        """Share update the tools for mcp and builtin connections."""

        # Get the builtin connection from default workspace
        conn = await self.conn_repository.get_connection(
            workspace_id=workspace_id,
            conn_id=connection_id,
        )
        if not conn:
            raise ConnectionServiceError("Builtin connection not found", 404)

        # Update the tools for the builtin connection
        updated_tools = []
        for tool in conn.tools:
            if tool["name"] == tool_name:
                updated_tools.append(
                    {
                        "name": tool_name,
                        "is_enabled": is_enabled,
                        "is_required_permission": is_required_permission,
                    }
                )
            else:
                updated_tools.append(tool)
        conn.tools = updated_tools

        # Update the connection in user's workspace
        conn = await self.conn_repository.update_builtin_connection(conn=conn)

        return BuiltinConnectionPublic(
            id=conn.id,
            name=conn.name,
            prefix=conn.prefix,
            description=conn.connection_template.description
            if conn.connection_template
            else "",
            builtin_connection_type=conn.builtin_connection_type,
            env=[ConnectionEnvConfig(**env) for env in json.loads(conn.env)],
            tools=[ConnectionToolConfig(**tool) for tool in conn.tools],
            is_connected=True,
            is_sandbox_supported=conn.connection_template.is_sandbox_supported,
            connection_env_type=conn.connection_env_type,
        )

    async def get_cloud_connection_credentials(
        self, workspace_id: UUID
    ) -> dict[str, str] | None:
        """Get the cloud credentials for the workspace."""
        builtin_cloud_conns = await self.get_builtin_connections(
            workspace_id=workspace_id,
            builtin_connection_types=[BuiltinConnectionType.CLOUD],
            mask_env=False,
        )
        if builtin_cloud_conns.data == []:
            return None

        # Current only support one cloud credential for aws, gcp, azure
        for conn in builtin_cloud_conns.data:
            if conn.is_connected:
                env = {env.key: env.value for env in conn.env}
                return env

        return None  # No cloud connection is connected

    async def get_k8s_connection_credentials(
        self, workspace_id: UUID
    ) -> dict[str, str] | None:
        """Get the cli credentials for the workspace."""
        k8s_conns = await self.get_builtin_connections(
            workspace_id=workspace_id,
            builtin_connection_types=[BuiltinConnectionType.ORCHESTRATION],
            mask_env=False,
        )
        if k8s_conns.data == []:
            return None

        # Current only support one k8s credential
        for conn in k8s_conns.data:
            if conn.prefix == "k8s" and conn.is_connected:
                env = {env.key: env.value for env in conn.env}
                return env

        return None  # No k8s connection is connected

    async def _auto_attach_connection_to_agent(
        self,
        workspace_id: UUID,
        connection_type: str,
        connection_prefix: str,
        connection_id: UUID,
    ) -> None:
        """
        Auto-attach a connection to the appropriate agent based on connection type and prefix.

        Supported mappings:
        - PostgreSQL (postgres) -> Tony (Database Engineer)
        - Kubernetes (k8s) -> Kai (Kubernetes Engineer)
        - AWS/GCP/Azure (aws/gcp/azure) -> Alex (Cloud Engineer)

        Args:
            workspace_id: The workspace ID
            connection_type: The BuiltinConnectionType (as string)
            connection_prefix: The connection prefix
            connection_id: The connection ID to create agent-connection relationship
        """
        try:
            logger.info(
                f"Attempting to auto-attach connection (type: {connection_type}, prefix: {connection_prefix}) "
                f"to agent in workspace {workspace_id}"
            )

            # Import here to avoid circular imports
            from app.repositories import AgentRepository

            agent_alias = get_agent_alias_for_connection(
                connection_type, connection_prefix
            )

            if not agent_alias:
                logger.info(
                    f"No agent mapping found for connection type: {connection_type}, prefix: {connection_prefix}"
                )
                return

            # Find the agent in the workspace by alias
            agent_repository = AgentRepository(self.async_session)
            target_agent = await agent_repository.get_agent_by_alias(
                agent_alias, workspace_id
            )

            if not target_agent:
                logger.warning(
                    f"No agent found with alias: {agent_alias} in workspace {workspace_id}"
                )
                return

            # Activate the agent if it's not already active
            if not target_agent.is_active:
                target_agent = await agent_repository.update_agent(
                    target_agent.id, is_active=True
                )
                logger.info(
                    f"Activated agent {target_agent.title} (alias: {agent_alias})"
                )

            # Create agent-connection relationship
            agent_connection_service = AgentConnectionService(self.async_session)
            await agent_connection_service.create_agent_connection(
                workspace_id=workspace_id,
                agent_id=target_agent.id,
                conn_id=connection_id,
            )
            logger.info(
                f"Attached connection {connection_id} (type: {connection_type}, prefix: {connection_prefix}) "
                f"to agent {target_agent.id} (alias: {agent_alias})"
            )
        except Exception as e:
            logger.error(f"Error during connection auto-attachment: {e}")

    async def install_sandbox_connection(
        self,
        workspace_id: UUID,
        sandbox_conn_ids: list[UUID] | None = None,
        filter_by_conn_ids: bool = True,
        provider: CloudProvider | None = None,
    ) -> BuiltinConnectionsPublic:
        conn_templates = (
            await self.connection_template_repository.get_connection_templates()
        )

        sandbox_conn_templates = [
            copy.deepcopy(conn_template)
            for conn_template in conn_templates
            if conn_template.is_sandbox_supported
        ]

        conns = BuiltinConnectionsPublic(data=[])

        for conn_template in sandbox_conn_templates:
            if (
                filter_by_conn_ids
                and sandbox_conn_ids
                and conn_template.id not in sandbox_conn_ids
            ):
                continue

            if provider and conn_template.prefix != provider.value.lower():
                continue

            conn = await self.install_builtin_connection(
                workspace_id=workspace_id,
                builtin_connection_id=conn_template.id,
                env=json.loads(conn_template.env),
                connection_env_type=BuiltinConnectionEnvType.SANDBOX,
            )

            conns.data.append(conn)

        return conns
