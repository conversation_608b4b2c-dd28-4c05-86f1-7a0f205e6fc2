import csv
import io
from typing import Any
from uuid import UUID

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.types import <PERSON>ole<PERSON>, DateTime, Float, Integer, Text
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.logger import logger
from app.models import MessageAttachment
from app.repositories.object_storage.provider import get_object_storage_repository


class CsvSandboxService:
    """Service for processing CSV files and importing them to sandbox database."""

    def __init__(self):
        self.engine = create_async_engine(
            str(settings.SANDBOX_DATABASE_URI),
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=300,
            pool_pre_ping=True,
            echo=False,
        )

    async def should_process_with_sandbox(self, attachment: MessageAttachment) -> bool:
        """Determine if a CSV file should be processed using sandbox database."""
        if attachment.file_type != "text/csv":
            return False

        try:
            # Count rows in CSV
            row_count = await self._count_csv_rows(attachment)
            return row_count > 30
        except Exception as e:
            logger.error(f"Error counting CSV rows for {attachment.id}: {e}")
            return False

    async def _count_csv_rows(self, attachment: MessageAttachment) -> int:
        """Count the number of rows in a CSV file."""
        try:
            from app.services.attachment_service import AttachmentService

            # Create attachment service instance for downloading
            attachment_service = AttachmentService.__new__(AttachmentService)
            attachment_service.os_repo = get_object_storage_repository()
            file_data = await attachment_service._download_attachment_content(
                attachment
            )

            # Try different delimiters
            delimiters = [",", ";", "\t", "|"]
            max_rows = 0

            for delimiter in delimiters:
                try:
                    csv_content = file_data.decode("utf-8")
                    reader = csv.reader(io.StringIO(csv_content), delimiter=delimiter)
                    rows = list(reader)
                    if len(rows) > max_rows:
                        max_rows = len(rows)
                except Exception:
                    continue

            return max_rows
        except Exception as e:
            logger.error(f"Error counting rows: {e}")
            return 0

    async def import_csv_to_sandbox(
        self, attachment: MessageAttachment, conversation_id: UUID
    ) -> str:
        """
        Import CSV data to sandbox database and return the table name.

        Returns the table name that was created.
        """
        try:
            # Download and parse CSV
            from app.services.attachment_service import AttachmentService

            # Create attachment service instance for downloading
            attachment_service = AttachmentService.__new__(AttachmentService)
            attachment_service.os_repo = get_object_storage_repository()
            file_data = await attachment_service._download_attachment_content(
                attachment
            )

            # Detect delimiter and parse CSV
            csv_data = await self._parse_csv_with_delimiter_detection(file_data)

            if not csv_data["rows"]:
                raise ValueError("No data found in CSV")

            # Infer column types
            column_types = await self._infer_column_types(csv_data["rows"])

            # Create table name (replace hyphens with underscores for PostgreSQL compatibility)
            table_name = f"{str(conversation_id).replace('-', '_')}_csv_table"

            # Create table and import data
            await self._create_table_and_import(
                table_name, csv_data["headers"], column_types, csv_data["rows"]
            )

            logger.info(
                f"Successfully imported CSV {attachment.filename} to table {table_name}"
            )
            return table_name

        except Exception as e:
            logger.error(f"Error importing CSV {attachment.id} to sandbox: {e}")
            raise

    async def _parse_csv_with_delimiter_detection(
        self, file_data: bytes
    ) -> dict[str, Any]:
        """Parse CSV with automatic delimiter detection."""
        csv_content = file_data.decode("utf-8")

        # Try different delimiters
        delimiters = [",", ";", "\t", "|"]
        best_result = {"headers": [], "rows": [], "delimiter": ","}

        for delimiter in delimiters:
            try:
                reader = csv.reader(io.StringIO(csv_content), delimiter=delimiter)
                rows = list(reader)

                if len(rows) < 2:  # Need at least header + 1 data row
                    continue

                headers = rows[0]
                data_rows = rows[1:]

                # Basic validation - check if we have consistent columns
                if len(data_rows) > 0 and all(
                    len(row) == len(headers) for row in data_rows[:5]
                ):  # Check first 5 rows
                    if len(data_rows) > len(best_result["rows"]):
                        best_result = {
                            "headers": headers,
                            "rows": data_rows,
                            "delimiter": delimiter,
                        }
            except Exception:
                continue

        if not best_result["rows"]:
            raise ValueError("Could not parse CSV with any delimiter")

        return best_result

    async def _infer_column_types(self, rows: list[list[str]]) -> dict[str, Any]:
        """Infer SQL column types from CSV data."""
        if not rows:
            return {}

        headers = rows[0] if isinstance(rows[0], list) else []
        column_types = {}

        # Sample first 100 rows for type inference
        sample_rows = rows[:100]

        for i, header in enumerate(headers):
            column_values = [row[i] for row in sample_rows if i < len(row)]

            # Infer type
            inferred_type = await self._infer_single_column_type(column_values)
            column_types[header] = inferred_type

        return column_types

    async def _infer_single_column_type(self, values: list[str]) -> Any:
        """Infer SQL type for a single column."""
        if not values:
            return Text()

        # Check for boolean
        if all(
            v.lower() in ["true", "false", "1", "0", "yes", "no", ""] for v in values
        ):
            return Boolean()

        # Check for integer
        try:
            for v in values:
                if v.strip():  # Skip empty values
                    int(v)
            return Integer()
        except ValueError:
            pass

        # Check for float
        try:
            for v in values:
                if v.strip():  # Skip empty values
                    float(v)
            return Float()
        except ValueError:
            pass

        # Check for datetime (basic check)
        datetime_patterns = ["%Y-%m-%d", "%Y-%m-%d %H:%M:%S", "%m/%d/%Y", "%d/%m/%Y"]
        if any(v for v in values if v.strip()):
            for pattern in datetime_patterns:
                try:
                    import datetime

                    for v in values:
                        if v.strip():
                            datetime.datetime.strptime(v, pattern)
                    return DateTime()
                except Exception:
                    continue

        # Default to text
        return Text()

    async def _create_table_and_import(
        self,
        table_name: str,
        headers: list[str],
        column_types: dict[str, Any],
        rows: list[list[str]],
    ):
        """Create table and import data."""
        try:
            async with AsyncSession(self.engine, expire_on_commit=False) as session:  # type: ignore
                # Drop table if exists
                await session.execute(text(f'DROP TABLE IF EXISTS "{table_name}"'))  # type: ignore

                # Create table
                # Create table using raw SQL instead of metadata.create_all
                columns_sql = []
                for header in headers:
                    col_type = column_types.get(header, Text())
                    if isinstance(col_type, Text):
                        sql_type = "TEXT"
                    elif isinstance(col_type, Integer):
                        sql_type = "INTEGER"
                    elif isinstance(col_type, Float):
                        sql_type = "FLOAT"
                    elif isinstance(col_type, Boolean):
                        sql_type = "BOOLEAN"
                    elif isinstance(col_type, DateTime):
                        sql_type = "TIMESTAMP"
                    else:
                        sql_type = "TEXT"

                    columns_sql.append(f'"{header}" {sql_type}')  # type: ignore

                create_table_sql = f"""
                CREATE TABLE "{table_name}" (
                    id SERIAL PRIMARY KEY,
                    {", ".join(columns_sql)}
                )
                """
                await session.execute(text(create_table_sql))  # type: ignore

                # Insert data in batches
                batch_size = 1000
                for i in range(0, len(rows), batch_size):
                    batch = rows[i : i + batch_size]

                    # Prepare insert statement with column names
                    column_placeholders = ", ".join(
                        [f":{header}" for header in headers]
                    )
                    quoted_headers = ", ".join([f'"{header}"' for header in headers])
                    insert_sql = f'INSERT INTO "{table_name}" ({quoted_headers}) VALUES ({column_placeholders})'

                    # Prepare batch data
                    batch_data = []
                    for row in batch:
                        row_dict = {}
                        for j, value in enumerate(row):
                            if j < len(headers):
                                row_dict[headers[j]] = value if value else None
                        batch_data.append(row_dict)

                    # Execute batch insert
                    if batch_data:
                        await session.execute(text(insert_sql), batch_data)  # type: ignore

                await session.commit()  # type: ignore

        except Exception as e:
            logger.error(f"Error creating table and importing data: {e}")
            raise
