import asyncio
from typing import Any, Callable
from uuid import UUID

from llama_index.core.ingestion import Ingestion<PERSON>ipeline
from llama_index.core.node_parser import TokenTextSplitter
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.logger import logger
from app.models import AsyncTaskStatus
from app.modules.knowledge_base.readers import file_reader, website_reader
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.quota import ResourceQuotaService, QuotaResourceType

from .base import BaseKBService


class IngestionService(BaseKBService):
    """Service responsible for document ingestion and processing"""

    def __init__(self, vector_store, session):
        super().__init__(vector_store, session)
        self.chunker = TokenTextSplitter(
            chunk_size=1024,
            chunk_overlap=128,
            include_metadata=False,
            include_prev_next_rel=False,
        )
        self.quota_service = ResourceQuotaService(session)

    async def ingest_from_website(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        callback: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        """
        Ingest documents from website URLs

        Args:
            session: Database session
            doc_ids: List of document IDs to ingest
            kb_id: Knowledge base ID
            callback: Progress callback function
            user_id: User ID (optional)
            workspace_id: Workspace ID (optional)

        Returns:
            Dictionary with ingestion status
        """
        # Get repositories
        kb_repo = KBRepository(session)
        os_repo = get_object_storage_repository()

        # Get documents to ingest
        docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)

        # Update progress
        callback(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 30, "message": "Crawling urls"},
        )

        # Read documents from websites
        docs, docs_to_ingest, children_docs = await website_reader.read(
            docs_to_ingest, callback
        )

        # Update document status and create children
        await kb_repo.update_documents(docs_to_ingest)
        await kb_repo.create_documents(children_docs)

        # Update progress
        callback(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 85, "message": "Checking storage quota"},
        )

        # Calculate document sizes and check quota
        doc_size_mb = 0
        if docs and workspace_id:
            doc_size_mb = self._calculate_documents_size_mb(docs)
            try:
                await self._check_and_update_storage_quota(workspace_id, doc_size_mb)
            except Exception as quota_error:
                logger.error(f"Storage quota check failed: {quota_error}")
                raise ValueError(f"Cannot process documents: {quota_error}")

        # Update progress
        callback(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 90, "message": "Embedding and uploading documents"},
        )

        # Run ingestion and upload in parallel
        await self._process_documents_parallel(docs, os_repo)

        # Update KB statistics after successful ingestion
        await kb_repo.update_kb_statistics(UUID(kb_id))

        return {"status": AsyncTaskStatus.COMPLETED}

    async def ingest_from_files(
        self,
        session: AsyncSession,
        doc_ids: list[str],
        kb_id: str,
        callback: Callable,
        user_id: UUID | None = None,
        workspace_id: UUID | None = None,
    ) -> dict[str, Any]:
        """
        Ingest documents from uploaded files

        Args:
            session: Database session
            doc_ids: List of document IDs to ingest
            kb_id: Knowledge base ID
            callback: Progress callback function
            user_id: User ID (optional)
            workspace_id: Workspace ID (optional)

        Returns:
            Dictionary with ingestion status
        """
        # Update progress
        callback(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 30, "message": "Ingesting files"},
        )

        # Get documents and read files
        kb_repo = KBRepository(session)
        docs_to_ingest = await kb_repo.get_documents(UUID(kb_id), doc_ids)
        documents, docs_to_ingest = await file_reader.read(docs_to_ingest)

        if len(documents) == 0:
            return {"status": AsyncTaskStatus.COMPLETED}

        # Update document status
        for doc_to_ingest in docs_to_ingest:
            doc_to_ingest.embed_status = AsyncTaskStatus.COMPLETED

        await kb_repo.update_documents(docs_to_ingest)

        # Add workspace metadata
        self._add_workspace_metadata(documents, workspace_id)

        # Skip quota check for presigned uploads (already validated in use case layer)
        # Only check quota for URL-based ingestion where size is unknown until crawl
        skip_quota_check = all(
            hasattr(doc, "id") and doc.id is not None for doc in docs_to_ingest
        )

        # Update progress
        if not skip_quota_check:
            callback(
                state=AsyncTaskStatus.IN_PROGRESS,
                meta={"progress": 85, "message": "Checking storage quota"},
            )

        if not skip_quota_check:
            # Calculate document sizes and check quota (for URL-based ingestion)
            doc_size_mb = 0
            if documents and workspace_id:
                doc_size_mb = self._calculate_documents_size_mb(documents)
                try:
                    await self._check_and_update_storage_quota(
                        workspace_id, doc_size_mb
                    )
                except Exception as quota_error:
                    logger.error(f"Storage quota check failed: {quota_error}")
                    raise ValueError(f"Cannot process documents: {quota_error}")

        # Update progress
        callback(
            state=AsyncTaskStatus.IN_PROGRESS,
            meta={"progress": 90, "message": "Chunking and storing in vector database"},
        )

        # Process documents through ingestion pipeline
        await self._ingest_to_vector_store(documents)

        # Update KB statistics after successful ingestion
        await kb_repo.update_kb_statistics(UUID(kb_id))

        return {"status": AsyncTaskStatus.COMPLETED}

    def _add_workspace_metadata(self, docs: list, workspace_id: UUID | None) -> None:
        """Add workspace_id metadata to documents for filtering"""

        if workspace_id:
            for doc in docs:
                doc.metadata["workspace_id"] = str(workspace_id)

    async def _process_documents_parallel(self, docs: list, os_repo) -> None:
        """Process documents in parallel: vector store ingestion and object storage upload"""

        async def ingest_to_vector_store():
            await self._ingest_to_vector_store(docs)

        async def upload_to_object_storage():
            for doc in docs:
                await os_repo.upload_bytes(
                    data=doc.text.encode(),
                    object_name=doc.metadata.get("object_name", ""),
                    bucket_name=settings.KB_BUCKET,
                )

        # Run both operations in parallel
        await asyncio.gather(
            ingest_to_vector_store(),
            upload_to_object_storage(),
        )

    async def _ingest_to_vector_store(self, documents: list) -> None:
        """Ingest documents into vector store using ingestion pipeline"""

        vector_store = await self.vector_store.get_vector_store()

        pipeline = IngestionPipeline(
            transformations=[self.chunker, self.vector_store.embed_model_doc],
            vector_store=vector_store,
        )

        await pipeline.arun(documents=documents, show_progress=True)

    async def _check_and_update_storage_quota(
        self, workspace_id: UUID, size_mb: float
    ) -> None:
        """Check storage quota and update if within limits"""
        workspace_owner_id = await self._get_workspace_owner_id(workspace_id)
        if not workspace_owner_id:
            raise ValueError(f"Workspace {workspace_id} not found")

        # Check if adding this size would exceed quota
        if await self.quota_service.check_quota(
            workspace_owner_id, QuotaResourceType.KB_STORAGE, size_mb
        ):
            raise ValueError(
                f"KB storage quota exceeded. Cannot add {size_mb}MB of documents."
            )

        # Update storage quota
        await self.quota_service.update_quota(
            workspace_owner_id, QuotaResourceType.KB_STORAGE, size_mb
        )
