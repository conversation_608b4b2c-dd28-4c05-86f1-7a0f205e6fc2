from llama_index.core.vector_stores.types import (
    Fi<PERSON><PERSON><PERSON><PERSON>,
    MetadataFilter,
    MetadataFilters,
)
from qdrant_client.models import FieldCondition, Filter, FilterSelector, MatchValue
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.exceptions.kb_exceptions import DocumentDeletionError
from app.logger import logger
from app.repositories.object_storage.provider import get_object_storage_repository
from app.services.base_vector_store import BaseVectorStore


class KBService:
    """
    Service layer for Knowledge Base vector store operations.

    Handles low-level interactions with the vector database for document storage,
    retrieval, and deletion. This service focuses on vector embeddings and
    similarity search operations, abstracting the complexity of vector store
    interactions from higher-level business logic.
    """

    def __init__(self, vector_store: BaseVectorStore, session: AsyncSession):
        """
        Initialize the KBService with required dependencies.

        Args:
            vector_store: Vector store instance for document embeddings
            session: Database session for repository operations
        """
        self.session = session
        self.vector_store = vector_store

    async def delete_documents_by_document_id(self, document_id: str) -> bool:
        """
        Delete documents by document ID from vector store.

        Performs targeted deletion of vector embeddings for a specific document.
        This is a low-level operation that directly interacts with Qdrant.

        Args:
            document_id: Document identifier to delete from vector store

        Returns:
            True if deletion was successful

        Raises:
            DocumentDeletionError: If deletion fails due to vector store issues
        """
        try:
            collection_name = settings.QDRANT_COLLECTION_NAME

            # Delete vector points using document_id filter for precise targeting
            await self.vector_store.aqdrant_client.delete(
                collection_name=collection_name,
                points_selector=FilterSelector(
                    filter=Filter(
                        must=[
                            FieldCondition(
                                key="document_id",
                                match=MatchValue(value=document_id),
                            )
                        ]
                    )
                ),
                wait=True,  # Synchronous deletion to ensure completion
            )
            return True

        except Exception as e:
            logger.error(f"Error deleting documents by document_id: {str(e)}")
            raise DocumentDeletionError("Failed to delete documents by ID")

    async def delete_kb_documents(self, workspace_id: str, kb_id: str) -> None:
        """
        Delete all documents for a specific Knowledge Base from vector store and object storage.

        This method performs bulk deletion of all vector embeddings and files associated
        with a knowledge base. Used when deleting an entire KB.

        Args:
            workspace_id: Workspace identifier for scoping the deletion
            kb_id: Knowledge base identifier for the documents to delete

        Raises:
            DocumentDeletionError: If deletion fails in vector store or object storage
        """
        try:
            vector_store = await self.vector_store.get_vector_store()

            # Find all vector nodes belonging to this KB and workspace
            nodes_to_delete = await vector_store.aget_nodes(
                filters=MetadataFilters(
                    filters=[
                        MetadataFilter(
                            key="kb_id",
                            value=str(kb_id),
                        ),
                        MetadataFilter(
                            key="workspace_id",
                            value=str(workspace_id),
                        ),
                    ],
                    condition=FilterCondition.AND,  # Both conditions must match
                )
            )

            # Delete vector embeddings if any found
            if nodes_to_delete:
                node_ids_to_delete = [node.node_id for node in nodes_to_delete]
                await vector_store.adelete_nodes(node_ids=node_ids_to_delete)

                logger.info(
                    f"Deleted {len(node_ids_to_delete)} vector embeddings for KB {kb_id} "
                    f"in workspace {workspace_id}"
                )

            # Clean up associated files from object storage
            os_repo = get_object_storage_repository()
            await os_repo.delete_multiple_files(
                object_names=[f"{kb_id}/*"],  # Delete all KB files using prefix
                bucket_name=settings.KB_BUCKET,
            )

        except Exception as e:
            logger.error(f"Error deleting KB documents from vector store: {str(e)}")
            raise DocumentDeletionError("Failed to delete KB documents")
