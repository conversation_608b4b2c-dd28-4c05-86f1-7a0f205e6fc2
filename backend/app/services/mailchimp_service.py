"""
Mailchimp Service for user activity tracking and marketing automation.

This service handles:
- User synchronization with Mailchimp audience  
- Activity event tracking for marketing segmentation
- Automated email campaign triggers based on user behavior
- Background task queuing for async processing
"""

import hashlib
import logging
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

import httpx
from pydantic import BaseModel, ConfigDict, EmailStr

from app.core.config import settings

logger = logging.getLogger(__name__)


class MailchimpUserStatus(str, Enum):
    """Mailchimp subscriber status."""
    SUBSCRIBED = "subscribed"
    UNSUBSCRIBED = "unsubscribed"
    CLEANED = "cleaned"
    PENDING = "pending"


class UserActivity(str, Enum):
    """User activity types for marketing segmentation."""
    # Authentication Events
    USER_REGISTERED = "user_registered"
    USER_LOGIN = "user_login"
    FIRST_LOGIN = "first_login"
    
    # Onboarding Events
    ONBOARDING_STARTED = "onboarding_started"
    WORKSPACE_CREATED = "workspace_created"
    PROVIDER_CONNECTED = "provider_connected"
    ONBOARDING_COMPLETED = "onboarding_completed"
    ONBOARDING_SKIPPED = "onboarding_skipped"

    # Subscription Events
    TRIAL_STARTED = "trial_started"
    TRIAL_EXTENDED = "trial_extended"
    SUBSCRIPTION_CREATED = "subscription_created"
    SUBSCRIPTION_UPGRADED = "subscription_upgraded"
    SUBSCRIPTION_DOWNGRADED = "subscription_downgraded"
    SUBSCRIPTION_CANCELLED = "subscription_cancelled"
    SUBSCRIPTION_REACTIVATED = "subscription_reactivated"
    
    # Payment Events
    PAYMENT_SUCCESS = "payment_success"
    PAYMENT_FAILED = "payment_failed"
    PAYMENT_RETRIED = "payment_retried"
    PAYMENT_METHOD_UPDATED = "payment_method_updated"
    INVOICE_GENERATED = "invoice_generated"

class MailchimpUserData(BaseModel):
    """User data structure for Mailchimp."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    email: EmailStr
    full_name: str = ""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    status: MailchimpUserStatus = MailchimpUserStatus.SUBSCRIBED
    merge_fields: Dict[str, str] = {}
    tags: List[str] = []


class MailchimpActivityEvent(BaseModel):
    """Activity event structure for Mailchimp."""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    user_id: UUID
    email: EmailStr
    activity: UserActivity
    timestamp: datetime
    properties: Dict[str, Any] = {}


class MailchimpService:
    """Service for integrating with Mailchimp Marketing API."""
    
    def __init__(self):
        self.api_key = settings.MAILCHIMP_API_KEY
        self.server_prefix = settings.MAILCHIMP_SERVER_PREFIX
        self.audience_id = settings.MAILCHIMP_AUDIENCE_ID
        self.base_url = f"https://{self.server_prefix}.api.mailchimp.com/3.0"
        
        self.client = httpx.AsyncClient(
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=30.0
        )

    @property
    def is_enabled(self) -> bool:
        """Check if Mailchimp integration is enabled."""
        return settings.mailchimp_is_enabled
    
    def _is_rate_limit_error(self, response_text: str) -> bool:
        """Check if the error is due to Mailchimp rate limiting."""
        rate_limit_indicators = [
            "signed up to a lot of lists very recently",
            "too many signups",
            "rate limit",
            "temporarily blocked"
        ]
        return any(indicator in response_text.lower() for indicator in rate_limit_indicators)
    
    def _is_duplicate_user_error(self, response_text: str) -> bool:
        """Check if the error is due to user already existing."""
        duplicate_indicators = [
            "already a list member",
            "member exists",
            "duplicate"
        ]
        return any(indicator in response_text.lower() for indicator in duplicate_indicators)

    def _get_subscriber_hash(self, email: str) -> str:
        """Generate MD5 hash of email for Mailchimp subscriber ID."""
        if not email:
            raise ValueError("Email cannot be None or empty")
        return hashlib.md5(email.lower().encode()).hexdigest()

    async def sync_user(self, user_data: MailchimpUserData) -> bool:
        """
        Sync user data to Mailchimp audience.
        
        Args:
            user_data: User data to sync
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            subscriber_hash = self._get_subscriber_hash(user_data.email)
            
            # Use provided first/last name or extract from full_name
            if user_data.first_name is not None and user_data.last_name is not None:
                # Use explicitly provided first and last names
                first_name = user_data.first_name
                last_name = user_data.last_name
            else:
                # Extract from full_name as fallback
                name_parts = user_data.full_name.split() if user_data.full_name else []
                first_name = name_parts[0] if name_parts else ""
                last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
            
            # Build merge fields with name data
            merge_fields = {
                "FNAME": first_name,
                "LNAME": last_name,
                **user_data.merge_fields
            }
            
            # Remove empty name fields to avoid overwriting existing data in Mailchimp
            if not first_name and "FNAME" in merge_fields:
                del merge_fields["FNAME"]
            if not last_name and "LNAME" in merge_fields:
                del merge_fields["LNAME"]
            
            payload = {
                "email_address": user_data.email,
                "status": user_data.status.value,
                "merge_fields": merge_fields,
                "tags": user_data.tags
            }
            
            # Use PUT to create or update subscriber
            response = await self.client.put(
                f"{self.base_url}/lists/{self.audience_id}/members/{subscriber_hash}",
                json=payload
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Successfully synced user {user_data.email} to Mailchimp")
                return True
            elif response.status_code == 400:
                if self._is_rate_limit_error(response.text):
                    logger.warning(f"Mailchimp rate limit for user {user_data.email}: {response.text}")
                    # This is a rate limit, don't retry but consider it handled to avoid blocking the app
                    return True
                elif self._is_duplicate_user_error(response.text):
                    logger.info(f"User {user_data.email} already exists in Mailchimp audience")
                    return True
                else:
                    logger.error(f"Failed to sync user {user_data.email}: {response.status_code} - {response.text}")
                    return False
            else:
                logger.error(f"Failed to sync user {user_data.email}: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error syncing user {user_data.email} to Mailchimp: {str(e)}")
            return False

    async def track_activity(self, event: MailchimpActivityEvent) -> bool:
        """
        Track user activity event in Mailchimp.
        
        Args:
            event: Activity event to track
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.debug(f"Starting track_activity for {event.email} with activity {event.activity.value}")
            
            subscriber_hash = self._get_subscriber_hash(event.email)
            logger.debug(f"Generated subscriber hash: {subscriber_hash[:8]}...")
            
            # Add activity-based tags
            logger.debug(f"Getting activity tags for {event.activity.value} with properties: {event.properties}")
            tags_to_add = self._get_activity_tags(event.activity, event.properties)
            logger.debug(f"Generated tags: {tags_to_add}")
            
            if tags_to_add:
                logger.debug(f"Adding tags: {tags_to_add}")
                await self._add_tags(subscriber_hash, tags_to_add)
            
            # Update merge fields based on activity
            logger.debug(f"Getting merge fields for {event.activity.value}")
            merge_fields = self._get_activity_merge_fields(event.activity, event.properties)
            logger.debug(f"Generated merge fields: {merge_fields}")
            
            if merge_fields:
                logger.debug(f"Updating merge fields: {merge_fields}")
                await self._update_merge_fields(subscriber_hash, merge_fields)
            
            logger.info(f"Successfully tracked activity {event.activity.value} for user {event.email}")
            return True
            
        except Exception as e:
            logger.error(f"Error tracking activity {event.activity.value} for user {event.email}: {str(e)}", exc_info=True)
            return False

    async def _add_tags(self, subscriber_hash: str, tags: List[str]) -> bool:
        """Add tags to a subscriber."""
        try:
            # Filter out any None or non-string tags
            valid_tags = [tag for tag in tags if tag and isinstance(tag, str)]
            logger.debug(f"Processing tags: {tags} -> valid tags: {valid_tags}")
            
            if not valid_tags:
                logger.debug("No valid tags to add")
                return True
                
            payload = {
                "tags": [{"name": tag, "status": "active"} for tag in valid_tags]
            }
            
            response = await self.client.post(
                f"{self.base_url}/lists/{self.audience_id}/members/{subscriber_hash}/tags",
                json=payload
            )
            
            return response.status_code == 204
            
        except Exception as e:
            logger.error(f"Error adding tags: {str(e)}")
            return False

    async def _update_merge_fields(self, subscriber_hash: str, merge_fields: Dict[str, str]) -> bool:
        """Update merge fields for a subscriber."""
        try:
            payload = {"merge_fields": merge_fields}
            
            response = await self.client.patch(
                f"{self.base_url}/lists/{self.audience_id}/members/{subscriber_hash}",
                json=payload
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Error updating merge fields: {str(e)}")
            return False

    def _get_activity_tags(self, activity: UserActivity, properties: Dict[str, Any]) -> List[str]:
        """Generate tags based on user activity."""
        tags = []
        
        # Activity-based tags
        activity_tag_map = {
            # Authentication & Onboarding
            UserActivity.USER_REGISTERED: ["new_user", "registered"],
            UserActivity.FIRST_LOGIN: ["first_login", "activated"],
            UserActivity.USER_LOGIN: ["active_user"],
            UserActivity.ONBOARDING_STARTED: ["onboarding_in_progress"],
            UserActivity.WORKSPACE_CREATED: ["workspace_user"],
            UserActivity.PROVIDER_CONNECTED: ["cloud_connected"],
            UserActivity.ONBOARDING_COMPLETED: ["onboarded", "active_user"],
            UserActivity.ONBOARDING_SKIPPED: ["onboarding_skipped"],
            
            # Subscription Lifecycle
            UserActivity.TRIAL_STARTED: ["trial_user"],
            UserActivity.TRIAL_EXTENDED: ["trial_extended"],
            UserActivity.SUBSCRIPTION_CREATED: ["new_subscriber", "paid_user"],
            UserActivity.SUBSCRIPTION_UPGRADED: ["paid_user", "upgraded"],
            UserActivity.SUBSCRIPTION_DOWNGRADED: ["downgraded"],
            UserActivity.SUBSCRIPTION_CANCELLED: ["churned_user"],
            UserActivity.SUBSCRIPTION_REACTIVATED: ["reactivated_user", "paid_user"],
            
            # Payment & Billing
            UserActivity.PAYMENT_SUCCESS: ["payment_success"],
            UserActivity.PAYMENT_FAILED: ["payment_issues"],
            UserActivity.PAYMENT_RETRIED: ["payment_retry"],
            UserActivity.PAYMENT_METHOD_UPDATED: ["payment_updated"],
            UserActivity.INVOICE_GENERATED: ["billing_active"],
        }
        
        tags.extend(activity_tag_map.get(activity, []))
        
        # Cloud provider tags
        if "selected_provider" in properties:
            provider = properties["selected_provider"]
            if provider and isinstance(provider, str):
                tags.append(f"cloud_{provider.lower()}")
        
        return tags

    def _get_activity_merge_fields(self, activity: UserActivity, properties: Dict[str, Any]) -> Dict[str, str]:
        """Generate merge fields based on user activity."""
        merge_fields = {}
        
        # Update last activity
        merge_fields["LASTACTIVITY"] = datetime.now().isoformat()
        
        # Activity-specific fields
        current_time = datetime.now().isoformat()
        
        # Authentication & Onboarding
        if activity == UserActivity.USER_REGISTERED:
            merge_fields["SIGNUP_DATE"] = properties.get("signup_date", current_time)
            if "signup_method" in properties:
                merge_fields["SIGNUP_METHOD"] = properties["signup_method"]
        
        elif activity == UserActivity.FIRST_LOGIN:
            merge_fields["FIRST_LOGIN_DATE"] = current_time
            
        elif activity == UserActivity.ONBOARDING_COMPLETED:
            merge_fields["ONBOARD_DATE"] = current_time
            if "selected_provider" in properties:
                merge_fields["CLOUD_PROVIDER"] = properties["selected_provider"]
        
        elif activity == UserActivity.PROVIDER_CONNECTED:
            merge_fields["PROVIDER_CONNECTED_DATE"] = current_time
            if "provider_type" in properties:
                merge_fields["CLOUD_PROVIDER"] = properties["provider_type"]
        
        # Subscription Events
        elif activity == UserActivity.TRIAL_STARTED:
            merge_fields["TRIAL_START_DATE"] = current_time
            if "trial_length" in properties:
                merge_fields["TRIAL_LENGTH"] = str(properties["trial_length"])
        
        elif activity == UserActivity.SUBSCRIPTION_CREATED:
            merge_fields["SUBSCRIPTION_START"] = current_time
            if "plan_name" in properties:
                merge_fields["PLAN"] = properties["plan_name"]
            if "billing_cycle" in properties:
                merge_fields["BILLING_CYCLE"] = properties["billing_cycle"]
        
        elif activity == UserActivity.SUBSCRIPTION_UPGRADED:
            merge_fields["UPGRADE_DATE"] = current_time
            if "plan_name" in properties:
                merge_fields["PLAN"] = properties["plan_name"]
            if "previous_plan" in properties:
                merge_fields["PREVIOUS_PLAN"] = properties["previous_plan"]
        
        elif activity == UserActivity.SUBSCRIPTION_CANCELLED:
            merge_fields["CHURN_DATE"] = current_time
            if "cancellation_reason" in properties:
                merge_fields["CHURN_REASON"] = properties["cancellation_reason"]
        
        # Payment Events
        elif activity == UserActivity.PAYMENT_SUCCESS:
            merge_fields["LAST_PAYMENT_DATE"] = current_time
            if "amount" in properties:
                merge_fields["LAST_PAYMENT_AMOUNT"] = str(properties["amount"])
        
        elif activity == UserActivity.PAYMENT_FAILED:
            merge_fields["LAST_PAYMENT_FAILURE"] = current_time
            if "failure_reason" in properties:
                merge_fields["PAYMENT_FAILURE_REASON"] = properties["failure_reason"]

        return merge_fields

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Singleton instance
_mailchimp_service: Optional[MailchimpService] = None


async def get_mailchimp_service() -> MailchimpService:
    """Get or create Mailchimp service instance."""
    global _mailchimp_service
    
    if _mailchimp_service is None:
        _mailchimp_service = MailchimpService()
    
    return _mailchimp_service


class MailchimpIntegrationService:
    """
    High-level service for Mailchimp integration that handles all business logic
    and feature flags in one place.
    
    This service acts as a facade for all Mailchimp operations, providing:
    - Centralized feature flag checking
    - Consistent task queuing patterns
    - Unified logging and error handling
    - Development-friendly rate limit handling
    """
    
    def __init__(self):
        self.settings = settings
    
    @property
    def is_enabled(self) -> bool:
        """Check if Mailchimp integration is enabled."""
        return self.settings.mailchimp_is_enabled
    
    def sync_new_user_registration(
        self,
        user_id: str,
        email: str,
        full_name: str = "",
        signup_method: str = "google_oauth",
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        selected_provider: Optional[str] = None,
    ) -> bool:
        """
        Sync new user registration to Mailchimp if enabled.
        
        Returns:
            bool: True if task was queued, False if disabled
        """
        if not self.is_enabled:
            logger.debug("Mailchimp integration disabled, skipping user registration sync")
            return False
        
        from app.tasks.mailchimp_tasks import sync_new_user_registration
        
        sync_new_user_registration.delay(
            user_id=user_id,
            email=email,
            full_name=full_name,
            signup_method=signup_method,
            first_name=first_name,
            last_name=last_name,
            selected_provider=selected_provider,
        )
        
        logger.info(f"Queued Mailchimp sync for new user registration: {email}")
        return True
    
    def track_user_activity(
        self,
        user_id: str,
        email: str,
        activity: UserActivity,
        properties: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Track user activity in Mailchimp if enabled.
        
        Returns:
            bool: True if task was queued, False if disabled
        """
        if not self.is_enabled:
            logger.debug(f"Mailchimp integration disabled, skipping activity tracking: {activity.value}")
            return False
        
        from app.tasks.mailchimp_tasks import track_user_activity
        
        track_user_activity.delay(
            user_id=user_id,
            email=email,
            activity=activity.value,
            properties=properties or {},
        )
        
        logger.info(f"Queued Mailchimp activity tracking: {activity.value} for {email}")
        return True
    
    def track_onboarding_step(
        self,
        user_id: str,
        email: str,
        step: int,
        selected_provider: Optional[str] = None,
        is_completed: bool = False,
        is_skipped: bool = False,
    ) -> bool:
        """
        Track onboarding step in Mailchimp if enabled.
        
        Returns:
            bool: True if task was queued, False if disabled
        """
        if not self.is_enabled:
            logger.debug(f"Mailchimp integration disabled, skipping onboarding step tracking: step {step}")
            return False
        
        from app.tasks.mailchimp_tasks import track_onboarding_step
        
        track_onboarding_step.delay(
            user_id=user_id,
            email=email,
            step=step,
            selected_provider=selected_provider,
            is_completed=is_completed,
            is_skipped=is_skipped,
        )
        
        logger.info(f"Queued Mailchimp onboarding step tracking: step {step} for {email}")
        return True
    
    def track_subscription_event(
        self,
        user_id: str,
        email: str,
        event_type: str,
        plan_name: Optional[str] = None,
        amount: Optional[float] = None,
    ) -> bool:
        """
        Track subscription event in Mailchimp if enabled.
        
        Returns:
            bool: True if task was queued, False if disabled
        """
        if not self.is_enabled:
            logger.debug(f"Mailchimp integration disabled, skipping subscription event: {event_type}")
            return False
        
        from app.tasks.mailchimp_tasks import track_subscription_event
        
        track_subscription_event.delay(
            user_id=user_id,
            email=email,
            event_type=event_type,
            plan_name=plan_name,
            amount=amount,
        )
        
        logger.info(f"Queued Mailchimp subscription event tracking: {event_type} for {email}")
        return True
    
    def track_payment_activity(
        self,
        user_id: str,
        email: str,
        payment_event: str,
        amount: Optional[float] = None,
        currency: Optional[str] = None,
        payment_method: Optional[str] = None,
        transaction_id: Optional[str] = None,
        failure_reason: Optional[str] = None,
    ) -> bool:
        """Track payment and billing activities if enabled."""
        if not self.is_enabled:
            logger.debug(f"Mailchimp integration disabled, skipping payment activity tracking: {payment_event}")
            return False
        
        from app.tasks.mailchimp_tasks import track_payment_activity
        
        track_payment_activity.delay(
            user_id=user_id,
            email=email,
            payment_event=payment_event,
            amount=amount,
            currency=currency,
            payment_method=payment_method,
            transaction_id=transaction_id,
            failure_reason=failure_reason,
        )
        
        logger.info(f"Queued Mailchimp payment activity tracking: {payment_event} for {email}")
        return True
    
    def track_subscription_activity(
        self,
        user_id: str,
        email: str,
        subscription_event: str,
        plan_name: Optional[str] = None,
        amount: Optional[float] = None,
    ) -> bool:
        """Track subscription activities (alias for track_subscription_event)."""
        return self.track_subscription_event(
            user_id=user_id,
            email=email,
            event_type=subscription_event,
            plan_name=plan_name,
            amount=amount,
        )
    
    def track_cloud_connection_activity(
        self,
        user_id: str,
        email: str,
        connection_event: str,
        cloud_provider: str,
    ) -> bool:
        """Track cloud provider connection activities."""
        if not self.is_enabled:
            logger.debug(f"Mailchimp integration disabled, skipping cloud connection activity: {connection_event}")
            return False
        
        # Map connection events to user activities
        activity_map = {
            "created": UserActivity.PROVIDER_CONNECTED,
            "updated": UserActivity.PROVIDER_CONNECTED,
        }
        
        activity = activity_map.get(connection_event)
        if not activity:
            logger.warning(f"Unknown cloud connection event: {connection_event}")
            return False
        
        return self.track_user_activity(
            user_id=user_id,
            email=email,
            activity=activity,
            properties={"provider_type": cloud_provider, "connection_event": connection_event},
        )

# Singleton instance for easy access
mailchimp_integration = MailchimpIntegrationService()