from typing import Literal

from pydantic import BaseModel, Field


class ExtractionDecision(BaseModel):
    """Model for a memory node extraction decision."""

    should_extract: bool = Field(description="Whether to start memory extraction")


class MemoryNode(BaseModel):
    """Model for a memory node extracted from a conversation."""

    task: str | None = Field(
        default=None,
        description="Specific tool execution objective or problem solved",
    )
    solution: str | None = Field(
        default=None,
        description="Exact tool call sequence with parameters and error recovery steps",
    )
    context: str | None = Field(
        default=None,
        description="Put the task (on your plan) you are working on here",
    )


class MemoriesExtraction(BaseModel):
    """Model for a memory node output."""

    memory_nodes: list[MemoryNode] = Field(
        default=[], description="List of memory nodes extracted from the conversation."
    )


class CombinedNode(BaseModel):
    """Model for a synthesized memory node."""

    combined_from: list[str] = Field(
        description="IDs of memory nodes that were combined."
    )
    new_node: MemoryNode = Field(description="The resulting synthesized memory node.")


class MemoryEvolution(BaseModel):
    """
    Model that determines how memory nodes should evolve through the system based
    on their relationships with other nodes.
    """

    should_evolve: bool = Field(
        description="Indicates if this memory requires evolution based on its context and relationships."
    )

    evolution_type: Literal["combine", "connect", "standalone"] | None = Field(
        default=None,
        description="The type of evolution to perform if should_evolve is True.",
    )

    combined_node: CombinedNode | None = Field(
        default=None,
        description="The new synthesized node when evolution_type is 'combine'. "
        "Only combine nodes with the same task where the combined solution "
        "is more valuable than individual solutions.",
    )

    connections: list[str] | None = Field(
        default=None,
        description="Memory IDs to connect with the current node (or the combined node).",
    )

    rationale: str | None = Field(
        default=None,
        description="Explanation for why this evolution decision was made. "
        "This should be a short and concise explanation of the reasoning "
        "behind the evolution decision.",
    )
