import json
import secrets
from datetime import UTC, datetime, timedelta
from io import BytesIO
import base64

import pyotp
import qrcode
from cryptography.fernet import <PERSON><PERSON><PERSON>
from sqlmodel import Session, select

from app.core.config import settings
from app.core.security import pwd_context
from app.logger import logger
from app.models.users import User, MFASession, SecurityEvent


class MFAService:
    """Multi-Factor Authentication service"""

    def __init__(self):
        self.cipher = Fernet(settings.mfa_encryption_key_bytes)

    def generate_secret(self) -> str:
        """Generate a new TOTP secret"""
        return pyotp.random_base32()

    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data for database storage"""
        return self.cipher.encrypt(data.encode()).decode()

    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data from database"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()

    def generate_qr_code(self, secret: str, user_email: str) -> str:
        """Generate QR code data URI for TOTP setup"""
        totp = pyotp.TOTP(secret)
        provisioning_uri = totp.provisioning_uri(
            name=user_email, issuer_name=settings.MFA_TOTP_ISSUER
        )

        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")

        # Convert to base64 data URI
        buffered = BytesIO()
        img.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()

        return f"data:image/png;base64,{img_str}"

    def verify_totp(self, secret: str, token: str) -> bool:
        """Verify TOTP token"""
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=settings.MFA_TOTP_WINDOW)
        except Exception as e:
            logger.error(f"TOTP verification error: {e}")
            return False

    def generate_backup_codes(self) -> list[str]:
        """Generate backup codes for account recovery"""
        codes = []
        for _ in range(settings.MFA_BACKUP_CODES_COUNT):
            code = "".join(
                secrets.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
                for _ in range(settings.MFA_BACKUP_CODE_LENGTH)
            )
            codes.append(code)
        return codes

    def hash_backup_codes(self, codes: list[str]) -> str:
        """Hash backup codes for secure storage"""
        hashed_codes = [pwd_context.hash(code) for code in codes]
        return json.dumps(hashed_codes)

    def verify_backup_code(
        self, code: str, encrypted_codes_json: str
    ) -> tuple[bool, str]:
        """
        Verify backup code and return updated codes list
        Returns (is_valid, updated_encrypted_codes_json)
        """
        try:
            # Decrypt the codes
            decrypted_codes_json = self.decrypt_data(encrypted_codes_json)
            hashed_codes = json.loads(decrypted_codes_json)

            for i, hashed_code in enumerate(hashed_codes):
                if pwd_context.verify(code, hashed_code):
                    # Remove used code
                    hashed_codes.pop(i)
                    updated_json = json.dumps(hashed_codes)
                    return True, self.encrypt_data(updated_json)

            return False, encrypted_codes_json

        except Exception as e:
            logger.error(f"Backup code verification error: {e}")
            return False, encrypted_codes_json

    def create_mfa_session(
        self,
        session: Session,
        user: User,
        ip_address: str | None = None,
        user_agent: str | None = None,
    ) -> str:
        """Create temporary MFA session during login process"""
        session_token = secrets.token_urlsafe(32)
        expires_at = datetime.now(UTC) + timedelta(
            minutes=settings.MFA_SESSION_EXPIRE_MINUTES
        )

        mfa_session = MFASession(
            user_id=user.id,
            session_token=session_token,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent,
        )

        session.add(mfa_session)
        session.commit()

        return session_token

    def verify_mfa_session(
        self, session: Session, session_token: str
    ) -> MFASession | None:
        """Verify and retrieve MFA session"""
        mfa_session = session.exec(
            select(MFASession).where(
                MFASession.session_token == session_token,
                MFASession.expires_at > datetime.now(UTC),
                MFASession.is_verified == False,
            )
        ).first()

        return mfa_session

    def complete_mfa_session(self, session: Session, mfa_session: MFASession):
        """Mark MFA session as verified"""
        mfa_session.is_verified = True
        session.add(mfa_session)
        session.commit()

    def cleanup_expired_sessions(self, session: Session):
        """Clean up expired MFA sessions"""
        expired_sessions = session.exec(
            select(MFASession).where(MFASession.expires_at <= datetime.now(UTC))
        ).all()

        for expired_session in expired_sessions:
            session.delete(expired_session)

        session.commit()

    def log_security_event(
        self,
        session: Session,
        user_id: str | None,
        event_type: str,
        success: bool,
        ip_address: str | None = None,
        user_agent: str | None = None,
        details: dict | None = None,
    ):
        """Log security events for auditing"""
        security_event = SecurityEvent(
            user_id=user_id,
            event_type=event_type,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            details=json.dumps(details) if details else None,
        )

        session.add(security_event)
        session.commit()

        # Also log to application logger
        level = "info" if success else "warning"
        getattr(logger, level)(
            f"Security Event: {event_type} - User: {user_id} - Success: {success}"
        )

    def is_account_locked(self, user: User) -> bool:
        """Check if user account is locked due to failed attempts"""
        if user.locked_until is None:
            return False
        return user.locked_until > datetime.now(UTC)

    def lock_account(self, session: Session, user: User):
        """Lock user account after failed attempts"""
        lockout_duration = timedelta(minutes=settings.LOCKOUT_DURATION_MINUTES)
        user.locked_until = datetime.now(UTC) + lockout_duration
        user.failed_login_attempts = 0  # Reset counter
        session.add(user)
        session.commit()

    def reset_failed_attempts(self, session: Session, user: User):
        """Reset failed login attempts on successful auth"""
        user.failed_login_attempts = 0
        user.locked_until = None
        session.add(user)
        session.commit()

    def increment_failed_attempts(self, session: Session, user: User):
        """Increment failed login attempts"""
        user.failed_login_attempts += 1

        if user.failed_login_attempts >= settings.MAX_LOGIN_ATTEMPTS:
            self.lock_account(session, user)
        else:
            session.add(user)
            session.commit()


# Global instance
mfa_service = MFAService()
