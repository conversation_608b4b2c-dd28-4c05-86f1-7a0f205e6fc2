import time
from uuid import <PERSON>UI<PERSON>

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.config import settings
from app.logger import logger
from app.modules.multi_agents.tools.query_sandbox.schema import (
    ListTablesResult,
    QueryRequest,
    QueryResult,
    SandboxTableInfo,
)


class QuerySandboxService:
    """Service for querying CSV data in the sandbox database."""

    def __init__(self):
        self.engine = create_async_engine(
            str(settings.SANDBOX_DATABASE_URI),
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=300,
            pool_pre_ping=True,
            echo=False,
        )

    async def execute_query(
        self, conversation_id: UUID, query_request: QueryRequest
    ) -> QueryResult:
        """Execute a SQL query against the sandbox database."""
        start_time = time.time()

        try:
            async with AsyncSession(self.engine, expire_on_commit=False) as session:  # type: ignore
                # Add conversation filter to prevent access to other conversation's tables
                table_prefix = f"{conversation_id}_"
                if (
                    not query_request.sql_query.strip()
                    .upper()
                    .startswith(("SELECT", "WITH"))
                ):
                    return QueryResult(
                        success=False,
                        columns=[],
                        rows=[],
                        row_count=0,
                        execution_time=time.time() - start_time,
                        message="Only SELECT queries are allowed for security",
                    )

                # Basic validation - ensure query only accesses conversation's tables
                sql_lower = query_request.sql_query.lower()
                if "conversation_" in sql_lower and not sql_lower.startswith(
                    f"select * from {table_prefix}"
                ):
                    # More complex validation could be added here
                    pass

                # Execute the query
                result = await session.exec(text(query_request.sql_query))  # type: ignore

                # Get column names
                if result:
                    columns = list(result.keys()) if hasattr(result, "keys") else []
                    rows = [list(row) for row in result]
                    row_count = len(rows)
                else:
                    columns = []
                    rows = []
                    row_count = 0

                # Limit rows if necessary
                if row_count > query_request.max_rows:
                    rows = rows[: query_request.max_rows]
                    row_count = query_request.max_rows

                execution_time = time.time() - start_time

                return QueryResult(
                    success=True,
                    columns=columns,
                    rows=rows,
                    row_count=row_count,
                    execution_time=execution_time,
                )

        except Exception as e:
            logger.error(f"Error executing query: {e}")
            return QueryResult(
                success=False,
                columns=[],
                rows=[],
                row_count=0,
                execution_time=time.time() - start_time,
                message=str(e),
            )

    async def list_tables(self, conversation_id: UUID) -> ListTablesResult:
        """List all tables for the given conversation in the sandbox database."""
        try:
            async with AsyncSession(self.engine, expire_on_commit=False) as session:  # type: ignore
                # Query to get all tables with conversation prefix
                query = text("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name LIKE :prefix
                    ORDER BY table_name
                """)

                table_prefix = f"{conversation_id}_%"
                result = await session.exec(query, {"prefix": table_prefix})  # type: ignore

                tables_info = []
                for row in result:
                    table_name = row[0]
                    # Get table info for each table
                    table_info = await self._get_table_details(session, table_name)  # type: ignore
                    if table_info:
                        tables_info.append(table_info)

                return ListTablesResult(
                    tables=tables_info, total_tables=len(tables_info)
                )

        except Exception as e:
            logger.error(f"Error listing tables: {e}")
            return ListTablesResult(tables=[], total_tables=0)

    async def get_table_info(
        self, conversation_id: UUID, table_name: str
    ) -> SandboxTableInfo | None:
        """Get detailed information about a specific table."""
        try:
            # Validate table belongs to conversation
            if not table_name.startswith(f"{conversation_id}_"):
                return None

            async with AsyncSession(self.engine, expire_on_commit=False) as session:  # type: ignore
                return await self._get_table_details(session, table_name)  # type: ignore

        except Exception as e:
            logger.error(f"Error getting table info for {table_name}: {e}")
            return None

    async def _get_table_details(
        self, session: AsyncSession, table_name: str
    ) -> SandboxTableInfo | None:  # type: ignore
        """Get detailed information about a table."""
        try:
            # Get column information
            columns_query = text("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = :table_name
                ORDER BY ordinal_position
            """)

            columns_result = await session.exec(
                columns_query, {"table_name": table_name}
            )  # type: ignore
            columns = [
                {
                    "name": row[0],
                    "type": row[1],
                    "nullable": row[2] == "YES",
                    "default": row[3],
                }
                for row in columns_result
            ]

            # Get approximate row count
            count_query = text(f"SELECT COUNT(*) FROM {table_name}")
            count_result = await session.exec(count_query)  # type: ignore
            row_count = count_result.first()[0] if count_result.first() else 0

            return SandboxTableInfo(
                table_name=table_name,
                column_count=len(columns),
                row_count=row_count,
                columns=columns,
            )

        except Exception as e:
            logger.error(f"Error getting table details for {table_name}: {e}")
            return None
