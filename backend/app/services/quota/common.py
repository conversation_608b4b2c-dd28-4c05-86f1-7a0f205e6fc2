from typing import <PERSON><PERSON><PERSON><PERSON>
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.circuit_breaker import (
    CircuitBreakerConfig,
    CircuitBreakerOpenError,
    circuit_breaker_registry,
)
from app.exceptions.legacy import TokenTrackingError
from app.exceptions.quota_exceptions import (
    QuotaContextResolutionError,
    QuotaNotFoundError,
    QuotaUpdateError,
)
from app.logger import logger
from app.models import UsageQuota
from app.modules.payment.schema import CustomerQuotaResponse
from app.repositories.quota import QuotaRepository
from app.repositories.users import UserRepository
from app.services.payment_service import PaymentClient, PaymentService


class QuotaContext(NamedTuple):
    """Immutable context data for quota operations"""

    owner_id: UUID
    user_quota: CustomerQuotaResponse
    quota_record: UsageQuota
    user_id: UUID


class BaseQuotaService:
    """
    Base class providing common quota operations.

    This class contains shared functionality for quota context resolution
    and user quota management used across different quota services.
    """

    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.payment_client = PaymentClient()
        self.payment_service = PaymentService(self.payment_client)
        self.user_repository = UserRepository(self.async_session)
        self.quota_repository = QuotaRepository(self.async_session)

    async def _call_payment_service_with_resilience(
        self, func_name: str, func, *args, **kwargs
    ):
        """
        Call payment service function with circuit breaker resilience.

        Args:
            func_name: Name for the circuit breaker
            func: Async function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Result of the function call

        Raises:
            CircuitBreakerOpenError: If circuit is open
            Exception: Original exception if call fails
        """
        try:
            # Use circuit breaker for external service calls
            config = CircuitBreakerConfig(
                failure_threshold=5,
                recovery_timeout=60,
                success_threshold=3,
                timeout=30.0,
            )
            breaker = circuit_breaker_registry.get_or_create(
                f"payment_service_{func_name}", config
            )

            return await breaker.call(func, *args, **kwargs)

        except CircuitBreakerOpenError:
            logger.warning(f"Circuit breaker is open for payment service {func_name}")
            raise QuotaUpdateError(
                f"Payment service temporarily unavailable for {func_name}"
            )
        except Exception as e:
            logger.error(f"Payment service call failed for {func_name}: {str(e)}")
            raise

    async def _resolve_quota_context(self, user_id: UUID, current_workspace_id: UUID | None = None) -> QuotaContext:
        """Resolve quota context by determining the actual quota owner."""
        try:
            # Get the user with workspace relationships loaded
            user = await self.user_repository.get_user_with_workspaces(user_id)
            if not user:
                raise TokenTrackingError(f"User {user_id} not found")

            # Determine the actual quota owner (user vs workspace owner)
            if len(user.own_workspaces) == 0:
                # Check if user is a member of any workspace (sub-user)
                if len(user.workspaces) > 0:
                    # Use current_workspace_id if provided to find the correct workspace
                    if current_workspace_id:
                        # Find the specific workspace the user is currently in
                        target_workspace = None
                        for user_workspace in user.workspaces:
                            if user_workspace.workspace_id == current_workspace_id:
                                target_workspace = user_workspace.workspace
                                break

                        if target_workspace:
                            owner_id = target_workspace.owner_id
                        else:
                            # Fallback to first workspace if current_workspace_id not found
                            owner_id = user.workspaces[0].workspace.owner_id
                    else:
                        # Fallback to first workspace if no current_workspace_id provided
                        owner_id = user.workspaces[0].workspace.owner_id

                    user_quota = await self._call_payment_service_with_resilience(
                        f"get_user_quota_{owner_id}",
                        self.payment_service.get_user_quota,
                        owner_id,
                    )
                    quota_record = await self._get_user_quota(owner_id)
                else:
                    # User has no workspaces at all (new user during onboarding)
                    # Treat as owner who can create their first workspace
                    owner_id = user_id
                    user_quota = await self._call_payment_service_with_resilience(
                        f"get_user_quota_{user_id}",
                        self.payment_service.get_user_quota,
                        user_id,
                    )
                    quota_record = await self._get_user_quota(user_id)
            else:
                # User is the owner
                owner_id = user_id
                user_quota = await self._call_payment_service_with_resilience(
                    f"get_user_quota_{user_id}",
                    self.payment_service.get_user_quota,
                    user_id,
                )
                quota_record = await self._get_user_quota(user_id)

            return QuotaContext(
                owner_id=owner_id,
                user_quota=user_quota,
                quota_record=quota_record,
                user_id=user_id,
            )

        except Exception as e:
            logger.error(
                f"Failed to resolve quota context for user {user_id}: {str(e)}"
            )
            raise QuotaContextResolutionError(
                f"Failed to resolve quota context: {str(e)}"
            )

    async def _get_user_quota(self, user_id: UUID) -> UsageQuota:
        """Get or create user quota record."""
        try:
            quota = await self.quota_repository.get_user_quota(user_id)
            if quota:
                return quota

            # Quota doesn't exist - create it on-demand
            logger.info(
                f"UsageQuota record not found for user {user_id}, creating on-demand"
            )
            try:
                return await self.quota_repository.create_user_quota(user_id)
            except Exception as create_error:
                logger.error(
                    f"Failed to create quota on-demand for user {user_id}: {str(create_error)}"
                )
                raise QuotaUpdateError(
                    f"Failed to create user quota: {str(create_error)}"
                )

        except QuotaNotFoundError:
            # Re-raise specific quota errors without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to get quota for user {user_id}: {str(e)}")
            raise QuotaUpdateError(f"Failed to get user quota: {str(e)}")
