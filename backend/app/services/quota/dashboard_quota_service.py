from uuid import UUID

from app.core.config import settings
from app.logger import logger
from app.models import UsageQuota

from .common import BaseQuotaService


class DashboardQuotaService(BaseQuotaService):
    """
    Specialized service for dashboard overview data aggregation.

    This service combines quota information from multiple sources to provide
    comprehensive dashboard data for users.
    """

    def _format_comprehensive_quota_data(
        self, user_quota, quota_record: UsageQuota
    ) -> dict:
        """Format comprehensive quota data for dashboard display."""
        # Get quota limits from payment service with defaults
        premium_limit: int = settings.DEFAULT_USER_QUOTA_LIMIT
        workspaces_limit: int = 1
        members_limit: int = 1
        scheduled_tasks_limit: int = 0
        kb_storage_limit: int = 0

        if user_quota.quota_definition:
            if user_quota.quota_definition.max_premium_credits is not None:
                premium_limit = user_quota.quota_definition.max_premium_credits
            workspaces_limit = user_quota.quota_definition.max_workspaces or 1
            members_limit = user_quota.quota_definition.max_members_per_workspace or 1
            scheduled_tasks_limit = user_quota.quota_definition.max_scheduled_tasks or 0
            kb_storage_limit = user_quota.quota_definition.max_kb_storage or 0

        # Calculate premium quota details
        premium_used = quota_record.credit_used
        premium_remaining = max(0, premium_limit - premium_used)
        premium_percentage = (
            round((premium_used / premium_limit * 100), 1) if premium_limit > 0 else 0.0
        )

        # Calculate resource quotas with remaining
        workspaces_remaining = max(0, workspaces_limit - quota_record.workspaces_used)
        members_remaining = max(0, members_limit - quota_record.members_used)
        scheduled_tasks_remaining = max(
            0, scheduled_tasks_limit - quota_record.scheduled_tasks_used
        )
        kb_storage_remaining = max(0.0, kb_storage_limit - quota_record.kb_storage_used)

        return {
            # Resource usage and limits
            "workspaces_used": quota_record.workspaces_used,
            "workspaces_limit": workspaces_limit,
            "workspaces_remaining": workspaces_remaining,
            "members_used": quota_record.members_used,
            "members_limit": members_limit,
            "members_remaining": members_remaining,
            "scheduled_tasks_used": quota_record.scheduled_tasks_used,
            "scheduled_tasks_limit": scheduled_tasks_limit,
            "scheduled_tasks_remaining": scheduled_tasks_remaining,
            # Knowledge base storage (rounded to 2 decimal places)
            "kb_storage_used": round(quota_record.kb_storage_used, 2),
            "kb_storage_limit": kb_storage_limit,
            "kb_storage_remaining": round(kb_storage_remaining, 2),
            # Premium message quota
            "premium_messages_used": premium_used,
            "premium_messages_limit": premium_limit,
            "premium_messages_remaining": premium_remaining,
            # Calculate premium usage percentage
            "premium_usage_percentage": premium_percentage,
        }

    async def get_dashboard_overview(
        self, user_id: UUID, current_workspace_id: UUID | None = None
    ) -> dict:
        """
        Get comprehensive dashboard overview for a user.

        Args:
            user_id: ID of the user
            current_workspace_id: ID of the current workspace for proper quota resolution

        Returns:
            Dictionary containing all resource quotas, usage data, and calculated metrics
        """
        try:
            # Get quota context
            context = await self._resolve_quota_context(user_id, current_workspace_id)

            # Format comprehensive data
            dashboard_data = self._format_comprehensive_quota_data(
                context.user_quota, context.quota_record
            )

            logger.info(f"Retrieved dashboard overview for user {user_id}")
            return dashboard_data

        except Exception as e:
            logger.error(
                f"Failed to get dashboard overview for user {user_id}: {str(e)}"
            )
            raise

    async def get_usage_summary(self, user_id: UUID) -> dict:
        """
        Get high-level usage summary for quick status checks.

        Args:
            user_id: ID of the user

        Returns:
            Dictionary containing usage summary metrics
        """
        try:
            dashboard_data = await self.get_dashboard_overview(user_id)

            # Calculate overall resource usage status
            resource_utilization = {
                "workspaces": (
                    dashboard_data["workspaces_used"]
                    / dashboard_data["workspaces_limit"]
                    * 100
                )
                if dashboard_data["workspaces_limit"] > 0
                else 0,
                "members": (
                    dashboard_data["members_used"]
                    / dashboard_data["members_limit"]
                    * 100
                )
                if dashboard_data["members_limit"] > 0
                else 0,
                "scheduled_tasks": (
                    dashboard_data["scheduled_tasks_used"]
                    / dashboard_data["scheduled_tasks_limit"]
                    * 100
                )
                if dashboard_data["scheduled_tasks_limit"] > 0
                else 0,
                "kb_storage": (
                    dashboard_data["kb_storage_used"]
                    / dashboard_data["kb_storage_limit"]
                    * 100
                )
                if dashboard_data["kb_storage_limit"] > 0
                else 0,
            }

            # Identify resources that are running low (>80% utilized)
            high_usage_resources = [
                resource
                for resource, usage in resource_utilization.items()
                if usage > 80
            ]

            summary = {
                "resource_utilization": {
                    k: round(v, 1) for k, v in resource_utilization.items()
                },
                "high_usage_resources": high_usage_resources,
                "premium_usage_percentage": dashboard_data["premium_usage_percentage"],
                "total_resources_monitored": 4,  # workspaces, members, tasks, storage
            }

            logger.info(f"Retrieved usage summary for user {user_id}")
            return summary

        except Exception as e:
            logger.error(f"Failed to get usage summary for user {user_id}: {str(e)}")
            raise
