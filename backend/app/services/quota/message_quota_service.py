from uuid import UUID

from app.core.config import settings
from app.exceptions.quota_exceptions import QuotaUpdateError
from app.logger import logger
from app.models import UsageQuota

from .common import BaseQuotaService


def _calculate_daily_limit(user_quota) -> int:
    """Pure function: Extract daily limit from quota definition"""
    if (
        user_quota.quota_definition
        and user_quota.quota_definition.max_daily_credits is not None
    ):
        return user_quota.quota_definition.max_daily_credits
    return 0


class MessageQuotaService(BaseQuotaService):
    """
    Specialized service for credit quota operations.

    This service focuses specifically on credit quota calculations and validation,
    providing a clean interface for credit-specific quota data.
    """

    def _format_credit_info(self, user_quota, quota_record: UsageQuota) -> dict:
        """Format credit quota data for COMPREHENSIVE format."""
        # Get quota limits from payment service with defaults
        premium_limit: int = settings.DEFAULT_USER_QUOTA_LIMIT
        daily_limit: int = 0

        if user_quota.quota_definition:
            if user_quota.quota_definition.max_premium_credits is not None:
                premium_limit = user_quota.quota_definition.max_premium_credits
            if user_quota.quota_definition.max_daily_credits is not None:
                daily_limit = user_quota.quota_definition.max_daily_credits

        # Calculate daily quota details
        daily_used = quota_record.daily_credit_used
        daily_remaining = max(0, daily_limit - daily_used) if daily_limit > 0 else 0

        # Calculate premium quota details
        premium_used = quota_record.credit_used
        premium_remaining = max(0, premium_limit - premium_used)

        # Calculate total available messages
        total_available = daily_remaining + premium_remaining

        return {
            "premium_credits_remaining": premium_remaining,
            "daily_credits_remaining": daily_remaining,
            "total_credits_remaining": total_available,
        }

    async def get_credits_info(
        self, user_id: UUID, current_workspace_id: UUID | None = None
    ) -> dict:
        """
        Get credit quota information for a user.

        Args:
            user_id: ID of the user
            current_workspace_id: ID of the current workspace for proper quota resolution

        Returns:
            Dictionary containing:
            - premium_credits_remaining: Number of premium credits left
            - daily_credits_remaining: Number of daily credits left
            - total_credits_remaining: Total credits available
        """
        try:
            # Get quota context
            context = await self._resolve_quota_context(user_id, current_workspace_id)

            # Format message quota data
            result = self._format_credit_info(context.user_quota, context.quota_record)

            logger.info(f"Retrieved message quota for user {user_id}: {result}")
            return result

        except Exception as e:
            logger.error(f"Failed to get message quota for user {user_id}: {str(e)}")
            raise

    async def check_message_availability(
        self, user_id: UUID, requested_messages: int = 1
    ) -> bool:
        """
        Check if user has enough message quota available.

        Args:
            user_id: ID of the user
            requested_messages: Number of messages being requested

        Returns:
            True if user has enough quota, False otherwise
        """
        try:
            # Get quota context to access limits
            context = await self._resolve_quota_context(user_id)

            # Get daily limit from payment service
            daily_limit = _calculate_daily_limit(context.user_quota)

            # Get premium limit from payment service
            premium_limit = settings.DEFAULT_USER_QUOTA_LIMIT
            if (
                context.user_quota.quota_definition
                and context.user_quota.quota_definition.max_premium_credits is not None
            ):
                premium_limit = context.user_quota.quota_definition.max_premium_credits

            # Use the atomic availability check from repository
            daily_available = await self.quota_repository.check_quota_availability(
                user_id, "daily_messages", requested_messages, daily_limit
            )
            if daily_available:
                return True

            # If daily quota is exhausted, check premium quota
            premium_available = await self.quota_repository.check_quota_availability(
                user_id, "messages", requested_messages, premium_limit
            )
            return premium_available

        except Exception as e:
            logger.error(
                f"Failed to check message availability for user {user_id}: {str(e)}"
            )
            # Default to False if there's an error to be safe
            return False

    async def check_out_of_messages(self, user_id: UUID) -> bool:
        """
        Check if the user has exceeded their usage quotas following priority order.

        Priority order (matching deduction logic):
        1. Check daily quota first (if configured)
        2. If daily quota has remaining capacity, user is NOT out of quota
        3. If daily quota is exhausted OR not configured, check premium quota

        Args:
            user_id: ID of the user

        Returns:
            True if the user is out of quota (no available quota remaining), False otherwise
        """
        try:
            context = await self._resolve_quota_context(user_id)

            # Get quota limits
            daily_limit = _calculate_daily_limit(context.user_quota)
            premium_limit = settings.DEFAULT_USER_QUOTA_LIMIT
            if (
                context.user_quota.quota_definition
                and context.user_quota.quota_definition.max_premium_credits is not None
            ):
                premium_limit = context.user_quota.quota_definition.max_premium_credits

            # Check daily quota first (highest priority)
            daily_used = context.quota_record.daily_credit_used
            daily_has_capacity = daily_limit > 0 and daily_used < daily_limit

            # If daily quota has capacity, user is not out of quota
            if daily_has_capacity:
                logger.info(
                    f"Quota check for user {user_id}: "
                    f"Daily quota has capacity: {daily_used}/{daily_limit}, user NOT out of quota"
                )
                return False

            # Daily quota is exhausted (or set to 0), check premium quota
            premium_used = context.quota_record.credit_used
            premium_exhausted = premium_limit > 0 and premium_used >= premium_limit

            # If daily quota is configured but exhausted, check premium as fallback
            out_of_quota = premium_exhausted

            logger.info(
                f"Quota check for user {user_id}: "
                f"Daily quota exhausted: {daily_used}/{daily_limit}, "
                f"Premium: {premium_used}/{premium_limit} (exhausted: {premium_exhausted}), "
                f"Out of quota: {out_of_quota}"
            )

            return out_of_quota

        except Exception as e:
            logger.error(f"Failed to check quota for user {user_id}: {str(e)}")
            # Default to not out of quota if there's an error
            return False

    async def reset_user_quota(self, user_id: UUID) -> None:
        """
        Reset user quota usage counters.

        Args:
            user_id: ID of the user

        Raises:
            QuotaUpdateError: If quota reset fails
        """
        try:
            from datetime import UTC, datetime

            context = await self._resolve_quota_context(user_id)
            reset_time = datetime.now(UTC)

            # Reset quota usage counters
            await self.quota_repository.reset_user_quota_timestamps(
                context.owner_id, reset_time
            )

            logger.info(f"Successfully reset quota for user {user_id}")

        except Exception as e:
            logger.error(f"Failed to reset quota for user {user_id}: {str(e)}")
            raise QuotaUpdateError(f"Failed to reset user quota: {str(e)}")

    async def reset_daily_quota(self) -> int:
        """
        Reset daily message quota for all users using batch operation.

        Returns:
            Number of users whose quotas were reset

        Raises:
            QuotaUpdateError: If batch reset fails
        """
        try:
            reset_count = await self.quota_repository.batch_reset_daily_quotas()
            logger.info(f"Successfully reset daily quotas for {reset_count} users")
            return reset_count

        except Exception as e:
            logger.error(f"Failed to reset daily quotas: {str(e)}")
            raise QuotaUpdateError(f"Failed to reset daily quotas: {str(e)}")

    async def create_user_quota(self, user_id: UUID) -> None:
        """
        Create initial quota record for a new user.

        Args:
            user_id: ID of the user

        Raises:
            QuotaUpdateError: If quota creation fails
        """
        try:
            # Create quota record directly using repository
            # Since this is for new users, we always create for the user_id itself
            await self.quota_repository.create_user_quota(user_id)
            logger.info(f"Successfully created quota record for user {user_id}")

        except Exception as e:
            logger.error(f"Failed to create quota for user {user_id}: {str(e)}")
            raise QuotaUpdateError(f"Failed to create user quota: {str(e)}")

    async def deduct_messages_atomic(
        self, user_id: UUID, message_count: int = 1
    ) -> dict:
        """
        Atomically deduct messages from user quota using priority order.

        Priority order:
        1. Daily quota (if available and not exhausted)
        2. Premium quota (if daily is exhausted)

        Args:
            user_id: ID of the user
            message_count: Number of messages to deduct

        Returns:
            Dictionary containing deduction results and remaining quotas

        Raises:
            InsufficientQuotaError: If user doesn't have enough quota
            QuotaUpdateError: If deduction fails
        """
        try:
            context = await self._resolve_quota_context(user_id)
            quota_record = context.quota_record

            # Check current daily quota availability
            daily_limit = _calculate_daily_limit(context.user_quota)
            daily_available = max(0, daily_limit - quota_record.daily_credit_used)

            # Get premium limit from quota definition
            premium_limit: int = settings.DEFAULT_USER_QUOTA_LIMIT
            if (
                context.user_quota.quota_definition
                and context.user_quota.quota_definition.max_premium_credits is not None
            ):
                premium_limit = context.user_quota.quota_definition.max_premium_credits

            messages_from_daily = min(message_count, daily_available)
            messages_from_premium = message_count - messages_from_daily

            # Check if we're already in a transaction to avoid nested transaction error
            if self.async_session.in_transaction():
                # Already in a transaction, perform operations directly
                logger.debug(
                    f"Already in transaction, performing direct quota deduction for user {user_id}"
                )

                # Deduct from daily quota first
                if messages_from_daily > 0:
                    await self.quota_repository.deduct_quota_atomic(
                        user_id, "daily_messages", messages_from_daily, daily_limit
                    )

                # Deduct remaining from premium quota
                if messages_from_premium > 0:
                    await self.quota_repository.deduct_quota_atomic(
                        user_id, "messages", messages_from_premium, premium_limit
                    )
            else:
                # Not in a transaction, create one
                logger.debug(
                    f"Creating new transaction for quota deduction for user {user_id}"
                )
                async with self.async_session.begin():
                    # Deduct from daily quota first
                    if messages_from_daily > 0:
                        await self.quota_repository.deduct_quota_atomic(
                            user_id, "daily_messages", messages_from_daily, daily_limit
                        )

                    # Deduct remaining from premium quota
                    if messages_from_premium > 0:
                        await self.quota_repository.deduct_quota_atomic(
                            user_id, "messages", messages_from_premium, premium_limit
                        )

            # Get updated quota information
            updated_quota = await self.get_credits_info(user_id)

            logger.info(
                f"Successfully deducted {message_count} messages for user {user_id}: "
                f"{messages_from_daily} from daily, {messages_from_premium} from premium"
            )

            return {
                "success": True,
                "messages_deducted": message_count,
                "daily_deducted": messages_from_daily,
                "premium_deducted": messages_from_premium,
                "remaining_quota": updated_quota,
            }

        except Exception as e:
            logger.error(f"Failed to deduct messages for user {user_id}: {str(e)}")
            raise
