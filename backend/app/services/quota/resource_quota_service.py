from enum import StrEnum
from uuid import UUID

from app.exceptions.quota_exceptions import (
    InsufficientQuotaError,
    QuotaContextResolutionError,
    QuotaNotFoundError,
    QuotaUpdateError,
)
from app.logger import logger
from app.models import UsageQuota

from .common import BaseQuotaService


class QuotaResourceType(StrEnum):
    """Enum for different types of quota resources."""

    TOKENS = "tokens"
    MESSAGES = "messages"
    WORKSPACES = "workspaces"
    MEMBERS = "members"
    SCHEDULED_TASKS = "scheduled_tasks"
    KB_STORAGE = "kb_storage"


class ResourceQuotaService(BaseQuotaService):
    """
    Specialized service for resource quota operations.

    This service handles workspaces, members, scheduled tasks, and knowledge base storage quotas.
    """

    def _format_resource_quota_data(self, user_quota, quota_record: UsageQuota) -> dict:
        """Format resource quota data."""
        # Get quota limits from payment service with defaults
        workspaces_limit: int = 1
        members_limit: int = 1
        scheduled_tasks_limit: int = 0
        kb_storage_limit: float = 0.0  # in MB

        if user_quota.quota_definition:
            workspaces_limit = user_quota.quota_definition.max_workspaces or 1
            members_limit = user_quota.quota_definition.max_members_per_workspace or 1
            scheduled_tasks_limit = user_quota.quota_definition.max_scheduled_tasks or 0
            kb_storage_limit = user_quota.quota_definition.max_kb_storage or 0

        # Calculate remaining resources (handle -1 as unlimited)
        workspaces_remaining = (
            -1
            if workspaces_limit == -1
            else max(0, workspaces_limit - quota_record.workspaces_used)
        )
        members_remaining = (
            -1
            if members_limit == -1
            else max(0, members_limit - quota_record.members_used)
        )
        scheduled_tasks_remaining = (
            -1
            if scheduled_tasks_limit == -1
            else max(0, scheduled_tasks_limit - quota_record.scheduled_tasks_used)
        )
        kb_storage_remaining = (
            -1.0
            if kb_storage_limit == -1
            else max(0.0, kb_storage_limit - quota_record.kb_storage_used)
        )

        return {
            "workspaces_used": quota_record.workspaces_used,
            "workspaces_limit": workspaces_limit,
            "workspaces_remaining": workspaces_remaining,
            "members_used": quota_record.members_used,
            "members_limit": members_limit,
            "members_remaining": members_remaining,
            "scheduled_tasks_used": quota_record.scheduled_tasks_used,
            "scheduled_tasks_limit": scheduled_tasks_limit,
            "scheduled_tasks_remaining": scheduled_tasks_remaining,
            "kb_storage_used": round(quota_record.kb_storage_used, 2),
            "kb_storage_limit": kb_storage_limit,
            "kb_storage_remaining": round(kb_storage_remaining, 2),
        }

    async def get_resource_quotas(self, user_id: UUID) -> dict:
        """
        Get resource quota information for a user.

        Args:
            user_id: ID of the user

        Returns:
            Dictionary containing resource usage and limits
        """
        try:
            context = await self._resolve_quota_context(user_id)
            resource_data = self._format_resource_quota_data(
                context.user_quota, context.quota_record
            )

            logger.info(f"Retrieved resource quotas for user {user_id}")
            return resource_data

        except Exception as e:
            logger.error(f"Failed to get resource quotas for user {user_id}: {str(e)}")
            raise

    async def check_resource_availability_atomic(
        self, user_id: UUID, resource_type: str, requested_amount: float = 1
    ) -> bool:
        """
        Check if user has enough resource quota available using atomic operations.

        Args:
            user_id: ID of the user
            resource_type: Type of resource to check
            requested_amount: Amount requested

        Returns:
            True if user has sufficient quota, False otherwise
        """
        try:
            context = await self._resolve_quota_context(user_id)

            # Get limits from payment service
            quota_definition = context.user_quota.quota_definition

            if resource_type == "workspaces":
                limit = quota_definition.max_workspaces if quota_definition else 1
            elif resource_type == "members":
                limit = (
                    quota_definition.max_members_per_workspace
                    if quota_definition
                    else 1
                )
            elif resource_type == "scheduled_tasks":
                limit = quota_definition.max_scheduled_tasks if quota_definition else 0
            elif resource_type == "kb_storage":
                limit = quota_definition.max_kb_storage if quota_definition else 0
            else:
                return False

            # Handle unlimited quota (-1)
            if limit == -1:
                return True

            # Use the resource availability check from repository
            return await self.quota_repository.check_resource_availability(
                user_id, resource_type, requested_amount, limit
            )

        except Exception as e:
            logger.error(
                f"Failed to check resource availability for user {user_id}: {str(e)}"
            )
            return False

    async def deduct_resource_atomic(
        self, user_id: UUID, resource_type: str, amount: float = 1
    ) -> dict:
        """
        Atomically deduct resource quota.

        Args:
            user_id: ID of the user
            resource_type: Type of resource ('workspaces', 'members', 'scheduled_tasks', 'kb_storage')
            amount: Amount to deduct

        Returns:
            Dictionary with deduction result

        Raises:
            InsufficientQuotaError: If user doesn't have enough quota
            QuotaUpdateError: If deduction fails
        """
        try:
            # Get quota context to access limits
            context = await self._resolve_quota_context(user_id)

            # Get limit from payment service
            quota_definition = context.user_quota.quota_definition

            if resource_type == "workspaces":
                limit = quota_definition.max_workspaces if quota_definition else 1
            elif resource_type == "members":
                limit = (
                    quota_definition.max_members_per_workspace
                    if quota_definition
                    else 1
                )
            elif resource_type == "scheduled_tasks":
                limit = quota_definition.max_scheduled_tasks if quota_definition else 0
            elif resource_type == "kb_storage":
                limit = quota_definition.max_kb_storage if quota_definition else 0
            else:
                raise QuotaUpdateError(f"Unsupported resource type: {resource_type}")

            # Handle unlimited quota (-1) - skip deduction check but still update usage
            if limit == -1:
                # For unlimited quota, we still track usage but don't enforce limits
                context = await self._resolve_quota_context(user_id)
                field_mappings = {
                    "workspaces": "workspaces_used",
                    "members": "members_used",
                    "scheduled_tasks": "scheduled_tasks_used",
                    "kb_storage": "kb_storage_used",
                }

                if resource_type in field_mappings:
                    field_name = field_mappings[resource_type]
                    current_value = getattr(context.quota_record, field_name)
                    new_value = max(0, current_value + amount)
                    updates = {field_name: new_value}
                    await self.quota_repository.update_quota_usage(
                        context.owner_id, updates
                    )
            else:
                # Use the new atomic resource deduction method
                await self.quota_repository.deduct_resource_atomic(
                    user_id, resource_type, amount, limit
                )

            return {
                "success": True,
                "resource_type": resource_type,
                "amount_deducted": amount,
            }

        except InsufficientQuotaError:
            raise
        except Exception as e:
            logger.error(f"Failed to deduct resource for user {user_id}: {str(e)}")
            raise QuotaUpdateError(f"Failed to deduct resource: {str(e)}")

    async def check_resource_availability(
        self, user_id: UUID, resource_type: str, requested_amount: float = 1
    ) -> bool:
        """
        Check if user has enough quota for a specific resource type.

        Args:
            user_id: ID of the user
            resource_type: Type of resource (workspaces, members, scheduled_tasks, kb_storage)
            requested_amount: Amount of resource being requested

        Returns:
            True if user has enough quota, False otherwise
        """
        try:
            resource_data = await self.get_resource_quotas(user_id)

            # Map resource types to data keys
            resource_mappings = {
                "workspaces": ("workspaces_used", "workspaces_limit"),
                "members": ("members_used", "members_limit"),
                "scheduled_tasks": ("scheduled_tasks_used", "scheduled_tasks_limit"),
                "kb_storage": ("kb_storage_used", "kb_storage_limit"),
            }

            if resource_type not in resource_mappings:
                logger.warning(f"Unknown resource type: {resource_type}")
                return False

            used_key, limit_key = resource_mappings[resource_type]
            current_used = resource_data.get(used_key, 0)
            current_limit = resource_data.get(limit_key, 0)

            # Handle unlimited quota (-1)
            if current_limit == -1:
                return True

            return (current_used + requested_amount) <= current_limit

        except Exception as e:
            logger.error(
                f"Failed to check resource availability for user {user_id}, "
                f"resource {resource_type}: {str(e)}"
            )
            # Default to False if there's an error to be safe
            return False

    async def check_quota(
        self,
        user_id: UUID,
        resource_type: QuotaResourceType | str,
        requested_amount: float = 1,
    ) -> bool:
        """
        Check if a user would exceed their quota for a specific resource type.

        Args:
            user_id: ID of the user
            resource_type: Type of resource (QuotaResourceType enum or legacy string)
            requested_amount: Amount of resource being requested (default: 1)

        Returns:
            True if the request would exceed quota, False otherwise
        """
        # Convert string to enum for backward compatibility
        if isinstance(resource_type, str):
            try:
                resource_type = QuotaResourceType(resource_type)
            except ValueError:
                logger.warning(f"Unknown resource type: {resource_type}")
                return False
        try:
            resource_quota = await self.get_resource_quotas(user_id)

            resource_map = {
                QuotaResourceType.WORKSPACES: {
                    "used": resource_quota["workspaces_used"],
                    "limit": resource_quota["workspaces_limit"],
                },
                QuotaResourceType.MEMBERS: {
                    "used": resource_quota["members_used"],
                    "limit": resource_quota["members_limit"],
                },
                QuotaResourceType.SCHEDULED_TASKS: {
                    "used": resource_quota["scheduled_tasks_used"],
                    "limit": resource_quota["scheduled_tasks_limit"],
                },
                QuotaResourceType.KB_STORAGE: {
                    "used": resource_quota["kb_storage_used"],
                    "limit": resource_quota["kb_storage_limit"],
                },
            }

            if resource_type not in resource_map:
                logger.warning(f"Unknown resource type: {resource_type}")
                return False

            resource = resource_map[resource_type]

            # Handle unlimited quota (-1)
            if resource["limit"] == -1:
                would_exceed = False
            else:
                would_exceed = (resource["used"] + requested_amount) > resource["limit"]

            logger.info(
                f"Quota check for user {user_id}, resource {resource_type}: "
                f"current: {resource['used']}, limit: {resource['limit']}, "
                f"requested: {requested_amount}, would_exceed: {would_exceed}"
            )

            return would_exceed

        except (QuotaContextResolutionError, QuotaNotFoundError):
            # Re-raise specific quota errors
            raise
        except Exception as e:
            logger.error(f"Failed to check resource quota for user {user_id}: {str(e)}")
            # Default to not exceeded if there's an error
            return False

    async def update_quota(
        self,
        user_id: UUID,
        resource_type: QuotaResourceType | str,
        amount: float,
    ) -> None:
        """
        Update resource quota by incrementing/decrementing usage.

        Args:
            user_id: ID of the user
            resource_type: Type of resource (QuotaResourceType enum or legacy string)
            amount: Amount to add/subtract from current usage

        Raises:
            QuotaContextResolutionError: If quota context cannot be resolved
            QuotaUpdateError: If quota update fails
        """
        # Convert string to enum for backward compatibility
        if isinstance(resource_type, str):
            try:
                resource_type = QuotaResourceType(resource_type)
            except ValueError:
                logger.warning(f"Unknown resource type: {resource_type}")
                raise QuotaUpdateError(f"Unknown resource type: {resource_type}")

        try:
            context = await self._resolve_quota_context(user_id)

            # Map resource types to database fields
            field_mappings = {
                QuotaResourceType.WORKSPACES: "workspaces_used",
                QuotaResourceType.MEMBERS: "members_used",
                QuotaResourceType.SCHEDULED_TASKS: "scheduled_tasks_used",
                QuotaResourceType.KB_STORAGE: "kb_storage_used",
            }

            if resource_type not in field_mappings:
                logger.warning(f"Unsupported resource type for update: {resource_type}")
                raise QuotaUpdateError(f"Unsupported resource type: {resource_type}")

            field_name = field_mappings[resource_type]
            current_value = getattr(context.quota_record, field_name)
            new_value = max(0, current_value + amount)  # Prevent negative values

            # Update the quota record using the repository
            updates = {field_name: new_value}
            await self.quota_repository.update_quota_usage(context.owner_id, updates)

            logger.info(
                f"Updated quota for user {user_id}, resource {resource_type}: "
                f"{current_value} + {amount} = {new_value}"
            )

        except (QuotaContextResolutionError, QuotaNotFoundError):
            # Re-raise specific quota errors
            raise
        except Exception as e:
            logger.error(
                f"Failed to update resource quota for user {user_id}, "
                f"resource {resource_type}: {str(e)}"
            )
            raise QuotaUpdateError(f"Failed to update resource quota: {str(e)}")
