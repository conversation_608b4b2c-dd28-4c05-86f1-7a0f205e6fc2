from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Literal
from uuid import UUI<PERSON>
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import TokenTrackingError
from app.logger import logger
from app.models import (
    TokenUsage,
    Workspace,
)
from app.repositories.users import UserRepository
from app.schemas.token_usage import (
    UsageOvertime,
    UsageOvertimeResponse,
)


class UsageOverTimeService:
    """
    Specialized service for usage over time analytics.

    This service handles time-series usage data for analytics dashboards
    and reporting functionality.
    """

    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.user_repository = UserRepository(self.async_session)

    async def get_usage_over_time(
        self,
        user_id: UUID,
        period: Literal["day", "week", "month", "year"] = "day",
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        current_workspace_id: UUID | None = None,
    ) -> UsageOvertimeResponse:
        """
        Get usage statistics over time with period grouping.

        Args:
            user_id: ID of the user
            period: Time period for grouping (day/week/month/year)
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering

        Returns:
            UsageOvertimeResponse with aggregated usage data
        """
        try:
            # Get all workspaces owned by this user
            workspaces_query = (
                select(Workspace.id)
                .where(Workspace.owner_id == user_id)
                .where(Workspace.is_deleted == False)
            )
            workspaces_result = await self.async_session.exec(workspaces_query)
            user_workspace_ids = list(workspaces_result)

            if not user_workspace_ids:
                logger.warning(f"User {user_id} doesn't own any workspaces")
                return UsageOvertimeResponse(
                    data=[],
                    period=period,
                    total_messages=0,
                    total_daily_messages=0,
                    total_premium_messages=0,
                )

            # Base query for token usage
            base_query = select(
                TokenUsage.created_at,
            ).where(col(TokenUsage.workspace_id).in_(user_workspace_ids))

            # Apply date filters if provided
            if start_date:
                base_query = base_query.where(TokenUsage.created_at >= start_date)
            if end_date:
                base_query = base_query.where(TokenUsage.created_at <= end_date)

            # Get all usage records for the period
            result = await self.async_session.exec(base_query)
            usage_records = result.all()

            # Define period grouping functions
            def get_period_start(
                date: datetime, period_type: Literal["day", "week", "month", "year"]
            ) -> datetime:
                if period_type == "day":
                    return date.replace(hour=0, minute=0, second=0, microsecond=0)
                elif period_type == "week":
                    # Start of week (Monday)
                    days_since_monday = date.weekday()
                    week_start = date.replace(
                        hour=0, minute=0, second=0, microsecond=0
                    ) - timedelta(days=days_since_monday)
                    return week_start
                elif period_type == "month":
                    return date.replace(
                        day=1, hour=0, minute=0, second=0, microsecond=0
                    )
                elif period_type == "year":
                    return date.replace(
                        month=1, day=1, hour=0, minute=0, second=0, microsecond=0
                    )
                else:
                    # Default to daily
                    return date.replace(hour=0, minute=0, second=0, microsecond=0)

            # Group usage by period
            period_usage = {}
            total_messages = 0
            total_daily_messages = 0
            total_premium_messages = 0

            for record in usage_records:
                created_at = record  # record is now a single datetime object

                # Get period start for grouping
                period_start = get_period_start(created_at, period)
                period_key = period_start

                # Initialize period data if not exists
                if period_key not in period_usage:
                    period_usage[period_key] = {
                        "daily_messages": 0,
                        "premium_messages": 0,
                    }

                # Update period data
                period_usage[period_key]["daily_messages"] += 1
                period_usage[period_key]["premium_messages"] += 0

                # Update totals
                total_messages += 1
                total_daily_messages += 1

            # Convert to response format
            usage_data = [
                UsageOvertime(
                    period=period,
                    date=period_start,
                    total_messages=data["daily_messages"] + data["premium_messages"],
                )
                for period_start, data in sorted(period_usage.items())
            ]

            return UsageOvertimeResponse(
                data=usage_data,
                period=period,
                total_messages=total_messages,
                total_daily_messages=total_daily_messages,
                total_premium_messages=total_premium_messages,
            )

        except Exception as e:
            logger.error(f"Failed to get usage over time for user {user_id}: {str(e)}")
            raise TokenTrackingError(f"Failed to get usage over time: {str(e)}")
