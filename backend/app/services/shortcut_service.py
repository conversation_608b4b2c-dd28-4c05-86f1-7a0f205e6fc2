import re
import uuid
from datetime import UTC, datetime

from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import CurrentUser
from app.models.shortcuts import (
    ShortcutCreate,
    ShortcutPublic,
    ShortcutsPublic,
    ShortcutUpdate,
)
from app.repositories.shortcuts import ShortcutRepository


class ShortcutService:
    def __init__(self, session: AsyncSession):
        self.repository = ShortcutRepository(session)

    async def list_shortcuts(
        self,
        current_user: CurrentUser,
        workspace_id: uuid.UUID,
        include_defaults: bool = True,
        search: str | None = None,
        page: int = 1,
        page_size: int = 20,
    ) -> ShortcutsPublic:
        offset = (page - 1) * page_size
        return await self.repository.list_by_workspace(
            workspace_id=workspace_id,
            user_id=current_user.id,
            include_defaults=include_defaults,
            search=search,
            offset=offset,
            limit=page_size,
        )

    async def create_shortcut(
        self, current_user: CurrentUser, data: ShortcutCreate
    ) -> ShortcutPublic:
        # Generate slug if not provided
        if not data.slug:
            data.slug = self._generate_slug(data.title)
        else:
            data.slug = self._sanitize_slug(data.slug)

        # Ensure slug uniqueness
        data.slug = await self._ensure_unique_slug(
            current_user.current_workspace_id, data.slug
        )

        shortcut = await self.repository.create_shortcut(
            data, current_user.id, current_user.current_workspace_id
        )
        return ShortcutPublic.model_validate(shortcut)

    async def update_shortcut(
        self, current_user: CurrentUser, shortcut_id: uuid.UUID, data: ShortcutUpdate
    ) -> ShortcutPublic | None:
        # If title is being changed and slug is not explicitly set, re-slugify
        if data.title and data.slug is None:
            data.slug = self._generate_slug(data.title)
            data.slug = await self._ensure_unique_slug(
                current_user.current_workspace_id, data.slug, exclude_id=shortcut_id
            )
        elif data.slug:
            data.slug = self._sanitize_slug(data.slug)
            data.slug = await self._ensure_unique_slug(
                current_user.current_workspace_id, data.slug, exclude_id=shortcut_id
            )

        # Update modified timestamp
        shortcut = await self.repository.update_shortcut(
            shortcut_id, data, current_user
        )
        if shortcut:
            shortcut.updated_at = datetime.now(UTC)
            return ShortcutPublic.model_validate(shortcut)
        return None

    async def delete_shortcut(
        self, current_user: CurrentUser, shortcut_id: uuid.UUID
    ) -> bool:
        return await self.repository.delete_shortcut(shortcut_id, current_user)

    async def get_shortcut_by_id(
        self, current_user: CurrentUser, shortcut_id: uuid.UUID
    ) -> ShortcutPublic | None:
        shortcut = await self.repository.get_by_id_with_access(
            shortcut_id, current_user
        )
        if shortcut:
            return ShortcutPublic.model_validate(shortcut)
        return None

    async def get_shortcut_by_slug(
        self, workspace_id: uuid.UUID, slug: str
    ) -> ShortcutPublic | None:
        shortcut = await self.repository.get_by_slug(workspace_id, slug)
        if shortcut:
            return ShortcutPublic.model_validate(shortcut)
        return None

    def _generate_slug(self, title: str) -> str:
        """Generate a URL-friendly slug from the title."""
        # Convert to lowercase and replace spaces with hyphens
        slug = re.sub(r"[^\w\s-]", "", title.lower().strip())
        slug = re.sub(r"[-\s]+", "-", slug)
        # Remove leading/trailing hyphens
        slug = slug.strip("-")
        # Limit length
        return slug[:100] if slug else "shortcut"

    def _sanitize_slug(self, slug: str) -> str:
        """Sanitize user-provided slug."""
        # Remove invalid characters and normalize
        slug = re.sub(r"[^\w-]", "", slug.lower().strip())
        slug = re.sub(r"-+", "-", slug)
        slug = slug.strip("-")
        return slug[:100] if slug else "shortcut"

    async def _ensure_unique_slug(
        self, workspace_id: uuid.UUID, slug: str, exclude_id: uuid.UUID | None = None
    ) -> str:
        """Ensure slug is unique within workspace by appending numbers if needed."""
        original_slug = slug
        counter = 1

        while await self.repository.is_slug_taken(workspace_id, slug, exclude_id):
            slug = f"{original_slug}-{counter}"
            counter += 1
            # Prevent infinite loops
            if counter > 1000:
                slug = f"{original_slug}-{uuid.uuid4().hex[:8]}"
                break

        return slug
