"""
WorkspaceMemberService - Handles workspace member management with quota tracking
"""

from uuid import UUI<PERSON>

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.exceptions.legacy import ServiceError
from app.logger import logger
from app.models import UserWorkspace, Workspace
from app.services.quota import ResourceQuotaService, QuotaResourceType


class WorkspaceMemberService:
    """Service responsible for workspace member management with quota tracking"""

    def __init__(self, async_session: AsyncSession):
        self.async_session = async_session
        self.quota_service = ResourceQuotaService(async_session)

    async def add_member_to_workspace(
        self,
        workspace_id: UUID,
        user_id: UUID,
        added_by_user_id: UUID,
    ) -> UserWorkspace:
        """
        Add a user to a workspace with quota tracking.

        Args:
            workspace_id: ID of the workspace
            user_id: ID of the user to add
            added_by_user_id: ID of the user adding the member (for permission check)

        Returns:
            UserWorkspace relationship object

        Raises:
            ServiceError: If quota exceeded or permission denied
        """
        try:
            # Get workspace and check ownership
            workspace_stmt = select(Workspace).where(Workspace.id == workspace_id)
            workspace_result = await self.async_session.exec(workspace_stmt)
            workspace = workspace_result.first()

            if not workspace:
                raise ServiceError("Workspace not found", status_code=404)

            workspace_owner_id = workspace.owner_id

            # Check if adding user has permission (must be owner or superuser)
            if added_by_user_id != workspace_owner_id:
                # TODO: Add superuser check when User service is available
                raise ServiceError(
                    "Only workspace owners can add members", status_code=403
                )

            # Check if user is already a member
            existing_member_stmt = select(UserWorkspace).where(
                UserWorkspace.workspace_id == workspace_id,
                UserWorkspace.user_id == user_id,
            )
            existing_result = await self.async_session.exec(existing_member_stmt)
            if existing_result.first():
                raise ServiceError(
                    "User is already a member of this workspace", status_code=400
                )

            # Check member quota before adding
            if await self.quota_service.check_quota(
                workspace_owner_id, QuotaResourceType.MEMBERS, 1
            ):
                raise ServiceError(
                    "Member quota exceeded. Cannot add more members to workspace.",
                    status_code=403,
                )

            # Create UserWorkspace relationship
            user_workspace = UserWorkspace(workspace_id=workspace_id, user_id=user_id)

            self.async_session.add(user_workspace)
            await self.async_session.commit()
            await self.async_session.refresh(user_workspace)

            # Update member quota usage
            try:
                await self.quota_service.update_quota(
                    workspace_owner_id, QuotaResourceType.MEMBERS, 1
                )
            except Exception as quota_error:
                # If quota update fails after member creation, rollback the member addition
                logger.error(
                    f"Failed to update quota after adding member: {quota_error}"
                )
                await self.async_session.delete(user_workspace)
                await self.async_session.commit()
                raise ServiceError(
                    "Failed to track quota usage. Member addition rolled back.", 500
                )

            logger.info(f"Added user {user_id} to workspace {workspace_id}")
            return user_workspace

        except Exception as e:
            await self.async_session.rollback()
            if isinstance(e, ServiceError):
                raise
            logger.error(f"Error adding member to workspace: {e}")
            raise ServiceError(
                "An unexpected error occurred while adding member to workspace.",
                status_code=500,
            )

    async def remove_member_from_workspace(
        self,
        workspace_id: UUID,
        user_id: UUID,
        removed_by_user_id: UUID,
    ) -> None:
        """
        Remove a user from a workspace with quota tracking.

        Args:
            workspace_id: ID of the workspace
            user_id: ID of the user to remove
            removed_by_user_id: ID of the user removing the member (for permission check)

        Raises:
            ServiceError: If permission denied or member not found
        """
        try:
            # Get workspace and check ownership
            workspace_stmt = select(Workspace).where(Workspace.id == workspace_id)
            workspace_result = await self.async_session.exec(workspace_stmt)
            workspace = workspace_result.first()

            if not workspace:
                raise ServiceError("Workspace not found", status_code=404)

            workspace_owner_id = workspace.owner_id

            # Check if removing user has permission (must be owner, superuser, or removing themselves)
            if (
                removed_by_user_id != workspace_owner_id
                and removed_by_user_id != user_id
            ):
                # TODO: Add superuser check when User service is available
                raise ServiceError(
                    "Only workspace owners can remove members, or users can remove themselves",
                    status_code=403,
                )

            # Prevent removing the workspace owner
            if user_id == workspace_owner_id:
                raise ServiceError(
                    "Cannot remove workspace owner from workspace", status_code=400
                )

            # Find and remove UserWorkspace relationship
            user_workspace_stmt = select(UserWorkspace).where(
                UserWorkspace.workspace_id == workspace_id,
                UserWorkspace.user_id == user_id,
            )
            user_workspace_result = await self.async_session.exec(user_workspace_stmt)
            user_workspace = user_workspace_result.first()

            if not user_workspace:
                raise ServiceError(
                    "User is not a member of this workspace", status_code=404
                )

            await self.async_session.delete(user_workspace)
            await self.async_session.commit()

            # Update member quota usage (decrement)
            try:
                await self.quota_service.update_quota(
                    workspace_owner_id, QuotaResourceType.MEMBERS, -1
                )
            except Exception as quota_error:
                # If quota update fails, log but don't prevent member removal
                logger.error(
                    f"Failed to update quota after removing member: {quota_error}"
                )
                # Don't raise - member was removed successfully

            logger.info(f"Removed user {user_id} from workspace {workspace_id}")

        except Exception as e:
            await self.async_session.rollback()
            if isinstance(e, ServiceError):
                raise
            logger.error(f"Error removing member from workspace: {e}")
            raise ServiceError(
                "An unexpected error occurred while removing member from workspace.",
                status_code=500,
            )

    async def get_workspace_member_count(self, workspace_id: UUID) -> int:
        """
        Get the current number of members in a workspace (including owner).

        Args:
            workspace_id: ID of the workspace

        Returns:
            Number of members including owner
        """
        try:
            # Count UserWorkspace relationships + 1 for owner
            member_stmt = select(UserWorkspace).where(
                UserWorkspace.workspace_id == workspace_id
            )
            member_result = await self.async_session.exec(member_stmt)
            members = member_result.all()

            return len(members) + 1  # +1 for workspace owner

        except Exception as e:
            logger.error(f"Error getting workspace member count: {e}")
            return 1  # Default to owner only
