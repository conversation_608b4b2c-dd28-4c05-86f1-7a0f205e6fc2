import asyncio

from app.core.celery_app import HIGH_PRIORITY, celery_app
from app.core.db_session import get_task_session
from app.logger import logger
from app.services.quota import MessageQuotaService


# Create a secure logger for this module
class SecureTaskLogger:
    """Secure logger for task processing that masks sensitive information"""

    @staticmethod
    def info(message: str, **kwargs):
        # Remove sensitive fields from kwargs
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if not k.lower().endswith(("_id", "token", "secret", "key"))
        }
        logger.info(message, **safe_kwargs)

    @staticmethod
    def error(message: str, **kwargs):
        # Remove sensitive fields from kwargs
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if not k.lower().endswith(("_id", "token", "secret", "key"))
        }
        logger.error(message, **safe_kwargs)

    @staticmethod
    def warning(message: str, **kwargs):
        # Remove sensitive fields from kwargs
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if not k.lower().endswith(("_id", "token", "secret", "key"))
        }
        logger.warning(message, **safe_kwargs)


secure_logger = SecureTaskLogger()


async def _reset_daily_quotas_async():
    """Async helper function to reset daily quotas for all users"""
    async with get_task_session() as session:
        quota_service = MessageQuotaService(session)
        try:
            reset_count = await quota_service.reset_daily_quota()
            secure_logger.info(
                f"Successfully reset daily quotas for {reset_count} users"
            )
            return reset_count
        except Exception as e:
            secure_logger.error(f"Failed to reset daily quotas: {str(e)}")
            raise


@celery_app.task(
    name="app.tasks.quota.reset_daily_quotas_all_users",
    priority=HIGH_PRIORITY,
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
)
def reset_daily_quotas_all_users(self):
    """
    Daily scheduled task to reset daily message quotas for all users at midnight UTC.

    This task is automatically scheduled via Celery Beat to run daily at 00:00 UTC.
    It resets the daily_credit_used field to 0 for all users and updates the
    daily_credits_reset_at timestamp.

    Returns:
        int: Number of users whose quotas were reset
    """
    secure_logger.info("Starting daily quota reset task for all users")

    try:
        reset_count = asyncio.run(_reset_daily_quotas_async())
        secure_logger.info(
            f"Daily quota reset task completed successfully. Reset {reset_count} users."
        )
        return reset_count

    except Exception as e:
        secure_logger.error(f"Daily quota reset task failed: {str(e)}")
        # Re-raise to trigger Celery's retry mechanism
        raise self.retry(exc=e)
