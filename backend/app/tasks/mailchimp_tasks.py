"""
Celery tasks for Mailchimp integration.

These background tasks handle:
- Async user synchronization with Mailchimp
- Activity event tracking without blocking API responses
- Retry logic for failed Mailchimp API calls
"""

import asyncio
from datetime import datetime
from logging import getLogger
from typing import Any, Dict, List, Optional
from uuid import UUID

from app.core.celery_app import celery_app
from app.constants import HIGH_PRIORITY, NORMAL_PRIORITY
from app.services.mailchimp_service import (
    MailchimpActivityEvent,
    MailchimpUserData,
    UserActivity,
    get_mailchimp_service,
)

logger = getLogger(__name__)


class MailchimpRateLimitError(Exception):
    """Exception for Mailchimp rate limiting that shouldn't be retried."""
    pass


@celery_app.task(
    name="app.tasks.mailchimp_tasks.sync_user_to_mailchimp",
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    priority=NORMAL_PRIORITY,
    dont_autoretry_for=(MailchimpRateLimitError,),
)
def sync_user_to_mailchimp(
    self,
    email: str,
    full_name: str = "",
    user_id: str = "",
    status: str = "subscribed",
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    merge_fields: Optional[Dict[str, str]] = None,
    tags: Optional[List[str]] = None,
):
    """
    Sync user data to Mailchimp audience asynchronously.
    
    Args:
        email: User email address
        full_name: User full name (fallback if first/last not provided)
        user_id: User UUID as string
        status: Mailchimp subscriber status
        first_name: User first name (preferred over parsing full_name)
        last_name: User last name (preferred over parsing full_name)
        merge_fields: Additional merge fields
        tags: Tags to apply to the user
    """
    try:
        async def sync_user():
            mailchimp_service = await get_mailchimp_service()
            
            # Check if Mailchimp is enabled first
            if not mailchimp_service.is_enabled:
                logger.debug(f"Mailchimp disabled, skipping user sync for {email}")
                return
            
            user_data = MailchimpUserData(
                email=email,
                full_name=full_name,
                first_name=first_name,
                last_name=last_name,
                status=status,
                merge_fields=merge_fields or {},
                tags=tags or [],
            )
            
            success = await mailchimp_service.sync_user(user_data)
            
            if not success:
                raise Exception(f"Failed to sync user {email} to Mailchimp")
            
            logger.info(f"Successfully synced user {email} to Mailchimp")
            
        # Run async code in sync context
        asyncio.run(sync_user())
        
    except Exception as exc:
        logger.error(
            f"Error syncing user {email} to Mailchimp: {str(exc)}, "
            f"attempt {self.request.retries + 1} of {self.max_retries + 1}"
        )
        raise self.retry(exc=exc)


@celery_app.task(
    name="app.tasks.mailchimp_tasks.track_user_activity",
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    priority=HIGH_PRIORITY,
)
def track_user_activity(
    self,
    user_id: str,
    email: str,
    activity: str,
    properties: Optional[Dict[str, Any]] = None,
    timestamp: Optional[str] = None,
):
    """
    Track user activity event in Mailchimp asynchronously.
    
    Args:
        user_id: User UUID as string
        email: User email address
        activity: Activity type (UserActivity enum value)
        properties: Additional event properties
        timestamp: Event timestamp (ISO format), defaults to current time
    """
    try:
        logger.debug(f"Track activity task called: user_id={user_id}, email={email}, activity={activity}, properties={properties}")
        
        async def track_activity():
            try:
                logger.debug("Getting mailchimp service...")
                mailchimp_service = await get_mailchimp_service()
                
                # Check if Mailchimp is enabled first
                logger.debug(f"Checking if mailchimp is enabled: {mailchimp_service.is_enabled}")
                if not mailchimp_service.is_enabled:
                    logger.debug(f"Mailchimp disabled, skipping activity tracking for {email}")
                    return
                
                # Validate required parameters
                logger.debug(f"Validating parameters: email={email}, user_id={user_id}")
                if not email:
                    logger.error(f"Email is None or empty for activity {activity}")
                    return
                if not user_id:
                    logger.error(f"User ID is None or empty for activity {activity}")
                    return
                
                # Parse timestamp
                logger.debug(f"Parsing timestamp: {timestamp}")
                event_timestamp = datetime.fromisoformat(timestamp) if timestamp else datetime.now()
                
                # Create activity event
                logger.debug(f"Creating activity event with activity={activity}")
                logger.debug(f"About to create UUID from: {user_id}")
                event_user_id = UUID(user_id)
                logger.debug(f"About to create UserActivity from: {activity}")
                event_activity = UserActivity(activity)
                logger.debug(f"About to create MailchimpActivityEvent...")
                event = MailchimpActivityEvent(
                    user_id=event_user_id,
                    email=email,
                    activity=event_activity,
                    timestamp=event_timestamp,
                    properties=properties or {},
                )
                
                logger.debug(f"Calling mailchimp_service.track_activity...")
                success = await mailchimp_service.track_activity(event)
                
                if not success:
                    raise Exception(f"Failed to track activity {activity} for user {email}")
                
                logger.info(f"Successfully tracked activity {activity} for user {email}")
                
            except Exception as e:
                logger.error(f"Exception in track_activity inner function: {str(e)}", exc_info=True)
                raise
            
        # Run async code in sync context
        asyncio.run(track_activity())
        
    except Exception as exc:
        logger.error(
            f"Error tracking activity {activity} for user {email}: {str(exc)}, "
            f"attempt {self.request.retries + 1} of {self.max_retries + 1}"
        )
        raise self.retry(exc=exc)


@celery_app.task(
    name="app.tasks.mailchimp_tasks.sync_new_user_registration",
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    retry_backoff_max=600,
    priority=HIGH_PRIORITY,
)
def sync_new_user_registration(
    self,
    user_id: str,
    email: str,
    full_name: str = "",
    signup_method: str = "google_oauth",
    selected_provider: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
):
    """
    Handle new user registration - sync to Mailchimp and track registration activity.
    
    Args:
        user_id: User UUID as string
        email: User email address
        full_name: User full name (fallback if first/last not provided)
        signup_method: How the user signed up (google_oauth, email, etc.)
        selected_provider: Cloud provider selected during signup
        first_name: User first name (preferred over parsing full_name)
        last_name: User last name (preferred over parsing full_name)
    """
    try:
        # Initial user sync with registration tags
        initial_tags = ["new_user", "registered", f"signup_{signup_method}"]
        # TEMPORARILY DISABLED FOR DEBUG
        if False and selected_provider and isinstance(selected_provider, str):
            initial_tags.append(f"cloud_{selected_provider.lower()}")
        
        merge_fields = {
            "SIGNUP_DATE": datetime.now().isoformat(),
            "SIGNUP_METHOD": signup_method,
        }
        if selected_provider:
            merge_fields["CLOUD_PROVIDER"] = selected_provider
        
        # Sync user to Mailchimp
        sync_user_to_mailchimp.apply_async(
            args=[email, full_name, user_id, "subscribed"],
            kwargs={
                "first_name": first_name,
                "last_name": last_name, 
                "merge_fields": merge_fields,
                "tags": initial_tags
            }
        )
        
        # Track registration activity
        track_user_activity.apply_async(
            args=[
                user_id,
                email,
                UserActivity.USER_REGISTERED.value,
                {
                    "signup_method": signup_method,
                    "selected_provider": selected_provider,
                    "signup_date": datetime.now().isoformat(),
                },
            ]
        )
        
        logger.info(f"Queued Mailchimp sync for new user registration: {email}")
        
    except Exception as exc:
        logger.error(f"Error queuing Mailchimp sync for new user {email}: {str(exc)}")
        raise self.retry(exc=exc)


@celery_app.task(
    name="app.tasks.mailchimp_tasks.track_onboarding_step",
    bind=True,
    max_retries=2,
    autoretry_for=(Exception,),
    retry_backoff=True,
    priority=NORMAL_PRIORITY,
)
def track_onboarding_step(
    self,
    user_id: str,
    email: str,
    step: int,
    selected_provider: Optional[str] = None,
    is_completed: bool = False,
    is_skipped: bool = False,
):
    """
    Track onboarding step completion.
    
    Args:
        user_id: User UUID as string
        email: User email address
        step: Onboarding step number (1-3)
        selected_provider: Selected cloud provider
        is_completed: Whether full onboarding is completed
        is_skipped: Whether onboarding was skipped
    """
    try:
        if step == 1:
            # Workspace created
            activity = UserActivity.WORKSPACE_CREATED
            properties = {"step": step}
        elif step == 2:
            # Provider connected
            activity = UserActivity.PROVIDER_CONNECTED
            properties = {"step": step, "selected_provider": selected_provider}
        elif step == 3:
            if is_skipped:
                activity = UserActivity.ONBOARDING_SKIPPED
                properties = {"step": step, "skipped": True}
            else:
                activity = UserActivity.ONBOARDING_COMPLETED
                properties = {"step": step, "selected_provider": selected_provider}
        else:
            logger.warning(f"Unknown onboarding step: {step}")
            return
        
        # Track the activity
        track_user_activity.apply_async(
            args=[user_id, email, activity.value, properties]
        )
        
        # If onboarding is completed, add special tags
        if is_completed or step == 3:
            completion_tags = ["onboarded", "active_user"]
            # TEMPORARILY DISABLED FOR DEBUG  
            if False and selected_provider and isinstance(selected_provider, str):
                completion_tags.append(f"cloud_{selected_provider.lower()}")
            
            sync_user_to_mailchimp.apply_async(
                args=[email, "", user_id, "subscribed"],
                kwargs={
                    "merge_fields": {"ONBOARD_DATE": datetime.now().isoformat()},
                    "tags": completion_tags,
                }
            )
        
        logger.info(f"Tracked onboarding step {step} for user {email}")
        
    except Exception as exc:
        logger.error(f"Error tracking onboarding step {step} for user {email}: {str(exc)}")
        raise self.retry(exc=exc)


@celery_app.task(
    name="app.tasks.mailchimp_tasks.track_subscription_event",
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    priority=HIGH_PRIORITY,
)
def track_subscription_event(
    self,
    user_id: str,
    email: str,
    event_type: str,
    plan_name: Optional[str] = None,
    amount: Optional[float] = None,
):
    """
    Track subscription-related events.
    
    Args:
        user_id: User UUID as string
        email: User email address
        event_type: Type of subscription event (upgraded, cancelled, trial_started, etc.)
        plan_name: Subscription plan name
        amount: Subscription amount
    """
    try:
        # Map event types to activities
        event_activity_map = {
            # Core subscription lifecycle events
            "trial_started": UserActivity.TRIAL_STARTED,
            "subscription_upgraded": UserActivity.SUBSCRIPTION_UPGRADED,
            "subscription_cancelled": UserActivity.SUBSCRIPTION_CANCELLED,
            "payment_failed": UserActivity.PAYMENT_FAILED,
            
            # Subscription interaction events
            "plans_viewed": UserActivity.USER_LOGIN,  # General engagement activity
            "status_checked": UserActivity.USER_LOGIN,  # General engagement activity
            "checkout_started": UserActivity.TRIAL_STARTED,  # Beginning of subscription process
            "cancelled": UserActivity.SUBSCRIPTION_CANCELLED,
            
            # Enterprise and plan change events
            "enterprise_enquiry_submitted": UserActivity.SUBSCRIPTION_UPGRADED,  # Intent to upgrade
            "plan_change_requested": UserActivity.SUBSCRIPTION_UPGRADED,  # Intent to change plan
        }
        
        activity = event_activity_map.get(event_type)
        if not activity:
            logger.warning(f"Unknown subscription event type: {event_type}")
            return
        
        properties = {"event_type": event_type}
        if plan_name:
            properties["plan_name"] = plan_name
        if amount:
            properties["amount"] = amount
        
        # Track the activity
        track_user_activity.apply_async(
            args=[user_id, email, activity.value, properties]
        )
        
        # Update user tags and merge fields based on event
        tags = []
        merge_fields = {}
        
        # Core subscription lifecycle events
        if event_type == "trial_started":
            tags = ["trial_user"]
            merge_fields = {"TRIAL_START": datetime.now().isoformat()}
        elif event_type == "subscription_upgraded":
            tags = ["paid_user", "upgraded"]
            merge_fields = {
                "UPGRADE_DATE": datetime.now().isoformat(),
                "PLAN": plan_name or "unknown",
            }
        elif event_type in ["subscription_cancelled", "cancelled"]:
            tags = ["churned_user"]
            merge_fields = {"CHURN_DATE": datetime.now().isoformat()}
        
        # Subscription interaction events
        elif event_type == "plans_viewed":
            tags = ["plan_browser", "engaged_user"]
            merge_fields = {"LAST_PLAN_VIEW": datetime.now().isoformat()}
        elif event_type == "status_checked":
            tags = ["status_checker", "engaged_user"]
            merge_fields = {"LAST_STATUS_CHECK": datetime.now().isoformat()}
        elif event_type == "checkout_started":
            tags = ["checkout_starter", "high_intent"]
            merge_fields = {"CHECKOUT_START": datetime.now().isoformat()}
        
        # Enterprise and plan change events
        elif event_type == "enterprise_enquiry_submitted":
            tags = ["enterprise_prospect", "high_value"]
            merge_fields = {"ENTERPRISE_ENQUIRY": datetime.now().isoformat()}
        elif event_type == "plan_change_requested":
            tags = ["plan_changer", "engaged_user"]
            merge_fields = {"PLAN_CHANGE_REQUEST": datetime.now().isoformat()}
        
        if tags or merge_fields:
            sync_user_to_mailchimp.apply_async(
                args=[email, "", user_id, "subscribed"],
                kwargs={
                    "merge_fields": merge_fields,
                    "tags": tags,
                }
            )
        
        logger.info(f"Tracked subscription event {event_type} for user {email}")
        
    except Exception as exc:
        logger.error(f"Error tracking subscription event {event_type} for user {email}: {str(exc)}")
        raise self.retry(exc=exc)







@celery_app.task(
    name="app.tasks.mailchimp_tasks.track_payment_activity",
    bind=True,
    max_retries=3,
    autoretry_for=(Exception,),
    retry_backoff=True,
    priority=HIGH_PRIORITY,
)
def track_payment_activity(
    self,
    user_id: str,
    email: str,
    payment_event: str,
    amount: float = None,
    currency: str = None,
    payment_method: str = None,
    transaction_id: str = None,
    failure_reason: str = None,
):
    """
    Track payment and billing activities.
    
    Args:
        user_id: User UUID as string
        email: User email address
        payment_event: Type of payment event (success, failed, retried, method_updated)
        amount: Payment amount
        currency: Payment currency
        payment_method: Payment method used
        transaction_id: Transaction identifier
        failure_reason: Reason for payment failure
    """
    try:
        activity_map = {
            "success": UserActivity.PAYMENT_SUCCESS,
            "failed": UserActivity.PAYMENT_FAILED,
            "retried": UserActivity.PAYMENT_RETRIED,
            "method_updated": UserActivity.PAYMENT_METHOD_UPDATED,
        }
        
        activity = activity_map.get(payment_event)
        if not activity:
            logger.warning(f"Unknown payment event type: {payment_event}")
            return
        
        properties = {"payment_event": payment_event}
        if amount:
            properties["amount"] = amount
        if currency:
            properties["currency"] = currency
        if payment_method:
            properties["payment_method"] = payment_method
        if transaction_id:
            properties["transaction_id"] = transaction_id
        if failure_reason:
            properties["failure_reason"] = failure_reason
        
        # Track the activity
        track_user_activity.apply_async(
            args=[user_id, email, activity.value, properties]
        )
        
        # Update user tags and merge fields based on payment event
        tags = []
        merge_fields = {}
        
        if payment_event == "success":
            tags = ["payment_success", "active_subscriber"]
            merge_fields = {
                "LAST_PAYMENT_DATE": datetime.now().isoformat(),
                "LAST_PAYMENT_AMOUNT": str(amount) if amount else "0",
            }
        elif payment_event == "failed":
            tags = ["payment_issues", "billing_attention"]
            merge_fields = {
                "LAST_PAYMENT_FAILURE": datetime.now().isoformat(),
                "PAYMENT_FAILURE_REASON": failure_reason or "unknown",
            }
        
        if tags or merge_fields:
            sync_user_to_mailchimp.apply_async(
                args=[email, "", user_id, "subscribed"],
                kwargs={
                    "merge_fields": merge_fields,
                    "tags": tags,
                }
            )
        
        logger.info(f"Tracked payment activity {payment_event} for user {email}")
        
    except Exception as exc:
        logger.error(f"Error tracking payment activity {payment_event} for user {email}: {str(exc)}")
        raise self.retry(exc=exc)