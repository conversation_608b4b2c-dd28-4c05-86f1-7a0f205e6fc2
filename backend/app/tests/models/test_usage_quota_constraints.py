"""
Tests for UsageQuota database constraints.

This module contains tests to verify that database-level constraints
are properly enforced for the UsageQuota model.
"""

import pytest
from datetime import UTC, datetime, timedelta
from uuid import uuid4

from sqlmodel import select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models.users import UsageQuota, User


class TestUsageQuotaConstraints:
    """Test database constraints for UsageQuota model."""

    @pytest.mark.asyncio
    async def test_positive_credit_used_constraint(self, async_session: AsyncSession):
        """Test that credit_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative messages
        quota = UsageQuota(
            user_id=user_id,
            credit_used=-1,  # This should violate constraint
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):  # Could be IntegrityError or similar
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_quota_used_tokens_constraint(
        self, async_session: AsyncSession
    ):
        """Test that quota_used_tokens cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative tokens
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=-5,  # This should violate constraint
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_daily_credit_used_constraint(
        self, async_session: AsyncSession
    ):
        """Test that daily_credit_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative daily messages
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=-10,  # This should violate constraint
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_workspaces_used_constraint(
        self, async_session: AsyncSession
    ):
        """Test that workspaces_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative workspaces
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=-1,  # This should violate constraint
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_members_used_constraint(self, async_session: AsyncSession):
        """Test that members_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative members
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=-3,  # This should violate constraint
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_scheduled_tasks_used_constraint(
        self, async_session: AsyncSession
    ):
        """Test that scheduled_tasks_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative scheduled tasks
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=-2,  # This should violate constraint
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_positive_kb_storage_used_constraint(
        self, async_session: AsyncSession
    ):
        """Test that kb_storage_used cannot be negative."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota with negative storage
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=-5.5,  # This should violate constraint
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_daily_within_premium_constraint(self, async_session: AsyncSession):
        """Test that daily_credit_used cannot exceed credit_used."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota where daily exceeds premium
        quota = UsageQuota(
            user_id=user_id,
            credit_used=5,
            quota_used_tokens=0,
            daily_credit_used=10,  # This exceeds premium, should violate constraint
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_created_before_reset_constraint(self, async_session: AsyncSession):
        """Test that created_at must be before reset_at."""
        user_id = uuid4()
        past_time = datetime.now(UTC) - timedelta(hours=1)
        current_time = datetime.now(UTC)

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota where created_at is after reset_at
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
            created_at=current_time,  # This is after reset_at
            reset_at=past_time,  # This is before created_at
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_created_before_updated_constraint(self, async_session: AsyncSession):
        """Test that created_at must be before updated_at."""
        user_id = uuid4()
        past_time = datetime.now(UTC) - timedelta(hours=1)
        current_time = datetime.now(UTC)

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Try to create quota where created_at is after updated_at
        quota = UsageQuota(
            user_id=user_id,
            credit_used=0,
            quota_used_tokens=0,
            daily_credit_used=0,
            workspaces_used=0,
            members_used=0,
            scheduled_tasks_used=0,
            kb_storage_used=0.0,
            created_at=current_time,  # This is after updated_at
            updated_at=past_time,  # This is before created_at
        )
        async_session.add(quota)

        # Should raise an error due to constraint violation
        with pytest.raises(Exception):
            await async_session.commit()

        await async_session.rollback()

    @pytest.mark.asyncio
    async def test_valid_quota_creation(self, async_session: AsyncSession):
        """Test that valid quota records can be created successfully."""
        user_id = uuid4()

        # Create a user first
        user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password="hashed",
            full_name="Test User",
        )
        async_session.add(user)

        # Create a valid quota record
        quota = UsageQuota(
            user_id=user_id,
            credit_used=10,
            quota_used_tokens=5,
            daily_credit_used=5,  # Within premium limit
            workspaces_used=2,
            members_used=3,
            scheduled_tasks_used=1,
            kb_storage_used=15.5,
        )
        async_session.add(quota)
        await async_session.commit()

        # Verify the record was created
        stmt = select(UsageQuota).where(UsageQuota.user_id == user_id)
        result = await async_session.exec(stmt)
        saved_quota = result.first()

        assert saved_quota is not None
        assert saved_quota.credit_used == 10
        assert saved_quota.daily_credit_used == 5
        assert saved_quota.kb_storage_used == 15.5

        # Clean up
        await async_session.delete(quota)
        await async_session.delete(user)
        await async_session.commit()

    @pytest.mark.asyncio
    async def test_constraint_names(self, async_session: AsyncSession):
        """Test that constraints have the expected names for error handling."""
        # This test verifies that our constraint names are properly defined
        # The actual constraint names should match what's in the model

        # Check that the UsageQuota model has the expected table args
        table_args = UsageQuota.__table_args__

        # Should contain our check constraints
        constraint_names = [
            "ck_positive_credit_used",
            "ck_positive_quota_used_tokens",
            "ck_positive_daily_credit_used",
            "ck_positive_workspaces_used",
            "ck_positive_members_used",
            "ck_positive_scheduled_tasks_used",
            "ck_positive_kb_storage_used",
            "ck_daily_within_premium",
            "ck_created_before_updated",
            "ck_created_before_reset",
            "ck_created_before_daily_reset",
        ]

        # Extract constraint names from table args
        found_constraints = []
        for arg in table_args:
            if hasattr(arg, "name"):
                found_constraints.append(arg.name)

        # Verify all expected constraints are present
        for constraint_name in constraint_names:
            assert constraint_name in found_constraints, (
                f"Missing constraint: {constraint_name}"
            )
