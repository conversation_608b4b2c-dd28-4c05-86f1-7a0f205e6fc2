"""
Tests for circuit breaker implementation.

This module contains comprehensive tests for the circuit breaker pattern
used in quota services to handle external service failures.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock

from app.core.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitBreakerOpenError,
    CircuitBreakerRegistry,
    CircuitBreakerState,
    circuit_breaker,
    circuit_breaker_registry,
)
from app.exceptions.quota_exceptions import QuotaUpdateError


class TestCircuitBreakerConfig:
    """Test circuit breaker configuration."""

    def test_default_config(self):
        """Test default configuration values."""
        config = CircuitBreakerConfig()
        assert config.failure_threshold == 5
        assert config.recovery_timeout == 60
        assert config.success_threshold == 3
        assert config.timeout == 30.0

    def test_custom_config(self):
        """Test custom configuration values."""
        config = CircuitBreakerConfig(
            failure_threshold=10,
            recovery_timeout=120,
            success_threshold=5,
            timeout=60.0
        )
        assert config.failure_threshold == 10
        assert config.recovery_timeout == 120
        assert config.success_threshold == 5
        assert config.timeout == 60.0


class TestCircuitBreaker:
    """Test circuit breaker functionality."""

    @pytest.fixture
    def config(self):
        """Create test configuration."""
        return CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=1,  # Short timeout for testing
            success_threshold=2,
            timeout=30.0
        )

    @pytest.fixture
    def breaker(self, config):
        """Create test circuit breaker."""
        return CircuitBreaker("test_breaker", config)

    @pytest.mark.asyncio
    async def test_successful_call(self, breaker):
        """Test successful function call."""
        async def success_func():
            return "success"

        result = await breaker.call(success_func)
        assert result == "success"
        assert breaker.is_closed
        assert breaker.metrics.successful_requests == 1
        assert breaker.metrics.failed_requests == 0

    @pytest.mark.asyncio
    async def test_failed_call_transitions_to_open(self, breaker):
        """Test that failures transition breaker to open state."""
        async def failing_func():
            raise Exception("Test failure")

        # Trigger failures to exceed threshold
        for _ in range(breaker.config.failure_threshold):
            with pytest.raises(Exception):
                await breaker.call(failing_func)

        assert breaker.is_open
        assert breaker.metrics.failed_requests == breaker.config.failure_threshold
        assert breaker.metrics.consecutive_failures == breaker.config.failure_threshold

    @pytest.mark.asyncio
    async def test_open_breaker_rejects_calls(self, breaker):
        """Test that open breaker rejects new calls."""
        # Force breaker to open state and set failure time before recovery timeout
        breaker._state = CircuitBreakerState.OPEN
        import time
        breaker.metrics.last_failure_time = time.time() - breaker.config.recovery_timeout - 1

        async def success_func():
            return "success"

        with pytest.raises(CircuitBreakerOpenError):
            await breaker.call(success_func)

    @pytest.mark.asyncio
    async def test_half_open_transition(self, breaker):
        """Test transition from open to half-open after timeout."""
        # Force to open state
        breaker._state = CircuitBreakerState.OPEN
        breaker.metrics.last_failure_time = asyncio.get_event_loop().time() - breaker.config.recovery_timeout - 1

        async def success_func():
            return "success"

        # Should transition to half-open and allow call
        result = await breaker.call(success_func)
        assert result == "success"
        assert breaker.is_half_open

    @pytest.mark.asyncio
    async def test_half_open_to_closed_on_success(self, breaker):
        """Test transition from half-open to closed on successful calls."""
        # Set up half-open state
        breaker._state = CircuitBreakerState.HALF_OPEN

        async def success_func():
            return "success"

        # Make successful calls to exceed success threshold
        for _ in range(breaker.config.success_threshold):
            result = await breaker.call(success_func)
            assert result == "success"

        assert breaker.is_closed
        assert breaker.metrics.consecutive_successes == 0  # Reset after transition to CLOSED

    @pytest.mark.asyncio
    async def test_half_open_back_to_open_on_failure(self, breaker):
        """Test transition from half-open back to open on failure."""
        # Set up half-open state
        breaker._state = CircuitBreakerState.HALF_OPEN

        async def failing_func():
            raise Exception("Test failure")

        with pytest.raises(Exception):
            await breaker.call(failing_func)

        assert breaker.is_open

    @pytest.mark.asyncio
    async def test_timeout_handling(self, breaker):
        """Test timeout handling."""
        async def slow_func():
            await asyncio.sleep(2)  # Longer than timeout
            return "success"

        # Set very short timeout
        breaker.config.timeout = 0.1

        with pytest.raises(asyncio.TimeoutError):
            await breaker.call(slow_func)

        assert breaker.metrics.failed_requests == 1

    def test_metrics_collection(self, breaker):
        """Test metrics collection."""
        metrics = breaker.get_metrics()
        assert metrics["name"] == "test_breaker"
        assert metrics["state"] == "closed"
        assert metrics["total_requests"] == 0
        assert metrics["successful_requests"] == 0
        assert metrics["failed_requests"] == 0
        assert "failure_rate" in metrics


class TestCircuitBreakerRegistry:
    """Test circuit breaker registry."""

    def test_get_or_create_new(self):
        """Test creating new circuit breaker."""
        registry = CircuitBreakerRegistry()
        config = CircuitBreakerConfig()

        breaker = registry.get_or_create("test", config)
        assert breaker.name == "test"
        assert breaker.config == config

    def test_get_or_create_existing(self):
        """Test getting existing circuit breaker."""
        registry = CircuitBreakerRegistry()
        config = CircuitBreakerConfig()

        breaker1 = registry.get_or_create("test", config)
        breaker2 = registry.get_or_create("test", config)

        assert breaker1 is breaker2

    def test_get_all_metrics(self):
        """Test getting all metrics."""
        registry = CircuitBreakerRegistry()
        config = CircuitBreakerConfig()

        registry.get_or_create("breaker1", config)
        registry.get_or_create("breaker2", config)

        metrics = registry.get_all_metrics()
        assert "breaker1" in metrics
        assert "breaker2" in metrics
        assert len(metrics) == 2

    def test_reset_all(self):
        """Test resetting all circuit breakers."""
        registry = CircuitBreakerRegistry()
        config = CircuitBreakerConfig()

        breaker = registry.get_or_create("test", config)
        breaker._state = CircuitBreakerState.OPEN

        registry.reset_all()

        assert breaker.is_closed


class TestCircuitBreakerDecorator:
    """Test circuit breaker decorator."""

    @pytest.mark.asyncio
    async def test_decorator_success(self):
        """Test decorator with successful call."""
        @circuit_breaker("test_decorator")
        async def success_func():
            return "success"

        result = await success_func()
        assert result == "success"

    @pytest.mark.asyncio
    async def test_decorator_failure(self):
        """Test decorator with failing call."""
        @circuit_breaker("test_decorator_fail", failure_threshold=2)
        async def failing_func():
            raise Exception("Test failure")

        # First failure
        with pytest.raises(Exception):
            await failing_func()

        # Second failure should open circuit
        with pytest.raises(Exception):
            await failing_func()

        # Third call should be rejected by circuit breaker
        with pytest.raises(CircuitBreakerOpenError):
            await failing_func()


class TestQuotaServiceIntegration:
    """Test circuit breaker integration with quota services."""

    @pytest.mark.asyncio
    async def test_payment_service_resilience(self):
        """Test payment service resilience with circuit breaker."""
        from app.services.quota.common import BaseQuotaService
        from app.services.payment_service import PaymentService
        from sqlmodel.ext.asyncio.session import AsyncSession

        # Mock dependencies
        mock_session = MagicMock(spec=AsyncSession)
        mock_payment_service = MagicMock(spec=PaymentService)
        mock_payment_service.get_user_quota = AsyncMock()

        # Create service instance
        service = BaseQuotaService(mock_session)
        service.payment_service = mock_payment_service

        # Test successful call
        mock_payment_service.get_user_quota.return_value = {"quota": 100}
        result = await service._call_payment_service_with_resilience(
            "test_quota",
            mock_payment_service.get_user_quota,
            "user123"
        )
        assert result == {"quota": 100}

        # Test circuit breaker activation
        mock_payment_service.get_user_quota.side_effect = Exception("Payment service down")

        # Trigger failures to open circuit
        for _ in range(5):  # Default failure threshold
            with pytest.raises(QuotaUpdateError):
                await service._call_payment_service_with_resilience(
                    "test_quota",
                    mock_payment_service.get_user_quota,
                    "user123"
                )

        # Next call should be rejected by circuit breaker
        with pytest.raises(QuotaUpdateError):
            await service._call_payment_service_with_resilience(
                "test_quota",
                mock_payment_service.get_user_quota,
                "user123"
            )