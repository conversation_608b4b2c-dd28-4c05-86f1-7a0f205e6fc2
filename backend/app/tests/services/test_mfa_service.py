import base64
import json
from datetime import UTC, datetime, timedelta
from io import Bytes<PERSON>
from unittest.mock import MagicMock, patch

import pytest
import pyotp
from cryptography.fernet import InvalidToken
from sqlmodel import Session

from app.core.config import settings
from app.models.users import MFASession, SecurityEvent
from app.services.mfa_service import MFAService
from app.tests.utils.user import create_random_user


class TestMFAService:
    """Test suite for MFAService"""

    @pytest.fixture
    def mock_session(self):
        """Mock database session"""
        return MagicMock(spec=Session)

    @pytest.fixture
    def service(self, mock_session):
        """MFAService instance"""
        service = MFAService()
        return service

    @pytest.fixture
    def sample_user(self, db: Session):
        """Sample user for testing"""
        user = create_random_user(db)
        return user

    @pytest.fixture
    def sample_secret(self):
        """Sample TOTP secret"""
        return pyotp.random_base32()

    @pytest.fixture
    def sample_encrypted_secret(self, service, sample_secret):
        """<PERSON>ple encrypted TOTP secret"""
        return service.encrypt_data(sample_secret)

    def test_generate_secret(self, service):
        """Test TOTP secret generation"""
        secret = service.generate_secret()

        assert isinstance(secret, str)
        assert len(secret) == 32  # Base32 secret length
        # Should be valid base32
        assert all(c in "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567" for c in secret)

    def test_encrypt_decrypt_data(self, service):
        """Test data encryption and decryption"""
        test_data = "test_secret_123"

        # Encrypt
        encrypted = service.encrypt_data(test_data)
        assert encrypted != test_data
        assert isinstance(encrypted, str)

        # Decrypt
        decrypted = service.decrypt_data(encrypted)
        assert decrypted == test_data

    def test_generate_qr_code(self, service, sample_secret):
        """Test QR code generation"""
        user_email = "<EMAIL>"
        qr_data = service.generate_qr_code(sample_secret, user_email)

        assert qr_data.startswith("data:image/png;base64,")

        # Verify it contains valid base64 data
        base64_data = qr_data.split(",")[1]
        decoded_data = BytesIO(base64.b64decode(base64_data))
        assert len(decoded_data.getvalue()) > 0

    def test_verify_totp_valid(self, service, sample_secret):
        """Test valid TOTP verification"""
        totp = pyotp.TOTP(sample_secret)
        token = totp.now()

        assert service.verify_totp(sample_secret, token) is True

    def test_verify_totp_invalid(self, service, sample_secret):
        """Test invalid TOTP verification"""
        assert service.verify_totp(sample_secret, "123456") is False
        assert service.verify_totp(sample_secret, "invalid") is False
        assert service.verify_totp(sample_secret, "") is False

    def test_verify_totp_invalid_secret(self, service):
        """Test TOTP verification with invalid secret"""
        assert service.verify_totp("invalid_secret", "123456") is False

    def test_generate_backup_codes(self, service):
        """Test backup codes generation"""
        codes = service.generate_backup_codes()

        assert isinstance(codes, list)
        assert len(codes) == settings.MFA_BACKUP_CODES_COUNT

        for code in codes:
            assert isinstance(code, str)
            assert len(code) == settings.MFA_BACKUP_CODE_LENGTH
            # Should contain only alphanumeric characters
            assert code.isalnum()

    def test_hash_backup_codes(self, service):
        """Test backup codes hashing"""
        codes = ["ABC123", "DEF456", "GHI789"]
        hashed_json = service.hash_backup_codes(codes)

        assert isinstance(hashed_json, str)

        # Should be valid JSON
        hashed_codes = json.loads(hashed_json)
        assert len(hashed_codes) == len(codes)

        # Each hash should be different from original
        for i, hashed in enumerate(hashed_codes):
            assert hashed != codes[i]

    def test_verify_backup_code_valid(self, service):
        """Test valid backup code verification"""
        codes = ["ABC123", "DEF456"]
        hashed_json = service.hash_backup_codes(codes)
        encrypted_codes = service.encrypt_data(hashed_json)

        # Test first code
        is_valid, updated_codes = service.verify_backup_code("ABC123", encrypted_codes)
        assert is_valid is True
        assert updated_codes != encrypted_codes

        # Verify the used code is removed
        decrypted_updated = service.decrypt_data(updated_codes)
        updated_hashes = json.loads(decrypted_updated)
        assert len(updated_hashes) == 1  # One code should be removed

        # Test second code
        is_valid2, updated_codes2 = service.verify_backup_code("DEF456", updated_codes)
        assert is_valid2 is True

        # Verify all codes are used
        decrypted_final = service.decrypt_data(updated_codes2)
        final_hashes = json.loads(decrypted_final)
        assert len(final_hashes) == 0

    def test_verify_backup_code_invalid(self, service):
        """Test invalid backup code verification"""
        codes = ["ABC123", "DEF456"]
        hashed_json = service.hash_backup_codes(codes)
        encrypted_codes = service.encrypt_data(hashed_json)

        # Test invalid code
        is_valid, updated_codes = service.verify_backup_code("INVALID", encrypted_codes)
        assert is_valid is False
        assert updated_codes == encrypted_codes  # Should remain unchanged

    def test_verify_backup_code_used(self, service):
        """Test already used backup code"""
        codes = ["ABC123"]
        hashed_json = service.hash_backup_codes(codes)
        encrypted_codes = service.encrypt_data(hashed_json)

        # Use the code once
        is_valid1, updated_codes = service.verify_backup_code("ABC123", encrypted_codes)
        assert is_valid1 is True

        # Try to use the same code again
        is_valid2, final_codes = service.verify_backup_code("ABC123", updated_codes)
        assert is_valid2 is False
        assert final_codes == updated_codes  # Should remain unchanged

    def test_verify_backup_code_empty(self, service):
        """Test backup code verification with empty codes"""
        encrypted_codes = service.encrypt_data("[]")

        is_valid, updated_codes = service.verify_backup_code("ABC123", encrypted_codes)
        assert is_valid is False
        assert updated_codes == encrypted_codes

    def test_create_mfa_session(self, service, mock_session, sample_user):
        """Test MFA session creation"""
        ip_address = "*************"
        user_agent = "Mozilla/5.0..."

        session_token = service.create_mfa_session(
            mock_session, sample_user, ip_address, user_agent
        )

        assert isinstance(session_token, str)
        assert len(session_token) > 0

        # Verify session was added to database
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

        # Check the session object that was added
        call_args = mock_session.add.call_args[0][0]
        assert isinstance(call_args, MFASession)
        assert call_args.user_id == sample_user.id
        assert call_args.session_token == session_token
        assert call_args.ip_address == ip_address
        assert call_args.user_agent == user_agent
        assert call_args.is_verified is False
        assert call_args.expires_at > datetime.now(UTC)

    def test_verify_mfa_session_valid(self, service, mock_session, sample_user):
        """Test valid MFA session verification"""
        session_token = "valid_session_token"
        mfa_session = MFASession(
            user_id=sample_user.id,
            session_token=session_token,
            expires_at=datetime.now(UTC) + timedelta(minutes=5),
            is_verified=False,
        )

        mock_session.exec.return_value.first.return_value = mfa_session

        result = service.verify_mfa_session(mock_session, session_token)

        assert result == mfa_session
        mock_session.exec.assert_called_once()

    def test_verify_mfa_session_expired(self, service, mock_session):
        """Test expired MFA session verification"""
        session_token = "expired_session_token"

        mock_session.exec.return_value.first.return_value = None

        result = service.verify_mfa_session(mock_session, session_token)

        assert result is None

    def test_verify_mfa_session_not_found(self, service, mock_session):
        """Test non-existent MFA session verification"""
        session_token = "non_existent_token"

        mock_session.exec.return_value.first.return_value = None

        result = service.verify_mfa_session(mock_session, session_token)

        assert result is None

    def test_complete_mfa_session(self, service, mock_session):
        """Test MFA session completion"""
        mfa_session = MagicMock()

        service.complete_mfa_session(mock_session, mfa_session)

        assert mfa_session.is_verified is True
        mock_session.add.assert_called_once_with(mfa_session)
        mock_session.commit.assert_called_once()

    def test_cleanup_expired_sessions(self, service, mock_session):
        """Test expired MFA sessions cleanup"""
        expired_sessions = [MagicMock(), MagicMock()]
        mock_session.exec.return_value.all.return_value = expired_sessions

        service.cleanup_expired_sessions(mock_session)

        # Should delete each expired session
        assert mock_session.delete.call_count == len(expired_sessions)
        mock_session.commit.assert_called_once()

    def test_log_security_event(self, service, mock_session, sample_user):
        """Test security event logging"""
        event_type = "mfa_login_success"
        success = True
        ip_address = "*************"
        user_agent = "Mozilla/5.0..."
        details = {"attempts": 1}

        service.log_security_event(
            mock_session,
            str(sample_user.id),
            event_type,
            success,
            ip_address,
            user_agent,
            details,
        )

        # Verify event was added to database
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()

        # Check the event object that was added
        call_args = mock_session.add.call_args[0][0]
        assert isinstance(call_args, SecurityEvent)
        assert call_args.user_id == str(sample_user.id)
        assert call_args.event_type == event_type
        assert call_args.success == success
        assert call_args.ip_address == ip_address
        assert call_args.user_agent == user_agent
        assert json.loads(call_args.details) == details

    def test_is_account_locked_not_locked(self, service, sample_user):
        """Test account lock check when not locked"""
        sample_user.locked_until = None

        assert service.is_account_locked(sample_user) is False

    def test_is_account_locked_future_time(self, service, sample_user):
        """Test account lock check when locked until future time"""
        sample_user.locked_until = datetime.now(UTC) + timedelta(hours=1)

        assert service.is_account_locked(sample_user) is True

    def test_is_account_locked_past_time(self, service, sample_user):
        """Test account lock check when lock expired"""
        sample_user.locked_until = datetime.now(UTC) - timedelta(hours=1)

        assert service.is_account_locked(sample_user) is False

    def test_lock_account(self, service, mock_session, sample_user):
        """Test account locking"""
        service.lock_account(mock_session, sample_user)

        assert sample_user.locked_until > datetime.now(UTC)
        assert sample_user.failed_login_attempts == 0  # Should be reset
        mock_session.add.assert_called_once_with(sample_user)
        mock_session.commit.assert_called_once()

    def test_reset_failed_attempts(self, service, mock_session, sample_user):
        """Test failed attempts reset"""
        sample_user.failed_login_attempts = 3
        sample_user.locked_until = datetime.now(UTC) + timedelta(hours=1)

        service.reset_failed_attempts(mock_session, sample_user)

        assert sample_user.failed_login_attempts == 0
        assert sample_user.locked_until is None
        mock_session.add.assert_called_once_with(sample_user)
        mock_session.commit.assert_called_once()

    def test_increment_failed_attempts_under_limit(
        self, service, mock_session, sample_user
    ):
        """Test failed attempts increment when under limit"""
        sample_user.failed_login_attempts = 2  # Under limit of 5

        service.increment_failed_attempts(mock_session, sample_user)

        assert sample_user.failed_login_attempts == 3
        mock_session.add.assert_called_once_with(sample_user)
        mock_session.commit.assert_called_once()

    def test_increment_failed_attempts_at_limit(
        self, service, mock_session, sample_user
    ):
        """Test failed attempts increment when at limit (should lock account)"""
        sample_user.failed_login_attempts = 4  # One less than limit

        with patch.object(service, "lock_account") as mock_lock:
            service.increment_failed_attempts(mock_session, sample_user)

            mock_lock.assert_called_once_with(mock_session, sample_user)

    @patch("pyotp.random_base32")
    def test_generate_secret_mock(self, mock_random_base32, service):
        """Test secret generation with mocked random function"""
        expected_secret = "JBSWY3DPEHPK3PXP"
        mock_random_base32.return_value = expected_secret

        result = service.generate_secret()

        assert result == expected_secret
        mock_random_base32.assert_called_once()

    def test_qr_code_provisioning_uri_format(self, service, sample_secret):
        """Test QR code contains correct provisioning URI format"""
        user_email = "<EMAIL>"

        with patch("qrcode.QRCode.add_data") as mock_add_data:
            with patch("qrcode.QRCode.make_image"):
                service.generate_qr_code(sample_secret, user_email)

                # Verify the provisioning URI was created correctly
                mock_add_data.assert_called_once()
                uri = mock_add_data.call_args[0][0]

                # Check that the URI contains the expected components
                assert "test%40example.com" in uri  # URL encoded email
                assert sample_secret in uri
                assert settings.MFA_TOTP_ISSUER in uri
                assert uri.startswith("otpauth://totp/")
                assert "secret=" in uri
                assert "issuer=" in uri

    def test_encryption_error_handling(self, service):
        """Test encryption error handling"""
        # Test with invalid encrypted data
        with pytest.raises(InvalidToken):
            service.decrypt_data("invalid_encrypted_data")

        # Test with non-string encrypted data
        with pytest.raises(AttributeError):
            service.decrypt_data(12345)

    def test_backup_code_verification_error_handling(self, service):
        """Test backup code verification error handling"""
        # Test with malformed JSON
        encrypted_codes = service.encrypt_data("invalid json")

        is_valid, updated_codes = service.verify_backup_code("ABC123", encrypted_codes)

        assert is_valid is False
        assert updated_codes == encrypted_codes

    def test_mfa_session_expiration_time(self, service, mock_session, sample_user):
        """Test MFA session expiration time is set correctly"""
        service.create_mfa_session(mock_session, sample_user)

        call_args = mock_session.add.call_args[0][0]
        expected_expiry = datetime.now(UTC) + timedelta(
            minutes=settings.MFA_SESSION_EXPIRE_MINUTES
        )

        # Should expire within a reasonable time range
        time_diff = abs((call_args.expires_at - expected_expiry).total_seconds())
        assert time_diff < 5  # Within 5 seconds

    def test_security_event_details_json_encoding(
        self, service, mock_session, sample_user
    ):
        """Test security event details are properly JSON encoded"""
        details = {"test": "value", "number": 123}

        service.log_security_event(
            mock_session, str(sample_user.id), "test_event", True, details=details
        )

        call_args = mock_session.add.call_args[0][0]
        stored_details = json.loads(call_args.details)

        assert stored_details == details

    def test_security_event_without_details(self, service, mock_session, sample_user):
        """Test security event logging without details"""
        service.log_security_event(
            mock_session, str(sample_user.id), "test_event", True
        )

        call_args = mock_session.add.call_args[0][0]
        assert call_args.details is None
