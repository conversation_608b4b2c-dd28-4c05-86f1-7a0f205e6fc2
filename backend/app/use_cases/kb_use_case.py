from datetime import <PERSON><PERSON><PERSON>
from uuid import UUID

from fastapi import Depends, HTTPException
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import get_async_session
from app.core.config import settings
from app.exceptions.kb_exceptions import DocumentDeletionError
from app.logger import logger
from app.models.document_kbs import DocumentKBCreate, DocumentsKBRead, DocumentType
from app.models.knowledge_bases import KBCreate, KBPublic, KBsPublic, KBUpdate
from app.models.shared import AsyncTaskStatus
from app.modules.knowledge_base.validation_utils import (
    validate_kb_access,
    validate_urls_batch,
    validate_usage_mode,
)
from app.repositories.kb import KBRepository
from app.repositories.object_storage.provider import get_object_storage_repository
from app.repositories.workspaces import WorkspaceRepository
from app.schemas.kb import (
    ConfirmUploadsRequest,
    DocumentOverviewResponse,
    KBOverviewResponse,
    URLsUploadRequest,
)
from app.services.base_vector_store import BaseVectorStore
from app.services.kb.kb_service import KBService
from app.services.quota import ResourceQuotaService, QuotaResourceType
from app.tasks.kb_tasks import (
    delete_kb_task,
    ingest_from_files_task,
    ingest_from_website_task,
)


class KBUseCase:
    """
    Use case layer for Knowledge Base operations.

    This class orchestrates business logic for knowledge base management,
    including creation, retrieval, updates, document ingestion, and deletion.
    It coordinates between repositories, services, and external storage systems.
    """

    def __init__(self, session: AsyncSession = Depends(get_async_session)):
        """
        Initialize the KBUseCase with required dependencies.

        Args:
            session: Database session for repository operations
        """
        self.session = session
        self.workspace_repo = WorkspaceRepository(session)
        self.kb_repo = KBRepository(session)
        self.vector_store = BaseVectorStore()
        self.kb_service = KBService(self.vector_store, session)
        self.quota_service = ResourceQuotaService(session)

    async def create_kb(
        self, kb: KBCreate, user_id: UUID, workspace_id: UUID
    ) -> KBPublic:
        """
        Create a new knowledge base.

        Args:
            kb: Knowledge base creation data
            user_id: ID of the user creating the knowledge base
            workspace_id: ID of the workspace to create the KB in

        Returns:
            The created knowledge base as a public model
        """
        kb = await self.kb_repo.create(kb, user_id, workspace_id)
        return KBPublic(**kb.model_dump())

    async def get_by_id(
        self, kb_id: UUID, user_id: UUID, workspace_id: UUID
    ) -> KBPublic:
        """
        Retrieve a knowledge base by ID with access validation.

        Args:
            kb_id: ID of the knowledge base to retrieve
            user_id: ID of the user requesting access
            workspace_id: ID of the workspace containing the KB

        Returns:
            The knowledge base as a public model

        Raises:
            HTTPException: If user doesn't have access to the knowledge base
        """
        kb = await self.kb_repo.get(kb_id, user_id, workspace_id)
        validate_kb_access(kb, user_id)
        return KBPublic(**kb.model_dump())

    async def list(
        self,
        workspace_id: UUID,
        user_id: UUID,
        skip: int = 0,
        limit: int = 10,
        search: str | None = None,
        access_level: str | None = None,
        usage_mode: str | None = None,
    ) -> KBsPublic:
        """
        List knowledge bases with optional filtering and pagination.

        Args:
            workspace_id: ID of the workspace to list KBs from
            user_id: ID of the user requesting the list
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            search: Optional search query to filter KBs by name/description
            access_level: Optional filter by access level
            usage_mode: Optional filter by usage mode

        Returns:
            Paginated list of knowledge bases accessible to the user
        """
        mode_enum = validate_usage_mode(usage_mode)
        return await self.kb_repo.get_kbs(
            workspace_id,
            user_id,
            skip,
            limit,
            search=search,
            access_level=access_level,
            mode=mode_enum,
        )

    async def update(
        self, kb_id: UUID, kb: KBUpdate, user_id: UUID, workspace_id: UUID
    ) -> KBPublic:
        """
        Update an existing knowledge base.

        Args:
            kb_id: ID of the knowledge base to update
            kb: Updated knowledge base data
            user_id: ID of the user performing the update
            workspace_id: ID of the workspace containing the KB

        Returns:
            The updated knowledge base as a public model
        """
        kb = await self.kb_repo.update(kb_id, kb, user_id, workspace_id)
        return KBPublic(**kb.model_dump())

    async def delete_kb(self, kb_id: UUID, user_id: UUID, workspace_id: UUID) -> None:
        """
        Delete a knowledge base and all its documents

        1. Delete the KB in the database
        2. Start async celery task to delete KB documents from vector store and object storage

        Args:
            kb_id: The ID of the knowledge base to delete
            user_id: The ID of the user deleting the knowledge base
            workspace_id: The ID of the workspace containing the knowledge base

        Returns:
            None
        """
        # 1. Delete the KB in the database
        await self.kb_repo.delete(kb_id, user_id, workspace_id)

        # 2. Start async celery task to delete KB documents from vector store and object storage
        delete_kb_task.delay(  # type: ignore
            kb_id=str(kb_id),
            workspace_id=str(workspace_id),
        )

    async def get_kb_document_overview(
        self, kb_id: UUID, user_id: UUID, workspace_id: UUID
    ) -> DocumentOverviewResponse:
        """
        Get overview statistics for documents in a knowledge base.

        Args:
            kb_id: ID of the knowledge base
            user_id: ID of the user requesting the overview
            workspace_id: ID of the workspace containing the KB

        Returns:
            Overview statistics including document counts and sizes
        """
        kb = await self.kb_repo.get(kb_id, user_id, workspace_id)
        validate_kb_access(kb, user_id)

        return await self.kb_repo.get_document_overview(kb_id, workspace_id)

    async def get_kb_overview(self, workspace_id: UUID) -> KBOverviewResponse:
        """
        Get overview statistics for all knowledge bases in a workspace.

        Args:
            workspace_id: ID of the workspace to get overview for

        Returns:
            Overview statistics including KB counts and usage metrics
        """
        return await self.kb_repo.get_kb_overview(workspace_id)

    async def ingest_from_presigned_urls(
        self,
        kb_id: UUID,
        user_id: UUID,
        workspace_id: UUID,
        request: ConfirmUploadsRequest,
    ):
        """
        Ingest documents from presigned URLs into a knowledge base.

        Creates document records and starts an async task to process and embed the files.

        Args:
            kb_id: ID of the knowledge base to ingest documents into
            user_id: ID of the user performing the ingestion
            workspace_id: ID of the workspace containing the KB
            request: Request containing uploaded file information

        Returns:
            The async task object for tracking ingestion progress

        Raises:
            HTTPException: If no valid files are found in storage
        """
        # Verify KB exists and user has access
        kb = await self.kb_repo.get(kb_id, user_id, workspace_id)
        validate_kb_access(kb, user_id)

        # Verify files exist in storage
        os_repo = get_object_storage_repository()
        verified_files = []
        for file_info in request.uploaded_files:
            # Check if file exists in storage before creating document record
            if await os_repo.file_exists(file_info.storage_key, settings.KB_BUCKET):
                verified_files.append(file_info)
            else:
                logger.warning(f"File not found in storage: {file_info.storage_key}")
        if not verified_files:
            raise HTTPException(
                status_code=400,
                detail="No uploaded files found in storage",
            )

        # Before creating records, enforce storage quota based on uploaded file sizes
        total_size_bytes = sum(
            file_info.file_size for file_info in verified_files if file_info.file_size
        )
        total_size_mb = total_size_bytes // (1024 * 1024)

        if total_size_mb > 0:
            workspace_owner_id = await self.workspace_repo.get_workspace_owner_id(
                workspace_id
            )
            if not workspace_owner_id:
                raise HTTPException(
                    status_code=404,
                    detail="Workspace owner not found",
                )

            would_exceed = await self.quota_service.check_quota(
                workspace_owner_id, QuotaResourceType.KB_STORAGE, float(total_size_mb)
            )
            if would_exceed:
                raise HTTPException(
                    status_code=403,
                    detail="Storage quota exceeded. Cannot ingest more documents.",
                )

        # Create document records in database with initial processing status
        docs_create = []
        for file_info in verified_files:
            doc_create = DocumentKBCreate(
                kb_id=kb_id,
                name=file_info.filename,
                type=DocumentType.FILE,
                object_name=file_info.storage_key,
                file_name=file_info.filename,
                file_type=file_info.content_type,
                file_size=file_info.file_size,
                embed_status=AsyncTaskStatus.IN_PROGRESS,  # Mark for async processing
            )
            docs_create.append(doc_create)

        docs = await self.kb_repo.create_documents(docs_create)

        # Update storage quota usage after records are created
        if total_size_mb > 0:
            try:
                workspace_owner_id = await self.workspace_repo.get_workspace_owner_id(
                    workspace_id
                )
                if workspace_owner_id:
                    await self.quota_service.update_quota(
                        workspace_owner_id,
                        QuotaResourceType.KB_STORAGE,
                        float(total_size_mb),
                    )
            except Exception as e:
                logger.warning(
                    f"Failed to update storage quota after document creation: {e}"
                )

        # Start async background task for document processing and embedding
        task = ingest_from_files_task.delay(  # type: ignore
            doc_ids=[str(doc.id) for doc in docs],
            kb_id=str(kb_id),
            user_id=str(user_id),
            workspace_id=str(workspace_id),
        )

        return task

    async def ingest_from_urls(
        self,
        kb_id: UUID,
        user_id: UUID,
        workspace_id: UUID,
        request: URLsUploadRequest,
    ):
        """
        Ingest documents from URLs into a knowledge base.

        Creates document records for each URL and starts an async task to crawl
        and embed the web content.

        Args:
            kb_id: ID of the knowledge base to ingest documents into
            user_id: ID of the user performing the ingestion
            workspace_id: ID of the workspace containing the KB
            request: Request containing URLs and crawling configuration

        Returns:
            The async task object for tracking ingestion progress
        """
        # Verify KB exists and user has access
        kb = await self.kb_repo.get(kb_id, user_id, workspace_id)
        validate_kb_access(kb, user_id)

        urls = request.urls
        deep_crawls = request.deep_crawls

        # Validate URLs before processing to prevent invalid requests
        validate_urls_batch(urls)

        doc_creates = []
        # Estimate storage size for URLs as 0 here (unknown before crawl). Quota
        # will be enforced during file-based ingestion. This path focuses on count.
        for url, deep_crawl in zip(urls, deep_crawls, strict=True):  # type: ignore
            document_create = DocumentKBCreate(
                name=url.split("/")[-1],
                kb_id=kb_id,
                url=url,
                deep_crawl=deep_crawl,
                type=DocumentType.URL,
                file_name=url.split("/")[-1],
                embed_status=AsyncTaskStatus.IN_PROGRESS,
            )
            doc_creates.append(document_create)
        docs = await self.kb_repo.create_documents(doc_creates)
        logger.info(f"Created {len(docs)} documents")
        doc_ids = [str(doc.id) for doc in docs]
        logger.info(f"Doc IDs: {doc_ids}")

        task = ingest_from_website_task.delay(  # type: ignore
            doc_ids=doc_ids,
            kb_id=str(kb_id),
            user_id=str(user_id),
            workspace_id=str(workspace_id),
        )

        return task

    async def list_documents(
        self,
        kb_id: UUID,
        user_id: UUID,
        workspace_id: UUID,
        skip: int = 0,
        limit: int = 10,
        search: str | None = None,
        type: DocumentType | None = None,
    ) -> DocumentsKBRead:
        """
        List documents in a knowledge base with optional filtering.

        Args:
            kb_id: ID of the knowledge base
            user_id: ID of the user requesting the list
            workspace_id: ID of the workspace containing the KB
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            search: Optional search query to filter documents
            type: Optional filter by document type (FILE, URL)

        Returns:
            Paginated list of documents in the knowledge base
        """
        return await self.kb_repo.list_documents(kb_id, skip, limit, search, type)

    async def view_document(
        self,
        kb_id: UUID,
        object_name: str,
        user_id: UUID,
        workspace_id: UUID,
    ) -> str:
        """
        Generate a presigned URL to view a document.

        Args:
            kb_id: ID of the knowledge base containing the document
            object_name: Name/key of the object in storage
            user_id: ID of the user requesting access
            workspace_id: ID of the workspace containing the KB

        Returns:
            Presigned URL for accessing the document (valid for 1 hour)

        Raises:
            HTTPException: If document not found in storage
        """
        await self.kb_repo.get(
            kb_id,
            user_id,
            workspace_id,
        )

        os_repo = get_object_storage_repository()
        is_exists = await os_repo.file_exists(
            object_name, bucket_name=settings.KB_BUCKET
        )
        if not is_exists:
            raise HTTPException(status_code=404, detail="Document not found")

        presigned_url: str = await os_repo.get_presigned_url(
            object_name=object_name,
            bucket_name=settings.KB_BUCKET,
            expires=timedelta(seconds=3600),
        )

        return presigned_url

    async def delete_document(
        self,
        kb_id: UUID,
        document_id: str,
        object_name: str,
        user_id: UUID,
        workspace_id: UUID,
    ) -> dict:
        """
        Delete a document from a knowledge base.

        Performs cleanup across multiple systems: database, object storage,
        vector store, and updates storage quotas.

        Args:
            kb_id: ID of the knowledge base containing the document
            document_id: ID of the document to delete
            object_name: Name/key of the object in storage
            user_id: ID of the user performing the deletion
            workspace_id: ID of the workspace containing the KB

        Returns:
            Dictionary with deletion status and message

        Raises:
            HTTPException: If document not found or access denied
        """
        # Verify user has access to the knowledge base
        await self.kb_repo.get(
            kb_id,
            user_id,
            workspace_id,
        )

        os_repo = get_object_storage_repository()
        if not await os_repo.file_exists(object_name, bucket_name=settings.KB_BUCKET):
            raise HTTPException(
                status_code=404,
                detail="Document not found",
            )

        doc = await self.kb_repo.get_document(
            kb_id=kb_id,
            document_id=document_id,
        )

        if not doc:
            raise DocumentDeletionError(f"Document {document_id} not found")

        # Calculate document size for quota tracking (estimate from file_size if available)
        doc_size_mb = 0
        if hasattr(doc, "file_size") and doc.file_size:
            doc_size_mb = doc.file_size // (1024 * 1024)  # Convert bytes to MB

        # Delete document from multiple systems in reverse dependency order
        # 1. Delete from object storage first (most critical)
        os_repo = get_object_storage_repository()
        await os_repo.delete_file(
            object_name=object_name, bucket_name=settings.KB_BUCKET
        )

        # 2. Delete from vector store (for search/embedding functionality)
        await self.kb_service.delete_documents_by_document_id(document_id=document_id)

        # 3. Soft delete from database (preserves audit trail)
        await self.kb_repo.soft_delete_document(document_id)

        # 4. Update storage quota usage (decrement for space freed up)
        if doc_size_mb > 0 and workspace_id:
            try:
                workspace_owner_id = await self.workspace_repo.get_workspace_owner_id(
                    workspace_id
                )
                if not workspace_owner_id:
                    raise HTTPException(
                        status_code=404,
                        detail="Workspace owner not found",
                    )
                await self.quota_service.update_quota(
                    workspace_owner_id, QuotaResourceType.KB_STORAGE, -doc_size_mb
                )
            except Exception as e:
                logger.warning(
                    f"Failed to update storage quota for document deletion: {e}"
                )

        # 5. Update KB statistics to reflect document removal
        if doc and doc.kb_id:
            await self.kb_repo.update_kb_statistics(UUID(str(doc.kb_id)))

        logger.info(f"Successfully deleted document {document_id}")
        return {
            "status": "COMPLETED",
            "message": "Document deleted successfully",
        }
