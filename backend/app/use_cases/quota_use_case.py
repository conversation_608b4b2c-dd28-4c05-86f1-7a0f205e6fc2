from datetime import datetime
from typing import Any, Literal
from uuid import UUID

from fastapi import Depends, HTTPException
from sqlmodel.ext.asyncio.session import AsyncSession

from app.api.deps import get_async_session
from app.exceptions.quota_exceptions import (
    QuotaContextResolutionError,
    QuotaNotFoundError,
    QuotaValidationError,
)
from app.logger import logger
from app.services.quota import (
    DashboardQuotaService,
    MessageQuotaService,
    ResourceQuotaService,
    UsageOverTimeService,
)


class QuotaUseCase:
    """
    Use case layer for Quota operations.

    This class orchestrates business logic for quota management,
    including message quotas, resource quotas, and dashboard overview data.
    It coordinates between repositories, services, and external payment systems.
    """

    def __init__(self, session: AsyncSession = Depends(get_async_session)):
        """
        Initialize the QuotaUseCase with required dependencies.

        Args:
            session: Database session for repository operations
        """
        self.session = session
        self.usage_over_time_service = UsageOverTimeService(session)
        self.message_quota_service = MessageQuotaService(session)
        self.resource_quota_service = ResourceQuotaService(session)
        self.dashboard_service = DashboardQuotaService(session)

    async def get_credits_info(
        self, user_id: UUID, current_workspace_id: UUID | None = None
    ) -> dict:
        """
        Get message quota information for a user.

        Args:
            user_id: ID of the user
            current_workspace_id: ID of the current workspace for proper quota resolution

        Returns:
            Dictionary containing message quota data

        Raises:
            HTTPException: If quota information cannot be retrieved
        """
        try:
            return await self.message_quota_service.get_credits_info(
                user_id, current_workspace_id
            )
        except (
            QuotaContextResolutionError,
            QuotaNotFoundError,
            QuotaValidationError,
        ) as e:
            logger.error(f"Quota error for user {user_id}: {str(e)}")
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            logger.error(f"Failed to get message quota for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=500, detail="Failed to retrieve message quota"
            )

    async def get_dashboard_overview(
        self, user_id: UUID, current_workspace_id: UUID | None = None
    ) -> dict:
        """
        Get dashboard overview with all quota and resource information for a user.

        Args:
            user_id: ID of the user
            current_workspace_id: ID of the current workspace for proper quota resolution

        Returns:
            Dictionary containing comprehensive dashboard data

        Raises:
            HTTPException: If dashboard information cannot be retrieved
        """
        try:
            return await self.dashboard_service.get_dashboard_overview(
                user_id, current_workspace_id
            )
        except (
            QuotaContextResolutionError,
            QuotaNotFoundError,
            QuotaValidationError,
        ) as e:
            logger.error(f"Quota error for user {user_id}: {str(e)}")
            raise HTTPException(status_code=404, detail=str(e))
        except Exception as e:
            logger.error(
                f"Failed to get dashboard overview for user {user_id}: {str(e)}"
            )
            raise HTTPException(
                status_code=500, detail="Failed to retrieve dashboard overview"
            )

    async def get_usage_over_time(
        self,
        user_id: UUID,
        period: Literal["day", "week", "month", "year"] = "day",
        start_date: datetime | None = None,
        end_date: datetime | None = None,
        current_workspace_id: UUID | None = None,
    ) -> Any:
        """
        Get usage statistics over time with period grouping.

        Args:
            user_id: ID of the user
            period: Time period for grouping (day/week/month/year)
            start_date: Optional start date for filtering
            end_date: Optional end date for filtering
            current_workspace_id: ID of the current workspace for proper quota resolution

        Returns:
            Usage statistics grouped by time period for charting

        Raises:
            HTTPException: If usage data cannot be retrieved
        """
        try:
            stats = await self.usage_over_time_service.get_usage_over_time(
                user_id,
                period=period,
                start_date=start_date,
                end_date=end_date,
                current_workspace_id=current_workspace_id,
            )
            return stats
        except Exception as e:
            logger.error(f"Failed to get usage over time for user {user_id}: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to retrieve usage data")
