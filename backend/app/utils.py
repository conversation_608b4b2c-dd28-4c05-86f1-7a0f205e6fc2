import uuid
from dataclasses import dataclass
from datetime import UTC, datetime, timedelta
from pathlib import Path
from typing import Any
from zoneinfo import ZoneInfo

import emails  # type: ignore
import jwt
from croniter import croniter
from fastapi import HTTPException
from jinja2 import Template
from jwt.exceptions import InvalidTokenError
from sqlalchemy import delete
from sqlalchemy.orm import Session
from sqlmodel import col

from app.core.config import settings
from app.logger import logger
from app.models import AuthorizedUser


def ensure_utc(dt: datetime) -> datetime:
    """Ensure datetime is UTC timezone-aware"""
    if dt.tzinfo is None:
        return dt.replace(tzinfo=UTC)
    return dt.astimezone(UTC)


def get_next_run_time(schedule: str) -> datetime:
    """Get next run time for a schedule."""
    now = datetime.now(UTC)
    cron = croniter(schedule, now)
    next_run = ensure_utc(cron.get_next(datetime))

    # If next run is within 1 minute, get the one after that to avoid scheduler conflicts
    if (next_run - now).total_seconds() <= 60:
        next_run = ensure_utc(cron.get_next(datetime))
    return next_run


def convert_cron_to_timezone(cron_expr: str, target_timezone: str) -> str:
    """
    Convert a cron expression from UTC to target timezone for display purposes.

    This handles basic cron expressions with simple time patterns.
    Complex expressions may not convert accurately due to DST and pattern complexity.

    Args:
        cron_expr: Cron expression in UTC (e.g., "0 9 * * *")
        target_timezone: Target timezone (e.g., "America/New_York")

    Returns:
        Converted cron expression for display in target timezone
    """
    if not cron_expr or target_timezone == "UTC":
        return cron_expr

    try:
        # Parse the cron expression
        parts = cron_expr.split()
        if len(parts) != 5:
            return cron_expr  # Return original if not standard 5-part cron

        minute, hour, day, month, weekday = parts

        # Only convert if hour and minute are simple numbers (not complex patterns)
        if hour.isdigit() and minute.isdigit():
            # Create a reference datetime in UTC with the cron hour/minute
            utc_time = datetime.now(UTC).replace(
                hour=int(hour), minute=int(minute), second=0, microsecond=0
            )

            # Convert to target timezone
            target_time = utc_time.astimezone(ZoneInfo(target_timezone))

            # Update the cron expression with converted time
            converted_hour = target_time.hour
            converted_minute = target_time.minute

            # Handle day rollover (basic case)
            if target_time.day != utc_time.day:
                # Day changed due to timezone conversion
                # This is complex to handle properly for all day/weekday patterns
                # For now, just update the time components and note that day handling
                # would require more complex logic for patterns like "1-5" (Mon-Fri)
                pass

            return f"{converted_minute} {converted_hour} {day} {month} {weekday}"

        return cron_expr  # Return original for complex hour patterns

    except Exception:
        # If conversion fails, return original
        return cron_expr


@dataclass
class EmailData:
    html_content: str
    subject: str


def render_email_template(*, template_name: str, context: dict[str, Any]) -> str:
    template_str = (
        Path(__file__).parent / "email-templates" / "build" / template_name
    ).read_text()
    html_content = Template(template_str).render(context)
    return html_content


def send_email(
    *,
    email_to: str,
    subject: str = "",
    html_content: str = "",
) -> None:
    assert settings.emails_enabled, "no provided configuration for email variables"
    message = emails.Message(
        subject=subject,
        html=html_content,
        mail_from=(settings.EMAILS_FROM_NAME, settings.EMAILS_FROM_EMAIL),
    )

    # attach logo
    logo_path = Path(__file__).parent / "email-templates" / "assets" / "logo.png"
    if logo_path.exists():
        with open(logo_path, "rb") as f:
            logo_data = f.read()
            message.attach(
                filename="logo.png", data=logo_data, content_disposition="inline"
            )

    smtp_options = {"host": settings.SMTP_HOST, "port": settings.SMTP_PORT}
    if settings.SMTP_TLS:
        smtp_options["tls"] = True
    elif settings.SMTP_SSL:
        smtp_options["ssl"] = True
    if settings.SMTP_USER:
        smtp_options["user"] = settings.SMTP_USER
    if settings.SMTP_PASSWORD:
        smtp_options["password"] = settings.SMTP_PASSWORD
    response = message.send(to=email_to, smtp=smtp_options)
    logger.info(f"send email result: {response}")
    return response


def generate_test_email(email_to: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    link = f"{settings.FRONTEND_HOST}/auth/reset-password?token={token}"

    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_new_account_email(
    email_to: str, username: str, password: str
) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New account for user {username}"
    html_content = render_email_template(
        template_name="new_account.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": username,
            "password": password,
            "email": email_to,
            "link": settings.FRONTEND_HOST,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_enterprise_enquiry_email(
    enquiry_data: dict, user_id: str, user_email: str, product_name: str
) -> EmailData:
    """Generate an email notification for a new enterprise enquiry."""
    project_name = settings.PROJECT_NAME
    subject = (
        f"{project_name} - New Enterprise Enquiry from {enquiry_data['company_name']}"
    )

    # Prepare context for email template
    context = {
        "project_name": settings.PROJECT_NAME,
        "first_name": enquiry_data["first_name"],
        "last_name": enquiry_data["last_name"],
        "work_email": enquiry_data["work_email"],
        "work_title": enquiry_data["work_title"],
        "company_name": enquiry_data["company_name"],
        "estimated_monthly_cost": enquiry_data["estimated_monthly_cost"],
        "message": enquiry_data["message"],
        "product_name": product_name,
        "user_id": user_id,
        "user_email": user_email,
    }

    html_content = render_email_template(
        template_name="enterprise_enquiry.html", context=context
    )

    return EmailData(html_content=html_content, subject=subject)


def generate_password_reset_token(email: str) -> str:
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.now(UTC)
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm="HS256",
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> str | None:
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None


def update_related_entities(
    session: Session,
    model,
    parent_id: int,
    new_ids: set[int],
    child_id_field: str,
    parent_id_field: str,
):
    # Fetch existing relations
    existing_relations = (
        session.query(model).filter(getattr(model, parent_id_field) == parent_id).all()
    )
    existing_ids = {
        getattr(relation, child_id_field) for relation in existing_relations
    }

    # Remove relations not in new_ids
    ids_to_remove = existing_ids - new_ids
    if ids_to_remove:
        session.execute(
            delete(model).where(
                (getattr(model, parent_id_field) == parent_id)
                & (col(getattr(model, child_id_field)).in_(ids_to_remove))
            )
        )

    # Add new relations
    ids_to_add = new_ids - existing_ids
    for id_to_add in ids_to_add:
        new_relation = model(**{parent_id_field: parent_id, child_id_field: id_to_add})
        session.add(new_relation)


def update_entity_relations(
    session: Session,
    entity_id: int,
    updates: dict[str, list[int]],
    model_mapping: dict[str, dict[str, Any]],
):
    try:
        for field, new_ids in updates.items():
            if field in model_mapping:
                update_related_entities(
                    session,
                    model_mapping[field]["model"],
                    entity_id,
                    set(new_ids),
                    model_mapping[field]["child_id_field"],
                    model_mapping[field]["parent_id_field"],
                )
        session.commit()
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


def is_allowed_access_to_workspace(
    user: AuthorizedUser, workspace_id: uuid.UUID
) -> bool:
    if user.is_superuser:
        return True

    all_workspaces = list(user.workspaces or []) + list(user.own_workspaces or [])
    target_workspace = next((w for w in all_workspaces if w.id == workspace_id), None)
    if not target_workspace or target_workspace.is_deleted:
        return False

    return True


def is_user_belong_to_workspaces(user: AuthorizedUser, workspace_id: uuid.UUID) -> bool:
    workspace_ids = [w.id for w in user.workspaces or []]
    return workspace_id in workspace_ids


def generate_plan_change_request_email(
    request_data: dict,
    user_id: str,
    user_email: str,
) -> EmailData:
    """Generate email content for plan change request"""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New Plan Change Request from {request_data['first_name']} {request_data['last_name']}"

    context = {
        "project_name": project_name,
        "first_name": request_data["first_name"],
        "last_name": request_data["last_name"],
        "work_email": request_data["work_email"],
        "work_title": request_data["work_title"],
        "company_name": request_data["company_name"],
        "customer_id": request_data["customer_id"],
        "user_id": user_id,
        "user_email": user_email,
        "current_plan": request_data["current_plan"],
        "requested_plan": request_data["requested_plan"],
        "reason": request_data["reason"],
    }

    html_content = render_email_template(
        template_name="plan_change_request.html", context=context
    )

    return EmailData(subject=subject, html_content=html_content)


def generate_activation_email(activation_link: str) -> EmailData:
    """Generate email content for account activation."""
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Activate Your Account"

    html_content = render_email_template(
        template_name="account_activation.html",
        context={
            "project_name": project_name,
            "activation_link": activation_link,
        },
    )

    return EmailData(subject=subject, html_content=html_content)


def generate_welcome_email(full_name: str) -> EmailData:
    """Generate welcome email content for new users."""
    project_name = settings.PROJECT_NAME
    subject = f"Welcome to {project_name}!"

    html_content = render_email_template(
        template_name="welcome_email.html",
        context={
            "project_name": project_name,
            "full_name": full_name,
            "dashboard_link": f"{settings.FRONTEND_HOST}",
            "discord_link": settings.DISCORD_INVITE_LINK,
        },
    )

    return EmailData(subject=subject, html_content=html_content)


# Mailchimp Integration Helper Functions


def extract_user_names(user) -> tuple[str | None, str | None]:
    """
    Extract first and last name from user profile.

    Args:
        user: User model instance

    Returns:
        Tuple of (first_name, last_name) or (None, None) if not available
    """
    if not user.full_name:
        return None, None

    # Split full name into parts
    name_parts = user.full_name.strip().split()

    if not name_parts:
        return None, None
    elif len(name_parts) == 1:
        # Only first name available
        return name_parts[0], None
    else:
        # First name + last name (may include middle names)
        first_name = name_parts[0]
        last_name = " ".join(name_parts[1:])
        return first_name, last_name


def get_user_mailchimp_data(
    user,
    signup_method: str | None = None,
    selected_provider: str | None = None,
    additional_tags: list | None = None,
    additional_merge_fields: dict[str, str] | None = None,
) -> dict[str, Any]:
    """
    Prepare user data for Mailchimp synchronization.

    Args:
        user: User model instance
        signup_method: How the user signed up (google_oauth, email, etc.)
        selected_provider: Cloud provider selected by user
        additional_tags: Extra tags to apply
        additional_merge_fields: Extra merge fields to include

    Returns:
        Dictionary with user data formatted for Mailchimp tasks
    """
    first_name, last_name = extract_user_names(user)

    # Base merge fields
    merge_fields = {}
    if signup_method:
        merge_fields["SIGNUP_METHOD"] = signup_method
    if selected_provider:
        merge_fields["CLOUD_PROVIDER"] = selected_provider

    # Add additional merge fields
    if additional_merge_fields:
        merge_fields.update(additional_merge_fields)

    # Base tags
    tags = []
    if signup_method:
        tags.append(f"signup_{signup_method}")
    if selected_provider:
        tags.append(f"cloud_{selected_provider.lower()}")

    # Add additional tags
    if additional_tags:
        tags.extend(additional_tags)

    return {
        "user_id": str(user.id),
        "email": user.email,
        "full_name": user.full_name or "",
        "first_name": first_name,
        "last_name": last_name,
        "merge_fields": merge_fields,
        "tags": tags,
    }


def format_user_display_name(
    first_name: str | None, last_name: str | None, email: str
) -> str:
    """
    Format a display name for the user.

    Args:
        first_name: User's first name
        last_name: User's last name
        email: User's email address

    Returns:
        Formatted display name
    """
    if first_name and last_name:
        return f"{first_name} {last_name}"
    elif first_name:
        return first_name
    else:
        # Fallback to email prefix
        return email.split("@")[0].replace(".", " ").replace("_", " ").title()


def validate_user_data_for_mailchimp(user) -> tuple[bool, str]:
    """
    Validate that user data is suitable for Mailchimp sync.

    Args:
        user: User model instance

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not user.email:
        return False, "User email is required"

    if not user.is_active:
        return False, "User is not active"

    if not user.is_email_verified:
        return False, "User email is not verified"

    return True, ""


def should_sync_user_to_mailchimp(user) -> bool:
    """
    Determine if a user should be synced to Mailchimp.

    Args:
        user: User model instance

    Returns:
        True if user should be synced, False otherwise
    """
    is_valid, _ = validate_user_data_for_mailchimp(user)
    return is_valid
