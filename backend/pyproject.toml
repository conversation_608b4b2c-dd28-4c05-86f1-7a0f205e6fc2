[project]
name = "app"
version = "0.1.0"
description = ""
requires-python = ">=3.11,<4.0"
dependencies = [
	"fastapi[standard]<1.0.0,>=0.115.6",
	"python-multipart<1.0.0,>=0.0.7",
	"email-validator<*******,>=2.1.0.post1",
	"passlib[bcrypt]<2.0.0,>=1.7.4",
	"tenacity<9.0.0,>=8.2.3",
	"pydantic>2.0",
	"emails<1.0,>=0.6",
	"jinja2<4.0.0,>=3.1.4",
	"alembic<2.0.0,>=1.12.1",
	"httpx<1.0.0,>=0.25.1",
	"psycopg[binary]>=3.1.13,<4.0.0",
	"sqlmodel<1.0.0,>=0.0.21",
	# Pin bcrypt until passlib supports the latest
	"bcrypt==4.0.1",
	"pydantic-settings<3.0.0,>=2.2.1",
	"pyjwt<3.0.0,>=2.8.0",
	"cryptography==44.0.0",
	"pyotp>=2.9.0",
	"qrcode[pil]>=7.4.2",
	"boto3>=1.37.24",
	"redis==5.2.1",
	"python-redis-lock==4.0.0",
	"rq-scheduler==0.14.0",
	"Faker==33.1.0",
	"alembic-autogenerate-enums==0.1.2",
	"beautifulsoup4>=4.12.3",
	"requests>=2.32.3",
	"fastapi-sso>=0.17.0",
	"authlib>=1.4.0",
	"itsdangerous>=2.2.0",
	"celery[redis]>=5.4.0",
	"flower>=2.0.1",
	"anthropic>=0.38.0",
	"greenlet>=3.1.1",
	"websockets>=12.0",
	"psycopg-pool>=3.2.4",
	"croniter>=6.0.0",
	"loguru>=0.7.3",
	"nest-asyncio>=1.6.0",
	"mcp==1.9.4",
	"minio>=7.2.15",
	"pytest-asyncio>=0.23.8",
	"python-slugify>=8.0.4",
	"pdfminer-six>=20250327",
	"langchain-mcp-adapters>=0.0.5",
	"langchain-postgres>=0.0.13",
	"langchain==0.3.26",
	"langchain-aws==0.2.30",
	"langchain-openai>=0.3.28",
	"langchain-anthropic>=0.3.18",
	"langchain-community>=0.3.14",
	"langgraph==0.6.4",
	"langgraph-checkpoint-postgres>=2.0.23",
	"llama-cloud-services>=0.6.9",
	"llama-index-core>=0.12.27",
	"llama-index-readers-file>=0.4.9",
	"llama-index-vector-stores-qdrant>=0.6.0",
	"llama-index-embeddings-bedrock<=0.5.0",
	"llama-index-postprocessor-bedrock-rerank>=0.3.1",
	"llama-index-llms-langchain>=0.6.1",
	"llama-index-llms-bedrock>=0.3.8",
	"llama-index-retrievers-bm25>=0.5.2",
	"llama-index-readers-docling>=0.3.2",
	"llama-index-llms-cohere>=0.5.0",
	"llama-index-embeddings-cohere>=0.5.1",
	"llama-index-postprocessor-cohere-rerank>=0.4.0",
	"qdrant-client>=1.15.1,<1.16.0",
	# "fastembed>=0.7.0",
	"langfuse==3.2.2",
	"opentelemetry-api>=1.36.0,<2.0.0",
	"opentelemetry-sdk>=1.36.0,<2.0.0",
	"minio>=7.0.0",
	"miniopy-async>=1.19.0",
	"pyrefly>=0.17.1",
	"pip>=25.1.1",
	"alembic-postgresql-enum>=1.7.0",
	"psutil>=5.9.0",
	"fitz>=0.0.1.dev2",
	"async-lru>=2.0.5",
	"crawl4ai==0.7.2",
	"python-magic>=0.4.27",
	"slowapi>=0.1.9",
	"pdf2image>=1.17.0",
	"uvloop>=0.21.0",
	"sqlalchemy-utils>=0.41.2",
	"rich>=13.9.4",
	"partial-json-parser>=*******.post6",
	"litellm>=1.75.7",
	"google-cloud-compute>=1.33.0",
	"google-auth>=2.38.0",
	"google-api-python-client>=2.177.0",
	"google-cloud-core>=2.4.3",
	# "google-cloud-sql-admin",
	# "google-cloud-container",
	"google-cloud-functions>=1.20.4",
	"sentry-sdk[fastapi]>=1.45.1",
	"langchain-core>=0.3.75",
	"llama-index-embeddings-litellm>=0.3.0",
	"opentelemetry-exporter-otlp>=1.36.0",
	"google-cloud-storage<3.0.0",
	"langchain-google-vertexai>=2.0.7",
	"markitdown[all]>=0.1.3",
]

[tool.uv]
dev-dependencies = [
	"pytest<8.0.0,>=7.4.3",
	"mypy<2.0.0,>=1.8.0",
	"ruff<1.0.0,>=0.2.2",
	"pre-commit<4.0.0,>=3.6.2",
	"types-passlib<*******,>=1.7.7.20240106",
	"coverage<8.0.0,>=7.4.3",
]

[tool.uv.sources]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py312"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
	"E",      # pycodestyle errors
	"W",      # pycodestyle warnings
	"F",      # pyflakes
	"B",      # flake8-bugbear
	"C4",     # flake8-comprehensions
	"UP",     # pyupgrade
	"ARG001", # unused arguments in functions
]
ignore = [
	"E501",   # line too long, handled by black
	"B008",   # do not perform function calls in argument defaults
	"W191",   # indentation contains tabs
	"B904",   # Allow raising exceptions without from e, for HTTPException
	"ARG001", # unused arguments in functions
	"UP038",  # unused import
	"UP035",
	"E712",
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true

[tool.pytest.ini_options]
filterwarnings = ["ignore::DeprecationWarning", "ignore::UserWarning"]
log_cli = true
log_cli_level = "INFO"
addopts = "-s"
[tool.pyrefly]
# ===== Project Configuration =====
# Strategic project exclusions for better performance
project_excludes = [
	"**/venv*",
	"**/.venv*",
	"**/alembic*",
	"**/htmlcov*",
	"**/__pycache__*",
	"**/node_modules*",
	"**/experiments*",
	"**/data*",
	"**/scripts*",
	"**/tests*",
]

# Include source directories for type checking
project_includes = ["app"]

# ===== Environment Configuration =====
# Set Python version to match runtime environment
python_version = "3.12"
# Platform configuration (use "auto" to detect automatically)
python_platform = "auto"
# Search paths for module resolution
search_path = ["app", "."]

# ===== Type Check Settings =====
# Replace problematic imports with Any to reduce noise
replace_imports_with_any = [
	"alembic.*",     # Migration files often have dynamic imports
	"tests.*",       # Test files may have dynamic imports
	"experiments.*", # Experimental code may have type issues
]

# Ignore errors in generated code
ignore_errors_in_generated_code = true
# Handle untyped imports gracefully
use_untyped_imports = true
# Ignore missing source files
ignore_missing_source = true

# ===== Error Suppression =====
[tool.pyrefly.errors]
# Suppress common false positives
import-error = false
missing-module-attribute = false
# Allow some flexibility in assignments during development
bad-assignment = false

# ===== Performance Optimization =====
# Note: Performance settings are handled via CLI flags in current pyrefly version
# Use: pyrefly check --parallel for parallel processing
# Caching is enabled by default in recent versions

# ===== Sub-configurations for Specific Directories =====

# Test directory - more lenient checking
[[tool.pyrefly.sub_config]]
matches = "app/tests/**"
[tool.pyrefly.sub_config.errors]
# Allow more dynamic behavior in tests
bad-argument-count = false
bad-argument-type = false
# Tests often use pytest fixtures
import-error = false

# Migration files - very lenient checking
[[tool.pyrefly.sub_config]]
matches = "alembic/versions/**"
# Replace many imports with Any for migration scripts
replace_imports_with_any = ["*.*"]
[tool.pyrefly.sub_config.errors]
# Suppress most errors in migration files
import-error = false
missing-module-attribute = false
bad-assignment = false

# Experimental code - most lenient
[[tool.pyrefly.sub_config]]
matches = "experiments/**"
replace_imports_with_any = ["*.*"]
[tool.pyrefly.sub_config.errors]
import-error = false
missing-module-attribute = false
bad-assignment = false
unknown-name = false
