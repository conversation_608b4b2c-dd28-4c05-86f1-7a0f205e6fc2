# Domain-Driven Design + Clean Architecture Implementation Guide

## Overview

This document provides a detailed implementation roadmap for refactoring the `/chat/stream` API using Domain-Driven Design principles and Clean Architecture patterns. The goal is to eliminate architectural debt while maintaining backwards compatibility.

## Target Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        PC[Controllers]
        PR[Route Handlers]
        PV[Request/Response Models]
    end

    subgraph "Application Layer"
        AU[Use Cases]
        AS[Application Services]
        AD[DTOs]
        AC[Commands/Queries]
    end

    subgraph "Domain Layer"
        subgraph "Conversation Bounded Context"
            CE[Conversation Entity]
            ME[Message Entity]
            CVO[Value Objects]
            CR[IConversationRepository]
            CS[Conversation Services]
        end

        subgraph "Streaming Bounded Context"
            SE[Stream Entity]
            SEV[Stream Events]
            SVO[Stream Value Objects]
            SR[IStreamRepository]
            SS[Stream Services]
        end

        subgraph "Agent Bounded Context"
            AE[Agent Entity]
            AC[Agent Config]
            AVO[Agent Value Objects]
            AR[IAgentRepository]
            AS[Agent Services]
        end

        subgraph "Shared Kernel"
            DE[Domain Events]
            DT[Domain Types]
            EX[Exceptions]
        end
    end

    subgraph "Infrastructure Layer"
        subgraph "Persistence"
            SQLR[SQL Repositories]
            REDIS[Redis Repositories]
            CACHE[Cache Repositories]
        end

        subgraph "External Services"
            LANG[LangGraph Client]
            KB[Knowledge Base Client]
            CLOUD[Cloud Services Client]
        end

        subgraph "Messaging"
            EVENT[Event Bus]
            QUEUE[Message Queue]
        end
    end

    PC --> AU
    PR --> AS
    AU --> CS
    AU --> SS
    AU --> AS
    CS --> CR
    SS --> SR
    AS --> AR
    CR --> SQLR
    SR --> REDIS
    AR --> SQLR
    CS --> EVENT
    SS --> EVENT
```

## Folder Structure

```
backend/app/
├── api/                           # Presentation Layer
│   ├── routes/
│   │   ├── chat/
│   │   │   ├── __init__.py
│   │   │   ├── stream_controller.py     # New clean controller
│   │   │   └── conversation_controller.py
│   │   └── autonomous_agent.py          # Legacy (to be removed)
│   ├── dependencies/
│   │   ├── __init__.py
│   │   ├── container.py                 # DI Container
│   │   └── deps.py                      # FastAPI Dependencies
│   └── models/                          # Request/Response DTOs
│       ├── __init__.py
│       ├── chat_models.py
│       └── stream_models.py
│
├── application/                   # Application Layer
│   ├── __init__.py
│   ├── use_cases/
│   │   ├── __init__.py
│   │   ├── conversation/
│   │   │   ├── __init__.py
│   │   │   ├── start_conversation.py
│   │   │   ├── add_message.py
│   │   │   └── get_conversation_history.py
│   │   ├── streaming/
│   │   │   ├── __init__.py
│   │   │   ├── start_stream.py
│   │   │   ├── reconnect_stream.py
│   │   │   ├── cancel_stream.py
│   │   │   └── get_stream_status.py
│   │   └── agent/
│   │       ├── __init__.py
│   │       ├── configure_agent.py
│   │       └── execute_agent.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── conversation_service.py
│   │   ├── streaming_service.py
│   │   └── agent_orchestration_service.py
│   ├── dtos/
│   │   ├── __init__.py
│   │   ├── conversation_dtos.py
│   │   ├── streaming_dtos.py
│   │   └── agent_dtos.py
│   └── interfaces/
│       ├── __init__.py
│       ├── event_bus.py
│       ├── external_services.py
│       └── cache.py
│
├── domain/                        # Domain Layer
│   ├── __init__.py
│   ├── conversation/              # Conversation Bounded Context
│   │   ├── __init__.py
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── conversation.py
│   │   │   ├── message.py
│   │   │   └── participant.py
│   │   ├── value_objects/
│   │   │   ├── __init__.py
│   │   │   ├── conversation_id.py
│   │   │   ├── message_id.py
│   │   │   ├── message_role.py
│   │   │   └── message_content.py
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── conversation_repository.py
│   │   │   └── message_repository.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── conversation_factory.py
│   │   │   └── title_generator.py
│   │   └── events/
│   │       ├── __init__.py
│   │       ├── conversation_started.py
│   │       ├── message_added.py
│   │       └── conversation_renamed.py
│   │
│   ├── streaming/                 # Streaming Bounded Context
│   │   ├── __init__.py
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── stream.py
│   │   │   ├── stream_session.py
│   │   │   └── stream_event.py
│   │   ├── value_objects/
│   │   │   ├── __init__.py
│   │   │   ├── stream_id.py
│   │   │   ├── stream_status.py
│   │   │   ├── event_position.py
│   │   │   └── stream_metadata.py
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── stream_repository.py
│   │   │   └── stream_event_repository.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── stream_factory.py
│   │   │   ├── event_serializer.py
│   │   │   └── stream_coordinator.py
│   │   └── events/
│   │       ├── __init__.py
│   │       ├── stream_started.py
│   │       ├── stream_event_received.py
│   │       ├── stream_interrupted.py
│   │       └── stream_completed.py
│   │
│   ├── agent/                     # Agent Bounded Context
│   │   ├── __init__.py
│   │   ├── entities/
│   │   │   ├── __init__.py
│   │   │   ├── agent.py
│   │   │   ├── agent_config.py
│   │   │   └── tool_config.py
│   │   ├── value_objects/
│   │   │   ├── __init__.py
│   │   │   ├── agent_id.py
│   │   │   ├── workspace_id.py
│   │   │   └── execution_context.py
│   │   ├── repositories/
│   │   │   ├── __init__.py
│   │   │   ├── agent_repository.py
│   │   │   └── agent_config_repository.py
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── agent_factory.py
│   │   │   ├── config_builder.py
│   │   │   └── execution_engine.py
│   │   └── events/
│   │       ├── __init__.py
│   │       ├── agent_configured.py
│   │       ├── execution_started.py
│   │       └── execution_completed.py
│   │
│   └── shared/                    # Shared Kernel
│       ├── __init__.py
│       ├── events/
│       │   ├── __init__.py
│       │   ├── domain_event.py
│       │   └── event_dispatcher.py
│       ├── exceptions/
│       │   ├── __init__.py
│       │   ├── domain_exception.py
│       │   ├── validation_exception.py
│       │   └── not_found_exception.py
│       ├── value_objects/
│       │   ├── __init__.py
│       │   ├── base_value_object.py
│       │   ├── entity_id.py
│       │   └── timestamp.py
│       └── entities/
│           ├── __init__.py
│           ├── base_entity.py
│           └── aggregate_root.py
│
├── infrastructure/                # Infrastructure Layer
│   ├── __init__.py
│   ├── persistence/
│   │   ├── __init__.py
│   │   ├── sqlalchemy/
│   │   │   ├── __init__.py
│   │   │   ├── conversation_repository.py
│   │   │   ├── message_repository.py
│   │   │   ├── agent_repository.py
│   │   │   └── unit_of_work.py
│   │   ├── redis/
│   │   │   ├── __init__.py
│   │   │   ├── stream_repository.py
│   │   │   ├── stream_event_repository.py
│   │   │   └── cache_repository.py
│   │   └── memory/
│   │       ├── __init__.py
│   │       └── in_memory_repositories.py
│   ├── external_services/
│   │   ├── __init__.py
│   │   ├── langgraph_client.py
│   │   ├── knowledge_base_client.py
│   │   ├── cloud_services_client.py
│   │   └── notification_client.py
│   ├── messaging/
│   │   ├── __init__.py
│   │   ├── in_process_event_bus.py
│   │   ├── redis_event_bus.py
│   │   └── event_handlers/
│   │       ├── __init__.py
│   │       ├── conversation_handlers.py
│   │       ├── streaming_handlers.py
│   │       └── agent_handlers.py
│   └── configuration/
│       ├── __init__.py
│       ├── dependency_injection.py
│       └── service_registration.py
│
└── services/agent/                # Legacy (to be migrated)
    ├── base_service.py            # To be decomposed
    ├── stream_persistence.py      # To be replaced
    └── ...                        # Other legacy files
```

## Phase 1: Domain Model Implementation (Week 1-2)

### 1.1 Shared Kernel Components

#### Base Domain Event

```python
# domain/shared/events/domain_event.py
from abc import ABC
from datetime import datetime, UTC
from typing import Any, Dict
from uuid import UUID, uuid4

from domain.shared.value_objects.timestamp import Timestamp

class DomainEvent(ABC):
    """Base class for all domain events."""

    def __init__(self, aggregate_id: str, occurred_at: datetime | None = None):
        self.event_id = str(uuid4())
        self.aggregate_id = aggregate_id
        self.occurred_at = occurred_at or datetime.now(UTC)
        self.version = 1

    def to_dict(self) -> Dict[str, Any]:
        """Serialize event for persistence/messaging."""
        return {
            "event_id": self.event_id,
            "event_type": self.__class__.__name__,
            "aggregate_id": self.aggregate_id,
            "occurred_at": self.occurred_at.isoformat(),
            "version": self.version,
            "data": self._get_event_data()
        }

    def _get_event_data(self) -> Dict[str, Any]:
        """Override in concrete events to provide event-specific data."""
        return {}
```

#### Base Entity

```python
# domain/shared/entities/base_entity.py
from abc import ABC
from typing import List, TypeVar
from uuid import UUID

from domain.shared.events.domain_event import DomainEvent

T = TypeVar('T', bound='BaseEntity')

class BaseEntity(ABC):
    """Base class for all domain entities."""

    def __init__(self, id: UUID):
        self._id = id
        self._domain_events: List[DomainEvent] = []

    @property
    def id(self) -> UUID:
        return self._id

    def _raise_domain_event(self, event: DomainEvent) -> None:
        """Add a domain event to be published."""
        self._domain_events.append(event)

    def get_domain_events(self) -> List[DomainEvent]:
        """Get all pending domain events."""
        return self._domain_events.copy()

    def clear_domain_events(self) -> None:
        """Clear all pending domain events."""
        self._domain_events.clear()

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, BaseEntity):
            return False
        return self._id == other._id

    def __hash__(self) -> int:
        return hash(self._id)
```

#### Value Objects Base

```python
# domain/shared/value_objects/base_value_object.py
from abc import ABC
from typing import Any

class BaseValueObject(ABC):
    """Base class for all value objects."""

    def __eq__(self, other: Any) -> bool:
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__

    def __hash__(self) -> int:
        return hash(tuple(sorted(self.__dict__.items())))

    def __repr__(self) -> str:
        attrs = ', '.join(f'{k}={v!r}' for k, v in self.__dict__.items())
        return f'{self.__class__.__name__}({attrs})'
```

#### Domain Exceptions

```python
# domain/shared/exceptions/domain_exception.py
class DomainException(Exception):
    """Base exception for domain-related errors."""

    def __init__(self, message: str, code: str | None = None):
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__

class ValidationException(DomainException):
    """Exception for domain validation errors."""
    pass

class NotFoundError(DomainException):
    """Exception for entity not found errors."""
    pass

class BusinessRuleViolationError(DomainException):
    """Exception for business rule violations."""
    pass
```

### 1.2 Conversation Domain

#### Conversation Value Objects

```python
# domain/conversation/value_objects/conversation_id.py
from uuid import UUID, uuid4
from domain.shared.value_objects.base_value_object import BaseValueObject

class ConversationId(BaseValueObject):
    """Conversation identifier value object."""

    def __init__(self, value: UUID | str):
        if isinstance(value, str):
            self._value = UUID(value)
        else:
            self._value = value

    @classmethod
    def generate(cls) -> 'ConversationId':
        """Generate a new conversation ID."""
        return cls(uuid4())

    @property
    def value(self) -> UUID:
        return self._value

    def __str__(self) -> str:
        return str(self._value)

# domain/conversation/value_objects/message_role.py
from enum import Enum
from domain.shared.value_objects.base_value_object import BaseValueObject

class MessageRole(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"

class MessageRoleVO(BaseValueObject):
    """Message role value object."""

    def __init__(self, role: MessageRole | str):
        if isinstance(role, str):
            self._role = MessageRole(role)
        else:
            self._role = role

    @property
    def value(self) -> MessageRole:
        return self._role

    def is_user(self) -> bool:
        return self._role == MessageRole.USER

    def is_assistant(self) -> bool:
        return self._role == MessageRole.ASSISTANT

    def __str__(self) -> str:
        return self._role.value

# domain/conversation/value_objects/message_content.py
from domain.shared.value_objects.base_value_object import BaseValueObject
from domain.shared.exceptions.domain_exception import ValidationException

class MessageContent(BaseValueObject):
    """Message content value object with validation."""

    def __init__(self, content: str):
        if not content or not content.strip():
            raise ValidationException("Message content cannot be empty")

        if len(content) > 50000:  # 50KB limit
            raise ValidationException("Message content exceeds maximum length")

        self._content = content.strip()

    @property
    def value(self) -> str:
        return self._content

    def is_empty(self) -> bool:
        return not self._content

    def word_count(self) -> int:
        return len(self._content.split())

    def __str__(self) -> str:
        return self._content
```

#### Message Entity

```python
# domain/conversation/entities/message.py
from datetime import datetime, UTC
from typing import List, Optional
from uuid import UUID

from domain.shared.entities.base_entity import BaseEntity
from domain.conversation.value_objects.message_id import MessageId
from domain.conversation.value_objects.message_role import MessageRoleVO
from domain.conversation.value_objects.message_content import MessageContent
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.conversation.events.message_added import MessageAdded

class Message(BaseEntity):
    """Message entity in the conversation domain."""

    def __init__(
        self,
        id: MessageId,
        conversation_id: ConversationId,
        content: MessageContent,
        role: MessageRoleVO,
        created_at: datetime | None = None,
        is_interrupt: bool = False,
        interrupt_message: str | None = None
    ):
        super().__init__(id.value)
        self._message_id = id
        self._conversation_id = conversation_id
        self._content = content
        self._role = role
        self._created_at = created_at or datetime.now(UTC)
        self._is_interrupt = is_interrupt
        self._interrupt_message = interrupt_message
        self._components: List['MessageComponent'] = []

    @classmethod
    def create(
        cls,
        conversation_id: ConversationId,
        content: str,
        role: str
    ) -> 'Message':
        """Factory method to create a new message."""
        message_id = MessageId.generate()
        message_content = MessageContent(content)
        message_role = MessageRoleVO(role)

        message = cls(
            id=message_id,
            conversation_id=conversation_id,
            content=message_content,
            role=message_role
        )

        # Raise domain event
        message._raise_domain_event(MessageAdded(
            aggregate_id=str(conversation_id.value),
            message_id=str(message_id.value),
            content=content,
            role=role
        ))

        return message

    @property
    def message_id(self) -> MessageId:
        return self._message_id

    @property
    def conversation_id(self) -> ConversationId:
        return self._conversation_id

    @property
    def content(self) -> MessageContent:
        return self._content

    @property
    def role(self) -> MessageRoleVO:
        return self._role

    @property
    def created_at(self) -> datetime:
        return self._created_at

    @property
    def is_interrupt(self) -> bool:
        return self._is_interrupt

    def mark_as_interrupt(self, interrupt_message: str) -> None:
        """Mark message as interrupted with reason."""
        self._is_interrupt = True
        self._interrupt_message = interrupt_message

    def add_component(self, component: 'MessageComponent') -> None:
        """Add a component to this message."""
        self._components.append(component)

    def get_components(self) -> List['MessageComponent']:
        """Get all message components."""
        return self._components.copy()
```

#### Conversation Entity

```python
# domain/conversation/entities/conversation.py
from datetime import datetime, UTC
from typing import List, Optional
from uuid import UUID

from domain.shared.entities.base_entity import BaseEntity
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.conversation.entities.message import Message
from domain.conversation.events.conversation_started import ConversationStarted
from domain.conversation.events.conversation_renamed import ConversationRenamed
from domain.shared.exceptions.domain_exception import ValidationException, BusinessRuleViolationError

class Conversation(BaseEntity):
    """Conversation aggregate root."""

    def __init__(
        self,
        id: ConversationId,
        agent_id: UUID,
        workspace_id: UUID,
        title: str = "New Conversation",
        created_at: datetime | None = None,
        resource_id: UUID | None = None
    ):
        super().__init__(id.value)
        self._conversation_id = id
        self._agent_id = agent_id
        self._workspace_id = workspace_id
        self._title = title
        self._created_at = created_at or datetime.now(UTC)
        self._resource_id = resource_id
        self._messages: List[Message] = []
        self._is_deleted = False

    @classmethod
    def start_new(
        cls,
        agent_id: UUID,
        workspace_id: UUID,
        initial_message_content: str,
        resource_id: UUID | None = None
    ) -> 'Conversation':
        """Factory method to start a new conversation."""
        conversation_id = ConversationId.generate()

        conversation = cls(
            id=conversation_id,
            agent_id=agent_id,
            workspace_id=workspace_id,
            resource_id=resource_id
        )

        # Add initial user message
        conversation.add_message(initial_message_content, "user")

        # Raise domain event
        conversation._raise_domain_event(ConversationStarted(
            aggregate_id=str(conversation_id.value),
            agent_id=str(agent_id),
            workspace_id=str(workspace_id),
            resource_id=str(resource_id) if resource_id else None
        ))

        return conversation

    def add_message(self, content: str, role: str) -> Message:
        """Add a new message to the conversation."""
        if self._is_deleted:
            raise BusinessRuleViolationError("Cannot add message to deleted conversation")

        message = Message.create(
            conversation_id=self._conversation_id,
            content=content,
            role=role
        )

        self._messages.append(message)

        # Collect events from message
        for event in message.get_domain_events():
            self._raise_domain_event(event)
        message.clear_domain_events()

        return message

    def rename(self, new_title: str) -> None:
        """Rename the conversation."""
        if not new_title or not new_title.strip():
            raise ValidationException("Title cannot be empty")

        old_title = self._title
        self._title = new_title.strip()

        self._raise_domain_event(ConversationRenamed(
            aggregate_id=str(self._conversation_id.value),
            old_title=old_title,
            new_title=new_title
        ))

    def get_last_user_message(self) -> Optional[Message]:
        """Get the last user message in the conversation."""
        user_messages = [msg for msg in self._messages if msg.role.is_user()]
        return user_messages[-1] if user_messages else None

    def get_last_assistant_message(self) -> Optional[Message]:
        """Get the last assistant message in the conversation."""
        assistant_messages = [msg for msg in self._messages if msg.role.is_assistant()]
        return assistant_messages[-1] if assistant_messages else None

    def get_user_message_count(self) -> int:
        """Get count of user messages."""
        return len([msg for msg in self._messages if msg.role.is_user()])

    def is_first_exchange(self) -> bool:
        """Check if this is the first user message."""
        return self.get_user_message_count() <= 1

    # Properties
    @property
    def conversation_id(self) -> ConversationId:
        return self._conversation_id

    @property
    def agent_id(self) -> UUID:
        return self._agent_id

    @property
    def workspace_id(self) -> UUID:
        return self._workspace_id

    @property
    def title(self) -> str:
        return self._title

    @property
    def created_at(self) -> datetime:
        return self._created_at

    @property
    def messages(self) -> List[Message]:
        return self._messages.copy()

    @property
    def resource_id(self) -> Optional[UUID]:
        return self._resource_id
```

#### Domain Events

```python
# domain/conversation/events/conversation_started.py
from typing import Dict, Any, Optional
from domain.shared.events.domain_event import DomainEvent

class ConversationStarted(DomainEvent):
    """Event raised when a new conversation is started."""

    def __init__(
        self,
        aggregate_id: str,
        agent_id: str,
        workspace_id: str,
        resource_id: Optional[str] = None
    ):
        super().__init__(aggregate_id)
        self.agent_id = agent_id
        self.workspace_id = workspace_id
        self.resource_id = resource_id

    def _get_event_data(self) -> Dict[str, Any]:
        return {
            "agent_id": self.agent_id,
            "workspace_id": self.workspace_id,
            "resource_id": self.resource_id
        }

# domain/conversation/events/message_added.py
from typing import Dict, Any
from domain.shared.events.domain_event import DomainEvent

class MessageAdded(DomainEvent):
    """Event raised when a message is added to a conversation."""

    def __init__(
        self,
        aggregate_id: str,
        message_id: str,
        content: str,
        role: str
    ):
        super().__init__(aggregate_id)
        self.message_id = message_id
        self.content = content
        self.role = role

    def _get_event_data(self) -> Dict[str, Any]:
        return {
            "message_id": self.message_id,
            "content": self.content,
            "role": self.role
        }
```

#### Repository Interfaces

```python
# domain/conversation/repositories/conversation_repository.py
from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from domain.conversation.entities.conversation import Conversation
from domain.conversation.value_objects.conversation_id import ConversationId

class IConversationRepository(ABC):
    """Interface for conversation repository."""

    @abstractmethod
    async def get_by_id(self, conversation_id: ConversationId) -> Optional[Conversation]:
        """Get conversation by ID."""
        pass

    @abstractmethod
    async def save(self, conversation: Conversation) -> None:
        """Save conversation."""
        pass

    @abstractmethod
    async def get_by_workspace(
        self,
        workspace_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Get conversations by workspace."""
        pass

    @abstractmethod
    async def delete(self, conversation_id: ConversationId) -> None:
        """Delete conversation."""
        pass

    @abstractmethod
    async def exists(self, conversation_id: ConversationId) -> bool:
        """Check if conversation exists."""
        pass

# domain/conversation/repositories/message_repository.py
from abc import ABC, abstractmethod
from typing import List, Optional
from uuid import UUID

from domain.conversation.entities.message import Message
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.conversation.value_objects.message_id import MessageId

class IMessageRepository(ABC):
    """Interface for message repository."""

    @abstractmethod
    async def get_by_id(self, message_id: MessageId) -> Optional[Message]:
        """Get message by ID."""
        pass

    @abstractmethod
    async def save(self, message: Message) -> None:
        """Save message."""
        pass

    @abstractmethod
    async def get_by_conversation(
        self,
        conversation_id: ConversationId,
        skip: int = 0,
        limit: int = 100
    ) -> List[Message]:
        """Get messages by conversation."""
        pass

    @abstractmethod
    async def get_last_user_message(
        self,
        conversation_id: ConversationId
    ) -> Optional[Message]:
        """Get last user message in conversation."""
        pass

    @abstractmethod
    async def get_last_interrupted_message(
        self,
        conversation_id: ConversationId
    ) -> Optional[Message]:
        """Get last interrupted message in conversation."""
        pass
```

### 1.3 Streaming Domain

#### Streaming Value Objects

```python
# domain/streaming/value_objects/stream_id.py
from uuid import UUID, uuid4
from domain.shared.value_objects.base_value_object import BaseValueObject

class StreamId(BaseValueObject):
    """Stream identifier value object."""

    def __init__(self, value: UUID | str):
        if isinstance(value, str):
            self._value = UUID(value)
        else:
            self._value = value

    @classmethod
    def generate(cls) -> 'StreamId':
        """Generate a new stream ID."""
        return cls(uuid4())

    @property
    def value(self) -> UUID:
        return self._value

    def __str__(self) -> str:
        return str(self._value)

# domain/streaming/value_objects/stream_status.py
from enum import Enum
from domain.shared.value_objects.base_value_object import BaseValueObject

class StreamStatusEnum(Enum):
    INACTIVE = "inactive"
    ACTIVE = "active"
    INTERRUPTED = "interrupted"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"

class StreamStatus(BaseValueObject):
    """Stream status value object."""

    def __init__(self, status: StreamStatusEnum | str):
        if isinstance(status, str):
            self._status = StreamStatusEnum(status)
        else:
            self._status = status

    @classmethod
    def inactive(cls) -> 'StreamStatus':
        return cls(StreamStatusEnum.INACTIVE)

    @classmethod
    def active(cls) -> 'StreamStatus':
        return cls(StreamStatusEnum.ACTIVE)

    @classmethod
    def completed(cls) -> 'StreamStatus':
        return cls(StreamStatusEnum.COMPLETED)

    @property
    def value(self) -> StreamStatusEnum:
        return self._status

    def is_active(self) -> bool:
        return self._status == StreamStatusEnum.ACTIVE

    def is_completed(self) -> bool:
        return self._status == StreamStatusEnum.COMPLETED

    def can_be_cancelled(self) -> bool:
        return self._status in [StreamStatusEnum.ACTIVE, StreamStatusEnum.INTERRUPTED]

    def __str__(self) -> str:
        return self._status.value
```

#### Stream Entity

```python
# domain/streaming/entities/stream.py
from datetime import datetime, UTC
from typing import List, Optional
from uuid import UUID

from domain.shared.entities.base_entity import BaseEntity
from domain.streaming.value_objects.stream_id import StreamId
from domain.streaming.value_objects.stream_status import StreamStatus, StreamStatusEnum
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.streaming.entities.stream_event import StreamEvent
from domain.streaming.events.stream_started import StreamStarted
from domain.streaming.events.stream_completed import StreamCompleted
from domain.streaming.events.stream_interrupted import StreamInterrupted
from domain.shared.exceptions.domain_exception import BusinessRuleViolationError

class Stream(BaseEntity):
    """Stream aggregate root."""

    def __init__(
        self,
        stream_id: StreamId,
        conversation_id: ConversationId,
        user_id: UUID,
        status: StreamStatus = None,
        started_at: datetime | None = None,
        completed_at: datetime | None = None
    ):
        super().__init__(stream_id.value)
        self._stream_id = stream_id
        self._conversation_id = conversation_id
        self._user_id = user_id
        self._status = status or StreamStatus.inactive()
        self._started_at = started_at
        self._completed_at = completed_at
        self._events: List[StreamEvent] = []
        self._position = 0

    @classmethod
    def start_new(
        cls,
        conversation_id: ConversationId,
        user_id: UUID,
        initial_message_content: str
    ) -> 'Stream':
        """Factory method to start a new stream."""
        stream_id = StreamId.generate()
        started_at = datetime.now(UTC)

        stream = cls(
            stream_id=stream_id,
            conversation_id=conversation_id,
            user_id=user_id,
            status=StreamStatus.active(),
            started_at=started_at
        )

        # Raise domain event
        stream._raise_domain_event(StreamStarted(
            aggregate_id=str(stream_id.value),
            conversation_id=str(conversation_id.value),
            user_id=str(user_id),
            message_content=initial_message_content
        ))

        return stream

    def add_event(self, event_type: str, content: dict) -> StreamEvent:
        """Add an event to the stream."""
        if not self._status.is_active():
            raise BusinessRuleViolationError("Cannot add events to inactive stream")

        self._position += 1
        stream_event = StreamEvent.create(
            stream_id=self._stream_id,
            event_type=event_type,
            content=content,
            position=self._position
        )

        self._events.append(stream_event)
        return stream_event

    def interrupt(self, reason: str) -> None:
        """Interrupt the stream."""
        if not self._status.can_be_cancelled():
            raise BusinessRuleViolationError("Stream cannot be interrupted in current state")

        self._status = StreamStatus(StreamStatusEnum.INTERRUPTED)

        self._raise_domain_event(StreamInterrupted(
            aggregate_id=str(self._stream_id.value),
            reason=reason
        ))

    def complete(self) -> None:
        """Complete the stream."""
        if not self._status.is_active():
            raise BusinessRuleViolationError("Can only complete active streams")

        self._status = StreamStatus.completed()
        self._completed_at = datetime.now(UTC)

        self._raise_domain_event(StreamCompleted(
            aggregate_id=str(self._stream_id.value),
            total_events=len(self._events),
            duration_seconds=self._get_duration_seconds()
        ))

    def _get_duration_seconds(self) -> float:
        """Calculate stream duration in seconds."""
        if not self._started_at:
            return 0.0

        end_time = self._completed_at or datetime.now(UTC)
        return (end_time - self._started_at).total_seconds()

    # Properties
    @property
    def stream_id(self) -> StreamId:
        return self._stream_id

    @property
    def conversation_id(self) -> ConversationId:
        return self._conversation_id

    @property
    def user_id(self) -> UUID:
        return self._user_id

    @property
    def status(self) -> StreamStatus:
        return self._status

    @property
    def events(self) -> List[StreamEvent]:
        return self._events.copy()

    @property
    def current_position(self) -> int:
        return self._position

    @property
    def started_at(self) -> Optional[datetime]:
        return self._started_at

    @property
    def completed_at(self) -> Optional[datetime]:
        return self._completed_at
```

## Phase 2: Application Layer (Week 3-4)

### 2.1 Use Cases

#### Start Stream Use Case

```python
# application/use_cases/streaming/start_stream.py
from typing import AsyncGenerator
from uuid import UUID

from domain.conversation.repositories.conversation_repository import IConversationRepository
from domain.streaming.repositories.stream_repository import IStreamRepository
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.streaming.entities.stream import Stream
from application.interfaces.event_bus import IEventBus
from application.dtos.streaming_dtos import StartStreamCommand, StreamEventDTO
from domain.shared.exceptions.domain_exception import NotFoundError

class StartStreamUseCase:
    """Use case for starting a new stream."""

    def __init__(
        self,
        conversation_repo: IConversationRepository,
        stream_repo: IStreamRepository,
        event_bus: IEventBus
    ):
        self._conversation_repo = conversation_repo
        self._stream_repo = stream_repo
        self._event_bus = event_bus

    async def execute(self, command: StartStreamCommand) -> AsyncGenerator[StreamEventDTO, None]:
        """Execute the start stream use case."""
        # 1. Validate conversation exists
        conversation_id = ConversationId(command.conversation_id)
        conversation = await self._conversation_repo.get_by_id(conversation_id)

        if not conversation:
            raise NotFoundError(f"Conversation {command.conversation_id} not found")

        # 2. Add user message to conversation
        user_message = conversation.add_message(command.message_content, "user")
        await self._conversation_repo.save(conversation)

        # 3. Start new stream
        stream = Stream.start_new(
            conversation_id=conversation_id,
            user_id=command.user_id,
            initial_message_content=command.message_content
        )

        await self._stream_repo.save(stream)

        # 4. Publish domain events
        for event in stream.get_domain_events():
            await self._event_bus.publish(event)
        stream.clear_domain_events()

        # 5. Yield conversation_id event first
        yield StreamEventDTO(
            type="conversation_id",
            conversation_id=str(conversation_id.value),
            content={}
        )

        # 6. Start processing and yield events
        async for event_dto in self._process_stream(stream):
            yield event_dto

    async def _process_stream(self, stream: Stream) -> AsyncGenerator[StreamEventDTO, None]:
        """Process the stream and yield events."""
        # This would integrate with the agent execution engine
        # For now, this is a placeholder that would be implemented
        # based on the existing LangGraph integration

        # Example implementation:
        # async for agent_event in self._agent_service.execute(stream.conversation_id):
        #     stream_event = stream.add_event(agent_event.type, agent_event.data)
        #     yield StreamEventDTO.from_domain_event(stream_event)

        # Complete the stream
        stream.complete()
        await self._stream_repo.save(stream)

        # Publish completion events
        for event in stream.get_domain_events():
            await self._event_bus.publish(event)
        stream.clear_domain_events()

        # Yield completion event
        yield StreamEventDTO(
            type="complete",
            conversation_id=str(stream.conversation_id.value),
            content={}
        )
```

#### Get Stream Status Use Case

```python
# application/use_cases/streaming/get_stream_status.py
from uuid import UUID
from typing import Optional

from domain.streaming.repositories.stream_repository import IStreamRepository
from domain.conversation.value_objects.conversation_id import ConversationId
from application.dtos.streaming_dtos import StreamStatusDTO

class GetStreamStatusUseCase:
    """Use case for getting stream status."""

    def __init__(self, stream_repo: IStreamRepository):
        self._stream_repo = stream_repo

    async def execute(self, conversation_id: UUID) -> Optional[StreamStatusDTO]:
        """Get the current stream status for a conversation."""
        conv_id = ConversationId(conversation_id)

        # Get active stream for conversation
        stream = await self._stream_repo.get_active_by_conversation(conv_id)

        if not stream:
            return None

        return StreamStatusDTO(
            stream_id=str(stream.stream_id.value),
            conversation_id=str(stream.conversation_id.value),
            status=str(stream.status.value),
            is_active=stream.status.is_active(),
            current_position=stream.current_position,
            started_at=stream.started_at,
            completed_at=stream.completed_at,
            event_count=len(stream.events)
        )
```

### 2.2 Application Services

#### Streaming Service

```python
# application/services/streaming_service.py
from typing import AsyncGenerator
from uuid import UUID

from application.use_cases.streaming.start_stream import StartStreamUseCase
from application.use_cases.streaming.get_stream_status import GetStreamStatusUseCase
from application.use_cases.streaming.cancel_stream import CancelStreamUseCase
from application.use_cases.streaming.reconnect_stream import ReconnectStreamUseCase
from application.dtos.streaming_dtos import (
    StartStreamCommand,
    StreamEventDTO,
    StreamStatusDTO,
    ReconnectStreamCommand
)

class StreamingService:
    """Application service for streaming operations."""

    def __init__(
        self,
        start_stream_use_case: StartStreamUseCase,
        get_stream_status_use_case: GetStreamStatusUseCase,
        cancel_stream_use_case: CancelStreamUseCase,
        reconnect_stream_use_case: ReconnectStreamUseCase
    ):
        self._start_stream = start_stream_use_case
        self._get_stream_status = get_stream_status_use_case
        self._cancel_stream = cancel_stream_use_case
        self._reconnect_stream = reconnect_stream_use_case

    async def start_stream(
        self,
        command: StartStreamCommand
    ) -> AsyncGenerator[StreamEventDTO, None]:
        """Start a new conversation stream."""
        async for event in self._start_stream.execute(command):
            yield event

    async def get_stream_status(self, conversation_id: UUID) -> StreamStatusDTO | None:
        """Get current stream status."""
        return await self._get_stream_status.execute(conversation_id)

    async def cancel_stream(self, conversation_id: UUID, reason: str = "User cancelled") -> bool:
        """Cancel an active stream."""
        return await self._cancel_stream.execute(conversation_id, reason)

    async def reconnect_to_stream(
        self,
        command: ReconnectStreamCommand
    ) -> AsyncGenerator[StreamEventDTO, None]:
        """Reconnect to an existing stream."""
        async for event in self._reconnect_stream.execute(command):
            yield event
```

### 2.3 DTOs (Data Transfer Objects)

```python
# application/dtos/streaming_dtos.py
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional
from uuid import UUID

@dataclass
class StartStreamCommand:
    """Command to start a new stream."""
    conversation_id: UUID
    user_id: UUID
    message_content: str
    resume: bool = False
    approve: bool = False
    resource_id: Optional[UUID] = None
    attachment_ids: Optional[list[UUID]] = None

@dataclass
class StreamEventDTO:
    """DTO for stream events."""
    type: str
    conversation_id: str
    content: Dict[str, Any]
    position: Optional[int] = None
    timestamp: Optional[datetime] = None

    @classmethod
    def from_domain_event(cls, event: 'StreamEvent') -> 'StreamEventDTO':
        """Create DTO from domain event."""
        return cls(
            type=event.event_type,
            conversation_id=str(event.stream_id.value),
            content=event.content,
            position=event.position,
            timestamp=event.created_at
        )

@dataclass
class StreamStatusDTO:
    """DTO for stream status."""
    stream_id: str
    conversation_id: str
    status: str
    is_active: bool
    current_position: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    event_count: int

@dataclass
class ReconnectStreamCommand:
    """Command to reconnect to an existing stream."""
    conversation_id: UUID
    user_id: UUID
    from_position: int = 0
    full_history: bool = False
```

## Phase 3: Infrastructure Layer (Week 5-6)

### 3.1 Repository Implementations

#### SQLAlchemy Conversation Repository

```python
# infrastructure/persistence/sqlalchemy/conversation_repository.py
from typing import List, Optional
from uuid import UUID
from sqlmodel import Session, select
from sqlmodel.ext.asyncio import AsyncSession

from domain.conversation.repositories.conversation_repository import IConversationRepository
from domain.conversation.entities.conversation import Conversation
from domain.conversation.value_objects.conversation_id import ConversationId
from models import Conversation as ConversationModel  # Existing SQLModel

class SQLAlchemyConversationRepository(IConversationRepository):
    """SQLAlchemy implementation of conversation repository."""

    def __init__(self, session: Session, async_session: AsyncSession):
        self._session = session
        self._async_session = async_session

    async def get_by_id(self, conversation_id: ConversationId) -> Optional[Conversation]:
        """Get conversation by ID."""
        stmt = select(ConversationModel).where(
            ConversationModel.id == conversation_id.value
        )
        result = await self._async_session.exec(stmt)
        conversation_model = result.first()

        if not conversation_model:
            return None

        return self._to_domain_entity(conversation_model)

    async def save(self, conversation: Conversation) -> None:
        """Save conversation."""
        # Convert domain entity to persistence model
        conversation_model = self._to_persistence_model(conversation)

        # Check if exists
        existing = await self._async_session.get(ConversationModel, conversation.id)

        if existing:
            # Update existing
            for field, value in conversation_model.__dict__.items():
                if not field.startswith('_'):
                    setattr(existing, field, value)
        else:
            # Create new
            self._async_session.add(conversation_model)

        await self._async_session.commit()

    async def get_by_workspace(
        self,
        workspace_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> List[Conversation]:
        """Get conversations by workspace."""
        stmt = (
            select(ConversationModel)
            .where(ConversationModel.workspace_id == workspace_id)
            .offset(skip)
            .limit(limit)
            .order_by(ConversationModel.created_at.desc())
        )

        result = await self._async_session.exec(stmt)
        conversation_models = result.all()

        return [self._to_domain_entity(model) for model in conversation_models]

    async def delete(self, conversation_id: ConversationId) -> None:
        """Delete conversation."""
        conversation_model = await self._async_session.get(
            ConversationModel,
            conversation_id.value
        )

        if conversation_model:
            await self._async_session.delete(conversation_model)
            await self._async_session.commit()

    async def exists(self, conversation_id: ConversationId) -> bool:
        """Check if conversation exists."""
        stmt = select(ConversationModel.id).where(
            ConversationModel.id == conversation_id.value
        )
        result = await self._async_session.exec(stmt)
        return result.first() is not None

    def _to_domain_entity(self, model: ConversationModel) -> Conversation:
        """Convert persistence model to domain entity."""
        conversation_id = ConversationId(model.id)

        # This would need to load messages as well
        # For brevity, showing simplified version
        return Conversation(
            id=conversation_id,
            agent_id=model.agent_id,
            workspace_id=model.workspace_id,
            title=model.title or "New Conversation",
            created_at=model.created_at,
            resource_id=model.resource_id
        )

    def _to_persistence_model(self, entity: Conversation) -> ConversationModel:
        """Convert domain entity to persistence model."""
        return ConversationModel(
            id=entity.id,
            agent_id=entity.agent_id,
            workspace_id=entity.workspace_id,
            title=entity.title,
            created_at=entity.created_at,
            resource_id=entity.resource_id
        )
```

#### Redis Stream Repository

```python
# infrastructure/persistence/redis/stream_repository.py
import json
from typing import List, Optional
from datetime import datetime

from domain.streaming.repositories.stream_repository import IStreamRepository
from domain.streaming.entities.stream import Stream
from domain.streaming.value_objects.stream_id import StreamId
from domain.conversation.value_objects.conversation_id import ConversationId
from infrastructure.persistence.redis.redis_client import RedisClient

class RedisStreamRepository(IStreamRepository):
    """Redis implementation of stream repository."""

    def __init__(self, redis_client: RedisClient):
        self._redis = redis_client

    async def save(self, stream: Stream) -> None:
        """Save stream to Redis."""
        stream_data = {
            "stream_id": str(stream.stream_id.value),
            "conversation_id": str(stream.conversation_id.value),
            "user_id": str(stream.user_id),
            "status": str(stream.status.value),
            "started_at": stream.started_at.isoformat() if stream.started_at else None,
            "completed_at": stream.completed_at.isoformat() if stream.completed_at else None,
            "current_position": stream.current_position
        }

        # Store stream metadata
        await self._redis.hset(
            f"stream:{stream.stream_id.value}",
            mapping=stream_data
        )

        # Store conversation -> stream mapping for active streams
        if stream.status.is_active():
            await self._redis.set(
                f"active_stream:{stream.conversation_id.value}",
                str(stream.stream_id.value),
                ex=3600  # 1 hour expiry
            )

    async def get_by_id(self, stream_id: StreamId) -> Optional[Stream]:
        """Get stream by ID."""
        stream_data = await self._redis.hgetall(f"stream:{stream_id.value}")

        if not stream_data:
            return None

        return self._to_domain_entity(stream_data)

    async def get_active_by_conversation(
        self,
        conversation_id: ConversationId
    ) -> Optional[Stream]:
        """Get active stream for conversation."""
        stream_id_str = await self._redis.get(f"active_stream:{conversation_id.value}")

        if not stream_id_str:
            return None

        stream_id = StreamId(stream_id_str)
        return await self.get_by_id(stream_id)

    def _to_domain_entity(self, data: dict) -> Stream:
        """Convert Redis data to domain entity."""
        from domain.streaming.value_objects.stream_status import StreamStatus

        stream_id = StreamId(data["stream_id"])
        conversation_id = ConversationId(data["conversation_id"])
        status = StreamStatus(data["status"])

        started_at = None
        if data.get("started_at"):
            started_at = datetime.fromisoformat(data["started_at"])

        completed_at = None
        if data.get("completed_at"):
            completed_at = datetime.fromisoformat(data["completed_at"])

        return Stream(
            stream_id=stream_id,
            conversation_id=conversation_id,
            user_id=UUID(data["user_id"]),
            status=status,
            started_at=started_at,
            completed_at=completed_at
        )
```

### 3.2 Dependency Injection Container

```python
# infrastructure/configuration/dependency_injection.py
from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

from domain.conversation.repositories.conversation_repository import IConversationRepository
from domain.streaming.repositories.stream_repository import IStreamRepository
from infrastructure.persistence.sqlalchemy.conversation_repository import SQLAlchemyConversationRepository
from infrastructure.persistence.redis.stream_repository import RedisStreamRepository
from application.services.streaming_service import StreamingService
from application.use_cases.streaming.start_stream import StartStreamUseCase
from application.interfaces.event_bus import IEventBus
from infrastructure.messaging.in_process_event_bus import InProcessEventBus

class Container(containers.DeclarativeContainer):
    """Dependency injection container."""

    # Configuration
    config = providers.Configuration()

    # Infrastructure
    db_session = providers.Resource(create_db_session)
    async_db_session = providers.Resource(create_async_db_session)
    redis_client = providers.Singleton(RedisClient, url=config.redis_url)

    # Event Bus
    event_bus: providers.Provider[IEventBus] = providers.Singleton(InProcessEventBus)

    # Repositories
    conversation_repository: providers.Provider[IConversationRepository] = providers.Factory(
        SQLAlchemyConversationRepository,
        session=db_session,
        async_session=async_db_session
    )

    stream_repository: providers.Provider[IStreamRepository] = providers.Factory(
        RedisStreamRepository,
        redis_client=redis_client
    )

    # Use Cases
    start_stream_use_case = providers.Factory(
        StartStreamUseCase,
        conversation_repo=conversation_repository,
        stream_repo=stream_repository,
        event_bus=event_bus
    )

    # Services
    streaming_service = providers.Factory(
        StreamingService,
        start_stream_use_case=start_stream_use_case,
        # ... other use cases
    )
```

## Phase 4: Presentation Layer (Week 6)

### 4.1 Clean Controllers

```python
# api/routes/chat/stream_controller.py
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from uuid import UUID
from typing import AsyncGenerator
import json

from application.services.streaming_service import StreamingService
from application.dtos.streaming_dtos import StartStreamCommand, ReconnectStreamCommand
from api.models.stream_models import StreamMessageRequest
from api.dependencies.container import get_streaming_service
from api.dependencies.deps import CurrentUser

router = APIRouter(prefix="/chat", tags=["streaming"])

@router.post("/stream")
async def start_stream(
    message: StreamMessageRequest,
    current_user: CurrentUser,
    conversation_id: UUID | None = None,
    streaming_service: StreamingService = Depends(get_streaming_service)
) -> StreamingResponse:
    """Start a new conversation stream."""

    # Convert to command
    command = StartStreamCommand(
        conversation_id=conversation_id or UUID(),  # Generate if not provided
        user_id=current_user.id,
        message_content=message.content,
        resume=message.resume,
        approve=message.approve,
        resource_id=UUID(message.resource_id) if message.resource_id else None,
        attachment_ids=message.attachment_ids
    )

    async def event_generator() -> AsyncGenerator[str, None]:
        """Generate SSE events from stream."""
        try:
            async for event_dto in streaming_service.start_stream(command):
                event_data = {
                    "type": event_dto.type,
                    "conversation_id": event_dto.conversation_id,
                    "content": event_dto.content
                }

                if event_dto.position:
                    event_data["position"] = event_dto.position

                yield f"data: {json.dumps(event_data)}\n\n"

        except Exception as e:
            error_event = {
                "type": "error",
                "content": {"message": str(e)},
                "status": 500
            }
            yield f"data: {json.dumps(error_event)}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@router.get("/{conversation_id}/reconnect-stream")
async def reconnect_stream(
    conversation_id: UUID,
    current_user: CurrentUser,
    streaming_service: StreamingService = Depends(get_streaming_service)
) -> StreamingResponse:
    """Reconnect to an existing stream."""

    # Check if stream exists and is active
    stream_status = await streaming_service.get_stream_status(conversation_id)
    if not stream_status or not stream_status.is_active:
        raise HTTPException(
            status_code=404,
            detail="No active stream found for this conversation"
        )

    command = ReconnectStreamCommand(
        conversation_id=conversation_id,
        user_id=current_user.id,
        full_history=True
    )

    async def reconnect_generator() -> AsyncGenerator[str, None]:
        """Generate SSE events for reconnection."""
        try:
            async for event_dto in streaming_service.reconnect_to_stream(command):
                event_data = {
                    "type": event_dto.type,
                    "conversation_id": event_dto.conversation_id,
                    "content": event_dto.content
                }
                yield f"data: {json.dumps(event_data)}\n\n"

        except Exception as e:
            error_event = {
                "type": "error",
                "content": {"message": str(e)},
                "status": 500
            }
            yield f"data: {json.dumps(error_event)}\n\n"

    return StreamingResponse(
        reconnect_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@router.get("/{conversation_id}/stream-status")
async def get_stream_status(
    conversation_id: UUID,
    current_user: CurrentUser,
    streaming_service: StreamingService = Depends(get_streaming_service)
):
    """Get current stream status."""
    status = await streaming_service.get_stream_status(conversation_id)

    if not status:
        return {"conversation_id": str(conversation_id), "status": "inactive"}

    return {
        "conversation_id": str(conversation_id),
        "stream_id": status.stream_id,
        "status": status.status,
        "is_active": status.is_active,
        "current_position": status.current_position,
        "event_count": status.event_count,
        "started_at": status.started_at,
        "completed_at": status.completed_at
    }

@router.post("/{conversation_id}/cancel-stream")
async def cancel_stream(
    conversation_id: UUID,
    current_user: CurrentUser,
    streaming_service: StreamingService = Depends(get_streaming_service)
):
    """Cancel active stream."""
    success = await streaming_service.cancel_stream(
        conversation_id,
        "User requested cancellation"
    )

    return {
        "conversation_id": str(conversation_id),
        "cancelled": success,
        "status": "cancelled" if success else "not_found"
    }
```

## Migration Strategy

### Week 1: Setup & Foundation

1. **Create new folder structure** alongside existing code
2. **Implement shared kernel** (base classes, events, exceptions)
3. **Create domain value objects** for each bounded context
4. **Add basic unit tests** for domain logic

### Week 2: Domain Entities

1. **Implement Conversation domain** entities and business rules
2. **Implement Streaming domain** entities and business rules
3. **Create domain repository interfaces**
4. **Add domain event definitions**

### Week 3: Use Cases & Application Services

1. **Implement core use cases** (start stream, get status, etc.)
2. **Create application services** to orchestrate use cases
3. **Define DTOs** for data transfer
4. **Add application layer tests**

### Week 4: Infrastructure Implementation

1. **Implement repository concrete classes**
2. **Setup dependency injection container**
3. **Create event bus implementation**
4. **Add infrastructure tests**

### Week 5: API Integration

1. **Create new clean controllers**
2. **Setup FastAPI dependencies**
3. **Implement request/response models**
4. **Add integration tests**

### Week 6: Migration & Cleanup

1. **Run both old and new APIs in parallel**
2. **Gradual traffic migration** with feature flags
3. **Performance testing** and optimization
4. **Remove legacy code** once migration is complete

## Testing Strategy

### Unit Tests

```python
# tests/unit/domain/conversation/test_conversation.py
import pytest
from uuid import uuid4
from domain.conversation.entities.conversation import Conversation
from domain.conversation.value_objects.conversation_id import ConversationId
from domain.shared.exceptions.domain_exception import BusinessRuleViolationError

class TestConversation:
    """Unit tests for Conversation entity."""

    def test_start_new_conversation_creates_valid_entity(self):
        """Test that starting a new conversation creates a valid entity."""
        agent_id = uuid4()
        workspace_id = uuid4()
        initial_message = "Hello, I need help with my cloud costs"

        conversation = Conversation.start_new(
            agent_id=agent_id,
            workspace_id=workspace_id,
            initial_message_content=initial_message
        )

        assert conversation.agent_id == agent_id
        assert conversation.workspace_id == workspace_id
        assert len(conversation.messages) == 1
        assert conversation.messages[0].content.value == initial_message
        assert conversation.is_first_exchange()

        # Check domain events
        events = conversation.get_domain_events()
        assert len(events) >= 1  # Should have ConversationStarted and MessageAdded

    def test_add_message_to_conversation(self):
        """Test adding a message to an existing conversation."""
        conversation = self._create_test_conversation()

        message = conversation.add_message("This is my response", "assistant")

        assert len(conversation.messages) == 2
        assert message.role.is_assistant()
        assert not conversation.is_first_exchange()

    def test_cannot_add_message_to_deleted_conversation(self):
        """Test that messages cannot be added to deleted conversations."""
        conversation = self._create_test_conversation()
        conversation._is_deleted = True  # Simulate deletion

        with pytest.raises(BusinessRuleViolationError):
            conversation.add_message("This should fail", "user")

    def _create_test_conversation(self) -> Conversation:
        """Helper to create a test conversation."""
        return Conversation.start_new(
            agent_id=uuid4(),
            workspace_id=uuid4(),
            initial_message_content="Test message"
        )
```

### Integration Tests

```python
# tests/integration/test_streaming_api.py
import pytest
from httpx import AsyncClient
from uuid import uuid4

@pytest.mark.asyncio
async def test_start_stream_endpoint(client: AsyncClient, auth_headers: dict):
    """Test the start stream endpoint."""
    conversation_id = str(uuid4())

    request_data = {
        "content": "Help me optimize my AWS costs",
        "resume": False,
        "approve": False
    }

    response = await client.post(
        f"/chat/stream?conversation_id={conversation_id}",
        json=request_data,
        headers=auth_headers
    )

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream"

    # Verify SSE events
    events = []
    async for line in response.aiter_lines():
        if line.startswith("data: "):
            events.append(line[6:])  # Remove "data: " prefix

    assert len(events) > 0
    # First event should be conversation_id
    first_event = json.loads(events[0])
    assert first_event["type"] == "conversation_id"
    assert first_event["conversation_id"] == conversation_id
```

## Success Metrics

### Technical Metrics

- **Code Quality**: Cyclomatic complexity < 10, test coverage > 80%
- **Performance**: API response time < 200ms, stream latency < 100ms
- **Reliability**: 99.9% uptime, error rate < 0.1%
- **Maintainability**: New feature implementation time reduced by 50%

### Architecture Metrics

- **Coupling**: Reduced from high to low coupling between components
- **Cohesion**: High cohesion within domain boundaries
- **Testability**: 100% unit test coverage for domain logic
- **Separation of Concerns**: Clear boundaries between layers

This implementation provides a solid foundation for clean, maintainable, and scalable architecture while preserving existing functionality and enabling future enhancements.
