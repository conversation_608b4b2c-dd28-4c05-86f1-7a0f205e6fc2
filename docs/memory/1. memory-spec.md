# CloudThinker Memory System Documentation

## Overview

The CloudThinker memory system provides intelligent, persistent memory capabilities for AI agents, enabling them to learn from past conversations and apply accumulated knowledge to future tasks. The system is designed around a **PREVENTION > REACTION** philosophy, focusing on extracting execution patterns that help agents complete cloud cost optimization and automation tasks correctly on the first attempt.

## Core Philosophy

The memory system prioritizes:

- **Agent-Initiated Learning**: Agents proactively identify and store valuable patterns when they recognize them
- **Preventive Learning**: Extract patterns that prevent errors rather than react to them
- **Execution Efficiency**: Minimize tool calls through proven workflows
- **Context Awareness**: Apply learned patterns based on situational context
- **Knowledge Evolution**: Continuously refine and connect related learnings

## Architecture Overview

### System Components

```mermaid
graph TB
    subgraph "Trigger Layer"
        AGENT[Agent Calls<br/>create_memory Tool]
        PLAN[Planning Agent Task<br/>Completion Trigger]
    end

    subgraph "Processing Layer"
        MS[MemoryService]
        MT[Memory Tasks<br/>Celery Workers]
        PM[PromptManager Integration]
        CA[Conversation Agent<br/>Cache Integration]
    end

    subgraph "Caching Layer"
        REDIS[(Redis Cache<br/>24h TTL)]
        STREAM[Stream Processing<br/>Background Tasks]
    end

    subgraph "Storage Layer"
        BVS[BaseVectorStore]
        QDB[(Qdrant Vector DB)]
        PG[(PostgreSQL Metadata)]
    end

    subgraph "AI Services"
        LLM[LLM Models<br/>Claude Haiku 3.5]
        EMB[AWS Bedrock<br/>Embeddings]
    end

    AGENT --> MT
    PLAN --> MT
    MT --> MS
    MS --> BVS
    CA --> REDIS
    PM --> CA
    BVS --> QDB
    MS --> PG
    MS --> LLM
    BVS --> EMB

    style REDIS fill:#ff6b6b
    style CA fill:#4ecdc4
```

## Memory Data Model

### MemoryNode Structure

```python
class MemoryNode(BaseModel):
    task: str | None = None                # Specific tool execution objective or problem solved
    solution: str | None = None            # Exact tool call sequence with parameters and error recovery steps
    context: str | None = None             # User high-level task context
```

### Supporting Data Models

```python
class ExtractionDecision(BaseModel):
    should_extract: bool                   # Whether to start memory extraction

class MemoriesExtraction(BaseModel):
    memory_nodes: list[MemoryNode]         # List of extracted memory nodes

class CombinedNode(BaseModel):
    combined_from: list[str]               # IDs of memory nodes that were combined
    new_node: MemoryNode                   # The resulting synthesized memory node
```

### Memory Evolution Types

The system supports three evolution strategies:

1. **Combine**: Merge related nodes with similar tasks into comprehensive solutions
2. **Connect**: Link related nodes through reference IDs without merging
3. **Standalone**: Keep nodes independent when no strong relationships exist

### MemoryEvolution Decision Model

```python
class MemoryEvolution(BaseModel):
    should_evolve: bool = Field(description="Indicates if this memory requires evolution")
    evolution_type: Literal["combine", "connect", "standalone"] | None = None
    combined_node: CombinedNode | None = Field(default=None, description="For combine evolution")
    connections: list[str] | None = Field(default=None, description="For connect evolution")
    rationale: str | None = Field(default=None, description="Explanation for the decision")
```

**Evolution Decision Criteria:**

- **Combine**: Merge nodes with the same task where combined solution is more valuable
- **Connect**: Link related nodes through reference IDs without merging
- **Standalone**: Keep independent when no strong relationships exist or when evolution isn't beneficial

## Memory Extraction Process

### 1. Agent-Initiated Memory Creation

Memory creation is initiated by agents themselves when they identify valuable execution patterns, error prevention strategies, or optimization opportunities. The `create_memory` tool allows agents to proactively store this knowledge:

```python
# In multi_agents/tools/memory.py
@tool
async def create_memory(inputs: MemoriesExtraction) -> str:
    """Store tool execution patterns and error prevention strategies for future agent use."""
```

The tool immediately triggers a background Celery task for processing:

```python
# In memory_tasks.py
@celery_app.task(name="memory_tasks.extract_memory", priority=HIGH_PRIORITY)
def extract_memory(memory_nodes_dict: list[dict], workspace_id: str) -> dict:
```

**Key Difference**: Memory creation is **not automatic** from conversation completion. Instead, it's a **deliberate agent action** when they recognize patterns worth preserving for future use.

### 2. Memory Processing Workflow

The memory system supports two primary workflows:

#### A. Agent-Initiated Memory Creation Workflow

```mermaid
flowchart TD
    START([Agent Identifies<br/>Execution Pattern]) --> TOOL[Agent Calls<br/>create_memory Tool]
    TOOL --> VALIDATE[Tool Validates<br/>Memory Input]
    VALIDATE --> CELERY[Tool Triggers<br/>Celery Background Task]

    CELERY --> PARSE[Parse Memory Nodes]
    PARSE --> EMBED[Generate Vector<br/>Embeddings]
    EMBED --> SEARCH[Find Related<br/>Memory Nodes]

    SEARCH --> EVOLVE[Analyze Memory<br/>Evolution with LLM]
    EVOLVE --> PROCESS{Evolution Type?}

    PROCESS -->|Combine| COMBINE[Create Combined Node<br/>Delete Old Nodes]
    PROCESS -->|Connect| CONNECT[Create Links Between Nodes]
    PROCESS -->|Standalone| STANDALONE[Store as Independent Node]

    COMBINE --> STORE[Vector Store Operations]
    CONNECT --> STORE
    STANDALONE --> STORE

    STORE --> COMPLETE[Memory Stored Successfully]
    COMPLETE --> END([End])
```

#### B. Memory Search and Caching Workflow

```mermaid
flowchart TD
    START([Planning Agent Task]) --> TRIGGER[Task Completion<br/>Detected]
    TRIGGER --> SEARCH_TASK[Launch Background<br/>Memory Search Task]

    SEARCH_TASK --> CACHE_SEARCH[Search Memory<br/>by Task Content]
    CACHE_SEARCH --> FILTER[Filter by Agent<br/>Workspace]
    FILTER --> CACHE[Cache Results in Redis<br/>24h TTL]

    CACHE --> AGENT_INTEGRATION[Agent Retrieves<br/>Cached Memories]
    AGENT_INTEGRATION --> PROMPT[Include in Agent Prompt]

    PROMPT --> CONVERSATION[Agent Uses<br/>Memory Context]
    CONVERSATION --> END([End])
```

### 3. Memory Evolution Analysis

The system uses LLM-based analysis to determine how memories should evolve:

**Evolution Analysis Process**

- Compares current memory node with related nodes found through vector similarity
- Uses Claude Haiku 3.5 model to analyze relationships and determine evolution strategy
- Returns structured `MemoryEvolution` object with evolution type and rationale

**Evolution Decision Criteria**

- **Combine**: Merge nodes with same task where combined solution is more valuable
- **Connect**: Link related nodes through reference IDs without merging
- **Standalone**: Keep independent when no strong relationships exist

## Memory Integration with Agents

### Memory Caching and Retrieval System

Memory is integrated through a sophisticated caching system that provides task-specific memory context to agents:

#### Primary Integration Points

1. **Planning Agent Integration**: Automatically triggers memory search when tasks are completed
2. **Redis Caching**: Task-specific memories are cached for 24 hours with agent/workspace scoping
3. **Conversation Agent Integration**: Retrieves cached memories before agent execution

#### Memory Retrieval Workflow

```python
# In ConversationAgent._check_and_override_cached_memories()
redis_manager = RedisManager()
cache_key = f"memories:{workspace_id}:{agent_name}"
cached_memories = redis_manager.get(cache_key)

if cached_memories:
    configuration.memory_prompt = cached_memories
    logger.info(f"Using cached task-specific memories for agent {agent_name}")
```

#### Background Memory Search

The planning agent triggers memory search through Celery tasks:

```python
# In PlanningAgent._launch_memory_search_task()
task = search_task_memories.delay(task_content, self.agent_name, self.workspace_id)
```

This ensures memory context is available for subsequent agent tasks without blocking the conversation flow.

### Search and Retrieval

The memory system provides two levels of search capabilities:

#### Real-time Vector Search

```python
async def search_memory(self, query: str, role: str, workspace_id: str) -> str:
    """Search for relevant memories based on query, role, and workspace."""
```

**Real-time Search Features:**

- **Vector Similarity**: Uses Cohere embeddings to find semantically related memories
- **Workspace Isolation**: Ensures memories are scoped to specific workspaces
- **Role-based Filtering**: Filters memories by specific agent roles
- **Relevance Scoring**: Applies configurable similarity thresholds (default: 0.4)
- **Payload-based Filtering**: Uses Qdrant metadata filtering for efficient search

#### Cached Task-specific Search

```python
async def search_and_cache_task_memories(
    self, task_content: str, agent_name: str, workspace_id: str
) -> None:
    """Search for memories related to task content and cache in Redis."""
```

**Caching Features:**

- **Task Content Analysis**: Searches based on specific task descriptions
- **Redis Caching**: Results cached for 24 hours to reduce vector search latency
- **Agent-specific Keys**: Cache keys scoped by `workspace_id:agent_name`
- **Background Processing**: Non-blocking search through Celery tasks
- **Automatic Expiration**: Cache entries automatically expire after 24 hours

## Configuration

### Memory Processing Settings

```python
# LLM Models for Memory Processing
MEMORY_EXTRACTION_DECISION_MODEL: str = "litellm/us-claude-haiku-3.5"
MEMORY_EXTRACTION_MODEL: str = "litellm/us-claude-haiku-3.5"

# Memory Search Configuration
MEMORY_MAX_RELATED_MEMORY_NODES: int = 7
MEMORY_RELATEDNESS_THRESHOLD: float = 0.4
MAX_MEMORY_TOOL_RESULTS: int = 10

# Memory Processing Thresholds
MEMORY_MIN_MESSAGES_FOR_EXTRACTION: int = 3
MEMORY_MIN_MESSAGES_FOR_ROLE_PROCESSING: int = 2

# Performance and Processing Settings
MEMORY_BATCH_SIZE: int = 10
MEMORY_MAX_CONCURRENT_OPERATIONS: int = 5

# Caching Configuration
MEMORY_CACHE_TTL_SECONDS: int = 86400  # 24 hours
MEMORY_CACHE_KEY_PREFIX: str = "memories"
```

### Vector Database Configuration

```python
# Qdrant Configuration
QDRANT_HOST: str = "qdrant"
QDRANT_PORT: int = 6333
QDRANT_GRPC_PORT: int = 6334
QDRANT_COLLECTION_NAME: str = "cloudthinker-collections"

# Embedding Configuration
EMBEDDING_MODEL_DIMS: int = 1024
EMBEDDING_MODEL: str = "bedrock:cohere.embed-multilingual-v3"
EMBEDDING_MODEL_NAME: str = "cohere.embed-multilingual-v3"
EMBEDDING_REGION_NAME: str = "us-east-1"

# Embedding Retry Settings
MAX_EMBEDDING_RETRIES: int = 3
EMBEDDING_RETRY_DELAY: int = 2  # seconds

# FastEmbed Configuration
FASTEMBED_SPARSE_MODEL_NAME: str = "Qdrant/bm25"
```

## Error Handling and Resilience

### Exception Hierarchy

```python
class MemoryServiceError(BaseAppException):
    """Base exception for memory service errors."""

class MemoryExtractionError(MemoryServiceError):
    """Raised when memory extraction fails."""

class MemoryNodeProcessingError(MemoryServiceError):
    """Raised when memory node processing fails."""

class VectorStoreOperationError(MemoryServiceError):
    """Raised when vector store operations fail."""

class CheckpointRetrievalError(MemoryServiceError):
    """Raised when checkpoint data retrieval fails."""

class MemoryEvolutionError(MemoryServiceError):
    """Raised when memory evolution analysis fails."""

class InvalidMemoryInputError(MemoryServiceError):
    """Raised when memory service receives invalid input."""
```

### Resilience Features

- **Embedding Retry Logic**: Exponential backoff for transient embedding failures
- **Graceful Degradation**: System continues operating even if memory extraction fails
- **Comprehensive Logging**: Detailed error tracking for debugging and monitoring
- **Background Processing**: Memory extraction doesn't block conversation flow
- **Checkpoint Recovery**: Robust handling of corrupted or missing checkpoint data

## Performance Optimizations

### Vector Store Optimizations

```python
# Qdrant collection configuration with performance tuning
vectors_config=qdrant_models.VectorParams(
    size=settings.EMBEDDING_MODEL_DIMS,
    distance=qdrant_models.Distance.COSINE,
    on_disk=True,
),
hnsw_config=qdrant_models.HnswConfigDiff(
    on_disk=True,
    payload_m=16,
    m=0,  # disable building global index for better performance
),
```

### Processing Optimizations

- **Batch Operations**: Efficient vector store operations for multiple nodes
- **Connection Pooling**: Reused Qdrant client connections
- **Async Processing**: Non-blocking database and vector operations
- **Role-based Parallel Processing**: Concurrent processing of different agent roles
- **Embedding Caching**: LRU cache for vector store instances

## Usage Examples

### Memory Node Example

Here's what a typical memory node looks like when extracted from a conversation about AWS cost optimization:

```json
{
  "task": "Prevent AWS Cost Explorer parameter validation errors",
  "solution": "Always validate TimePeriod format before calling get_cost_and_usage. Use Granularity='DAILY' for periods <90 days, 'MONTHLY' for longer periods. Include Service dimension filter to prevent data overflow",
  "context": "Apply before any Cost Explorer API calls to prevent validation errors"
}
```

This memory helps future agents:

- Use the correct API syntax immediately
- Avoid common parameter mistakes
- Execute efficiently with fewer API calls
- Apply context-aware optimizations

### Search Results Format

The memory system returns search results in this format:

```
Memory 1.
Task: Prevent AWS Cost Explorer parameter validation errors
Score: 0.87
Solution: Always validate TimePeriod format before calling get_cost_and_usage. Use Granularity='DAILY' for periods <90 days, 'MONTHLY' for longer periods. Include Service dimension filter to prevent data overflow

Memory 2.
Task: Handle AWS API throttling with exponential backoff
Score: 0.75
Solution: When receiving ThrottlingException, implement exponential backoff: start at 1s, double each retry, add random jitter ±25%, maximum 60s delay, retry up to 5 times before failing
```

## Key Files and Components

### Core Service Files

- [`memory_service.py`](../../backend/app/services/memory/memory_service.py) - Main memory service with caching and search capabilities
- [`schema.py`](../../backend/app/services/memory/schema.py) - Data models and schemas (MemoryNode, MemoryEvolution, etc.)
- [`base_vector_store.py`](../../backend/app/services/base_vector_store.py) - Vector store abstraction with Qdrant integration

### Integration Files

- [`memory.py`](../../backend/app/modules/multi_agents/tools/memory.py) - create_memory tool for agent integration
- [`memory_tasks.py`](../../backend/app/tasks/memory_tasks.py) - Celery background tasks for memory processing and search
- [`conversation_agent.py`](../../backend/app/modules/multi_agents/agents/conversation_agent.py) - Memory cache integration (lines 149-189)

### Caching and Infrastructure

- [`redis_manager.py`](../../backend/app/core/redis/redis_manager.py) - Redis caching infrastructure for memory storage
- [`plan.py`](../../backend/app/modules/multi_agents/tools/plan/plan.py) - Planning agent memory search triggers (lines 235-253)

### Configuration and Exceptions

- [`config.py`](../../backend/app/core/config.py) - Memory system configuration settings (lines 326-336)
- [`legacy.py`](../../backend/app/exceptions/legacy.py) - Memory-specific exception definitions (lines 107-152)

## Tool Integration

### create_memory Tool

The memory system integrates with agents through the `create_memory` tool that allows agents to **proactively store** execution patterns they've discovered:

**Key Features:**

- **Agent-Initiated Storage**: Agents decide when to create memories based on their execution experience
- **Tool Error Prevention Focus**: Prioritizes memories that help agents use correct parameter formats, handle API rate limits, and implement proper error recovery
- **Structured Input**: Accepts `MemoriesExtraction` objects with multiple memory nodes
- **Background Processing**: Executes through Celery to avoid blocking agent conversations
- **Workspace Scoping**: Automatically scopes memories to the current workspace
- **Agent Attribution**: Associates memories with specific agent roles when available

**Usage Pattern:**

```python
# Agent calls create_memory tool with structured input
memory_input = MemoriesExtraction(memory_nodes=[
    MemoryNode(
        task="Handle AWS API throttling with exponential backoff",
        solution="When receiving ThrottlingException, implement exponential backoff: start at 1s, double each retry, add random jitter ±25%, maximum 60s delay, retry up to 5 times before failing",
        context="Use for any AWS API call that might hit rate limits"
    )
])
```

### Automatic Memory Search Integration

The memory system automatically triggers search operations through the planning agent:

**Task Completion Trigger:**

```python
# In PlanningAgent._trigger_memory_search_if_needed()
if newly_completed_task_ids:
    next_pending_task = self.plan_manager.get_next_pending_task()
    if next_pending_task:
        self._launch_memory_search_task(next_pending_task.content)
```

**Background Search Task:**

```python
# In PlanningAgent._launch_memory_search_task()
task = search_task_memories.delay(task_content, self.agent_name, self.workspace_id)
```

This ensures that relevant memories are proactively cached for upcoming tasks, improving agent performance and reducing redundant tool calls.

## Future Enhancements

### Planned Features

- **Memory Analytics**: Dashboard for memory system performance and insights
- **Memory Validation**: Automated testing of extracted memory patterns
- **Cross-workspace Learning**: Selective sharing of anonymized patterns across workspaces
- **Memory Compression**: Automatic consolidation of redundant memory nodes

### Scalability Considerations

- **Distributed Processing**: Support for multiple memory extraction workers
- **Memory Archival**: Long-term storage strategies for historical memories
- **Search Optimization**: Advanced indexing for faster memory retrieval
- **Memory Lifecycle Management**: Automated cleanup of outdated or unused memories
