# JSX Best Practices

This guide outlines our conventions for writing clean, maintainable JSX in React applications.

## Utility Functions

### Class Name Management

When merging complex classes, always use the `cn` utility from `clsx`/`tailwind-merge`:

```tsx
import { cn } from '@/lib/utils';

// Simple usage
<button className={cn('btn', className)}>Submit</button>

// Conditional classes
<div className={cn('base-class', {
  'text-lg': isLarge,
  'bg-primary': isPrimary,
  'opacity-50': isDisabled
})}>
  Content
</div>

// Array syntax for dynamic classes
<span className={cn([
  'badge',
  variant === 'success' && 'badge-success',
  variant === 'error' && 'badge-error'
])}>
  {label}
</span>
```

Why use `cn`:

- <PERSON><PERSON> merging tailwind classes correctly
- Automatically removes duplicate classes
- Resolves conflicting classes by keeping the last one
- Provides type-safety with TypeScript

## Common Patterns

### Conditional Rendering with `If`

Prefer the `If` component to complex ternary operators in JSX:

```tsx
import { If } from '@/components/ui/common/if';

// Basic usage
<If condition={isLoading}>
  <Spinner />
</If>

// With fallback
<If condition={isLoading} fallback={<Content />}>
  <Spinner />
</If>

// With callback function for condition match
<If condition={user}>
  {(userData) => <UserProfile data={userData} />}
</If>
```

Benefits:

- Improves readability compared to ternary operators
- Type-safe with TypeScript
- Reduces nesting and complexity in JSX

### List Rendering

Consistently use these patterns for list rendering:

```tsx
// Empty state handling, avoid ternaries
{
  items.length > 0 ? (
    <ul className="list">
      {items.map((item) => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  ) : (
    <EmptyState message="No items found" />
  );
}

// Even better with If component
<If
  condition={items.length > 0}
  fallback={<EmptyState message="No items found" />}
>
  <ul className="list">
    {items.map((item) => (
      <li key={item.id}>{item.name}</li>
    ))}
  </ul>
</If>;
```

## Error and Loading States

Use consistent patterns for handling loading and error states:

```tsx
// Loading state
<If condition={isLoading}>
  <div className="flex justify-center p-8">
    <Spinner />
  </div>
</If>

// Error state that infer the type of the condition. The type of the variable "err" is now inferred
// Always use this pattern when the value of the condition is used within the body
<If condition={error}>
  {(err) => (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>
       {errorTitle}
      </AlertTitle>

      <AlertDescription>
        {err.message}
      </AlertDescription>
    </Alert>
  )}
</If>

// Empty state
<If condition={items.length === 0}>
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <EmptyIcon className="h-12 w-12 text-muted-foreground" />

    <h3 className="mt-4 text-lg font-medium">
      {noData}
    </h3>

    <p className="text-sm text-muted-foreground">
      {noDataDescription}
    </p>
  </div>
</If>
```

## Testing Attributes

Add consistent data attributes for testing:

```tsx
<button data-test="submit-button">
  Submit
</button>

<div data-test="user-profile" data-user-id={user.id}>
  {/* User profile content */}
</div>

<form data-test="signup-form">
  {/* Form fields */}
</form>
```

# React

## Core Principles

- **Component-Driven Development**: Build applications as a composition of isolated, reusable components
- **One-Way Data Flow**: Follow React's unidirectional data flow pattern
- **Single Responsibility**: Each component should have a clear, singular purpose
- **TypeScript First**: Use TypeScript for type safety and better developer experience

## React Components

### Component Structure

- Always use functional components with TypeScript
- Name components using PascalCase (e.g., `UserProfile`)
- Use named exports for components, not default exports
- Split components by responsibility and avoid "god components"
- Name files to match their component name (e.g., `user-profile.tsx`)

### Props

- Always type props using TypeScript interfaces or type aliases
- Use discriminated unions for complex prop types with conditional rendering
- Destructure props at the start of component functions
- Use prop spreading cautiously and only when appropriate
- Provide default props for optional parameters when it makes sense

```typescript
type ButtonProps = {
  variant: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
};

function Button({
  variant,
  size = 'md',
  children,
  disabled = false,
  onClick,
}: ButtonProps) {
  // Component implementation
}
```

### State Management

- Keep state as local as possible
- Lift state up when multiple components need access
- Use Context sparingly and only for truly global state
- Prefer the "Container/Presenter" pattern when separating data and UI

```typescript
// Container component (manages data)
function UserProfileContainer() {
  const {data, isLoading} = userQuery.query.useProfile();

  if (data) {
    return <UserProfilePresenter data={userData.data} />;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return <ErrorMessage error={userData.error} />;

}

// Presenter component (renders UI)
function UserProfilePresenter({ data }: { data: UserData }) {
  return (
    <div>
      <h1>{data.name}</h1>
      {/* Rest of the UI */}
    </div>
  );
}
```

### Hooks

- Follow the Rules of Hooks (only call hooks at the top level, only call them from React functions)
- Create custom hooks for reusable logic
- Keep custom hooks focused on a single concern
- Name custom hooks with a 'use' prefix (e.g., `useUserProfile`)
- Extract complex effect logic into separate functions
- Always provide a complete dependencies array to `useEffect`

### Performance Optimization

- Apply `useMemo` for expensive calculations
- Use `useCallback` for functions passed as props to child components
- Split code using dynamic imports and `React.lazy()`

```typescript
const MemoizedComponent = React.memo(function Component(props: Props) {
  // Component implementation
});

// For expensive calculations
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);

// For callback functions passed as props
const memoizedCallback = useCallback(() => {
  doSomething(a, b);
}, [a, b]);
```

## Server Components

### Fundamentals

- Server Components render React server-side and never run on the client
- Use Server Components as the default choice, especially for data fetching
- No use of hooks, browser APIs, or event handlers in Server Components
- No use of `useState`, `useEffect`, or any other React hooks
- Server Components can render Client Components but not vice versa

### Data Fetching

- Fetch data directly using async/await in Server Components
- Use Suspense boundaries around data-fetching components
- Apply security checks before fetching sensitive data
- Never pass sensitive data (API keys, tokens) to Client Components
- Use React's `cache()` function for caching data requests

### Error Handling

- Implement error boundaries at appropriate levels
- Use the Next.js `error.tsx` file for route-level error handling
- Create fallback UI for when data fetching fails
- Log server errors appropriately without exposing details to clients

### Streaming and Suspense

- Use React Suspense for progressive loading experiences if specified
- Implement streaming rendering for large or complex pages
- Structure components to enable meaningful loading states
- Prioritize above-the-fold content when using streaming

## Client Components

### Fundamentals

- Add the `'use client'` directive at the top of files for Client Components
- Keep Client Components focused on interactivity and browser APIs
- Use hooks appropriately following the Rules of Hooks
- Implement controlled components for form elements
- Handle all browser events in Client Components

### Data Fetching

- Use React Query (TanStack Query) for data fetching in Client Components
- Create custom hooks for data fetching logic (e.g., `useUserData`)
- Always handle loading, success, and error states

### Form Handling

- Use libraries like React Hook Form for complex forms
- Implement proper validation with libraries like Zod
- Create reusable form components
- Handle form submissions with loading and error states
- Use controlled components for form inputs

### Error Handling

- Implement error boundaries to catch and handle component errors if using client components
- Always handle network request errors
- Provide user-friendly error messages
- Log errors appropriately
- Implement retry mechanisms where applicable

```typescript
'use client';

import { ErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div role="alert">
      <p>Something went wrong:</p>
      <pre>{error.message}</pre>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
}

export function UserProfileWithErrorHandling() {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // Reset application state here if needed
      }}
    >
      <UserProfile userId="123" />
    </ErrorBoundary>
  );
}
```

## Styling Conventions

### Tailwind CSS Best Practices

- **Use `size-*` for equal dimensions**: When height and width are the same, use `size-*` instead of separate `h-*` and `w-*` classes
- **Especially important for icons**: This rule is particularly important for Lucide React icons and other square elements
- **Consistency**: Maintain consistent sizing patterns across the application

```typescript
// ❌ Avoid - separate height and width for equal dimensions
<User className="h-4 w-4" />
<Search className="h-6 w-6 text-gray-500" />
<div className="h-8 w-8 bg-blue-500 rounded" />

// ✅ Preferred - use size-* for equal dimensions
<User className="size-4" />
<Search className="size-6 text-gray-500" />
<div className="size-8 bg-blue-500 rounded" />

// ✅ Still use separate h-* and w-* when dimensions differ
<div className="h-4 w-full bg-gray-200" />
<img className="h-32 w-48 object-cover" />
```

### Icon Usage Guidelines

- Use `size-*` utilities for all square icons (Lucide, Heroicons, etc.)
- Common icon sizes: `size-3`, `size-4`, `size-5`, `size-6`, `size-8`
- Maintain consistent icon sizing within similar UI contexts
- Use semantic sizing (e.g., `size-4` for inline text icons, `size-6` for buttons)

# Typescript

- Write clean, clear, well-designed, explicit Typescript
- Make sure types are validated strictly
- Use implicit type inference, unless impossible
- Consider using classes for server-side services, but export a function instead of the class

```tsx
// service.ts
class UserService {
  getUser(id: number) {
    // ... implementation ...
    return { id, name: 'Example User' };
  }
}

export function createUserService() {
  return new UserService();
}
```

- Follow the Single Responsibility Principle (SRP). Each module/function/class should have one reason to change.
- Favor composition over inheritance.
- Handle errors gracefully using try/catch and appropriate error types.
- Keep functions short and focused.
- Use descriptive names for variables, functions, and classes.
- Avoid unnecessary complexity.
- Avoid using `any` type as much as possible. If necessary, use `unknown`
- Use enums only when appropriate. Consider union types of string literals as an alternative.
- Be aware of performance implications of your code.

# UI Components

- Reusable UI components are defined in the `components/ui` directory.
- Components are imported using the `@/components/ui/{component-name}` format.

## Styling

- Styling is done using Tailwind CSS. We use the "cn" function from `@/lib/utils` to generate class names.
- Avoid fixed classes such as "bg-gray-500". Instead, use Shadcn classes such as "bg-background", "text-secondary-foreground", "text-muted-foreground", etc.

## Importing Components

```tsx
// Import Shadcn UI components
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Dialog } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
```

## Available Shadcn UI Components

| Component          | Description                               | Import Path                         |
| ------------------ | ----------------------------------------- | ----------------------------------- |
| `Accordion`        | Expandable/collapsible content sections   | `@/components/ui/accordion`         |
| `AlertDialog`      | Modal dialog for important actions        | `@/components/ui/alert-dialog`      |
| `Alert`            | Status/notification messages              | `@/components/ui/alert`             |
| `Avatar`           | User profile images with fallback         | `@/components/ui/avatar`            |
| `Badge`            | Small status indicators                   | `@/components/ui/badge`             |
| `Breadcrumb`       | Navigation path indicators                | `@/components/ui/breadcrumb`        |
| `Button`           | Clickable action elements                 | `@/components/ui/button`            |
| `Calendar`         | Date picker and date display              | `@/components/ui/calendar`          |
| `Card`             | Container for grouped content             | `@/components/ui/card`              |
| `Carousel`         | Image/content carousel                    | `@/components/ui/carousel`          |
| `Chart`            | Data visualization charts                 | `@/components/ui/chart`             |
| `Checkbox`         | Selection input                           | `@/components/ui/checkbox`          |
| `CodeBlock`        | Syntax highlighted code display           | `@/components/ui/code-block`        |
| `Collapsible`      | Expandable content container              | `@/components/ui/collapsible`       |
| `ComboBox`         | Searchable select input                   | `@/components/ui/combo-box`         |
| `Command`          | Command palette interface                 | `@/components/ui/command`           |
| `CopyToClipboard`  | Copy text to clipboard utility            | `@/components/ui/copy-to-clipboard` |
| `CronInput`        | Cron expression input                     | `@/components/ui/cron-input`        |
| `Dialog`           | Modal window for focused interactions     | `@/components/ui/dialog`            |
| `DropdownMenu`     | Menu triggered by a button                | `@/components/ui/dropdown-menu`     |
| `ErrorAlert`       | Error message display                     | `@/components/ui/error-alert`       |
| `Form`             | Form components with validation           | `@/components/ui/form`              |
| `GoogleButton`     | Google OAuth button                       | `@/components/ui/google-button`     |
| `Heading`          | Styled heading text                       | `@/components/ui/heading`           |
| `Input`            | Text input field                          | `@/components/ui/input`             |
| `Label`            | Text label for form elements              | `@/components/ui/label`             |
| `LoadingSpinner`   | Loading indicator                         | `@/components/ui/loading-spinner`   |
| `Logo`             | Application logo component                | `@/components/ui/logo`              |
| `Mermaid`          | Mermaid diagram renderer                  | `@/components/ui/mermaid`           |
| `MessageLoading`   | Loading state for messages                | `@/components/ui/message-loading`   |
| `Modal`            | Modal wrapper component                   | `@/components/ui/modal`             |
| `Popover`          | Floating content triggered by interaction | `@/components/ui/popover`           |
| `Progress`         | Progress indicator                        | `@/components/ui/progress`          |
| `RadioGroup`       | Radio button selection group              | `@/components/ui/radio-group`       |
| `RefreshButton`    | Refresh action button                     | `@/components/ui/refresh-button`    |
| `Resizable`        | Resizable panels                          | `@/components/ui/resizable`         |
| `ScrollArea`       | Customizable scrollable area              | `@/components/ui/scroll-area`       |
| `Select`           | Dropdown selection menu                   | `@/components/ui/select`            |
| `Separator`        | Visual divider between content            | `@/components/ui/separator`         |
| `Sheet`            | Sliding panel from screen edge            | `@/components/ui/sheet`             |
| `Sidebar`          | Sidebar navigation component              | `@/components/ui/sidebar`           |
| `SidebarDialog`    | Sidebar in dialog format                  | `@/components/ui/sidebar-dialog`    |
| `Skeleton`         | Loading placeholder                       | `@/components/ui/skeleton`          |
| `Slider`           | Range input slider                        | `@/components/ui/slider`            |
| `Sonner`           | Toast notifications                       | `@/components/ui/sonner`            |
| `Spinner`          | Loading spinner                           | `@/components/ui/spinner`           |
| `SpinnerContainer` | Container with spinner                    | `@/components/ui/spinner-container` |
| `Steps`            | Step indicator component                  | `@/components/ui/steps`             |
| `Switch`           | Toggle control                            | `@/components/ui/switch`            |
| `Table`            | Data table                                | `@/components/ui/table`             |
| `Tabs`             | Tab-based navigation                      | `@/components/ui/tabs`              |
| `Textarea`         | Multi-line text input                     | `@/components/ui/textarea`          |
| `Timeline`         | Timeline component                        | `@/components/ui/timeline`          |
| `Toast`            | Toast notification component              | `@/components/ui/toast`             |
| `Toaster`          | Toast container                           | `@/components/ui/toaster`           |
| `Tooltip`          | Contextual information on hover           | `@/components/ui/tooltip`           |

## Specialized Badge Components

| Component           | Description              | Import Path                           |
| ------------------- | ------------------------ | ------------------------------------- |
| `AccessLevelBadge`  | Access level indicator   | `@/components/ui/access-level-badge`  |
| `ResourceTypeBadge` | Resource type indicator  | `@/components/ui/resource-type-badge` |
| `SeverityBadge`     | Severity level indicator | `@/components/ui/severity-badge`      |
| `StatusBadge`       | Status indicator         | `@/components/ui/status-badge`        |

## Usage Examples

```tsx
// Basic form with validation
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// Dialog usage
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Toast notifications
import { toast } from '@/components/ui/sonner';
// Table with data
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

function showSuccess() {
  toast.success('Operation completed successfully!');
}
```

## Icon Usage

When importing icons from `lucide-react`, always use the Icon suffix for better clarity and consistency:

```tsx
// ✅ Preferred - with Icon suffix
import { CalendarIcon, PaperclipIcon, UserIcon } from 'lucide-react';
// ❌ Avoid - without Icon suffix
import { Calendar, Paperclip, User } from 'lucide-react';
```

This convention:

- Makes it clear that the component is an icon
- Avoids naming conflicts with other components or variables
- Improves code readability and maintainability
- Follows consistent naming patterns across the codebase

## Best Practices

1. **Import only what you need**: Import specific components rather than entire modules
2. **Use semantic class names**: Prefer `bg-background` over `bg-white`
3. **Leverage variant props**: Most components support variant props for different styles
4. **Compose components**: Build complex UI by composing simpler components
5. **Use TypeScript**: All components are fully typed for better development experience
6. **Use Icon suffix**: Always import Lucide icons with the Icon suffix (e.g., `UserIcon` not `User`)

YOU MUST NEVER START THE DEV SERVER UNLESS THE USER HAS TOLD YOU TO.

Never run pnpm run dev, pnpm start, or the equivalent "start or build" command.

The user is running the dev server locally and if you do that you could mess up the dev server.
