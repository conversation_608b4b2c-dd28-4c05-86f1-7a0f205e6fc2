'use client';

import { useEffect, useRef, useState } from 'react';

import { ChatInputPlaceholder } from '@/components/chat/chat-input-placeholder';
// Import mention-related components and utilities
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import { AutocompleteDropdown } from '@/components/chat/components/autocomplete-dropdown';
import { ChatHelpDialog } from '@/components/chat/components/chat-help-dialog/chat-help-dialog';
import { ChatInputContainer } from '@/components/chat/components/chat-input-container';
import { populateResourceCategories } from '@/components/chat/components/data';
import { PlusButtonDropdown } from '@/components/chat/components/plus-button-dropdown';
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import { FilePreviewList } from '@/components/chat/file-preview';
import {
  getIncompleteAtCursor,
  highlightMentions,
} from '@/components/chat/utils/mention-highlighting';
import { handleShortcutSelect } from '@/components/chat/utils/shortcut-handlers';
import { ResourceAttachmentIndicator } from '@/components/resource-attachment-indicator';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Textarea } from '@/components/ui/textarea';
import { WithTooltip } from '@/components/ui/tooltip';
import { QuickSelectAgents } from '@/features/agent/components/quick-select-agents';
import { useNavigateAgentDetail } from '@/features/agent/hooks/use-navigate-agent-detail';
import { builtinToolsQuery } from '@/features/builtin-tools/hooks/builtin-tools.query';
import { HomeChatQuickConnection } from '@/features/connection-v2/components/home-chat-quick-connection';
import { resourceQuery } from '@/features/resources/hooks/resource.query';
import { ShortcutMentionDropdown } from '@/features/shortcut/components/shortcut-mention-dropdown';
import { expandShortcuts } from '@/features/shortcut/utils/shortcut-expansion';
import { OperationHubDialog } from '@/features/task/components/operation-hub-dialog';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useKnowledgeBases } from '@/hooks/use-knowledge-bases';
import { cn } from '@/lib/utils';
import {
  SchemaAppSchemasTaskTemplateTaskTemplateResponse,
  SchemaShortcutPublic,
} from '@/openapi-ts/gens';
import { CalendarIcon } from 'lucide-react';
import { toast } from 'sonner';
import { useDebounceValue } from 'usehooks-ts';

// import { ExamplePrompts } from './example-prompts';
import { HomeQuickBuiltinTools } from '../../../../features/builtin-tools/components/home-quick-builtin-tools';
import { calculateMentionDropdownPosition } from '../utils/calculate-mention-dropdown-position';
// import { HomeQuickNavigate } from './home-quick-navigate';
import { QuickIntegrationNavigate } from './quick-integration-navigate';
import { SendButton } from './send-button';

export const QuickGenerate = () => {
  const navigateAgentDetail = useNavigateAgentDetail();

  // File upload hook
  const {
    files,
    addFiles,
    removeFile,
    clearAll: clearAllFiles,
    isUploading,
    hasValidationErrors,
    completedAttachmentIds,
  } = useFileUpload();

  const [autocompleteFilter, setAutocompleteFilter] = useState('');
  const [debouncedAutocompleteFilter] = useDebounceValue(
    autocompleteFilter,
    300,
  );
  // Original state
  const [inputValue, setInputValue] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Mention-related state
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState(
    () => ({
      top: 0,
      left: 0,
    }),
  );
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState(() => ({
    top: 0,
    left: 0,
  }));
  const [agentMentionFilter, setAgentMentionFilter] = useState('');

  // Shortcut mention state
  const [showShortcutMention, setShowShortcutMention] = useState(false);
  const [shortcutMentionPosition, setShortcutMentionPosition] = useState(
    () => ({
      top: 0,
      left: 0,
    }),
  );
  const [shortcutMentionFilter, setShortcutMentionFilter] = useState('');

  // Resource selection state
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(
    null,
  );

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);
  const isSubmittingRef = useRef(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch builtin tools and knowledge bases
  const { data: builtinTools, isPending: isBuiltinToolsLoading } =
    builtinToolsQuery.query.useList();
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

  // Fetch resources for resource selection
  const { data: resourcesData } = resourceQuery.query.useInfiniteList({});
  const allResources = resourcesData?.pages.flatMap((page) => page.data) || [];

  // Find selected resource
  const selectedResource = selectedResourceId
    ? allResources.find((resource) => resource.id === selectedResourceId)
    : null;

  // Prepare dynamic resource categories using the helper function
  const dynamicResourceCategories = populateResourceCategories({
    builtinTools,
    isBuiltinToolsLoading,
    kbs,
    kbsLoading,
  });

  const handleDirectMessage = async () => {
    if (
      (!inputValue.trim() &&
        completedAttachmentIds.length === 0 &&
        !selectedResourceId) ||
      isSending
    )
      return;

    setIsSending(true);
    try {
      // Generate default message based on what's attached
      const generateDefaultMessage = () => {
        const hasFiles = completedAttachmentIds.length > 0;
        const hasResource = !!selectedResourceId;

        if (hasFiles && hasResource) {
          return 'Please analyze these files and resource';
        }
        if (hasFiles) {
          return 'Please analyze these files';
        }
        if (hasResource) {
          return 'Please analyze this resource';
        }
        return '';
      };

      const rawMessage = inputValue.trim() || generateDefaultMessage();

      // Expand shortcuts before sending the message
      let message: string;
      try {
        message = await expandShortcuts(rawMessage);
      } catch (error) {
        console.error('Error expanding shortcuts:', error);
        // If shortcut expansion fails, use the original message
        message = rawMessage;
      }

      // If we have attachments or resource, store them in session storage for the agent detail page
      if (completedAttachmentIds.length > 0 || selectedResourceId) {
        const messageData = {
          attachmentIds:
            completedAttachmentIds.length > 0
              ? completedAttachmentIds
              : undefined,
          resourceId: selectedResourceId,
          timestamp: Date.now(),
        };
        sessionStorage.setItem(
          'quick-generate-message-data',
          JSON.stringify(messageData),
        );
      }

      navigateAgentDetail({
        initialMessage: message,
      });

      setInputValue('');
      clearAllFiles(); // Clear files after sending
      setSelectedResourceId(null); // Clear selected resource
    } finally {
      setIsSending(false);
    }
  };

  // File upload handlers
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      addFiles(files);
    }
    // Reset input to allow selecting the same file again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      addFiles(files);
    }
  };

  // Update handleKeyDown to handle mention navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention || showShortcutMention) {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
        setShowShortcutMention(false);
        return;
      }
      // Don't handle other keys when dropdowns are open
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (
        !isSending &&
        (inputValue.trim() ||
          completedAttachmentIds.length > 0 ||
          selectedResourceId)
      ) {
        handleDirectMessage();
      }
    }

    // Handle #, @, and / key presses to trigger mentions
    if (e.key === '#' || e.key === '@' || e.key === '/') {
      const cursorPos = (e.currentTarget as HTMLTextAreaElement).selectionStart;
      const textBeforeCursor = inputValue.substring(0, cursorPos);

      // Check if the character before is a space or start of line
      const lastChar = textBeforeCursor.slice(-1);
      if (!lastChar || lastChar === ' ' || lastChar === '\n') {
        // Let the character be typed, checkForMentions will handle showing the dropdown
        return;
      }
    }
  };

  // Add effect to handle global keyboard events for dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention && !showShortcutMention)
      return;

    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowResourceMention(false);
        setShowAgentMention(false);
        setShowShortcutMention(false);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [showResourceMention, showAgentMention, showShortcutMention]);

  // Add effect to handle clicks outside dropdowns
  useEffect(() => {
    if (!showResourceMention && !showAgentMention && !showShortcutMention)
      return;

    const handleClickOutside = (e: MouseEvent) => {
      // Check if click is outside both textarea AND dropdown
      if (
        textareaRef.current &&
        !textareaRef.current.contains(e.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node)
      ) {
        setShowResourceMention(false);
        setShowAgentMention(false);
        setShowShortcutMention(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showResourceMention, showAgentMention, showShortcutMention]);

  // Add effect to update mention position on window resize and cursor movement
  useEffect(() => {
    if (!showResourceMention && !showAgentMention && !showShortcutMention)
      return;

    const updateMentionPosition = () => {
      if (textareaRef.current) {
        const cursorPos = textareaRef.current.selectionStart;

        if (showResourceMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'resource',
          );
          setResourceMentionPosition(position);
        }

        if (showAgentMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'agent',
          );
          setAgentMentionPosition(position);
        }

        if (showShortcutMention) {
          const position = calculateMentionDropdownPosition(
            textareaRef.current,
            cursorPos,
            'agent', // Use 'agent' for similar positioning
          );
          setShortcutMentionPosition(position);
        }
      }
    };

    const handleResize = updateMentionPosition;
    const handleScroll = updateMentionPosition;

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    // Also update position when textarea is scrolled
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.addEventListener('scroll', handleScroll);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
      if (textarea) {
        textarea.removeEventListener('scroll', handleScroll);
      }
    };
  }, [showResourceMention, showAgentMention, showShortcutMention]);

  const { highlightedText, hasMentions } = highlightMentions(inputValue);

  // Generic mention handler to reduce code duplication
  const handleMentionSelect = (
    rawValue: string,
    mentionType: 'resource' | 'agent' | 'shortcut',
    prefix: string,
    setShowMention: (show: boolean) => void,
    valueTransformer?: (value: string) => string,
  ) => {
    if (!textareaRef.current) return;

    const cursorPos = textareaRef.current.selectionStart;
    const textBeforeCursor = inputValue.substring(0, cursorPos);
    const textAfterCursor = inputValue.substring(cursorPos);

    const mentionInfo = getIncompleteAtCursor(inputValue, cursorPos);

    if (mentionInfo && mentionInfo.type === mentionType) {
      // Apply value transformation if provided
      const processedValue = valueTransformer
        ? valueTransformer(rawValue)
        : rawValue;

      // Construct new text with the replacement
      const mentionText = `${prefix}${processedValue}`;
      const newText =
        textBeforeCursor.substring(0, mentionInfo.startIndex) +
        mentionText +
        ' ' +
        textAfterCursor;

      setInputValue(newText);
      setShowMention(false);

      // Use requestAnimationFrame for smoother focus and scroll
      requestAnimationFrame(() => {
        if (textareaRef.current) {
          const newCursorPos =
            mentionInfo.startIndex + `${mentionText} `.length;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);

          // Smooth scroll to cursor position
          textareaRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      });
    }
  };

  // Add handlers for mention selection
  const handleResourceSelect = (fullPath: string) => {
    handleMentionSelect(
      fullPath,
      'resource',
      '#',
      setShowResourceMention,
      (path) => path.replace(/\//g, '/').toLowerCase(),
    );
  };

  const handleAgentSelect = (agentName: string) => {
    handleMentionSelect(
      agentName,
      'agent',
      '@',
      setShowAgentMention,
      (name) => name.split(' (')[0], // Remove parenthetical information
    );
  };

  // Handle shortcut selection
  const handleShortcutSelectLocal = (shortcut: SchemaShortcutPublic) => {
    handleShortcutSelect(shortcut, {
      textareaRef,
      localValue: inputValue,
      setLocalValue: setInputValue,
      onChange: setInputValue,
      setShowShortcutMention,
    });
  };

  // Add new methods for handling mentions
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      setShowResourceMention(false);
      setShowAgentMention(false);
      setShowShortcutMention(false);
      return;
    }

    if (mentionInfo.type === 'resource' && textareaRef.current) {
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateMentionDropdownPosition(
        textareaRef.current,
        cursorPos,
        'resource',
      );
      setResourceMentionPosition(position);
      setShowResourceMention(true);
      setShowAgentMention(false);
      setShowShortcutMention(false);
    } else if (mentionInfo.type === 'agent' && textareaRef.current) {
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateMentionDropdownPosition(
        textareaRef.current,
        cursorPos,
        'agent',
      );
      setAgentMentionPosition(position);
      setShowAgentMention(true);
      setShowResourceMention(false);
      setShowShortcutMention(false);
    } else if (mentionInfo.type === 'shortcut' && textareaRef.current) {
      setShortcutMentionFilter(mentionInfo.filter.toLowerCase());
      const position = calculateMentionDropdownPosition(
        textareaRef.current,
        cursorPos,
        'agent', // Use 'agent' for similar positioning
      );
      setShortcutMentionPosition(position);
      setShowShortcutMention(true);
      setShowResourceMention(false);
      setShowAgentMention(false);
    }
  };

  // Enhanced handleChange with improved autocomplete logic
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    setInputValue(newValue);

    // Check for mentions
    checkForMentions(newValue, e.target.selectionStart);

    // Enhanced autocomplete handling
    const cursorPos = e.target.selectionStart;
    const textBeforeCursor = newValue.substring(0, cursorPos);
    const words = textBeforeCursor.split(/\s+/);
    const lastWord = words[words.length - 1] || '';

    // Show autocomplete if user typed at least 2 characters
    if (
      lastWord.length >= 2 &&
      !lastWord.startsWith('#') &&
      !lastWord.startsWith('@')
    ) {
      setAutocompleteFilter(lastWord);
    } else {
      setAutocompleteFilter('');
    }
  };

  // Enhanced autocomplete selection handler
  const handleAutocompleteSelect = (
    suggestion: SchemaAppSchemasTaskTemplateTaskTemplateResponse,
  ) => {
    // Don't select loading or empty state items

    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = inputValue.substring(0, cursorPos);

      // Find the last word before cursor
      const words = textBeforeCursor.split(/\s+/);
      const lastWord = words[words.length - 1] || '';

      setInputValue(suggestion.context);
      setAutocompleteFilter('');

      // Focus and position cursor with smooth scroll
      requestAnimationFrame(() => {
        if (textareaRef.current) {
          const newCursorPos =
            textBeforeCursor.length -
            lastWord.length +
            suggestion.context.length +
            1;
          textareaRef.current.focus();
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);

          // Smooth scroll to cursor position
          textareaRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          });
        }
      });

      toast.success(`Applied template: ${suggestion.task}`);
    }
  };

  // Handle cursor position changes (click, arrow keys, etc.)
  const handleCursorMove = () => {
    if (
      textareaRef.current &&
      (showResourceMention || showAgentMention || showShortcutMention)
    ) {
      const cursorPos = textareaRef.current.selectionStart;

      if (showResourceMention) {
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'resource',
        );
        setResourceMentionPosition(position);
      }

      if (showAgentMention) {
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'agent',
        );
        setAgentMentionPosition(position);
      }

      if (showShortcutMention) {
        const position = calculateMentionDropdownPosition(
          textareaRef.current,
          cursorPos,
          'agent', // Use 'agent' for similar positioning
        );
        setShortcutMentionPosition(position);
      }
    }
  };

  // Add syncScroll for overlay synchronization
  const syncScroll = () => {
    if (overlayRef.current && textareaRef.current) {
      // Use RAF for smooth scrolling without flickering
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
    }
  };

  return (
    <>
      <div className="relative space-y-8 rounded-lg transition-all">
        <ChatInputContainer>
          {/* File Upload Area */}
          <If condition={files.length > 0}>
            <div className="border-muted-foreground/10 border-b p-3">
              <FilePreviewList
                files={files}
                showPreview={true}
                onRemove={removeFile}
              />
            </div>
          </If>

          {/* Resource Mention Dropdown */}
          <ResourceMentionDropdown
            isVisible={showResourceMention}
            position={resourceMentionPosition}
            filter={resourceMentionFilter}
            categories={dynamicResourceCategories}
            onSelect={handleResourceSelect}
            onClose={() => setShowResourceMention(false)}
            dropdownRef={dropdownRef}
          />

          {/* Agent Mention Dropdown */}
          <AgentMentionDropdown
            isVisible={showAgentMention}
            position={agentMentionPosition}
            filter={agentMentionFilter}
            onSelect={handleAgentSelect}
            onClose={() => setShowAgentMention(false)}
          />

          {/* Shortcut Mention Dropdown */}
          <ShortcutMentionDropdown
            isVisible={showShortcutMention}
            position={shortcutMentionPosition}
            filter={shortcutMentionFilter}
            onSelect={handleShortcutSelectLocal}
            onClose={() => setShowShortcutMention(false)}
          />

          {/* Input Area with Drag & Drop Support */}
          <div
            ref={containerRef}
            className="relative"
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onPaste={() => {
                // Smooth scroll after paste operation
                requestAnimationFrame(() => {
                  if (overlayRef.current && textareaRef.current) {
                    // Sync overlay scroll with textarea
                    overlayRef.current.scrollTop =
                      textareaRef.current.scrollTop;

                    // Smooth scroll to cursor position
                    textareaRef.current.scrollIntoView({
                      behavior: 'smooth',
                      block: 'nearest',
                      inline: 'nearest',
                    });
                  }
                });
              }}
              onScroll={syncScroll}
              onClick={handleCursorMove}
              onKeyUp={handleCursorMove}
              onSelect={handleCursorMove}
              placeholder=""
              disabled={isSending}
              className={cn(
                'w-full',
                'max-h-48 min-h-36',
                'custom-scrollbar resize-none overflow-y-auto',
                paddingClassName,
                'border-0 hover:shadow-none focus-visible:ring-0',
                'bg-transparent',
                'text-base',
                'transition-[height] duration-100 ease-out', // Add smooth height transition
                hasMentions
                  ? 'caret-foreground selection:bg-primary/20 text-transparent'
                  : 'caret-foreground',
                'placeholder:opacity-0',
                'border-none! shadow-none!',
              )}
            />

            {/* Highlighted Text Overlay */}
            <div
              ref={overlayRef}
              className={cn(
                'custom-scrollbar pointer-events-none absolute inset-0 h-full w-full resize-none overflow-y-auto text-base break-words whitespace-pre-wrap transition-[height] duration-100 ease-out',
                paddingClassName,
                { invisible: !hasMentions },
              )}
            >
              {highlightedText}
            </div>

            {/* Placeholder */}
            {!inputValue && files.length === 0 && (
              <ChatInputPlaceholder className={paddingClassName} />
            )}

            {!inputValue && files.length > 0 && (
              <div
                className={cn(
                  'text-muted-foreground pointer-events-none absolute inset-0',
                  paddingClassName,
                )}
              >
                <div className="text-base leading-relaxed">
                  {isUploading
                    ? 'Processing files...'
                    : hasValidationErrors
                      ? 'Fix file errors before sending'
                      : 'Type your message...'}
                </div>
              </div>
            )}
          </div>

          {/* Bottom Toolbar */}
          <div
            className={cn(
              'flex items-center justify-between gap-2',
              paddingClassName,
            )}
          >
            <div className="flex items-center gap-2">
              {/* Plus Button Dropdown for File Upload and Resource Selection */}
              <PlusButtonDropdown
                onFileSelect={handleFileSelect}
                onResourceSelect={setSelectedResourceId}
                selectedResourceId={selectedResourceId}
                disabled={isSending}
                isStreaming={false}
              />

              {/* Resource Attachment Indicator */}
              <If condition={!!selectedResource}>
                <ResourceAttachmentIndicator
                  selectedResource={selectedResource}
                  conversationResource={null}
                  onRemove={() => setSelectedResourceId(null)}
                />
              </If>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,.pdf,.txt,.csv,.json,.docx,.xlsx"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
            <div className="flex items-center gap-2">
              <ChatHelpDialog />

              <QuickIntegrationNavigate />

              {/* Generate Button */}
              <OperationHubDialog
                taskTemplate={{
                  task: inputValue.split('\n')[0] || 'New Task',
                  description:
                    inputValue.trim() || 'Task created from homepage input',
                  context: inputValue.trim(),
                  schedule: '',
                }}
              >
                <WithTooltip tooltip="Create a standard task definition format based on your requirements">
                  <Button
                    size="icon"
                    variant="outline"
                    disabled={!inputValue.trim() || isSending}
                    className="h-8 w-8 rounded-full transition-all duration-200"
                  >
                    <CalendarIcon className="size-4" />
                    <span className="sr-only">Generate</span>
                  </Button>
                </WithTooltip>
              </OperationHubDialog>
              <SendButton
                value={
                  inputValue ||
                  (completedAttachmentIds.length > 0 ? 'files' : '') ||
                  (selectedResourceId ? 'resource' : '')
                }
                isGenerating={false}
                isSending={isSending || isUploading || hasValidationErrors}
                onSend={handleDirectMessage}
              />
            </div>
          </div>
        </ChatInputContainer>

        {/* Quick Actions */}
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-2 max-sm:flex-col">
            <QuickSelectAgents setInputValue={setInputValue} />

            <div className="flex gap-2">
              <HomeChatQuickConnection />
            </div>
          </div>
          {/* <Separator className="w-full" />
          <div className="flex gap-2">
            <HomeQuickNavigate />
          </div> */}
          <div className="flex flex-wrap justify-center gap-2">
            <HomeQuickBuiltinTools
              inputValue={inputValue}
              setInputValue={setInputValue}
            />
          </div>
        </div>

        <If condition={autocompleteFilter}>
          <div className="absolute top-full z-20 w-full">
            <AutocompleteDropdown
              onSelect={handleAutocompleteSelect}
              searchQuery={debouncedAutocompleteFilter}
              onClose={() => setAutocompleteFilter('')}
            />
          </div>
        </If>
      </div>
    </>
  );
};

const paddingClassName = 'p-3';
