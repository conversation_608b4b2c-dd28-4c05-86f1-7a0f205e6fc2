import Link from 'next/link';

import { But<PERSON> } from '@/components/ui/button';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { useGetNavigationConfig } from '@/hooks/use-get-navigation-config';

export function QuickIntegrationNavigate() {
  const { getRoutePath } = useGetNavigationConfig();

  const integrationRoute = getRoutePath(pathsConfig.app.integrations);

  return (
    <WithTooltip tooltip={integrationRoute.label}>
      <Button size="icon" variant="ghost" asChild className="size-8">
        <Link href={integrationRoute.path}>
          {integrationRoute.Icon && (
            <integrationRoute.Icon className="size-4" />
          )}
        </Link>
      </Button>
    </WithTooltip>
  );
}
