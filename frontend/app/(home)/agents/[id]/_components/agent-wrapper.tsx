'use client';

import {
  Fragment,
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
} from 'react';

export const ClearStateNewChatWrapper = ({
  conversationId,
  children,
}: PropsWithChildren<{
  conversationId: string | undefined;
}>) => {
  const preConversationId = useRef(conversationId);
  const [sessionKey, setSessionKey] = useState(() => crypto.randomUUID());

  useEffect(() => {
    if (
      // user start a new chat
      !conversationId ||
      // user navigate to another chat → reset key
      (preConversationId.current &&
        preConversationId.current !== conversationId)
    ) {
      // user start a new chat → reset key
      setSessionKey(crypto.randomUUID());
    }
    preConversationId.current = conversationId;
  }, [conversationId]);

  return <Fragment key={sessionKey}>{children}</Fragment>;
};
