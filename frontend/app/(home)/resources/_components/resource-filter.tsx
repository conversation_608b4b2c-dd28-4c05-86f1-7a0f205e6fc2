'use client';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import pathsConfig from '@/config/paths.config';
import { CloudSyncConfigDialog } from '@/features/cloud-sync-config';
import { RESOURCE_CATEGORY_CONFIG } from '@/features/resources/config/resource-category.config';
import { RESOURCE_TYPE_CONFIG } from '@/features/resources/config/resource-type.config';
import { ResourceQueryParams } from '@/features/resources/models/resource.type';
import { useFormFilter } from '@/hooks/use-form-filter';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';
import { SearchIcon, Settings, TrendingUpIcon } from 'lucide-react';

type FormFilter = WithPaginationDefaults<ResourceQueryParams>;

type Props = {
  defaultValues: FormFilter;
};

export function ResourceFilter({ defaultValues }: Props) {
  const { form, onSubmit } = useFormFilter<FormFilter>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'name',
  });

  const { control, handleSubmit } = form;

  return (
    <div className="flex flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
      <Form {...form}>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex flex-wrap items-center gap-4 pl-0.5 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
        >
          <FormField
            control={control}
            name="name"
            render={({ field }) => (
              <FormItem className="grow md:max-w-lg">
                <FormControl>
                  <InputIconPrefix
                    Icon={SearchIcon}
                    placeholder={`Search resource...`}
                    className={'w-full'}
                    {...field}
                    value={field.value ?? ''}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
            <FormField
              control={control}
              name="resource_type"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Autocomplete
                      options={RESOURCE_TYPE_CONFIG.LIST}
                      mode="multiple"
                      {...field}
                      name="resource type"
                      PopoverContentProps={{
                        className: 'md:w-[340px]',
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Autocomplete
                      options={RESOURCE_CATEGORY_CONFIG.LIST}
                      mode="multiple"
                      {...field}
                      name="category"
                      PopoverContentProps={{
                        className: 'md:w-[340px]',
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>

      <div className="flex items-center gap-2">
        <Link href={pathsConfig.app.wellArchitectedAssessment}>
          <Button
            type="button"
            size="sm"
            className="bg-gradient transform gap-2 shadow-lg hover:shadow-xl hover:brightness-110"
          >
            <TrendingUpIcon className="size-4" />
            Well-Architected Assessment
          </Button>
        </Link>

        <CloudSyncConfigDialog>
          <Button type="button" size="sm" variant="outline" className="gap-2">
            <Settings className="size-4" />
            Configure
          </Button>
        </CloudSyncConfigDialog>
      </div>
    </div>
  );
}
