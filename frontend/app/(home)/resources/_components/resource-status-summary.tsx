'use client';

import DashboardCard from '@/components/dashboard-card';
import { resourceQuery } from '@/features/resources/hooks/resource.query';
import { BarChart4, CloudCog, DollarSign } from 'lucide-react';

export function ResourceStatusSummary() {
  const {
    data: statistics,
    isLoading,
    isError,
    error,
  } = resourceQuery.query.useStatistics();

  if (statistics) {
    const totalSavings =
      statistics.potential_monthly_savings?.total_savings || 0;

    const topResourceTypes =
      statistics.top_resource_types?.map((item) => [
        item.resource_type,
        item.count,
      ]) || [];

    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <DashboardCard
          title="Potential Monthly Savings"
          value={`$${totalSavings.toFixed(2)}`}
          subtitle={`From ${statistics.potential_monthly_savings?.resources_with_savings || 0} resources`}
          icon={<DollarSign className="text-muted-foreground size-4" />}
        />

        <DashboardCard
          title="Total Resources"
          value={statistics.total_resources || 0}
          subtitle="Across the workspace"
          icon={<CloudCog className="text-muted-foreground size-4" />}
        />

        <TopResourceTypesCard
          topResourceTypes={topResourceTypes}
          icon={<BarChart4 className="text-muted-foreground size-4" />}
        />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Loading..."
            value="..."
            subtitle="Loading resource data..."
            icon={<div className="size-4 animate-pulse rounded bg-gray-200" />}
          />
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Error"
            value="..."
            subtitle={error?.message || 'Failed to load resource data'}
            icon={<div className="size-4 rounded bg-red-200" />}
          />
        ))}
      </div>
    );
  }
}

function TopResourceTypesCard({
  topResourceTypes,
  icon,
}: {
  topResourceTypes: Array<string | number>[];
  icon: React.ReactNode;
}) {
  return (
    <DashboardCard
      title="Top Resource Types"
      icon={icon}
      value={
        <div className="grid grid-cols-2 content-start gap-x-2 gap-y-1">
          {topResourceTypes.length > 0 ? (
            topResourceTypes.map(([type, count]) => (
              <div
                key={type}
                className="flex min-w-0 items-center justify-between text-sm"
              >
                <span className="truncate text-xs font-medium">{type}</span>
                <span className="text-muted-foreground shrink-0 text-xs font-semibold">
                  {count}
                </span>
              </div>
            ))
          ) : (
            <p className="text-muted-foreground col-span-2 text-sm">
              No type data
            </p>
          )}
        </div>
      }
    />
  );
}
