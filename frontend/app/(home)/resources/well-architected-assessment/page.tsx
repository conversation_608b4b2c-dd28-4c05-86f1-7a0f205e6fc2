import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { WellArchitectedAssessment } from '@/features/well-architected-assessment';

export const metadata = {
  title: 'Well-Architected Assessment',
  description: 'Evaluate your architecture against AWS best practices',
};

export default function WellArchitectedAssessmentPage() {
  return (
    <NewPageContainer>
      <PageHeader
        title="Well-Architected Assessment"
        description="Evaluate your architecture against AWS best practices across all pillars"
      />
      <div className="overflow-thin-auto flex-1">
        <WellArchitectedAssessment />
      </div>
    </NewPageContainer>
  );
}
