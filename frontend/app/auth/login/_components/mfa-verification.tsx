'use client';

import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form';
import { MFAInputOTP } from '@/features/user/components/mfa';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const mfaSchema = z.object({
  mfaCode: z.string().length(6, { message: 'MFA code must be 6 digits' }),
  rememberMe: z.boolean().optional(),
});

interface MFAVerificationProps {
  onSubmit: (mfaCode: string, rememberMe: boolean) => Promise<void>;
  onBack: () => void;
  loading: boolean;
}

export function MFAVerification({
  onSubmit,
  onBack,
  loading,
}: MFAVerificationProps) {
  const form = useForm<z.infer<typeof mfaSchema>>({
    resolver: zodResolver(mfaSchema),
    defaultValues: {
      mfaCode: '',
      rememberMe: false,
    },
  });

  const handleSubmit = async (data: z.infer<typeof mfaSchema>) => {
    await onSubmit(data.mfaCode, data.rememberMe || false);
  };

  const code = form.watch('mfaCode');

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Multi-Factor Authentication</h2>
        <p className="text-muted-foreground mt-2 text-sm">
          Enter the 6-digit code from your authenticator app
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <MFAInputOTP
            control={form.control}
            name="mfaCode"
            disabled={loading}
          />

          <FormField
            control={form.control}
            name="rememberMe"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={loading}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="text-sm font-normal">
                    Remember this device for 7 days
                  </FormLabel>
                  <p className="text-muted-foreground text-xs">
                    You won&apos;t need to enter MFA codes when logging in from
                    this device
                  </p>
                </div>
              </FormItem>
            )}
          />

          <Button
            disabled={loading || (code?.length ?? 0) !== 6}
            className="w-full"
            type="submit"
          >
            {loading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
            Verify Code
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Button
          variant="ghost"
          onClick={onBack}
          disabled={loading}
          className="text-muted-foreground hover:text-foreground text-sm"
        >
          ← Back to login
        </Button>
      </div>

      <div className="text-muted-foreground text-center text-sm">
        <p>Can&apos;t access your authenticator app?</p>
        <p className="mt-1">Use a backup code or contact support for help</p>
      </div>
    </div>
  );
}
