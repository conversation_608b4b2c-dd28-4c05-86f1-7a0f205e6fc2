'use client';

import { useEffect, useState } from 'react';

import Link from 'next/link';

import { AppLogo } from '@/components/app-logo';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import pathsConfig from '@/config/paths.config';
import { Home, RefreshCw } from 'lucide-react';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

// type ErrorType =
//   | 'network'
//   | 'permission'
//   | 'timeout'
//   | 'server'
//   | 'client'
//   | 'unknown';

// function getErrorType(error: Error): ErrorType {
//   const message = error.message.toLowerCase();
//   const name = error.name.toLowerCase();

//   if (
//     message.includes('network') ||
//     message.includes('fetch') ||
//     name.includes('network')
//   ) {
//     return 'network';
//   }

//   if (
//     message.includes('permission') ||
//     message.includes('unauthorized') ||
//     message.includes('forbidden')
//   ) {
//     return 'permission';
//   }

//   if (message.includes('timeout') || name.includes('timeout')) {
//     return 'timeout';
//   }

//   if (
//     message.includes('server') ||
//     message.includes('internal') ||
//     name.includes('server')
//   ) {
//     return 'server';
//   }

//   if (message.includes('client') || name.includes('client')) {
//     return 'client';
//   }

//   return 'unknown';
// }

// function getErrorConfig(errorType: ErrorType, _error: Error) {
//   const configs = {
//     network: {
//       icon: Wifi,
//       title: 'Connection Error',
//       description:
//         'Unable to connect to our servers. Please check your internet connection and try again.',
//       color: 'bg-orange-500/10 text-orange-600 border-orange-200',
//       badgeVariant: 'secondary' as const,
//       suggestions: [
//         'Check your internet connection',
//         'Try refreshing the page',
//         'Disable any VPN or proxy',
//         'Contact support if the issue persists',
//       ],
//     },
//     permission: {
//       icon: Shield,
//       title: 'Access Denied',
//       description:
//         "You don't have permission to access this resource. Please contact your administrator.",
//       color: 'bg-red-500/10 text-red-600 border-red-200',
//       badgeVariant: 'destructive' as const,
//       suggestions: [
//         'Verify your account permissions',
//         'Try logging out and back in',
//         'Contact your administrator',
//         'Check if your session has expired',
//       ],
//     },
//     timeout: {
//       icon: Clock,
//       title: 'Request Timeout',
//       description: 'The request took too long to complete. Please try again.',
//       color: 'bg-yellow-500/10 text-yellow-600 border-yellow-200',
//       badgeVariant: 'secondary' as const,
//       suggestions: [
//         'Try again in a moment',
//         'Check your internet speed',
//         'Close other browser tabs',
//         'Contact support if this continues',
//       ],
//     },
//     server: {
//       icon: AlertCircle,
//       title: 'Server Error',
//       description:
//         "Something went wrong on our end. We're working to fix this issue.",
//       color: 'bg-red-500/10 text-red-600 border-red-200',
//       badgeVariant: 'destructive' as const,
//       suggestions: [
//         'Try refreshing the page',
//         'Wait a few minutes and try again',
//         'Check our status page',
//         'Contact support with the error ID below',
//       ],
//     },
//     client: {
//       icon: Bug,
//       title: 'Client Error',
//       description:
//         'There was an issue with your request. Please try again or contact support.',
//       color: 'bg-blue-500/10 text-blue-600 border-blue-200',
//       badgeVariant: 'secondary' as const,
//       suggestions: [
//         'Refresh the page',
//         'Clear your browser cache',
//         'Try a different browser',
//         'Contact support if needed',
//       ],
//     },
//     unknown: {
//       icon: AlertCircle,
//       title: 'Something went wrong',
//       description:
//         'An unexpected error occurred. Please try again or contact support if the issue persists.',
//       color: 'bg-gray-500/10 text-gray-600 border-gray-200',
//       badgeVariant: 'secondary' as const,
//       suggestions: [
//         'Try refreshing the page',
//         'Clear your browser cache',
//         'Wait a moment and try again',
//         'Contact support if the issue continues',
//       ],
//     },
//   };

//   return configs[errorType];
// }

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  const [isRetrying, setIsRetrying] = useState(false);

  // const errorType = getErrorType(error);
  // const config = getErrorConfig(errorType, error);
  // const ErrorIcon = config.icon;

  // Log error for debugging
  useEffect(() => {
    console.error('Error boundary caught an error:', error);
  }, [error]);

  const handleRetry = async () => {
    setIsRetrying(true);
    // setRetryCount((prev) => prev + 1);

    // Add a small delay for better UX
    await new Promise((resolve) => setTimeout(resolve, 500));

    try {
      reset();
    } finally {
      setIsRetrying(false);
    }
  };

  // Generate error ID for support purposes
  const errorId =
    error.digest || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="overflow-thin-auto flex items-center justify-center">
      <div className="flex h-dvh w-full max-w-4xl flex-col items-center justify-center space-y-12 p-4">
        {/* Header Section */}
        <div className="space-y-4 text-center">
          <div className="flex justify-center">
            {/* <div className={`rounded-full p-6 ${config.color}`}>
              <ErrorIcon className="size-12" />
            </div> */}
            <AppLogo />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center gap-3">
              <Heading level={1} className="text-gradient">
                {/* {config.title} */}
                Oops! Something went wrong.
              </Heading>
              {/* <Badge variant={config.badgeVariant} className="text-sm">
                {errorType.toUpperCase()}
              </Badge> */}
            </div>

            {/* <p className="text-muted-foreground mx-auto max-w-3xl text-lg leading-relaxed">
              {config.description}
            </p> */}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mx-auto flex max-w-md flex-col justify-center gap-4 sm:flex-row">
          <Button variant="outline" size="lg" className="flex-1 gap-2" asChild>
            <Link href={pathsConfig.app.home}>
              <Home className="size-5" />
              Go Home
            </Link>
          </Button>

          <Button
            onClick={handleRetry}
            disabled={isRetrying}
            size="lg"
            className="flex-1 gap-2 whitespace-nowrap"
          >
            <RefreshCw
              className={`size-5 ${isRetrying ? 'animate-spin' : ''}`}
            />
            {isRetrying ? 'Retrying...' : 'Try Again'}
          </Button>
        </div>

        {/* Quick Solutions */}
        {/* <div className="mx-auto max-w-2xl space-y-6">
          <div className="text-center">
            <Heading level={2}>Quick Solutions</Heading>
            <p className="text-muted-foreground">
              Try these steps to resolve the issue
            </p>
          </div>

          <div className="grid gap-4 sm:grid-cols-2">
            {config.suggestions.map((suggestion, index) => (
              <div
                key={index}
                className="bg-card flex items-start gap-3 rounded-lg border p-4"
              >
                <div className="bg-primary mt-2 size-2 flex-shrink-0 rounded-full" />
                <span className="text-sm leading-relaxed">{suggestion}</span>
              </div>
            ))}
          </div>
        </div> */}

        {/* Technical Details */}
        {/* <div className="mx-auto max-w-2xl">
          <Collapsible open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
            <CollapsibleTrigger asChild>
              <Button
                variant="outline"
                size="lg"
                className="bg-card h-auto w-full justify-between p-4"
              >
                <span className="text-lg font-semibold">Technical Details</span>
                {isDetailsOpen ? (
                  <ChevronUp className="size-5" />
                ) : (
                  <ChevronDown className="size-5" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4 space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="bg-card rounded-lg border p-4">
                  <div className="text-muted-foreground mb-1 text-sm font-medium">
                    Error ID
                  </div>
                  <code className="font-mono text-sm break-all">{errorId}</code>
                </div>

                <div className="bg-card rounded-lg border p-4">
                  <div className="text-muted-foreground mb-1 text-sm font-medium">
                    Error Type
                  </div>
                  <span className="text-sm">{error.name}</span>
                </div>

                {retryCount > 0 && (
                  <div className="bg-card rounded-lg border p-4">
                    <div className="text-muted-foreground mb-1 text-sm font-medium">
                      Retry Attempts
                    </div>
                    <span className="text-sm">{retryCount}</span>
                  </div>
                )}
              </div>

              <div className="bg-card rounded-lg border p-4">
                <div className="text-muted-foreground mb-2 text-sm font-medium">
                  Error Message
                </div>
                <code className="text-muted-foreground text-sm leading-relaxed break-all">
                  {error.message}
                </code>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div> */}

        {/* Support Contact */}
        <div className="space-y-3 border-t py-8 text-center">
          <p className="text-lg">
            Need help? Contact our support team at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-gradient font-semibold hover:underline"
            >
              <EMAIL>
            </a>
          </p>
          <p className="text-muted-foreground">
            Please include error ID{' '}
            <code className="bg-muted rounded px-2 py-1 font-mono text-sm">
              {errorId}
            </code>{' '}
            in your message for faster assistance.
          </p>
        </div>
      </div>
    </div>
  );
}
