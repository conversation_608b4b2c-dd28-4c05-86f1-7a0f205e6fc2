import { <PERSON>ada<PERSON> } from 'next';

import { cookies } from 'next/headers';
import Link from 'next/link';
import { redirect } from 'next/navigation';

import { BrandingSection } from '@/components/BrandingSection';
import { Button } from '@/components/ui/button';
import pathsConfig from '@/config/paths.config';
import { getCurrentUser } from '@/features/user/services/get-current-user';
import { UserInfo } from '@/types/common.enum';
import { CheckCircle } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Slack Integration Complete',
  description: 'Congratulations! Your Slack integration is now complete.',
};

export default async function SlackCompletePage() {
  const cookieStore = await cookies();
  const user = await getCurrentUser();
  const access_token = cookieStore.get(UserInfo.AccessToken)?.value;
  const app_id = cookieStore.get('app_id')?.value;
  const team_id = cookieStore.get('team_id')?.value;
  const user_id = user?.id;

  // If no session or token, redirect to login
  if (!access_token || !app_id || !team_id) {
    redirect(pathsConfig.auth.signIn);
  }

  try {
    // Call Slack integration service to complete OAuth
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SLACK_URL}/slack/complete-oauth`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${access_token}`,
        },
        body: JSON.stringify({
          app_id,
          team_id,
          user_id,
        }),
      },
    );

    if (!response.ok) {
      throw new Error('Failed to complete Slack OAuth');
    }
  } catch (error) {
    console.error('Error completing Slack OAuth:', error);
    redirect('/settings?error=slack_oauth_failed');
  }

  // Slack browser URL only
  const slackBrowserUrl = `https://app.slack.com/client/${team_id}`;

  return (
    <div className="relative h-dvh flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      {/* Left side - Branding */}
      <BrandingSection
        title="The Future of Cloud Operations"
        description="Our advanced AI agents work tirelessly to help streamline operations, cut costs, and boost efficiency—empowering your team to focus on what matters most."
        image={{
          src: '/hero-banner.png',
          alt: 'Cloud Thinker Dashboard Preview',
        }}
      />

      {/* Right side - Success message */}
      <div className="relative flex h-full min-h-dvh items-center justify-center lg:min-h-0 lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <div className="flex flex-col space-y-2 text-center">
            <CheckCircle className="mx-auto size-12 text-green-500" />
            <h1 className="text-2xl font-semibold tracking-tight">
              Slack Integration Complete!
            </h1>
            <p className="text-muted-foreground text-sm">
              Your Slack workspace has been successfully connected to
              CloudThinker. You can now start using our AI-powered features in
              Slack.
            </p>
          </div>

          <div className="grid gap-2">
            <Button asChild>
              <Link href={slackBrowserUrl} target="_blank">
                Open in Browser
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
