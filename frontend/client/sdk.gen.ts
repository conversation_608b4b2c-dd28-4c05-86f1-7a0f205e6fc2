// This file is auto-generated by @hey-api/openapi-ts

import type { CancelablePromise } from './core/CancelablePromise';
import { OpenAPI } from './core/OpenAPI';
import { request as __request } from './core/request';
import type { AgentsReadAgentsResponse, AgentsUpdateAgentInstructionsData, AgentsUpdateAgentInstructionsResponse, AgentsUpdateAgentStatusData, AgentsUpdateAgentStatusResponse, AgentsBuiltinToolsGetWorkspaceAgentBuiltinToolsResponse, AgentsBuiltinToolsGetAgentBuiltinToolsDetailData, AgentsBuiltinToolsGetAgentBuiltinToolsDetailResponse, AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsData, AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsResponse, AgentsConnectionsGetWorkspaceAgentConnectionsResponse, AgentsConnectionsCreateAgentConnectionData, AgentsConnectionsCreateAgentConnectionResponse, AgentsConnectionsDeleteAgentConnectionData, AgentsConnectionsDeleteAgentConnectionResponse, AlertsGetAlertStatusSummaryResponse, AlertsCreateAlertData, AlertsCreateAlertResponse, AlertsListAlertsData, AlertsListAlertsResponse, AlertsGetAlertData, AlertsGetAlertResponse, AlertsUpdateAlertData, AlertsUpdateAlertResponse, AlertsDeleteAlertData, AlertsDeleteAlertResponse, AlertsUpdateAlertStatusData, AlertsUpdateAlertStatusResponse, AlertsMarkAllAlertsAcknowledgedResponse, AttachmentsGenerateAttachmentPresignedUrlsData, AttachmentsGenerateAttachmentPresignedUrlsResponse, AttachmentsConfirmAttachmentUploadsData, AttachmentsConfirmAttachmentUploadsResponse, AttachmentsGetAttachmentTaskStatusData, AttachmentsGetAttachmentTaskStatusResponse, AttachmentsGetAttachmentDownloadUrlData, AttachmentsGetAttachmentDownloadUrlResponse, AttachmentsGetAttachmentMetadataData, AttachmentsGetAttachmentMetadataResponse, AuthRegisterData, AuthRegisterResponse, AuthActivateAccountData, AuthActivateAccountResponse, AuthResendActivationData, AuthResendActivationResponse, AutonomousAgentsRenameConversationData, AutonomousAgentsRenameConversationResponse, AutonomousAgentsDeleteConversationData, AutonomousAgentsDeleteConversationResponse, AutonomousAgentsGetConversationsData, AutonomousAgentsGetConversationsResponse, AutonomousAgentsGetMessagesHistoryData, AutonomousAgentsGetMessagesHistoryResponse, AutonomousAgentsGetMessagePlansData, AutonomousAgentsGetMessagePlansResponse, AutonomousAgentsChatStreamData, AutonomousAgentsChatStreamResponse, AutonomousAgentsReconnectToStreamData, AutonomousAgentsReconnectToStreamResponse, AutonomousAgentsGetStreamStatusData, AutonomousAgentsGetStreamStatusResponse, AutonomousAgentsCancelStreamData, AutonomousAgentsCancelStreamResponse, AwsAccountsUpdateAwsAccountData, AwsAccountsUpdateAwsAccountResponse, AwsAccountsCreateAwsAccountData, AwsAccountsCreateAwsAccountResponse, BuiltinConnectionGetBuiltinConnectionsResponse, BuiltinConnectionInstallBuiltinConnectionData, BuiltinConnectionInstallBuiltinConnectionResponse, BuiltinConnectionUpdateBuiltinConnectionData, BuiltinConnectionUpdateBuiltinConnectionResponse, BuiltinConnectionDeleteBuiltinConnectionData, BuiltinConnectionDeleteBuiltinConnectionResponse, BuiltinConnectionUpdateBuiltinConnectionToolsData, BuiltinConnectionUpdateBuiltinConnectionToolsResponse, BuiltinConnectionInstallSandboxConnectionData, BuiltinConnectionInstallSandboxConnectionResponse, BuiltinToolsGetWorkspaceBuiltinToolsResponse, BuiltinToolsUpdateWorkspaceBuiltinToolData, BuiltinToolsUpdateWorkspaceBuiltinToolResponse, CloudSyncConfigGetResourceTypesData, CloudSyncConfigGetResourceTypesResponse, CloudSyncConfigGetWorkspaceConfigsResponse, CloudSyncConfigCreateOrUpdateConfigData, CloudSyncConfigCreateOrUpdateConfigResponse, CloudSyncConfigUpdateConfigData, CloudSyncConfigUpdateConfigResponse, CloudSyncConfigDeleteConfigData, CloudSyncConfigDeleteConfigResponse, CloudSyncConfigTriggerManualSyncData, CloudSyncConfigTriggerManualSyncResponse, ConsoleProxyGetFilesResponse, ConsoleProxyGetFileContentData, ConsoleProxyGetFileContentResponse, DashboardAnalyticsGetDashboardAnalyticsData, DashboardAnalyticsGetDashboardAnalyticsResponse, DashboardAnalyticsGetHighConsumingServicesData, DashboardAnalyticsGetHighConsumingServicesResponse, DashboardAnalyticsGetTopRecommendationsData, DashboardAnalyticsGetTopRecommendationsResponse, DashboardsGetDashboardByConversationData, DashboardsGetDashboardByConversationResponse, DashboardsCreateShareLinkData, DashboardsCreateShareLinkResponse, DashboardsRevokeShareLinkData, DashboardsRevokeShareLinkResponse, GcpAccountsUpdateGcpAccountData, GcpAccountsUpdateGcpAccountResponse, GcpAccountsCreateGcpAccountData, GcpAccountsCreateGcpAccountResponse, GoogleGoogleLoginResponse, GoogleGoogleCallbackResponse, KnowledgeBaseCreateKbData, KnowledgeBaseCreateKbResponse, KnowledgeBaseGetKbsData, KnowledgeBaseGetKbsResponse, KnowledgeBaseGetKbOverviewResponse, KnowledgeBaseGetKbByIdData, KnowledgeBaseGetKbByIdResponse, KnowledgeBaseUpdateKbData, KnowledgeBaseUpdateKbResponse, KnowledgeBaseDeleteKbData, KnowledgeBaseDeleteKbResponse, KnowledgeBaseGetKbDocumentOverviewData, KnowledgeBaseGetKbDocumentOverviewResponse, KnowledgeBaseGeneratePresignedUrlsData, KnowledgeBaseGeneratePresignedUrlsResponse, KnowledgeBaseIngestFromPresignedUrlsData, KnowledgeBaseIngestFromPresignedUrlsResponse, KnowledgeBaseIngestFromUrlsData, KnowledgeBaseIngestFromUrlsResponse, KnowledgeBaseListDocumentsData, KnowledgeBaseListDocumentsResponse, KnowledgeBaseGetDocumentContentData, KnowledgeBaseGetDocumentContentResponse, KnowledgeBaseDeleteDocumentData, KnowledgeBaseDeleteDocumentResponse, KnowledgeBaseGetTaskStatusData, KnowledgeBaseGetTaskStatusResponse, LoginLoginAccessTokenData, LoginLoginAccessTokenResponse, LoginTestTokenResponse, LoginRecoverPasswordData, LoginRecoverPasswordResponse, LoginResetPasswordData, LoginResetPasswordResponse, LoginRecoverPasswordHtmlContentData, LoginRecoverPasswordHtmlContentResponse, LoginVerifyMfaLoginData, LoginVerifyMfaLoginResponse, McpConnectionGetMcpConnectionsResponse, McpConnectionCreateMcpConnectionData, McpConnectionCreateMcpConnectionResponse, McpConnectionUpdateMcpConnectionData, McpConnectionUpdateMcpConnectionResponse, McpConnectionDeleteMcpConnectionData, McpConnectionDeleteMcpConnectionResponse, McpConnectionUpdateMcpConnectionToolsData, McpConnectionUpdateMcpConnectionToolsResponse, MessageFeedbackGetMessageFeedbackData, MessageFeedbackGetMessageFeedbackResponse, MessageFeedbackUpdateMessageFeedbackData, MessageFeedbackUpdateMessageFeedbackResponse, MessageFeedbackDeleteMessageFeedbackData, MessageFeedbackDeleteMessageFeedbackResponse, MessageFeedbackCreateMessageFeedbackData, MessageFeedbackCreateMessageFeedbackResponse, MfaGetMfaStatusResponse, MfaSetupMfaData, MfaSetupMfaResponse, MfaVerifyMfaSetupData, MfaVerifyMfaSetupResponse, MfaDisableMfaData, MfaDisableMfaResponse, MfaRegenerateBackupCodesData, MfaRegenerateBackupCodesResponse, ModuleSettingGetModuleSettingsResponse, NotificationsListNotificationsData, NotificationsListNotificationsResponse, NotificationsMarkNotificationReadData, NotificationsMarkNotificationReadResponse, NotificationsMarkAllNotificationsReadData, NotificationsMarkAllNotificationsReadResponse, OnboardingGetOnboardingStatusResponse, OnboardingCreateWorkspaceData, OnboardingCreateWorkspaceResponse, OnboardingSkipOnboardingResponse, OnboardingConnectAwsData, OnboardingConnectAwsResponse, OnboardingConnectGcpData, OnboardingConnectGcpResponse, OnboardingConnectAzureData, OnboardingConnectAzureResponse, OnboardingConnectSandboxResponse, OnboardingGetTaskTemplateResponse, OnboardingCompleteTaskTemplateData, OnboardingCompleteTaskTemplateResponse, PublicGetSharedReportData, PublicGetSharedReportResponse, PublicGetSharedDashboardData, PublicGetSharedDashboardResponse, QuotasGetCreditQuotaData, QuotasGetCreditQuotaResponse, QuotasGetDashboardOverviewData, QuotasGetDashboardOverviewResponse, QuotasGetUsageOverTimeData, QuotasGetUsageOverTimeResponse, RecommendationsGetRecomendationOveralResponse, RecommendationsReadRecommendationsData, RecommendationsReadRecommendationsResponse, RecommendationsCreateRecommendationData, RecommendationsCreateRecommendationResponse, RecommendationsReadRecommendationData, RecommendationsReadRecommendationResponse, RecommendationsUpdateRecommendationData, RecommendationsUpdateRecommendationResponse, RecommendationsDeleteRecommendationData, RecommendationsDeleteRecommendationResponse, RecommendationsUpdateRecommendationStatusData, RecommendationsUpdateRecommendationStatusResponse, ReportsGetReportByConversationData, ReportsGetReportByConversationResponse, ReportsCreateShareLinkData, ReportsCreateShareLinkResponse, ReportsRevokeShareLinkData, ReportsRevokeShareLinkResponse, ResourcesReadResourcesData, ResourcesReadResourcesResponse, ResourcesGetResourceStatisticsResponse, ResourcesReadResourceByIdData, ResourcesReadResourceByIdResponse, ResourcesDeleteResourceByIdData, ResourcesDeleteResourceByIdResponse, SampleDataCreateSampleResourcesData, SampleDataCreateSampleResourcesResponse, SampleDataClearSampleResourcesData, SampleDataClearSampleResourcesResponse, SampleDataPreviewSampleResourcesData, SampleDataPreviewSampleResourcesResponse, ShareChatCreateShareLinkData, ShareChatCreateShareLinkResponse, ShareChatRevokeShareLinkData, ShareChatRevokeShareLinkResponse, ShareChatGetShareLinkData, ShareChatGetShareLinkResponse, ShareChatGetSharedConversationData, ShareChatGetSharedConversationResponse, SubscriptionsGetAvailablePlansResponse, SubscriptionsGetUserSubscriptionStatusResponse, SubscriptionsGetWorkspaceSubscriptionStatusData, SubscriptionsGetWorkspaceSubscriptionStatusResponse, SubscriptionsCreateCheckoutSessionData, SubscriptionsCreateCheckoutSessionResponse, SubscriptionsGetUserPaymentMethodsData, SubscriptionsGetUserPaymentMethodsResponse, SubscriptionsGetUserInvoicesData, SubscriptionsGetUserInvoicesResponse, SubscriptionsSubmitEnterpriseEnquiryData, SubscriptionsSubmitEnterpriseEnquiryResponse, SubscriptionsSubmitPlanChangeRequestData, SubscriptionsSubmitPlanChangeRequestResponse, SubscriptionsWebhookResponse, SubscriptionsCancelSubscriptionResponse, SubscriptionsCreateBillingPortalSessionData, SubscriptionsCreateBillingPortalSessionResponse, SubscriptionsCreateWorkspaceBillingPortalSessionData, SubscriptionsCreateWorkspaceBillingPortalSessionResponse, TasksCreateTaskData, TasksCreateTaskResponse, TasksListTasksData, TasksListTasksResponse, TasksGetTaskData, TasksGetTaskResponse, TasksUpdateTaskData, TasksUpdateTaskResponse, TasksDeleteTaskData, TasksDeleteTaskResponse, TasksGetTaskAverageRunTimeData, TasksGetTaskAverageRunTimeResponse, TasksGetTaskLastRunTimeData, TasksGetTaskLastRunTimeResponse, TasksGetTaskHistoriesData, TasksGetTaskHistoriesResponse, TasksUpdateTaskEnableData, TasksUpdateTaskEnableResponse, TasksStopTaskExecutionData, TasksStopTaskExecutionResponse, TaskTemplatesGenerateData, TaskTemplatesGenerateResponse, TaskTemplatesCreateTemplateData, TaskTemplatesCreateTemplateResponse, TaskTemplatesListTemplatesData, TaskTemplatesListTemplatesResponse, TaskTemplatesGetTemplateData, TaskTemplatesGetTemplateResponse, TaskTemplatesUpdateTemplateData, TaskTemplatesUpdateTemplateResponse, TaskTemplatesDeleteTemplateData, TaskTemplatesDeleteTemplateResponse, UsersReadUsersData, UsersReadUsersResponse, UsersCreateUserData, UsersCreateUserResponse, UsersReadUserMeResponse, UsersDeleteUserMeResponse, UsersUpdateUserMeData, UsersUpdateUserMeResponse, UsersUpdatePasswordMeData, UsersUpdatePasswordMeResponse, UsersReadUserByIdData, UsersReadUserByIdResponse, UsersUpdateUserData, UsersUpdateUserResponse, UsersDeleteUserData, UsersDeleteUserResponse, UsersSwitchWorkspaceData, UsersSwitchWorkspaceResponse, UtilsHealthCheckResponse, UtilsGetCloudRegionsData, UtilsGetCloudRegionsResponse, UtilsGetQuickStartTemplatesResponse, UtilsGetExamplePromptsData, UtilsGetExamplePromptsResponse, WorkspacesGetWorkspacesData, WorkspacesGetWorkspacesResponse, WorkspacesCreateWorkspaceData, WorkspacesCreateWorkspaceResponse, WorkspacesGetWorkspaceDetailsData, WorkspacesGetWorkspaceDetailsResponse, WorkspacesUpdateWorkspaceData, WorkspacesUpdateWorkspaceResponse, WorkspacesDeleteWorkspaceData, WorkspacesDeleteWorkspaceResponse } from './types.gen';

export class AgentsService {
    /**
     * Read Agents
     * Retrieve Agents for the current workspace.
     * @returns AgentsPublic Successful Response
     * @throws ApiError
     */
    public static readAgents(): CancelablePromise<AgentsReadAgentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents/'
        });
    }
    
    /**
     * Update Agent Instructions
     * Update the instructions of an agent.
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static updateAgentInstructions(data: AgentsUpdateAgentInstructionsData): CancelablePromise<AgentsUpdateAgentInstructionsResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/agents/{agent_id}/instructions',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Agent Status
     * Update the status of an agent.
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentPublic Successful Response
     * @throws ApiError
     */
    public static updateAgentStatus(data: AgentsUpdateAgentStatusData): CancelablePromise<AgentsUpdateAgentStatusResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/agents/{agent_id}/status',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AgentsBuiltinToolsService {
    /**
     * Get Workspace Agent Builtin Tools
     * Get built-in tools for all agents in the current workspace
     * @returns AgentsBuiltInToolsResponse Successful Response
     * @throws ApiError
     */
    public static getWorkspaceAgentBuiltinTools(): CancelablePromise<AgentsBuiltinToolsGetWorkspaceAgentBuiltinToolsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents-builtin-tools/'
        });
    }
    
    /**
     * Get Agent Builtin Tools Detail
     * Get built-in tools for a specific agent in the current workspace
     * @param data The data for the request.
     * @param data.agentId
     * @returns AgentBuiltInToolsPublic Successful Response
     * @throws ApiError
     */
    public static getAgentBuiltinToolsDetail(data: AgentsBuiltinToolsGetAgentBuiltinToolsDetailData): CancelablePromise<AgentsBuiltinToolsGetAgentBuiltinToolsDetailResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents-builtin-tools/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Bulk Update Agent Builtin Tools
     * Bulk update builtin tools for an agent
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns AgentBuiltInToolsBulkUpdateResponse Successful Response
     * @throws ApiError
     */
    public static bulkUpdateAgentBuiltinTools(data: AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsData): CancelablePromise<AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/agents-builtin-tools/{agent_id}',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AgentsConnectionsService {
    /**
     * Get Workspace Agent Connections
     * Get connections for all agents in the current workspace
     * @returns AgentsConnectionsResponse Successful Response
     * @throws ApiError
     */
    public static getWorkspaceAgentConnections(): CancelablePromise<AgentsConnectionsGetWorkspaceAgentConnectionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/agents-connections/'
        });
    }
    
    /**
     * Create Agent Connection
     * Create a connection for an agent
     * @param data The data for the request.
     * @param data.agentId
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createAgentConnection(data: AgentsConnectionsCreateAgentConnectionData): CancelablePromise<AgentsConnectionsCreateAgentConnectionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/agents-connections/{agent_id}/connections',
            path: {
                agent_id: data.agentId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Agent Connection
     * Delete a connection from an agent
     * @param data The data for the request.
     * @param data.agentId
     * @param data.connectionId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteAgentConnection(data: AgentsConnectionsDeleteAgentConnectionData): CancelablePromise<AgentsConnectionsDeleteAgentConnectionResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/agents-connections/{agent_id}/connections/{connection_id}',
            path: {
                agent_id: data.agentId,
                connection_id: data.connectionId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AlertsService {
    /**
     * Get Alert Status Summary
     * Get a summary of alerts by status for the last 30 days.
     * @returns AlertStatusSummary Successful Response
     * @throws ApiError
     */
    public static getAlertStatusSummary(): CancelablePromise<AlertsGetAlertStatusSummaryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/summary/status'
        });
    }
    
    /**
     * Create Alert
     * Create new alert.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static createAlert(data: AlertsCreateAlertData): CancelablePromise<AlertsCreateAlertResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/alerts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * List Alerts
     * List alerts with optional filters and search.
     * @param data The data for the request.
     * @param data.severity
     * @param data.status
     * @param data.search Search alerts by title or description
     * @param data.sortBy Field to sort by
     * @param data.sortDesc Sort in descending order
     * @param data.skip
     * @param data.limit
     * @returns AlertList Successful Response
     * @throws ApiError
     */
    public static listAlerts(data: AlertsListAlertsData = {}): CancelablePromise<AlertsListAlertsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/',
            query: {
                severity: data.severity,
                status: data.status,
                search: data.search,
                sort_by: data.sortBy,
                sort_desc: data.sortDesc,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Alert
     * Get alert by ID.
     * @param data The data for the request.
     * @param data.alertId
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static getAlert(data: AlertsGetAlertData): CancelablePromise<AlertsGetAlertResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Alert
     * Update alert.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlert(data: AlertsUpdateAlertData): CancelablePromise<AlertsUpdateAlertResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Alert
     * Delete alert.
     * @param data The data for the request.
     * @param data.alertId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteAlert(data: AlertsDeleteAlertData): CancelablePromise<AlertsDeleteAlertResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/alerts/{alert_id}',
            path: {
                alert_id: data.alertId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Alert Status
     * Update alert status.
     * @param data The data for the request.
     * @param data.alertId
     * @param data.requestBody
     * @returns AlertResponse Successful Response
     * @throws ApiError
     */
    public static updateAlertStatus(data: AlertsUpdateAlertStatusData): CancelablePromise<AlertsUpdateAlertStatusResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/alerts/{alert_id}/status',
            path: {
                alert_id: data.alertId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Mark All Alerts Acknowledged
     * Mark all alerts as acknowledged for the current workspace.
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static markAllAlertsAcknowledged(): CancelablePromise<AlertsMarkAllAlertsAcknowledgedResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/alerts/mark-all-acknowledged'
        });
    }
    
}

export class AttachmentsService {
    /**
     * Generate Attachment Presigned Urls
     * Generate presigned URLs for file uploads.
     * Clients can use these URLs to upload files directly to the object storage.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AttachmentPresignedUrlResponse Successful Response
     * @throws ApiError
     */
    public static generateAttachmentPresignedUrls(data: AttachmentsGenerateAttachmentPresignedUrlsData): CancelablePromise<AttachmentsGenerateAttachmentPresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/attachments/attachments/presigned-urls',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Confirm Attachment Uploads
     * Confirm that files have been uploaded and trigger the validation task.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns app__schemas__message_attachment__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static confirmAttachmentUploads(data: AttachmentsConfirmAttachmentUploadsData): CancelablePromise<AttachmentsConfirmAttachmentUploadsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/attachments/attachments/confirm-uploads',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Attachment Task Status
     * Get the status of an asynchronous attachment validation task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns app__schemas__message_attachment__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentTaskStatus(data: AttachmentsGetAttachmentTaskStatusData): CancelablePromise<AttachmentsGetAttachmentTaskStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Attachment Download Url
     * Generate a presigned GET URL to download an attachment.
     * @param data The data for the request.
     * @param data.attachmentId
     * @returns AttachmentDownloadResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentDownloadUrl(data: AttachmentsGetAttachmentDownloadUrlData): CancelablePromise<AttachmentsGetAttachmentDownloadUrlResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/{attachment_id}/download-url',
            path: {
                attachment_id: data.attachmentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Attachment Metadata
     * Get attachment metadata by attachment ID.
     * @param data The data for the request.
     * @param data.attachmentId
     * @returns AttachmentMetadataResponse Successful Response
     * @throws ApiError
     */
    public static getAttachmentMetadata(data: AttachmentsGetAttachmentMetadataData): CancelablePromise<AttachmentsGetAttachmentMetadataResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/attachments/attachments/{attachment_id}/metadata',
            path: {
                attachment_id: data.attachmentId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AuthService {
    /**
     * Register
     * Register new user and send activation email.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static register(data: AuthRegisterData): CancelablePromise<AuthRegisterResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/signup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Activate Account
     * Activate a user account using the activation token and return authentication token.
     * @param data The data for the request.
     * @param data.token
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static activateAccount(data: AuthActivateAccountData): CancelablePromise<AuthActivateAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/activate/{token}',
            path: {
                token: data.token
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Resend Activation
     * Resend activation email for unactivated accounts with reCAPTCHA v3 validation.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns ActivationResponse Successful Response
     * @throws ApiError
     */
    public static resendActivation(data: AuthResendActivationData): CancelablePromise<AuthResendActivationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/auth/resend-activation',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AutonomousAgentsService {
    /**
     * Rename Conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @param data.requestBody
     * @returns ConversationPublic Successful Response
     * @throws ApiError
     */
    public static renameConversation(data: AutonomousAgentsRenameConversationData): CancelablePromise<AutonomousAgentsRenameConversationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}/name',
            path: {
                conversation_id: data.conversationId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Conversation
     * Delete a conversation and its associated LangGraph thread data.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns string Successful Response
     * @throws ApiError
     */
    public static deleteConversation(data: AutonomousAgentsDeleteConversationData): CancelablePromise<AutonomousAgentsDeleteConversationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/autonomous-agents/conversations/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Conversations
     * Get list of conversations with filtering and pagination.
     * @param data The data for the request.
     * @param data.agentId
     * @param data.resourceId
     * @param data.search
     * @param data.skip Number of records to skip for pagination
     * @param data.limit Maximum number of records to return
     * @returns ConversationsPublic Successful Response
     * @throws ApiError
     */
    public static getConversations(data: AutonomousAgentsGetConversationsData = {}): CancelablePromise<AutonomousAgentsGetConversationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/conversations',
            query: {
                agent_id: data.agentId,
                resource_id: data.resourceId,
                search: data.search,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Messages History
     * @param data The data for the request.
     * @param data.conversationId
     * @returns MessagePublicList Successful Response
     * @throws ApiError
     */
    public static getMessagesHistory(data: AutonomousAgentsGetMessagesHistoryData): CancelablePromise<AutonomousAgentsGetMessagesHistoryResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/messages/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Message Plans
     * @param data The data for the request.
     * @param data.conversationId
     * @returns MessagePlanList Successful Response
     * @throws ApiError
     */
    public static getMessagePlans(data: AutonomousAgentsGetMessagePlansData): CancelablePromise<AutonomousAgentsGetMessagePlansResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/messages/{conversation_id}/plans',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Chat Stream
     * @param data The data for the request.
     * @param data.requestBody
     * @param data.conversationId
     * @returns StreamResponse Successful Response
     * @throws ApiError
     */
    public static chatStream(data: AutonomousAgentsChatStreamData): CancelablePromise<AutonomousAgentsChatStreamResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/chat/stream',
            query: {
                conversation_id: data.conversationId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Reconnect To Stream
     * Reconnects to an existing stream, sending complete current message + continuing live stream.
     * Used when users return to a conversation page with an active stream.
     * No position tracking needed - automatically sends complete current AI response.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static reconnectToStream(data: AutonomousAgentsReconnectToStreamData): CancelablePromise<AutonomousAgentsReconnectToStreamResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/chat/{conversation_id}/reconnect-stream',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Stream Status
     * Get detailed status of stream and background task for debugging.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getStreamStatus(data: AutonomousAgentsGetStreamStatusData): CancelablePromise<AutonomousAgentsGetStreamStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/autonomous-agents/chat/{conversation_id}/stream-status',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Cancel Stream
     * Cancel active background stream for a conversation.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static cancelStream(data: AutonomousAgentsCancelStreamData): CancelablePromise<AutonomousAgentsCancelStreamResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/autonomous-agents/chat/{conversation_id}/cancel-stream',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class AwsAccountsService {
    /**
     * Update Aws Account
     * Update an AWS account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static updateAwsAccount(data: AwsAccountsUpdateAwsAccountData): CancelablePromise<AwsAccountsUpdateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/aws-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Aws Account
     * Create new AWS account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns AWSAccountPublic Successful Response
     * @throws ApiError
     */
    public static createAwsAccount(data: AwsAccountsCreateAwsAccountData): CancelablePromise<AwsAccountsCreateAwsAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/aws-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class BuiltinConnectionService {
    /**
     * Get Builtin Connections
     * Get all available builtin connections.
     * @returns BuiltinConnectionsPublic Successful Response
     * @throws ApiError
     */
    public static getBuiltinConnections(): CancelablePromise<BuiltinConnectionGetBuiltinConnectionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/builtin-connection'
        });
    }
    
    /**
     * Install Builtin Connection
     * Install a builtin connection to the user's workspace.
     * @param data The data for the request.
     * @param data.builtinId
     * @param data.requestBody
     * @returns BuiltinConnectionPublic Successful Response
     * @throws ApiError
     */
    public static installBuiltinConnection(data: BuiltinConnectionInstallBuiltinConnectionData): CancelablePromise<BuiltinConnectionInstallBuiltinConnectionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/builtin-connection/{builtin_id}',
            path: {
                builtin_id: data.builtinId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Builtin Connection
     * Update a builtin connection to the user's workspace.
     * @param data The data for the request.
     * @param data.builtinId
     * @param data.requestBody
     * @returns BuiltinConnectionPublic Successful Response
     * @throws ApiError
     */
    public static updateBuiltinConnection(data: BuiltinConnectionUpdateBuiltinConnectionData): CancelablePromise<BuiltinConnectionUpdateBuiltinConnectionResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/builtin-connection/{builtin_id}',
            path: {
                builtin_id: data.builtinId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Builtin Connection
     * Delete a builtin connection from the user's workspace.
     * @param data The data for the request.
     * @param data.builtinId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteBuiltinConnection(data: BuiltinConnectionDeleteBuiltinConnectionData): CancelablePromise<BuiltinConnectionDeleteBuiltinConnectionResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/builtin-connection/{builtin_id}',
            path: {
                builtin_id: data.builtinId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Builtin Connection Tools
     * Update the tools for a builtin connection.
     * @param data The data for the request.
     * @param data.builtinId
     * @param data.requestBody
     * @returns BuiltinConnectionPublic Successful Response
     * @throws ApiError
     */
    public static updateBuiltinConnectionTools(data: BuiltinConnectionUpdateBuiltinConnectionToolsData): CancelablePromise<BuiltinConnectionUpdateBuiltinConnectionToolsResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/builtin-connection/{builtin_id}/tool',
            path: {
                builtin_id: data.builtinId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Install Sandbox Connection
     * Install a sandbox connection to the user's workspace.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns BuiltinConnectionsPublic Successful Response
     * @throws ApiError
     */
    public static installSandboxConnection(data: BuiltinConnectionInstallSandboxConnectionData): CancelablePromise<BuiltinConnectionInstallSandboxConnectionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/builtin-connection/sandbox/install',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class BuiltinToolsService {
    /**
     * Get Workspace Builtin Tools
     * List all built-in tools for a workspace
     * @returns WorkspaceBuiltInToolResponse Successful Response
     * @throws ApiError
     */
    public static getWorkspaceBuiltinTools(): CancelablePromise<BuiltinToolsGetWorkspaceBuiltinToolsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/builtin-tools/'
        });
    }
    
    /**
     * Update Workspace Builtin Tool
     * Update the permission requirement of a built-in tool for a workspace
     * @param data The data for the request.
     * @param data.workspaceBuiltinToolId
     * @param data.requestBody
     * @returns boolean Successful Response
     * @throws ApiError
     */
    public static updateWorkspaceBuiltinTool(data: BuiltinToolsUpdateWorkspaceBuiltinToolData): CancelablePromise<BuiltinToolsUpdateWorkspaceBuiltinToolResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/builtin-tools/{workspace_builtin_tool_id}/',
            path: {
                workspace_builtin_tool_id: data.workspaceBuiltinToolId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class CloudSyncConfigService {
    /**
     * Get Resource Types
     * Get available resource types for a specific cloud provider.
     * @param data The data for the request.
     * @param data.cloudProvider Cloud provider (AWS, GCP, AZURE)
     * @returns ResourceTypesResponse Successful Response
     * @throws ApiError
     */
    public static getResourceTypes(data: CloudSyncConfigGetResourceTypesData): CancelablePromise<CloudSyncConfigGetResourceTypesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/cloud-sync-config/resource-types',
            query: {
                cloud_provider: data.cloudProvider
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Workspace Configs
     * Get all cloud sync configurations for the current workspace.
     * @returns CloudSyncConfigPublic Successful Response
     * @throws ApiError
     */
    public static getWorkspaceConfigs(): CancelablePromise<CloudSyncConfigGetWorkspaceConfigsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/cloud-sync-config/'
        });
    }
    
    /**
     * Create Or Update Config
     * Create or update a cloud sync configuration.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns CloudSyncConfigPublic Successful Response
     * @throws ApiError
     */
    public static createOrUpdateConfig(data: CloudSyncConfigCreateOrUpdateConfigData): CancelablePromise<CloudSyncConfigCreateOrUpdateConfigResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/cloud-sync-config/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Config
     * Update a specific cloud sync configuration.
     * @param data The data for the request.
     * @param data.configId
     * @param data.requestBody
     * @returns CloudSyncConfigPublic Successful Response
     * @throws ApiError
     */
    public static updateConfig(data: CloudSyncConfigUpdateConfigData): CancelablePromise<CloudSyncConfigUpdateConfigResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/cloud-sync-config/{config_id}',
            path: {
                config_id: data.configId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Config
     * Delete a cloud sync configuration.
     * @param data The data for the request.
     * @param data.configId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteConfig(data: CloudSyncConfigDeleteConfigData): CancelablePromise<CloudSyncConfigDeleteConfigResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/cloud-sync-config/{config_id}',
            path: {
                config_id: data.configId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Trigger Manual Sync
     * Trigger a manual sync for a specific configuration.
     * @param data The data for the request.
     * @param data.configId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static triggerManualSync(data: CloudSyncConfigTriggerManualSyncData): CancelablePromise<CloudSyncConfigTriggerManualSyncResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/cloud-sync-config/{config_id}/sync',
            path: {
                config_id: data.configId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ConsoleProxyService {
    /**
     * Get Files
     * Get files for the current user's workspace by proxying to executor
     * @returns FileListResponse Successful Response
     * @throws ApiError
     */
    public static getFiles(): CancelablePromise<ConsoleProxyGetFilesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/console/files'
        });
    }
    
    /**
     * Get File Content
     * Get file content from executor for display in the file explorer
     * @param data The data for the request.
     * @param data.path File path
     * @returns FileContentResponse Successful Response
     * @throws ApiError
     */
    public static getFileContent(data: ConsoleProxyGetFileContentData): CancelablePromise<ConsoleProxyGetFileContentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/console/files/content',
            query: {
                path: data.path
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class DashboardAnalyticsService {
    /**
     * Get comprehensive dashboard analytics
     * Get complete dashboard analytics including cost overview, high-consuming services, and top recommendations
     * @param data The data for the request.
     * @param data.cloudProvider Filter by specific cloud provider
     * @param data.serviceCategory Filter by service category
     * @param data.minSavingsAmount Minimum monthly savings threshold
     * @param data.topServicesLimit Number of top services to return
     * @returns DashboardAnalyticsResponse Successful Response
     * @throws ApiError
     */
    public static getDashboardAnalytics(data: DashboardAnalyticsGetDashboardAnalyticsData = {}): CancelablePromise<DashboardAnalyticsGetDashboardAnalyticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/dashboard-analytics/analytics',
            query: {
                cloud_provider: data.cloudProvider,
                service_category: data.serviceCategory,
                min_savings_amount: data.minSavingsAmount,
                top_services_limit: data.topServicesLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get high-consuming services
     * Get services with highest potential monthly savings opportunities
     * @param data The data for the request.
     * @param data.cloudProvider Filter by specific cloud provider
     * @param data.serviceCategory Filter by service category
     * @param data.minSavingsAmount Minimum monthly savings threshold
     * @param data.topServicesLimit Number of top services to return
     * @returns HighConsumingServicesResponse Successful Response
     * @throws ApiError
     */
    public static getHighConsumingServices(data: DashboardAnalyticsGetHighConsumingServicesData = {}): CancelablePromise<DashboardAnalyticsGetHighConsumingServicesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/dashboard-analytics/high-consuming-services',
            query: {
                cloud_provider: data.cloudProvider,
                service_category: data.serviceCategory,
                min_savings_amount: data.minSavingsAmount,
                top_services_limit: data.topServicesLimit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get top 5 recommendations
     * Get top 5 potential monthly savings recommendations with effort and risk assessment
     * @param data The data for the request.
     * @param data.cloudProvider Filter by specific cloud provider
     * @param data.serviceCategory Filter by service category
     * @param data.minSavingsAmount Minimum monthly savings threshold
     * @returns TopRecommendationsResponse Successful Response
     * @throws ApiError
     */
    public static getTopRecommendations(data: DashboardAnalyticsGetTopRecommendationsData = {}): CancelablePromise<DashboardAnalyticsGetTopRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/dashboard-analytics/top-recommendations',
            query: {
                cloud_provider: data.cloudProvider,
                service_category: data.serviceCategory,
                min_savings_amount: data.minSavingsAmount
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class DashboardsService {
    /**
     * Get Dashboard By Conversation
     * Get dashboard by conversation ID for the current user's workspace.
     * @param data The data for the request.
     * @param data.conversationId
     * @returns Dashboard Successful Response
     * @throws ApiError
     */
    public static getDashboardByConversation(data: DashboardsGetDashboardByConversationData): CancelablePromise<DashboardsGetDashboardByConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/dashboards/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Share Link
     * @param data The data for the request.
     * @param data.dashboardId
     * @returns Dashboard Successful Response
     * @throws ApiError
     */
    public static createShareLink(data: DashboardsCreateShareLinkData): CancelablePromise<DashboardsCreateShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/dashboards/{dashboard_id}/share',
            path: {
                dashboard_id: data.dashboardId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Revoke Share Link
     * @param data The data for the request.
     * @param data.dashboardId
     * @returns Dashboard Successful Response
     * @throws ApiError
     */
    public static revokeShareLink(data: DashboardsRevokeShareLinkData): CancelablePromise<DashboardsRevokeShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/dashboards/{dashboard_id}/share',
            path: {
                dashboard_id: data.dashboardId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class GcpAccountsService {
    /**
     * Update Gcp Account
     * Update a GCP account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns GCPAccountPublic Successful Response
     * @throws ApiError
     */
    public static updateGcpAccount(data: GcpAccountsUpdateGcpAccountData): CancelablePromise<GcpAccountsUpdateGcpAccountResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/gcp-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Gcp Account
     * Create new GCP account.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns GCPAccountPublic Successful Response
     * @throws ApiError
     */
    public static createGcpAccount(data: GcpAccountsCreateGcpAccountData): CancelablePromise<GcpAccountsCreateGcpAccountResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/gcp-accounts/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class GoogleService {
    /**
     * Google Login
     * Initiate Google OAuth login flow
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static googleLogin(): CancelablePromise<GoogleGoogleLoginResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/login'
        });
    }
    
    /**
     * Google Callback
     * Handle Google OAuth callback and login/create user
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static googleCallback(): CancelablePromise<GoogleGoogleCallbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/google/callback'
        });
    }
    
}

export class KnowledgeBaseService {
    /**
     * Create Kb
     * @param data The data for the request.
     * @param data.requestBody
     * @returns KBPublic Successful Response
     * @throws ApiError
     */
    public static createKb(data: KnowledgeBaseCreateKbData): CancelablePromise<KnowledgeBaseCreateKbResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Kbs
     * Get all knowledge bases for the current user
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.accessLevel
     * @param data.usageMode
     * @returns KBsPublic Successful Response
     * @throws ApiError
     */
    public static getKbs(data: KnowledgeBaseGetKbsData = {}): CancelablePromise<KnowledgeBaseGetKbsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs',
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                access_level: data.accessLevel,
                usage_mode: data.usageMode
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Kb Overview
     * Get overview statistics for knowledge bases in the current workspace
     * @returns KBOverviewResponse Successful Response
     * @throws ApiError
     */
    public static getKbOverview(): CancelablePromise<KnowledgeBaseGetKbOverviewResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/overview'
        });
    }
    
    /**
     * Get Kb By Id
     * Get a specific knowledge base by ID
     * @param data The data for the request.
     * @param data.kbId
     * @returns KBPublic Successful Response
     * @throws ApiError
     */
    public static getKbById(data: KnowledgeBaseGetKbByIdData): CancelablePromise<KnowledgeBaseGetKbByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Kb
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns KBPublic Successful Response
     * @throws ApiError
     */
    public static updateKb(data: KnowledgeBaseUpdateKbData): CancelablePromise<KnowledgeBaseUpdateKbResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Kb
     * @param data The data for the request.
     * @param data.kbId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteKb(data: KnowledgeBaseDeleteKbData): CancelablePromise<KnowledgeBaseDeleteKbResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Kb Document Overview
     * Get overview statistics for documents in a specific knowledge base
     * @param data The data for the request.
     * @param data.kbId
     * @returns DocumentOverviewResponse Successful Response
     * @throws ApiError
     */
    public static getKbDocumentOverview(data: KnowledgeBaseGetKbDocumentOverviewData): CancelablePromise<KnowledgeBaseGetKbDocumentOverviewResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/overview',
            path: {
                kb_id: data.kbId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Generate Presigned Urls
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns PresignedUrlResponse Successful Response
     * @throws ApiError
     */
    public static generatePresignedUrls(data: KnowledgeBaseGeneratePresignedUrlsData): CancelablePromise<KnowledgeBaseGeneratePresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Ingest From Presigned Urls
     * Confirm file uploads and start ingestion process.
     *
     * This endpoint should be called after files have been successfully uploaded
     * using the presigned URLs to start the document ingestion process.
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static ingestFromPresignedUrls(data: KnowledgeBaseIngestFromPresignedUrlsData): CancelablePromise<KnowledgeBaseIngestFromPresignedUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/ingest-from-presigned-urls',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Ingest From Urls
     * @param data The data for the request.
     * @param data.kbId
     * @param data.requestBody
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static ingestFromUrls(data: KnowledgeBaseIngestFromUrlsData): CancelablePromise<KnowledgeBaseIngestFromUrlsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/ingest-from-urls',
            path: {
                kb_id: data.kbId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * List Documents
     * List documents in a knowledge base.
     *
     * User must have access to the knowledge base (owner for personal knowledge bases,
     * workspace member for workspace knowledge bases).
     * @param data The data for the request.
     * @param data.kbId
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.type
     * @returns DocumentsKBRead Successful Response
     * @throws ApiError
     */
    public static listDocuments(data: KnowledgeBaseListDocumentsData): CancelablePromise<KnowledgeBaseListDocumentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents',
            path: {
                kb_id: data.kbId
            },
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                type: data.type
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Document Content
     * @param data The data for the request.
     * @param data.kbId
     * @param data.objectName
     * @returns string Successful Response
     * @throws ApiError
     */
    public static getDocumentContent(data: KnowledgeBaseGetDocumentContentData): CancelablePromise<KnowledgeBaseGetDocumentContentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/content',
            path: {
                kb_id: data.kbId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Document
     * @param data The data for the request.
     * @param data.kbId
     * @param data.documentId
     * @param data.objectName Storage object name of the document
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteDocument(data: KnowledgeBaseDeleteDocumentData): CancelablePromise<KnowledgeBaseDeleteDocumentResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}',
            path: {
                kb_id: data.kbId,
                document_id: data.documentId
            },
            query: {
                object_name: data.objectName
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Task Status
     * Get the status of an asynchronous task.
     *
     * This endpoint returns the current status and progress of a Celery task,
     * such as document ingestion.
     * @param data The data for the request.
     * @param data.taskId
     * @returns app__schemas__kb__TaskStatusResponse Successful Response
     * @throws ApiError
     */
    public static getTaskStatus(data: KnowledgeBaseGetTaskStatusData): CancelablePromise<KnowledgeBaseGetTaskStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/knowledge_base/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class LoginService {
    /**
     * Login Access Token
     * OAuth2 compatible token login, get an access token for future requests
     * @param data The data for the request.
     * @param data.formData
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static loginAccessToken(data: LoginLoginAccessTokenData): CancelablePromise<LoginLoginAccessTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/access-token',
            formData: data.formData,
            mediaType: 'application/x-www-form-urlencoded',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Test Token
     * Test access token
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static testToken(): CancelablePromise<LoginTestTokenResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/test-token'
        });
    }
    
    /**
     * Recover Password
     * Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static recoverPassword(data: LoginRecoverPasswordData): CancelablePromise<LoginRecoverPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Reset Password
     * Reset password
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static resetPassword(data: LoginResetPasswordData): CancelablePromise<LoginResetPasswordResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/reset-password/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Recover Password Html Content
     * HTML Content for Password Recovery
     * @param data The data for the request.
     * @param data.email
     * @returns string Successful Response
     * @throws ApiError
     */
    public static recoverPasswordHtmlContent(data: LoginRecoverPasswordHtmlContentData): CancelablePromise<LoginRecoverPasswordHtmlContentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/password-recovery-html-content/{email}',
            path: {
                email: data.email
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Verify Mfa Login
     * Complete login with MFA verification
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static verifyMfaLogin(data: LoginVerifyMfaLoginData): CancelablePromise<LoginVerifyMfaLoginResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/login/mfa-verify',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class McpConnectionService {
    /**
     * Get Mcp Connections
     * @returns MCPConnectionsPublic Successful Response
     * @throws ApiError
     */
    public static getMcpConnections(): CancelablePromise<McpConnectionGetMcpConnectionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mcp-connection'
        });
    }
    
    /**
     * Create Mcp Connection
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MCPConnectionPublic Successful Response
     * @throws ApiError
     */
    public static createMcpConnection(data: McpConnectionCreateMcpConnectionData): CancelablePromise<McpConnectionCreateMcpConnectionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mcp-connection',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Mcp Connection
     * @param data The data for the request.
     * @param data.mcpConnId
     * @param data.requestBody
     * @returns MCPConnectionPublic Successful Response
     * @throws ApiError
     */
    public static updateMcpConnection(data: McpConnectionUpdateMcpConnectionData): CancelablePromise<McpConnectionUpdateMcpConnectionResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/mcp-connection/{mcp_conn_id}',
            path: {
                mcp_conn_id: data.mcpConnId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Mcp Connection
     * @param data The data for the request.
     * @param data.mcpConnId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMcpConnection(data: McpConnectionDeleteMcpConnectionData): CancelablePromise<McpConnectionDeleteMcpConnectionResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/mcp-connection/{mcp_conn_id}',
            path: {
                mcp_conn_id: data.mcpConnId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Mcp Connection Tools
     * Update the tools for a mcp connection.
     * @param data The data for the request.
     * @param data.mcpConnId
     * @param data.requestBody
     * @returns MCPConnectionPublic Successful Response
     * @throws ApiError
     */
    public static updateMcpConnectionTools(data: McpConnectionUpdateMcpConnectionToolsData): CancelablePromise<McpConnectionUpdateMcpConnectionToolsResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/mcp-connection/{mcp_conn_id}/tool',
            path: {
                mcp_conn_id: data.mcpConnId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class MessageFeedbackService {
    /**
     * Get Message Feedback
     * Get feedback for a specific message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static getMessageFeedback(data: MessageFeedbackGetMessageFeedbackData): CancelablePromise<MessageFeedbackGetMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Message Feedback
     * Update feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static updateMessageFeedback(data: MessageFeedbackUpdateMessageFeedbackData): CancelablePromise<MessageFeedbackUpdateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Message Feedback
     * Delete feedback for a message.
     * @param data The data for the request.
     * @param data.messageId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteMessageFeedback(data: MessageFeedbackDeleteMessageFeedbackData): CancelablePromise<MessageFeedbackDeleteMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/message-feedback/message/{message_id}',
            path: {
                message_id: data.messageId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Message Feedback
     * Create feedback for a message.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MessageFeedbackPublic Successful Response
     * @throws ApiError
     */
    public static createMessageFeedback(data: MessageFeedbackCreateMessageFeedbackData): CancelablePromise<MessageFeedbackCreateMessageFeedbackResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/message-feedback/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class MfaService {
    /**
     * Get Mfa Status
     * Get current MFA status for authenticated user
     * @returns MFAStatusResponse Successful Response
     * @throws ApiError
     */
    public static getMfaStatus(): CancelablePromise<MfaGetMfaStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/mfa/status'
        });
    }
    
    /**
     * Setup Mfa
     * Setup MFA for authenticated user
     * @param data The data for the request.
     * @param data.requestBody
     * @returns MFASetupResponse Successful Response
     * @throws ApiError
     */
    public static setupMfa(data: MfaSetupMfaData): CancelablePromise<MfaSetupMfaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mfa/setup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Verify Mfa Setup
     * Verify and enable MFA after setup
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static verifyMfaSetup(data: MfaVerifyMfaSetupData): CancelablePromise<MfaVerifyMfaSetupResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mfa/verify-setup',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Disable Mfa
     * Disable MFA for authenticated user
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static disableMfa(data: MfaDisableMfaData): CancelablePromise<MfaDisableMfaResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mfa/disable',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Regenerate Backup Codes
     * Regenerate backup codes for MFA-enabled user
     * @param data The data for the request.
     * @param data.requestBody
     * @returns string Successful Response
     * @throws ApiError
     */
    public static regenerateBackupCodes(data: MfaRegenerateBackupCodesData): CancelablePromise<MfaRegenerateBackupCodesResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/mfa/regenerate-backup-codes',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ModuleSettingService {
    /**
     * Get Module Settings
     * Retrieve all module settings.
     * @returns ModuleSetting Successful Response
     * @throws ApiError
     */
    public static getModuleSettings(): CancelablePromise<ModuleSettingGetModuleSettingsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/module_setting/'
        });
    }
    
}

export class NotificationsService {
    /**
     * List Notifications
     * @param data The data for the request.
     * @param data.requiresAction
     * @param data.timeframe
     * @param data.skip
     * @param data.limit
     * @param data.requestBody
     * @returns NotificationList Successful Response
     * @throws ApiError
     */
    public static listNotifications(data: NotificationsListNotificationsData = {}): CancelablePromise<NotificationsListNotificationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/notifications/',
            query: {
                requires_action: data.requiresAction,
                timeframe: data.timeframe,
                skip: data.skip,
                limit: data.limit
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Mark Notification Read
     * @param data The data for the request.
     * @param data.notificationId
     * @returns NotificationResponse Successful Response
     * @throws ApiError
     */
    public static markNotificationRead(data: NotificationsMarkNotificationReadData): CancelablePromise<NotificationsMarkNotificationReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/{notification_id}/mark-read',
            path: {
                notification_id: data.notificationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Mark All Notifications Read
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static markAllNotificationsRead(data: NotificationsMarkAllNotificationsReadData = {}): CancelablePromise<NotificationsMarkAllNotificationsReadResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/notifications/mark-all-read',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class OnboardingService {
    /**
     * Get Onboarding Status
     * @returns OnboardingStatus Successful Response
     * @throws ApiError
     */
    public static getOnboardingStatus(): CancelablePromise<OnboardingGetOnboardingStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/onboarding/status'
        });
    }
    
    /**
     * Create Workspace
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static createWorkspace(data: OnboardingCreateWorkspaceData): CancelablePromise<OnboardingCreateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/workspace',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Skip Onboarding
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static skipOnboarding(): CancelablePromise<OnboardingSkipOnboardingResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/skip'
        });
    }
    
    /**
     * Connect Aws
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static connectAws(data: OnboardingConnectAwsData): CancelablePromise<OnboardingConnectAwsResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/connect-aws',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Connect Gcp
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static connectGcp(data: OnboardingConnectGcpData): CancelablePromise<OnboardingConnectGcpResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/connect-gcp',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Connect Azure
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static connectAzure(data: OnboardingConnectAzureData): CancelablePromise<OnboardingConnectAzureResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/connect-azure',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Connect Sandbox
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static connectSandbox(): CancelablePromise<OnboardingConnectSandboxResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/connect-sandbox'
        });
    }
    
    /**
     * Get Task Template
     * @returns app__schemas__onboarding__TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static getTaskTemplate(): CancelablePromise<OnboardingGetTaskTemplateResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/onboarding/task-template'
        });
    }
    
    /**
     * Complete Task Template
     * Complete the task template step by either selecting a template or skipping.
     * This marks step 3 of onboarding as completed.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static completeTaskTemplate(data: OnboardingCompleteTaskTemplateData): CancelablePromise<OnboardingCompleteTaskTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/onboarding/complete-task-template',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class PublicService {
    /**
     * Get Shared Report
     * Get a publicly shared report by share_id. No authentication required.
     * @param data The data for the request.
     * @param data.shareId
     * @returns ReportShare Successful Response
     * @throws ApiError
     */
    public static getSharedReport(data: PublicGetSharedReportData): CancelablePromise<PublicGetSharedReportResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/public/reports/{share_id}',
            path: {
                share_id: data.shareId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Shared Dashboard
     * Get a publicly shared dashboard by share_id. No authentication required.
     * @param data The data for the request.
     * @param data.shareId
     * @returns DashboardShare Successful Response
     * @throws ApiError
     */
    public static getSharedDashboard(data: PublicGetSharedDashboardData): CancelablePromise<PublicGetSharedDashboardResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/public/dashboards/{share_id}',
            path: {
                share_id: data.shareId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class QuotasService {
    /**
     * Get Credit Quota
     * Get credit quota information for a user.
     *
     * This endpoint provides focused credit quota data for UI components
     * that only need to display credit availability (e.g., chat interfaces,
     * credit counters).
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * CreditResponse with premium, daily, and total credits remaining
     * @param data The data for the request.
     * @param data.userId
     * @returns CreditResponse Successful Response
     * @throws ApiError
     */
    public static getCreditQuota(data: QuotasGetCreditQuotaData): CancelablePromise<QuotasGetCreditQuotaResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/credit-quota',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Dashboard Overview
     * Get comprehensive dashboard overview for a user.
     *
     * This endpoint provides all quota and resource usage information needed
     * for dashboard displays that require complete visibility into user limits
     * and current usage across all resource types.
     *
     * Args:
     * user_id: ID of the user
     *
     * Returns:
     * DashboardOverviewResponse with all resource quotas and usage metrics
     * @param data The data for the request.
     * @param data.userId
     * @returns DashboardOverviewResponse Successful Response
     * @throws ApiError
     */
    public static getDashboardOverview(data: QuotasGetDashboardOverviewData): CancelablePromise<QuotasGetDashboardOverviewResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/dashboard-overview',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Usage Over Time
     * Get usage statistics over time with period grouping.
     *
     * This endpoint maintains compatibility with the existing usage-over-time
     * functionality while being accessible through the new quota API structure.
     *
     * Args:
     * user_id: ID of the user
     * period: Time period for grouping (day/week/month/year)
     * start_date: Optional start date for filtering
     * end_date: Optional end date for filtering
     *
     * Returns:
     * Usage statistics grouped by time period for charting
     * @param data The data for the request.
     * @param data.userId
     * @param data.period
     * @param data.startDate
     * @param data.endDate
     * @returns UsageOvertimeResponse Successful Response
     * @throws ApiError
     */
    public static getUsageOverTime(data: QuotasGetUsageOverTimeData): CancelablePromise<QuotasGetUsageOverTimeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/quotas/{user_id}/usage-over-time',
            path: {
                user_id: data.userId
            },
            query: {
                period: data.period,
                start_date: data.startDate,
                end_date: data.endDate
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class RecommendationsService {
    /**
     * Get Recomendation Overal
     * Get overal recommendation statistics.
     * @returns RecommendationOveralPublic Successful Response
     * @throws ApiError
     */
    public static getRecomendationOveral(): CancelablePromise<RecommendationsGetRecomendationOveralResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/overal'
        });
    }
    
    /**
     * Read Recommendations
     * Retrieve recommendations.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.search
     * @param data.resourceId
     * @param data.resourceType
     * @param data.status
     * @param data.startDate
     * @param data.endDate
     * @param data.orderBy
     * @param data.orderDirection
     * @returns RecommendationsPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendations(data: RecommendationsReadRecommendationsData = {}): CancelablePromise<RecommendationsReadRecommendationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/',
            query: {
                skip: data.skip,
                limit: data.limit,
                search: data.search,
                resource_id: data.resourceId,
                resource_type: data.resourceType,
                status: data.status,
                start_date: data.startDate,
                end_date: data.endDate,
                order_by: data.orderBy,
                order_direction: data.orderDirection
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Recommendation
     * @param data The data for the request.
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static createRecommendation(data: RecommendationsCreateRecommendationData): CancelablePromise<RecommendationsCreateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/recommendations/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read Recommendation
     * Get recommendation by ID.
     * @param data The data for the request.
     * @param data.id
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static readRecommendation(data: RecommendationsReadRecommendationData): CancelablePromise<RecommendationsReadRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Recommendation
     * Update a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.requestBody
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendation(data: RecommendationsUpdateRecommendationData): CancelablePromise<RecommendationsUpdateRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Recommendation
     * Delete a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @returns SimpleMessage Successful Response
     * @throws ApiError
     */
    public static deleteRecommendation(data: RecommendationsDeleteRecommendationData): CancelablePromise<RecommendationsDeleteRecommendationResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/recommendations/{id}',
            path: {
                id: data.id
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Recommendation Status
     * Update the status of a recommendation.
     * @param data The data for the request.
     * @param data.id
     * @param data.status
     * @returns RecommendationPublic Successful Response
     * @throws ApiError
     */
    public static updateRecommendationStatus(data: RecommendationsUpdateRecommendationStatusData): CancelablePromise<RecommendationsUpdateRecommendationStatusResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/recommendations/{id}/status',
            path: {
                id: data.id
            },
            query: {
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ReportsService {
    /**
     * Get Report By Conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns Report Successful Response
     * @throws ApiError
     */
    public static getReportByConversation(data: ReportsGetReportByConversationData): CancelablePromise<ReportsGetReportByConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/reports/{conversation_id}',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Share Link
     * @param data The data for the request.
     * @param data.reportId
     * @returns Report Successful Response
     * @throws ApiError
     */
    public static createShareLink(data: ReportsCreateShareLinkData): CancelablePromise<ReportsCreateShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/reports/{report_id}/share',
            path: {
                report_id: data.reportId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Revoke Share Link
     * @param data The data for the request.
     * @param data.reportId
     * @returns Report Successful Response
     * @throws ApiError
     */
    public static revokeShareLink(data: ReportsRevokeShareLinkData): CancelablePromise<ReportsRevokeShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/reports/{report_id}/share',
            path: {
                report_id: data.reportId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ResourcesService {
    /**
     * Read Resources
     * Retrieve resources with pagination and caching.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @param data.name
     * @param data.resourceType
     * @param data.status
     * @param data.region
     * @returns ResourcesPublic Successful Response
     * @throws ApiError
     */
    public static readResources(data: ResourcesReadResourcesData = {}): CancelablePromise<ResourcesReadResourcesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/',
            query: {
                skip: data.skip,
                limit: data.limit,
                name: data.name,
                resource_type: data.resourceType,
                status: data.status,
                region: data.region
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Resource Statistics
     * Get resource statistics for the current workspace including status counts,
     * total resources, top resource types, and potential monthly savings.
     * @returns ResourceStatistics Successful Response
     * @throws ApiError
     */
    public static getResourceStatistics(): CancelablePromise<ResourcesGetResourceStatisticsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/statistics'
        });
    }
    
    /**
     * Read Resource By Id
     * Retrieve a specific resource by ID with detailed information.
     * @param data The data for the request.
     * @param data.resourceId
     * @returns ResourceDetail Successful Response
     * @throws ApiError
     */
    public static readResourceById(data: ResourcesReadResourceByIdData): CancelablePromise<ResourcesReadResourceByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/resources/{resource_id}',
            path: {
                resource_id: data.resourceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Resource By Id
     * Delete a specific resource by ID within the current workspace.
     * @param data The data for the request.
     * @param data.resourceId
     * @returns MessageResponse Successful Response
     * @throws ApiError
     */
    public static deleteResourceById(data: ResourcesDeleteResourceByIdData): CancelablePromise<ResourcesDeleteResourceByIdResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/resources/{resource_id}',
            path: {
                resource_id: data.resourceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class SampleDataService {
    /**
     * Create Sample Resources
     * Create sample resources for the current workspace.
     *
     * Args:
     * count: Number of resources to create (1-500, default: 50)
     *
     * Returns:
     * Summary of created resources
     * @param data The data for the request.
     * @param data.count Number of sample resources to create
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static createSampleResources(data: SampleDataCreateSampleResourcesData = {}): CancelablePromise<SampleDataCreateSampleResourcesResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/sample-data/resources',
            query: {
                count: data.count
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Clear Sample Resources
     * Clear all resources for the current workspace.
     *
     * ⚠️ WARNING: This will delete ALL resources in the workspace.
     * Use with extreme caution!
     *
     * Args:
     * confirm: Must be set to true to confirm the deletion
     *
     * Returns:
     * Summary of deleted resources
     * @param data The data for the request.
     * @param data.confirm Confirm deletion of all resources
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static clearSampleResources(data: SampleDataClearSampleResourcesData = {}): CancelablePromise<SampleDataClearSampleResourcesResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/sample-data/resources',
            query: {
                confirm: data.confirm
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Preview Sample Resources
     * Preview sample resources without creating them in the database.
     *
     * Args:
     * count: Number of resources to preview (1-20, default: 5)
     *
     * Returns:
     * Preview of sample resources that would be created
     * @param data The data for the request.
     * @param data.count Number of sample resources to preview
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static previewSampleResources(data: SampleDataPreviewSampleResourcesData = {}): CancelablePromise<SampleDataPreviewSampleResourcesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/sample-data/resources/preview',
            query: {
                count: data.count
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class ShareChatService {
    /**
     * Create Share Link
     * Create a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static createShareLink(data: ShareChatCreateShareLinkData): CancelablePromise<ShareChatCreateShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Revoke Share Link
     * Revoke a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static revokeShareLink(data: ShareChatRevokeShareLinkData): CancelablePromise<ShareChatRevokeShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Share Link
     * Get a share link for a conversation
     * @param data The data for the request.
     * @param data.conversationId
     * @returns ShareResponse Successful Response
     * @throws ApiError
     */
    public static getShareLink(data: ShareChatGetShareLinkData): CancelablePromise<ShareChatGetShareLinkResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/{conversation_id}/share',
            path: {
                conversation_id: data.conversationId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Shared Conversation
     * Get message history for a shared conversation by share ID (no authentication required)
     * @param data The data for the request.
     * @param data.shareId
     * @param data.skip
     * @param data.limit
     * @returns MessagePublicList Successful Response
     * @throws ApiError
     */
    public static getSharedConversation(data: ShareChatGetSharedConversationData): CancelablePromise<ShareChatGetSharedConversationResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/share-chat/conversations/shared/{share_id}',
            path: {
                share_id: data.shareId
            },
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class SubscriptionsService {
    /**
     * Get Available Plans
     * Get available plans
     * @returns ProductResponse Successful Response
     * @throws ApiError
     */
    public static getAvailablePlans(): CancelablePromise<SubscriptionsGetAvailablePlansResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/plans'
        });
    }
    
    /**
     * Get User Subscription Status
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getUserSubscriptionStatus(): CancelablePromise<SubscriptionsGetUserSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/status'
        });
    }
    
    /**
     * Get Workspace Subscription Status
     * Get subscription status for a workspace
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns SubscriptionStatus Successful Response
     * @throws ApiError
     */
    public static getWorkspaceSubscriptionStatus(data: SubscriptionsGetWorkspaceSubscriptionStatusData): CancelablePromise<SubscriptionsGetWorkspaceSubscriptionStatusResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/workspace/{workspace_id}/status',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Checkout Session
     * Create a checkout session for subscription
     * @param data The data for the request.
     * @param data.priceId
     * @returns CheckoutSessionResponse Successful Response
     * @throws ApiError
     */
    public static createCheckoutSession(data: SubscriptionsCreateCheckoutSessionData): CancelablePromise<SubscriptionsCreateCheckoutSessionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/checkout',
            query: {
                price_id: data.priceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get User Payment Methods
     * DEPRECATED: Get current user's payment methods
     *
     * This endpoint is deprecated as payment methods are now managed through the Stripe Customer Portal.
     * Use the billing portal session endpoint to redirect users to Stripe's native payment management.
     * @param data The data for the request.
     * @param data.paymentType
     * @returns PaymentMethodResponse Successful Response
     * @throws ApiError
     */
    public static getUserPaymentMethods(data: SubscriptionsGetUserPaymentMethodsData = {}): CancelablePromise<SubscriptionsGetUserPaymentMethodsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/payment-methods',
            query: {
                payment_type: data.paymentType
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get User Invoices
     * DEPRECATED: Get current user's invoices
     *
     * This endpoint is deprecated as invoice history is now managed through the Stripe Customer Portal.
     * Use the billing portal session endpoint to redirect users to Stripe's native invoice management.
     * @param data The data for the request.
     * @param data.limit
     * @param data.status
     * @returns InvoiceResponse Successful Response
     * @throws ApiError
     */
    public static getUserInvoices(data: SubscriptionsGetUserInvoicesData = {}): CancelablePromise<SubscriptionsGetUserInvoicesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/invoices',
            query: {
                limit: data.limit,
                status: data.status
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Submit Enterprise Enquiry
     * Submit an enterprise plan enquiry
     * @param data The data for the request.
     * @param data.requestBody
     * @returns EnterpriseEnquiryMessageResponse Successful Response
     * @throws ApiError
     */
    public static submitEnterpriseEnquiry(data: SubscriptionsSubmitEnterpriseEnquiryData): CancelablePromise<SubscriptionsSubmitEnterpriseEnquiryResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/enterprise-enquiry',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Submit Plan Change Request
     * Submit a plan change request
     * @param data The data for the request.
     * @param data.requestBody
     * @returns PlanChangeRequestResponse Successful Response
     * @throws ApiError
     */
    public static submitPlanChangeRequest(data: SubscriptionsSubmitPlanChangeRequestData): CancelablePromise<SubscriptionsSubmitPlanChangeRequestResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/plan-change',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Webhook
     * Handle webhook events from payment provider
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static webhook(): CancelablePromise<SubscriptionsWebhookResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/webhook'
        });
    }
    
    /**
     * Cancel Subscription
     * Cancel subscription for the current user
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static cancelSubscription(): CancelablePromise<SubscriptionsCancelSubscriptionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/subscriptions/cancel'
        });
    }
    
    /**
     * Create Billing Portal Session
     * Create a Stripe Customer Portal session for subscription management
     * @param data The data for the request.
     * @param data.returnUrl
     * @returns BillingPortalResponse Successful Response
     * @throws ApiError
     */
    public static createBillingPortalSession(data: SubscriptionsCreateBillingPortalSessionData = {}): CancelablePromise<SubscriptionsCreateBillingPortalSessionResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/billing-portal',
            query: {
                return_url: data.returnUrl
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Workspace Billing Portal Session
     * Create a Stripe Customer Portal session for workspace subscription management
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.returnUrl
     * @returns BillingPortalResponse Successful Response
     * @throws ApiError
     */
    public static createWorkspaceBillingPortalSession(data: SubscriptionsCreateWorkspaceBillingPortalSessionData): CancelablePromise<SubscriptionsCreateWorkspaceBillingPortalSessionResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/subscriptions/workspace/{workspace_id}/billing-portal',
            path: {
                workspace_id: data.workspaceId
            },
            query: {
                return_url: data.returnUrl
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class TasksService {
    /**
     * Create Task
     * Create new task.
     * @param data The data for the request.
     * @param data.requestBody
     * @param data.xTimezone
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static createTask(data: TasksCreateTaskData): CancelablePromise<TasksCreateTaskResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/',
            headers: {
                'x-timezone': data.xTimezone
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * List Tasks
     * List tasks with filters.
     * @param data The data for the request.
     * @param data.search
     * @param data.executionStatus
     * @param data.skip
     * @param data.limit
     * @param data.xTimezone
     * @returns TaskList Successful Response
     * @throws ApiError
     */
    public static listTasks(data: TasksListTasksData = {}): CancelablePromise<TasksListTasksResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/',
            headers: {
                'x-timezone': data.xTimezone
            },
            query: {
                search: data.search,
                execution_status: data.executionStatus,
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Task
     * Get task by ID.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.xTimezone
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static getTask(data: TasksGetTaskData): CancelablePromise<TasksGetTaskResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            headers: {
                'x-timezone': data.xTimezone
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Task
     * Update task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTask(data: TasksUpdateTaskData): CancelablePromise<TasksUpdateTaskResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Task
     * Delete task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns TaskDeleteResponse Successful Response
     * @throws ApiError
     */
    public static deleteTask(data: TasksDeleteTaskData): CancelablePromise<TasksDeleteTaskResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/tasks/{task_id}',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Task Average Run Time
     * Get the average run time for a task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns number Successful Response
     * @throws ApiError
     */
    public static getTaskAverageRunTime(data: TasksGetTaskAverageRunTimeData): CancelablePromise<TasksGetTaskAverageRunTimeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}/average-run-time',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Task Last Run Time
     * Get the last run time for a task.
     * @param data The data for the request.
     * @param data.taskId
     * @returns number Successful Response
     * @throws ApiError
     */
    public static getTaskLastRunTime(data: TasksGetTaskLastRunTimeData): CancelablePromise<TasksGetTaskLastRunTimeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}/last-run-time',
            path: {
                task_id: data.taskId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Task Histories
     * Get task histories by task ID.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.skip
     * @param data.limit
     * @param data.xTimezone
     * @returns TaskHistoriesResponse Successful Response
     * @throws ApiError
     */
    public static getTaskHistories(data: TasksGetTaskHistoriesData): CancelablePromise<TasksGetTaskHistoriesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/tasks/{task_id}/histories',
            path: {
                task_id: data.taskId
            },
            headers: {
                'x-timezone': data.xTimezone
            },
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Task Enable
     * Update task enable.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.requestBody
     * @returns TaskResponse Successful Response
     * @throws ApiError
     */
    public static updateTaskEnable(data: TasksUpdateTaskEnableData): CancelablePromise<TasksUpdateTaskEnableResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/tasks/{task_id}/enable',
            path: {
                task_id: data.taskId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Stop Task Execution
     * Stop an executing task.
     * @param data The data for the request.
     * @param data.taskId
     * @param data.requestBody
     * @returns TaskStopResponse Successful Response
     * @throws ApiError
     */
    public static stopTaskExecution(data: TasksStopTaskExecutionData): CancelablePromise<TasksStopTaskExecutionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/tasks/{task_id}/stop',
            path: {
                task_id: data.taskId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class TaskTemplatesService {
    /**
     * Generate
     * Generate the task template based on user's input
     * @param data The data for the request.
     * @param data.input
     * @returns app__schemas__task_template__TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static generate(data: TaskTemplatesGenerateData): CancelablePromise<TaskTemplatesGenerateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/generate',
            query: {
                input: data.input
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Template
     * Create new task template.
     * @param data The data for the request.
     * @param data.requestBody
     * @param data.isDefault
     * @returns app__schemas__task_template__TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static createTemplate(data: TaskTemplatesCreateTemplateData): CancelablePromise<TaskTemplatesCreateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/task_templates/',
            query: {
                is_default: data.isDefault
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * List Templates
     * List task templates with optional category and service filters.
     * @param data The data for the request.
     * @param data.category
     * @param data.services
     * @param data.includeDefaults
     * @param data.skip
     * @param data.limit
     * @param data.searchQuery
     * @returns TaskTemplateList Successful Response
     * @throws ApiError
     */
    public static listTemplates(data: TaskTemplatesListTemplatesData = {}): CancelablePromise<TaskTemplatesListTemplatesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/',
            query: {
                category: data.category,
                services: data.services,
                include_defaults: data.includeDefaults,
                skip: data.skip,
                limit: data.limit,
                search_query: data.searchQuery
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Template
     * Get template by ID.
     * @param data The data for the request.
     * @param data.templateId
     * @returns app__schemas__task_template__TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static getTemplate(data: TaskTemplatesGetTemplateData): CancelablePromise<TaskTemplatesGetTemplateResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Template
     * Update template.
     * @param data The data for the request.
     * @param data.templateId
     * @param data.requestBody
     * @returns app__schemas__task_template__TaskTemplateResponse Successful Response
     * @throws ApiError
     */
    public static updateTemplate(data: TaskTemplatesUpdateTemplateData): CancelablePromise<TaskTemplatesUpdateTemplateResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Template
     * Delete template.
     * @param data The data for the request.
     * @param data.templateId
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static deleteTemplate(data: TaskTemplatesDeleteTemplateData): CancelablePromise<TaskTemplatesDeleteTemplateResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/task_templates/{template_id}',
            path: {
                template_id: data.templateId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class UsersService {
    /**
     * Read Users
     * Retrieve users based on workspace relationship.
     * Only returns users that belong to the current user's active workspace.
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns UsersPublic Successful Response
     * @throws ApiError
     */
    public static readUsers(data: UsersReadUsersData = {}): CancelablePromise<UsersReadUsersResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create User
     * Create new user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static createUser(data: UsersCreateUserData): CancelablePromise<UsersCreateUserResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/users/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read User Me
     * Get current user.
     * @returns UserDetail Successful Response
     * @throws ApiError
     */
    public static readUserMe(): CancelablePromise<UsersReadUserMeResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/me'
        });
    }
    
    /**
     * Delete User Me
     * Delete own user.
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUserMe(): CancelablePromise<UsersDeleteUserMeResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/me'
        });
    }
    
    /**
     * Update User Me
     * Update own user.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUserMe(data: UsersUpdateUserMeData): CancelablePromise<UsersUpdateUserMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Password Me
     * Update own password.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static updatePasswordMe(data: UsersUpdatePasswordMeData): CancelablePromise<UsersUpdatePasswordMeResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/me/password',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Read User By Id
     * Get a specific user by id.
     * @param data The data for the request.
     * @param data.userId
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static readUserById(data: UsersReadUserByIdData): CancelablePromise<UsersReadUserByIdResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update User
     * Update a user.
     * @param data The data for the request.
     * @param data.userId
     * @param data.requestBody
     * @returns UserPublic Successful Response
     * @throws ApiError
     */
    public static updateUser(data: UsersUpdateUserData): CancelablePromise<UsersUpdateUserResponse> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete User
     * Delete a user.
     * @param data The data for the request.
     * @param data.userId
     * @returns Message Successful Response
     * @throws ApiError
     */
    public static deleteUser(data: UsersDeleteUserData): CancelablePromise<UsersDeleteUserResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/users/{user_id}',
            path: {
                user_id: data.userId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Switch Workspace
     * Allow user to get new token for a different workspace.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns Token Successful Response
     * @throws ApiError
     */
    public static switchWorkspace(data: UsersSwitchWorkspaceData): CancelablePromise<UsersSwitchWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/users/switch-workspace/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class UtilsService {
    /**
     * Health Check
     * Comprehensive health check endpoint.
     * @returns unknown Successful Response
     * @throws ApiError
     */
    public static healthCheck(): CancelablePromise<UtilsHealthCheckResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/health-check/'
        });
    }
    
    /**
     * Get Cloud Regions
     * @param data The data for the request.
     * @param data.provider
     * @returns CloudRegionPublic Successful Response
     * @throws ApiError
     */
    public static getCloudRegions(data: UtilsGetCloudRegionsData): CancelablePromise<UtilsGetCloudRegionsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/constants/cloud_regions',
            query: {
                provider: data.provider
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Quick Start Templates
     * @returns QuickStartTemplatesPublic Successful Response
     * @throws ApiError
     */
    public static getQuickStartTemplates(): CancelablePromise<UtilsGetQuickStartTemplatesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/constants/quick_start_templates'
        });
    }
    
    /**
     * Get Example Prompts
     * Get example prompts based on template type.
     *
     * Args:
     * template_type: Resource type like 'ec2', 'rds', 'eks', etc. If None, returns 4 general prompts.
     *
     * Returns:
     * QuickStartTemplatesPublic with example prompts appropriate for the template type.
     * @param data The data for the request.
     * @param data.templateType Resource type like 'ec2', 'rds', 'eks', etc. If None, returns general prompts
     * @returns QuickStartTemplatesPublic Successful Response
     * @throws ApiError
     */
    public static getExamplePrompts(data: UtilsGetExamplePromptsData = {}): CancelablePromise<UtilsGetExamplePromptsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/utils/constants/example_prompts',
            query: {
                template_type: data.templateType
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}

export class WorkspacesService {
    /**
     * Get Workspaces
     * Get workspaces - both owned and invited (non-deleted only).
     * @param data The data for the request.
     * @param data.skip
     * @param data.limit
     * @returns WorkspacesPublic Successful Response
     * @throws ApiError
     */
    public static getWorkspaces(data: WorkspacesGetWorkspacesData = {}): CancelablePromise<WorkspacesGetWorkspacesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/',
            query: {
                skip: data.skip,
                limit: data.limit
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Create Workspace
     * Create new workspace. Only users who already own workspaces or superusers can create new ones.
     * @param data The data for the request.
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static createWorkspace(data: WorkspacesCreateWorkspaceData): CancelablePromise<WorkspacesCreateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/api/v1/workspaces/',
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Get Workspace Details
     * Get Workspace by ID. Accessible by workspace owner and invited users. Only non-deleted workspaces are returned.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns WorkspaceDetail Successful Response
     * @throws ApiError
     */
    public static getWorkspaceDetails(data: WorkspacesGetWorkspaceDetailsData): CancelablePromise<WorkspacesGetWorkspaceDetailsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/api/v1/workspaces/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Update Workspace
     * Update a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.workspaceId
     * @param data.requestBody
     * @returns WorkspacePublic Successful Response
     * @throws ApiError
     */
    public static updateWorkspace(data: WorkspacesUpdateWorkspaceData): CancelablePromise<WorkspacesUpdateWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/api/v1/workspaces/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            body: data.requestBody,
            mediaType: 'application/json',
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
    /**
     * Delete Workspace
     * Delete a workspace. Only workspace owners can perform this action.
     * @param data The data for the request.
     * @param data.workspaceId
     * @returns MessageResponse Successful Response
     * @throws ApiError
     */
    public static deleteWorkspace(data: WorkspacesDeleteWorkspaceData): CancelablePromise<WorkspacesDeleteWorkspaceResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/api/v1/workspaces/{workspace_id}',
            path: {
                workspace_id: data.workspaceId
            },
            errors: {
                422: 'Validation Error'
            }
        });
    }
    
}