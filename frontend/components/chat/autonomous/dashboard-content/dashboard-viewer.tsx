import { DashboardWidget, GridConfig } from '@/features/dashboard';
import { SchemaDashboard } from '@/openapi-ts/gens';

import DashboardGrid from './dashboard-grid';

type Props = {
  dashboardData: SchemaDashboard;
};

export const DashboardViewer: React.FC<Props> = ({ dashboardData }) => {
  return (
    <>
      {/* Dashboard Grid */}
      {dashboardData?.widgets && Array.isArray(dashboardData.widgets) && (
        <DashboardGrid
          widgets={dashboardData.widgets as DashboardWidget[]}
          gridConfig={
            (dashboardData.grid_config as GridConfig) || { columns: 12 }
          }
        />
      )}

      {/* Fallback if no structured content exists but dashboard has data */}
      {dashboardData && !dashboardData.title && !dashboardData.widgets && (
        <div className="prose prose-sm max-w-none">
          <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
            {JSON.stringify(dashboardData, null, 2)}
          </pre>
        </div>
      )}
    </>
  );
};
