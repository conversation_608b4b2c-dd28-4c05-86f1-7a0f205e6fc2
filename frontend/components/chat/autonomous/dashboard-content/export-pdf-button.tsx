import { useState } from 'react';

import { Button } from '@/components/ui/button';
import html2canvas from 'html2canvas-pro';
import jsPDF from 'jspdf';
import { ArrowDownToLineIcon, LoaderIcon } from 'lucide-react';
import { toast } from 'sonner';

type Props = {
  componentRef: React.RefObject<HTMLDivElement | null>;
};

export const ExportPDFButton: React.FC<Props> = ({ componentRef }) => {
  const [isExporting, setIsExporting] = useState(false);

  const handleExportPDF = async () => {
    if (!componentRef.current || isExporting) return;

    setIsExporting(true);
    const element = componentRef.current;

    // Temporarily set styles to ensure proper content capture
    const originalOverflow = element.style.overflow;
    const originalMaxHeight = element.style.maxHeight;
    element.style.overflow = 'visible';
    element.style.maxHeight = 'none';

    // Wait for any dynamic content to render
    await new Promise((resolve) => setTimeout(resolve, 100));

    try {
      // Capture canvas with optimized settings to minimize white space
      const canvas = await html2canvas(element, {
        scrollY: -window.scrollY,
        useCORS: true,
        allowTaint: true,
        scale: 2, // Higher quality
        logging: false,
        backgroundColor: null, // Transparent background
        removeContainer: true,
        // Use actual content dimensions instead of scroll dimensions
        width: element.offsetWidth,
        height: element.offsetHeight,
        // Crop to content bounds
        x: 0,
        y: 0,
      });

      // Create PDF with appropriate orientation based on content aspect ratio
      const canvasAspectRatio = canvas.width / canvas.height;
      const isLandscape = canvasAspectRatio > 1.4;

      const pdf = new jsPDF({
        orientation: isLandscape ? 'landscape' : 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // Fill entire page without margins
      const availableWidth = pageWidth;
      const availableHeight = pageHeight;

      // Scale to fit within entire page while maintaining aspect ratio
      const scale = Math.min(
        availableWidth / (canvas.width * 0.264583), // Convert px to mm
        availableHeight / (canvas.height * 0.264583),
      );

      const scaledWidth = canvas.width * 0.264583 * scale;
      const scaledHeight = canvas.height * 0.264583 * scale;

      // Center the content on the page
      const xOffset = (pageWidth - scaledWidth) / 2;
      const yOffset = (pageHeight - scaledHeight) / 2;

      const imgData = canvas.toDataURL('image/png', 1.0);

      // Check if content fits on single page
      if (scaledHeight <= availableHeight) {
        // Single page - center the content
        pdf.addImage(
          imgData,
          'PNG',
          xOffset,
          yOffset,
          scaledWidth,
          scaledHeight,
        );
      } else {
        // Multiple pages needed
        const pageContentHeight = availableHeight;
        const totalPages = Math.ceil(scaledHeight / pageContentHeight);

        for (let page = 0; page < totalPages; page++) {
          if (page > 0) {
            pdf.addPage();
          }

          const sourceY = (page * pageContentHeight) / scale;
          const sourceHeight = Math.min(
            pageContentHeight / scale,
            canvas.height * 0.264583 - sourceY,
          );

          // Create a temporary canvas for this page section
          const pageCanvas = document.createElement('canvas');
          const pageCtx = pageCanvas.getContext('2d');

          if (pageCtx) {
            pageCanvas.width = canvas.width;
            pageCanvas.height = sourceHeight / 0.264583;

            pageCtx.drawImage(
              canvas,
              0,
              sourceY / 0.264583,
              canvas.width,
              sourceHeight / 0.264583,
              0,
              0,
              canvas.width,
              sourceHeight / 0.264583,
            );

            const pageImgData = pageCanvas.toDataURL('image/png', 1.0);
            pdf.addImage(
              pageImgData,
              'PNG',
              0,
              0,
              availableWidth,
              sourceHeight * scale,
            );
          }
        }
      }

      pdf.save('dashboard-export.pdf');
    } catch {
      toast.error('Error exporting PDF');
    } finally {
      // Restore original styles
      element.style.overflow = originalOverflow;
      element.style.maxHeight = originalMaxHeight;
      setIsExporting(false);
    }
  };

  return (
    <Button
      size="sm"
      onClick={handleExportPDF}
      disabled={isExporting}
      className="gap-2"
    >
      {isExporting ? (
        <LoaderIcon className="size-4 animate-spin" />
      ) : (
        <ArrowDownToLineIcon className="size-4" />
      )}
      <span className="hidden sm:inline">
        {isExporting ? 'Exporting...' : 'Export PDF'}
      </span>
    </Button>
  );
};
