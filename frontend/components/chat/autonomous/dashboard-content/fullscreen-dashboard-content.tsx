import { ScrollArea } from '@/components/ui/scroll-area';
import { SchemaDashboard } from '@/openapi-ts/gens';

import DashboardHeader from './dashboard-header';
import { DashboardViewer } from './dashboard-viewer';

type Props = {
  dashboardData: SchemaDashboard;
  componentRef?: React.RefObject<HTMLDivElement | null>;
  hidden?: boolean;
};

export const FullscreenDashboardContent: React.FC<Props> = ({
  dashboardData,
  componentRef,
  hidden,
}) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    if (hidden) {
      return (
        <ScrollArea className="fixed top-0 max-h-0 w-[1600px] flex-1">
          {children}
        </ScrollArea>
      );
    }
    return <>{children}</>;
  };

  return (
    <Wrapper>
      <div className="bg-background w-full p-6 pt-0" ref={componentRef}>
        {/* Dashboard Header */}
        {dashboardData?.title && (
          <DashboardHeader
            title={dashboardData.title}
            description={dashboardData.description}
            createdAt={dashboardData.created_at}
            // Don't show expand button in fullscreen mode
            onExpandFullscreen={undefined}
          />
        )}

        <DashboardViewer dashboardData={dashboardData} />
      </div>
    </Wrapper>
  );
};
