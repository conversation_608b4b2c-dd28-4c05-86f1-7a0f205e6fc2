'use client';

import { FC, MouseEvent, useRef } from 'react';

import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { SchemaDashboard } from '@/openapi-ts/gens';
import { Minimize2 } from 'lucide-react';
import { useEventListener } from 'usehooks-ts';

import { ExportPDFButton } from './export-pdf-button';
import { FullscreenDashboardContent } from './fullscreen-dashboard-content';

interface FullscreenDashboardModalProps {
  dashboardData: SchemaDashboard;
  onClose: () => void;
}

const FullscreenDashboardModal: FC<FullscreenDashboardModalProps> = ({
  dashboardData,
  onClose,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const componentRef = useRef<HTMLDivElement>(null);

  // Handle ESC key
  useEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      onClose();
    }
  });

  // Handle click outside
  const handleBackdropClick = (event: MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      onClose();
    }
  };

  // // Parse dashboard data if it's a string
  // let dashboardData: SchemaDashboard;
  // try {
  //   if (typeof dashboard === 'string') {
  //     dashboardData = JSON.parse(dashboard);
  //   } else if (typeof dashboard === 'object' && dashboard !== null) {
  //     dashboardData = dashboard;
  //   } else {
  //     dashboardData = dashboard;
  //   }
  // } catch {
  //   dashboardData = dashboard;
  // }

  // Extract grid config with fallback
  // const gridConfig = dashboardData.grid_config?.columns
  //   ? { columns: dashboardData.grid_config.columns }
  //   : { columns: 12 };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      onClick={handleBackdropClick}
    >
      <div
        ref={modalRef}
        className="bg-background relative flex h-[95vh] w-[95vw] max-w-none flex-col rounded-lg border shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <div className="flex items-center justify-end gap-2 px-4 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="gap-2"
          >
            <Minimize2 className="size-4" />
            <span className="hidden sm:inline">Exit Fullscreen</span>
          </Button>

          <ExportPDFButton componentRef={componentRef} />
        </div>

        <FullscreenDashboardContent
          dashboardData={dashboardData}
          componentRef={componentRef}
          hidden
        />

        {/* Dashboard Content */}
        <ScrollArea className="flex-1">
          <FullscreenDashboardContent dashboardData={dashboardData} />
        </ScrollArea>
      </div>
    </div>
  );
};

export default FullscreenDashboardModal;
