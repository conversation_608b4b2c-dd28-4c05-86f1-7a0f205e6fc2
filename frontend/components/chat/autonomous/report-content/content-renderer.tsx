import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/chat/components/message/charts';
import { processChartData } from '@/components/chat/components/message/charts/common/functional-utils';
import { ProcessedChartData } from '@/components/chat/components/message/charts/common/types';
import { BasicTable } from '@/components/chat/components/message/tables';
import BaseCard from '@/components/shared/BaseCard';
import BrokenContentAlert from '@/components/shared/BrokenContentAlert';
import {
  ChartStructuredOutput,
  Content,
  KPICard,
  TableStructuredOutput,
} from '@/features/report';

interface ContentRendererProps {
  content: Content;
}

const ContentRenderer: React.FC<ContentRendererProps> = ({ content }) => {
  // Safety check for content object
  if (!content || typeof content !== 'object') {
    return null;
  }

  const renderContent = () => {
    switch (content.type) {
      case 'paragraph':
        if (!content.content) {
          return (
            <div className="my-4">
              <BrokenContentAlert
                type="card"
                title="Content Missing"
                description="No content available to display."
              />
            </div>
          );
        }
        return (
          <div className="prose prose-sm max-w-none">
            <p className="text-foreground leading-relaxed transition-opacity duration-300">
              {content.content as string}
            </p>
          </div>
        );

      case 'chart': {
        if (!content.content) {
          return (
            <BrokenContentAlert
              type="chart"
              title="Chart Data Missing"
              description="No chart data available to display."
            />
          );
        }

        const chartContent = content.content as ChartStructuredOutput;

        // Check data availability based on chart type
        const hasValidData =
          chartContent.chart_type === 'sankey'
            ? chartContent.sankey_nodes?.length &&
              chartContent.sankey_links?.length
            : chartContent.categories?.length && chartContent.datasets?.length;

        if (!hasValidData) {
          return (
            <BrokenContentAlert
              type="chart"
              title="Invalid Chart Data"
              description="The chart data is incomplete or invalid. Missing categories, datasets, or chart configuration."
            />
          );
        }

        // Convert to frontend chart format
        const chartData = {
          labels: chartContent.categories || [],
          datasets:
            chartContent.datasets?.map((dataset) => ({
              ...dataset,
              backgroundColor: undefined, // Let chart components handle colors
            })) || [],
          x_axis: {
            ...(chartContent.x_axis || {}),
            type:
              (chartContent.x_axis?.type as
                | 'time'
                | 'linear'
                | 'logarithmic'
                | 'category') || 'category',
          },
          y_axis: {
            ...(chartContent.y_axis || {}),
            type:
              (chartContent.y_axis?.type as
                | 'time'
                | 'linear'
                | 'logarithmic'
                | 'category') || 'linear',
          },
          display_options: {
            show_legend: chartContent.show_legend !== false,
            show_grid: chartContent.show_grid !== false,
            currency_format: chartContent.currency_format || false,
            percentage_format: chartContent.percentage_format || false,
          },
        };

        // Process the data for chart components - skip for Sankey charts
        let processedData: ProcessedChartData[] = [];
        if (chartContent.chart_type !== 'sankey') {
          try {
            processedData = processChartData(chartData);
          } catch (error) {
            console.error('Chart data processing error:', error);
            processedData = [];
          }
        }

        // Error boundary component for chart rendering
        const ChartErrorBoundary: React.FC<{ children: React.ReactNode }> = ({
          children,
        }) => {
          try {
            return <>{children}</>;
          } catch (error) {
            console.error('Chart rendering error:', error);
            return (
              <BrokenContentAlert
                type="chart"
                title="Chart Rendering Error"
                description="Unable to render the chart. The data may be invalid or corrupted."
              />
            );
          }
        };

        // Render the appropriate chart component
        const renderChart = () => {
          const commonProps = {
            data: processedData,
            chartData: chartData,
            height: 350,
            title: chartContent.title,
          };

          try {
            switch (chartContent.chart_type) {
              case 'bar':
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'line':
                return (
                  <ChartErrorBoundary>
                    <LineChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'pie':
                return (
                  <ChartErrorBoundary>
                    <PieChart {...commonProps} height={400} />
                  </ChartErrorBoundary>
                );
              case 'area':
                return (
                  <ChartErrorBoundary>
                    <AreaChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'step_area':
                return (
                  <ChartErrorBoundary>
                    <StepAreaChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              case 'radar':
                return (
                  <ChartErrorBoundary>
                    <RadarChart {...commonProps} height={400} />
                  </ChartErrorBoundary>
                );
              case 'sankey':
                if (chartContent.sankey_nodes && chartContent.sankey_links) {
                  const sankeyData = {
                    sankey_nodes: chartContent.sankey_nodes,
                    sankey_links: chartContent.sankey_links,
                  };
                  return (
                    <ChartErrorBoundary>
                      <SankeyChart
                        data={sankeyData}
                        chartData={chartData}
                        height={400}
                        title={chartContent.title}
                      />
                    </ChartErrorBoundary>
                  );
                }
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
              default:
                return (
                  <ChartErrorBoundary>
                    <BarChart {...commonProps} />
                  </ChartErrorBoundary>
                );
            }
          } catch (error) {
            console.error('Chart type switch error:', error);
            return (
              <BrokenContentAlert
                type="chart"
                title="Chart Rendering Error"
                description="Unable to render the chart due to an unsupported chart type or data format issue."
              />
            );
          }
        };

        return (
          <div className="my-6">
            <div className="relative w-full">{renderChart()}</div>
            {(chartContent.title || chartContent.description) && (
              <div className="mt-2 text-center">
                <p className="text-muted-foreground text-sm">
                  {chartContent.description && (
                    <>
                      {chartContent.title}: {chartContent.description}
                    </>
                  )}
                </p>
              </div>
            )}
          </div>
        );
      }

      case 'table': {
        if (!content.content) {
          return (
            <BrokenContentAlert
              type="table"
              title="Table Data Missing"
              description="No table data available to display."
            />
          );
        }

        const tableContent = content.content as TableStructuredOutput;

        if (!tableContent.columns?.length || !tableContent.rows?.length) {
          return (
            <BrokenContentAlert
              type="table"
              title="Invalid Table Data"
              description="The table data is incomplete or invalid. Missing columns or rows."
            />
          );
        }

        const tableData = {
          headers: tableContent.columns,
          rows: tableContent.rows,
        };

        return (
          <div className="my-6">
            <div className="overflow-hidden rounded-lg border">
              <BasicTable data={tableData} showFullView />
            </div>
            {(tableContent.title || tableContent.description) && (
              <div className="mt-2 text-center">
                <p className="text-muted-foreground text-sm">
                  {tableContent.description && (
                    <>
                      {tableContent.title}: {tableContent.description}
                    </>
                  )}
                </p>
              </div>
            )}
          </div>
        );
      }

      case 'card': {
        if (!content.content) {
          return (
            <BrokenContentAlert
              type="card"
              title="Card Data Missing"
              description="No card data available to display."
            />
          );
        }

        // Handle both single card and array of cards
        const cardContent = content.content as KPICard | KPICard[];
        const cards = Array.isArray(cardContent) ? cardContent : [cardContent];

        // Validate that we have at least one valid card
        if (
          !cards.length ||
          !cards.some((card) => card && card.title && card.value)
        ) {
          return (
            <BrokenContentAlert
              type="card"
              title="Invalid Card Data"
              description="The card data is incomplete or invalid. Missing title or value."
            />
          );
        }

        // For multiple cards in a single content item (fallback case)
        const cardCount = cards.length;
        let gridClasses = '';

        if (cardCount === 1) {
          gridClasses = 'grid grid-cols-1 max-w-sm mx-auto gap-6';
        } else if (cardCount === 2) {
          gridClasses = 'grid grid-cols-1 md:grid-cols-2 gap-6';
        } else if (cardCount === 3) {
          gridClasses = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        } else {
          // For 4+ cards, use 2x2 grid instead of cramming in one row
          gridClasses = 'grid grid-cols-1 sm:grid-cols-2 gap-6';
        }

        return (
          <div className={gridClasses}>
            {cards.map((card, index) => (
              <BaseCard
                key={index}
                title={card.title}
                value={card.value}
                description={card.description}
                trend={card.trend}
                icon={card.icon}
                alert={card.alert}
                variant="compact"
                minHeight="min-h-[180px]"
              />
            ))}
          </div>
        );
      }

      default:
        return null;
    }
  };

  return <div className="content-item">{renderContent()}</div>;
};

export default ContentRenderer;
