import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';

import { useChatContext } from '@/features/chat/context/chat-context';
import { recommendationQuery } from '@/features/recommendation/hooks/recommendation.query';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { SchemaPaginationMeta } from '@/openapi-ts/gens';
import {
  RecommendationStatus,
  SchemaRecommendationPublic,
} from '@/openapi-ts/gens';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { toast } from 'sonner';

import { ResourceRecommendationTable } from '../recommendation-table/resource-recommendation-table';
import {
  RecommendationCreate,
  StreamingRecommendation,
} from '../recommendation-table/types';

interface ResourceRecommendationsSessionProps {
  resourceId?: string;
}

// Export a ref type for parent components to use
export interface ResourceRecommendationsSessionRef {
  addStreamingRecommendation: (recommendation: StreamingRecommendation) => void;
}

export const ResourceRecommendationsSession = forwardRef<
  ResourceRecommendationsSessionRef,
  ResourceRecommendationsSessionProps
>(({ resourceId }, ref) => {
  const { currentRecommendations = [], conversationId } = useChatContext();

  // Load API recommendations
  const hasResourceId = !!resourceId;

  // Local pagination and search state
  const [page, setPage] = useState<number>(1);
  const [limit] = useState<number>(10);
  const [search, setSearch] = useState<string>('');

  const listParams: WithPaginationDefaults<RecommendationQueryParams> =
    useMemo(() => {
      const base: WithPaginationDefaults<RecommendationQueryParams> = {
        page,
        limit,
        search: search || undefined,
      };

      return {
        ...base,
        resource_id: hasResourceId ? [resourceId as string] : null,
      };
    }, [page, limit, search, hasResourceId, resourceId]);

  const { data: recommendationsResponse, isLoading: isLoadingRecommendations } =
    recommendationQuery.query.useList(listParams);

  const savedRecommendations: SchemaRecommendationPublic[] =
    recommendationsResponse?.data || [];
  const paginationMeta: SchemaPaginationMeta | undefined =
    recommendationsResponse?.meta as SchemaPaginationMeta | undefined;

  // Create recommendation mutation
  const createRecommendationMutation = recommendationQuery.mutation.useCreate();

  // localStorage keys
  const localStorageKey = `streaming-recommendations-${conversationId || 'no-conversation'}-${resourceId || 'all'}`;
  const processedIdsKey = `processed-ids-${conversationId || 'no-conversation'}-${resourceId || 'all'}`;

  // Helper functions for localStorage
  const saveRecommendationsToStorage = useCallback(
    (recommendations: StreamingRecommendation[]) => {
      try {
        localStorage.setItem(localStorageKey, JSON.stringify(recommendations));
      } catch (error) {
        console.error('Failed to save recommendations to localStorage:', error);
      }
    },
    [localStorageKey],
  );

  const saveProcessedIdsToStorage = useCallback(
    (ids: Set<string>) => {
      try {
        localStorage.setItem(processedIdsKey, JSON.stringify(Array.from(ids)));
      } catch (error) {
        console.error('Failed to save processed IDs to localStorage:', error);
      }
    },
    [processedIdsKey],
  );

  // Initialize state with localStorage data
  const [localRecommendations, setLocalRecommendations] = useState<
    StreamingRecommendation[]
  >(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(localStorageKey);
        return stored ? JSON.parse(stored) : [];
      } catch (error) {
        console.error(
          'Failed to load recommendations from localStorage:',
          error,
        );
        return [];
      }
    }
    return [];
  });

  const [processedIds, setProcessedIds] = useState<Set<string>>(() => {
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(processedIdsKey);
        return stored ? new Set(JSON.parse(stored)) : new Set();
      } catch (error) {
        console.error('Failed to load processed IDs from localStorage:', error);
        return new Set();
      }
    }
    return new Set();
  });

  // Initialize local recommendations from ChatContext and track new ones
  useEffect(() => {
    if (currentRecommendations.length > 0) {
      setLocalRecommendations((prev) => {
        const newRecs = currentRecommendations.filter(
          (newRec) =>
            !processedIds.has(newRec.tempId) &&
            !prev.some((existingRec) => existingRec.tempId === newRec.tempId),
        );

        if (newRecs.length > 0) {
          // Mark these IDs as processed
          setProcessedIds((prevIds) => {
            const newIds = new Set(prevIds);
            newRecs.forEach((rec) => newIds.add(rec.tempId));
            saveProcessedIdsToStorage(newIds);
            return newIds;
          });

          const updatedRecommendations = [...prev, ...newRecs];
          saveRecommendationsToStorage(updatedRecommendations);
          return updatedRecommendations;
        }

        return prev;
      });
    }
  }, [
    currentRecommendations,
    processedIds,
    saveRecommendationsToStorage,
    saveProcessedIdsToStorage,
  ]);

  // Method to add streaming recommendations (will be called from parent component)
  const addStreamingRecommendation = useCallback(
    (recommendation: StreamingRecommendation) => {
      setLocalRecommendations((prev) => {
        // Check if already exists by tempId or by data content
        const exists = prev.some(
          (rec) =>
            rec.tempId === recommendation.tempId ||
            (rec.transformedData?.title ===
              recommendation.transformedData?.title &&
              rec.transformedData?.description ===
                recommendation.transformedData?.description),
        );
        if (exists) {
          return prev;
        }

        const updatedRecommendations = [...prev, recommendation];
        saveRecommendationsToStorage(updatedRecommendations);
        return updatedRecommendations;
      });
    },
    [saveRecommendationsToStorage],
  );

  const handleSaveRecommendation = useCallback(
    async (recommendation: StreamingRecommendation) => {
      if (!recommendation.transformedData) {
        toast.error('Invalid recommendation data');
        return;
      }

      // Update status to saving
      setLocalRecommendations((prev) =>
        prev.map((rec) =>
          rec.tempId === recommendation.tempId
            ? { ...rec, status: 'saving' as const }
            : rec,
        ),
      );

      try {
        const createData: RecommendationCreate = {
          // When no resource selected, backend accepts null/None. We omit to keep types happy.
          resource_id: hasResourceId ? resourceId : undefined,
          type: recommendation.transformedData.type,
          title: recommendation.transformedData.title,
          description: recommendation.transformedData.description,
          potential_savings: recommendation.transformedData.potential_savings,
          effort: recommendation.transformedData.effort,
          risk: recommendation.transformedData.risk,
          status: RecommendationStatus.pending, // Fix: use enum value
        };

        const result =
          await createRecommendationMutation.mutateAsync(createData);

        // Fix: API returns the recommendation directly, not wrapped in success/data
        if (result) {
          // Remove from streaming recommendations and mark as processed
          setLocalRecommendations((prev) => {
            const updatedRecommendations = prev.filter(
              (rec) => rec.tempId !== recommendation.tempId,
            );
            saveRecommendationsToStorage(updatedRecommendations);
            return updatedRecommendations;
          });

          // Mark as processed to prevent re-adding
          setProcessedIds((prevIds) => {
            const newIds = new Set(prevIds);
            newIds.add(recommendation.tempId);
            saveProcessedIdsToStorage(newIds);
            return newIds;
          });

          toast.success('Recommendation saved successfully');
        } else {
          throw new Error('Failed to save recommendation');
        }
      } catch (error) {
        console.error('Error saving recommendation:', error);

        // Update status to error
        setLocalRecommendations((prev) =>
          prev.map((rec) =>
            rec.tempId === recommendation.tempId
              ? { ...rec, status: 'error' as const }
              : rec,
          ),
        );

        toast.error('Failed to save recommendation', {
          action: {
            label: 'Retry',
            onClick: () => handleSaveRecommendation(recommendation),
          },
        });
      }
    },
    [
      resourceId,
      hasResourceId,
      createRecommendationMutation,
      saveRecommendationsToStorage,
      saveProcessedIdsToStorage,
    ],
  );

  const handleRemoveRecommendation = useCallback(
    (tempId: string) => {
      setLocalRecommendations((prev) => {
        const updatedRecommendations = prev.filter(
          (rec) => rec.tempId !== tempId,
        );
        saveRecommendationsToStorage(updatedRecommendations);
        return updatedRecommendations;
      });

      // Mark as processed to prevent re-adding
      setProcessedIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.add(tempId);
        saveProcessedIdsToStorage(newIds);
        return newIds;
      });

      toast.success('Recommendation removed');
    },
    [saveRecommendationsToStorage, saveProcessedIdsToStorage],
  );

  const handleClearAllRecommendations = useCallback(() => {
    // Mark all current local recommendations as processed to prevent re-adding
    setProcessedIds((prevIds) => {
      const newIds = new Set(prevIds);
      localRecommendations.forEach((rec) => newIds.add(rec.tempId));
      saveProcessedIdsToStorage(newIds);
      return newIds;
    });

    // Clear all local recommendations
    setLocalRecommendations([]);
    saveRecommendationsToStorage([]);

    toast.success('All recommendations cleared');
  }, [localRecommendations, saveRecommendationsToStorage, saveProcessedIdsToStorage]);

  // Expose the addStreamingRecommendation method to parent
  useImperativeHandle(
    ref,
    () => ({
      addStreamingRecommendation,
    }),
    [addStreamingRecommendation],
  );

  return (
    <div className="space-y-6">
      <ResourceRecommendationTable
        resourceId={resourceId}
        savedRecommendations={savedRecommendations}
        streamingRecommendations={localRecommendations}
        onSaveRecommendation={handleSaveRecommendation}
        onRemoveRecommendation={handleRemoveRecommendation}
        onClearAllRecommendations={handleClearAllRecommendations}
        isLoading={isLoadingRecommendations}
        meta={paginationMeta}
        onPageChange={(p) => setPage(p)}
        onSearchChange={(value) => {
          setSearch(value);
          setPage(1);
        }}
      />
    </div>
  );
});

ResourceRecommendationsSession.displayName = 'ResourceRecommendationsSession';
