'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { DollarSignIcon, LayersIcon, ListChecksIcon } from 'lucide-react';

export function SummaryStatsCard(props: {
  totalResources: number;
  optimized: number;
  opportunities: number;
}) {
  const { totalResources, optimized, opportunities } = props;
  return (
    <Card>
      <CardHeader className="p-4">
        <CardTitle className="flex items-center justify-between gap-2">
          <p>Overall Optimization</p>
          <ListChecksIcon className="size-4" />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col p-4 pt-0">
        <div className="grid grid-cols-3 gap-3">
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Scanned</p>
            <div className="flex items-center gap-1">
              <LayersIcon className="text-info size-4" />
              <span className="text-sm font-medium">{totalResources}</span>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Well Optimized</p>
            <div className="flex items-center gap-1">
              <ListChecksIcon className="text-success size-4" />
              <span className="text-sm font-medium">{optimized}</span>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs">Opportunities</p>
            <div className="flex items-center gap-1">
              <ListChecksIcon className="text-warning size-4" />
              <span className="text-sm font-medium">{opportunities}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function SummarySavingsCard(props: { amount: number }) {
  const { amount } = props;
  return (
    <Card>
      <CardHeader className="p-4">
        <CardTitle className="flex items-center justify-between gap-2">
          <p>Estimated Savings</p>
          <DollarSignIcon className="size-4" />
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col p-4 pt-0">
        <div className="space-y-1">
          <p className="text-muted-foreground text-xs">Potential Savings</p>
          <div className="flex items-center gap-1">
            <DollarSignIcon className="text-success size-4" />
            <span className="text-success text-sm font-medium">
              {amount.toFixed(2)}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
