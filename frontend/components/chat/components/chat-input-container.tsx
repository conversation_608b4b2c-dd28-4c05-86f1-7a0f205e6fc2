'use client';

import { PropsWithChildren } from 'react';

import { cn } from '@/lib/utils';

export function ChatInputContainer({
  children,
  disabled,
  className,
}: PropsWithChildren<{
  className?: string;
  disabled?: boolean;
}>) {
  return (
    <div className="bg-gradient-animated overflow-hidden rounded-lg p-[1px]">
      <div className="bg-background rounded-lg">
        <div
          className={cn(
            'overflow-hidden rounded-lg shadow-none', // Layout, Border, and Shape
            'bg-muted/10', // Background
            'transition-all', // Transition Effects
            disabled && 'pointer-events-none opacity-50',
            className,
          )}
        >
          {children}
        </div>
      </div>
    </div>
  );
}
