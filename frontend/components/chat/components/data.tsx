import React from 'react';

import { ResourceCategory } from '@/components/chat/types';
import { HARDCODED_TOOLS } from '@/config/hardcoded-tools.config';
import { BookOpen, Settings } from 'lucide-react';

// Resource categories for # mention feature
export const resourceCategories: ResourceCategory[] = [
  {
    id: 'tools',
    name: 'Tools',
    icon: <Settings className="size-4" />,
    items: [
      // These will be dynamically populated from builtin tools and hardcoded tools
      // For now, keep a placeholder to maintain structure
      {
        id: 'builtin_tools',
        title: 'Tools',
        description: 'Available tools and integrations',
      },
    ],
    isDynamic: true, // Flag to indicate this category should be populated dynamically
    source: 'builtin_tools', // Source identifier for the data
  },
  {
    id: 'documents',
    name: 'Knowledge Base',
    icon: <BookOpen className="size-4" />,
    items: [
      // These will be dynamically populated from KB collections
      // For now, keep a placeholder to maintain structure
      {
        id: 'kb_collections',
        title: 'Knowledge Base',
        description: 'Search your knowledge base collections',
      },
    ],
    isDynamic: true, // Flag to indicate this category should be populated dynamically
    source: 'kb_collections', // Source identifier for the data
  },
];

// Helper function to populate dynamic resource categories
export function populateResourceCategories(options: {
  builtinTools?: Array<{
    id: string;
    builtin_tool: { display_name: string; description: string };
  }>;
  isBuiltinToolsLoading?: boolean;
  kbs?: Array<{ id: string; title: string; description: string }>;
  kbsLoading?: boolean;
}): ResourceCategory[] {
  const { builtinTools, isBuiltinToolsLoading, kbs, kbsLoading } = options;

  return resourceCategories.map((category) => {
    if (category.isDynamic && category.source === 'builtin_tools') {
      return {
        ...category,
        items: isBuiltinToolsLoading
          ? [
              {
                id: 'loading',
                title: 'Loading tools...',
                description: 'Please wait',
              },
            ]
          : [
              // Add hardcoded tools first
              ...HARDCODED_TOOLS.map((tool) => ({
                id: tool.id,
                title: tool.displayName,
                description: tool.description || '',
              })),
              // Then add dynamic builtin tools
              ...(builtinTools?.map((tool) => ({
                id: tool.id,
                title: tool.builtin_tool.display_name,
                description: tool.builtin_tool.description,
              })) || []),
            ],
      };
    }

    if (category.isDynamic && category.source === 'kb_collections') {
      return {
        ...category,
        items: kbsLoading
          ? [
              {
                id: 'loading',
                title: 'Loading collections...',
                description: 'Please wait',
              },
            ]
          : kbs || [],
      };
    }

    return category;
  });
}
