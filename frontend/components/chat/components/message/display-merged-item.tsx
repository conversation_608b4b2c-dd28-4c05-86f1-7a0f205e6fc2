import { <PERSON>down<PERSON>enderer } from '@/components/markdown';
import { If } from '@/components/ui/common/if';
import { SchemaMessageDisplayComponentPublic } from '@/openapi-ts/gens';

import { ThinkingComponent, ToolCall } from '../../types';
import { DisplayToolCall } from '../tools/components/display-tool-call';
import { DisplayComponent } from './display-component';
import { GroupChatSection } from './group-chat-section';
import { StreamingMessageContainer } from './streaming-message-container';
import { TypewriterAnimation } from './typewriter-animation';

type MergedItem =
  | { type: 'toolCall'; id: string; position: number; item: ToolCall }
  | {
      type: 'displayComponent';
      id: string;
      position: number;
      item: SchemaMessageDisplayComponentPublic;
    }
  | {
      type: 'thinkingComponent';
      id: string;
      position: number;
      item: ThinkingComponent;
    };

export const DisplayMergedItem = ({
  mergedItem,
  isStreaming,
}: {
  mergedItem: MergedItem;
  isStreaming: boolean;
}) => {
  switch (mergedItem.type) {
    case 'toolCall': {
      const toolContent = getToolContent(mergedItem.item, isStreaming);

      return toolContent;
    }

    case 'displayComponent':
      return (
        <StreamingMessageContainer isStreaming={isStreaming}>
          <DisplayComponent component={mergedItem.item} />
        </StreamingMessageContainer>
      );

    case 'thinkingComponent': {
      const content = mergedItem.item.content;
      const className = 'text-muted-foreground text-sm';

      return (
        <StreamingMessageContainer isStreaming={isStreaming}>
          <If
            condition={!isStreaming}
            fallback={
              <TypewriterAnimation
                content={content}
                className={className}
                speed={40} // Adjust speed as needed (characters per second)
              />
            }
          >
            <div className={className}>
              <MarkdownRenderer content={content} />
            </div>
          </If>
        </StreamingMessageContainer>
      );
    }

    default:
      return null;
  }
};

const getToolContent = (
  toolCall: ToolCall,
  isStreaming: boolean,
): React.ReactNode | null => {
  const toolContentMap: Record<string, () => React.ReactNode | null> = {
    group_chat: () => (
      <GroupChatSection isStreaming={isStreaming} toolCall={toolCall} />
    ),
    planning: () => null,
    RolePlayingOutput: () => null,
  };

  const handler = toolContentMap[toolCall.name];
  if (handler) {
    return handler();
  }

  return <DisplayToolCall toolCall={toolCall} />;
};
