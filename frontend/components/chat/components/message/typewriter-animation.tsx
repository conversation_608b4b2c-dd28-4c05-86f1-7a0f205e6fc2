'use client';

import { memo, useEffect, useState } from 'react';

import { MarkdownRenderer } from '@/components/markdown';
import { motion } from 'framer-motion';

interface TypewriterAnimationProps {
  content: string;
  speed?: number; // Characters per second
  className?: string;
  enableMentions?: boolean;
}

export const TypewriterAnimation = memo(
  ({
    content,
    speed = 50, // Default 50 characters per second
    className = '',
    enableMentions = false,
  }: TypewriterAnimationProps) => {
    const [displayedContent, setDisplayedContent] = useState('');

    useEffect(() => {
      if (!content) return;

      const totalCharacters = content.length;
      const intervalTime = 1000 / speed; // Convert speed to interval in milliseconds

      // Start from the current displayed content length instead of resetting
      let currentIndex = displayedContent.length;

      // If content is shorter than what we've displayed, reset
      if (totalCharacters < displayedContent.length) {
        setDisplayedContent(content);
        return;
      }

      const interval = setInterval(() => {
        if (currentIndex < totalCharacters) {
          const newContent = content.slice(0, currentIndex + 1);
          if (newContent) {
            setDisplayedContent(newContent);
          }
          currentIndex++;
        } else {
          clearInterval(interval);
        }
      }, intervalTime);

      return () => clearInterval(interval);
    }, [content, speed, displayedContent.length]);

    return (
      <motion.div
        className={className}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <MarkdownRenderer
          content={displayedContent}
          enableMentions={enableMentions}
        />
      </motion.div>
    );
  },
);

TypewriterAnimation.displayName = 'TypewriterAnimation';
