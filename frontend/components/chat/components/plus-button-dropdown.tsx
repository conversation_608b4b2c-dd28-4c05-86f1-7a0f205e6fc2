'use client';

import { useEffect, useRef, useState } from 'react';

import { createPortal } from 'react-dom';

import { Button } from '@/components/ui/button';
import { ChatContextType } from '@/features/chat/context/chat-context';
import { resourceQuery } from '@/features/resources/hooks/resource.query';
import { cn } from '@/lib/utils';
import { ChevronRight, DatabaseIcon, Plus, UploadIcon } from 'lucide-react';

import { ResourceSelector } from './resource-selector';
import { ResourceSwitchWarningDialog } from './resource-switch-warning-dialog';

type PlusButtonDropdownProps = {
  onFileSelect: () => void;
  onResourceSelect: (resourceId: string | null) => void;
  selectedResourceId: string | null;
  disabled: boolean;
  isStreaming: boolean;
} & Pick<ChatContextType, 'conversationResourceId'>;

export function PlusButtonDropdown({
  onFileSelect,
  onResourceSelect,
  selectedResourceId,
  disabled,
  isStreaming,
  conversationResourceId,
}: PlusButtonDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<
    'files' | 'resources'
  >('resources');

  const highlightedCategoryIndex = selectedCategory === 'resources' ? 1 : 0;
  const [focusedColumn, setFocusedColumn] = useState<'categories' | 'content'>(
    'categories',
  );
  const [buttonPosition, setButtonPosition] = useState({ top: 0, left: 0 });

  // Resource switching warning state
  const [pendingResourceSelection, setPendingResourceSelection] = useState<{
    resourceId: string;
    resourceName: string;
  } | null>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Fetch conversation resource to get its name
  const { data: conversationResource } = resourceQuery.query.useById(
    conversationResourceId || '',
    { enabled: !!conversationResourceId },
  );

  // Fetch resources to find selected resource details
  const { data: resourcesData } = resourceQuery.query.useInfiniteList({});
  const allResources = resourcesData?.pages.flatMap((page) => page.data) || [];

  const handleFileUploadClick = () => {
    // Directly trigger file selection without showing right panel
    onFileSelect();
    setIsOpen(false);
  };

  const handleCategoryClick = (categoryId: 'files' | 'resources') => {
    if (categoryId === 'files') {
      // For files, directly trigger file upload
      handleFileUploadClick();
    } else {
      // For resources, show the right panel
      setSelectedCategory(categoryId);
      setFocusedColumn('content');
    }
  };

  const handleResourceSelect = (resourceId: string | null) => {
    // If deselecting (resourceId is null), just proceed
    if (!resourceId) {
      onResourceSelect(resourceId);
      return;
    }

    // Check if there's an existing conversation resource and it's different from the new selection
    if (
      conversationResourceId &&
      conversationResourceId !== resourceId &&
      conversationResource
    ) {
      // Find the new resource details
      const newResource = allResources.find((r) => r.id === resourceId);

      if (newResource) {
        // Store the pending selection and show warning dialog
        setPendingResourceSelection({
          resourceId: resourceId,
          resourceName: newResource.name,
        });
        return; // Don't proceed with selection yet
      }
    }

    // If no conflict, proceed with selection
    onResourceSelect(resourceId);
  };

  const handleConfirmResourceSwitch = () => {
    // This will be called when user confirms the resource switch
    // The dialog handles navigation to new conversation
    setPendingResourceSelection(null);
    setIsOpen(false);
  };

  // Calculate dropdown position when opening with viewport bounds check
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const dropdownWidth = 750;
      const dropdownHeight = 280;
      const spacing = 8;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let top = buttonRect.bottom + spacing;
      let left = buttonRect.left;

      // Adjust horizontal position if dropdown would go off-screen
      if (left + dropdownWidth > viewportWidth) {
        // Try to align right edge of dropdown with right edge of button
        left = buttonRect.right - dropdownWidth;

        // If still off-screen, align with viewport edge
        if (left < 0) {
          left = Math.max(10, viewportWidth - dropdownWidth - 10);
        }
      }

      // Adjust vertical position if dropdown would go off-screen
      if (top + dropdownHeight > viewportHeight) {
        // Show above button instead
        top = buttonRect.top - dropdownHeight - spacing;

        // If still off-screen, align with viewport
        if (top < 0) {
          top = Math.max(10, viewportHeight - dropdownHeight - 10);
        }
      }

      setButtonPosition({ top, left });
    }
  }, [isOpen]);

  // Handle window resize and scroll events to reposition dropdown
  useEffect(() => {
    if (!isOpen) return;

    const handleResize = () => {
      if (buttonRef.current) {
        const buttonRect = buttonRef.current.getBoundingClientRect();
        const dropdownWidth = 750;
        const dropdownHeight = 280;
        const spacing = 8;

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let top = buttonRect.bottom + spacing;
        let left = buttonRect.left;

        // Adjust horizontal position if dropdown would go off-screen
        if (left + dropdownWidth > viewportWidth) {
          left = buttonRect.right - dropdownWidth;
          if (left < 0) {
            left = Math.max(10, viewportWidth - dropdownWidth - 10);
          }
        }

        // Adjust vertical position if dropdown would go off-screen
        if (top + dropdownHeight > viewportHeight) {
          top = buttonRect.top - dropdownHeight - spacing;
          if (top < 0) {
            top = Math.max(10, viewportHeight - dropdownHeight - 10);
          }
        }

        setButtonPosition({ top, left });
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize, true);
    };
  }, [isOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (focusedColumn === 'categories') {
        if (e.key === 'ArrowUp') {
          e.preventDefault();
          const newIndex =
            highlightedCategoryIndex > 0
              ? highlightedCategoryIndex - 1
              : categories.length - 1;
          setSelectedCategory(categories[newIndex].id);
        } else if (e.key === 'ArrowDown') {
          e.preventDefault();
          const newIndex =
            highlightedCategoryIndex < categories.length - 1
              ? highlightedCategoryIndex + 1
              : 0;
          setSelectedCategory(categories[newIndex].id);
        } else if (e.key === 'ArrowRight' || e.key === 'Enter') {
          e.preventDefault();
          setFocusedColumn('content');
        }
      }

      if (e.key === 'ArrowLeft' && focusedColumn === 'content') {
        e.preventDefault();
        setFocusedColumn('categories');
      }

      if (e.key === 'Escape') {
        e.preventDefault();
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedColumn, highlightedCategoryIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (e: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(e.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(e.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  const dropdown = isOpen ? (
    <div
      ref={dropdownRef}
      className={cn(
        'bg-background/95 fixed z-50 backdrop-blur-xs',
        'border-muted-foreground/10 border',
        'rounded-lg shadow-lg',
        'max-h-[280px] w-[750px] overflow-hidden',
      )}
      style={{
        top: `${buttonPosition.top}px`,
        left: `${buttonPosition.left}px`,
      }}
    >
      <div className="flex h-[280px]">
        {/* Categories Column */}
        <div className="custom-scrollbar border-muted-foreground/10 w-[260px] overflow-y-auto border-r">
          {categories.map((category, index) => (
            <div
              key={category.id}
              className={cn(
                'flex items-center justify-between px-4 py-3',
                'hover:bg-muted/50 transition-colors duration-150',
                'border-l-2',
                focusedColumn === 'categories' &&
                  index === highlightedCategoryIndex
                  ? 'border-primary bg-muted/50'
                  : 'border-transparent',
                selectedCategory === category.id &&
                  focusedColumn !== 'categories'
                  ? 'bg-muted/30'
                  : '',
              )}
              onClick={() => handleCategoryClick(category.id)}
            >
              <div className="flex items-center gap-3">
                <div className="bg-background/60 flex h-6 w-6 shrink-0 items-center justify-center rounded-lg">
                  {category.icon}
                </div>
                <div className="flex flex-col">
                  <span className="text-foreground text-sm font-medium break-words">
                    {category.name}
                  </span>
                </div>
              </div>
              {category.id !== 'files' && (
                <ChevronRight className="text-muted-foreground h-4 w-4" />
              )}
            </div>
          ))}
        </div>

        {/* Content Column - Only show when a category is selected */}
        {selectedCategory && (
          <div
            className={cn(
              'custom-scrollbar flex-1 overflow-y-auto',
              focusedColumn === 'content' ? 'bg-muted/10' : '',
            )}
          >
            {selectedCategory === 'resources' && (
              <div className="h-full">
                <ResourceSelector
                  onResourceSelect={handleResourceSelect}
                  selectedResourceId={selectedResourceId}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  ) : null;

  return (
    <>
      <Button
        ref={buttonRef}
        type="button"
        variant="ghost"
        size="sm"
        disabled={disabled || isStreaming}
        className="h-8 w-8 p-0"
        title="Add files or select resources"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Plus className="h-4 w-4" />
      </Button>
      {dropdown && createPortal(dropdown, document.body)}

      {/* Resource Switch Warning Dialog */}
      {pendingResourceSelection && conversationResource && (
        <ResourceSwitchWarningDialog
          currentResourceName={conversationResource.name}
          newResourceName={pendingResourceSelection.resourceName}
          newResourceId={pendingResourceSelection.resourceId}
          onConfirm={handleConfirmResourceSwitch}
          onResourceSelect={onResourceSelect}
          isOpen={!!pendingResourceSelection}
          onOpenChange={(open) => {
            if (!open) {
              setPendingResourceSelection(null);
            }
          }}
        />
      )}
    </>
  );
}

const categories = [
  {
    id: 'files',
    name: 'Upload Files',
    icon: <UploadIcon className="text-foreground h-4 w-4" />,
  },
  {
    id: 'resources',
    name: 'Select Resources',
    icon: <DatabaseIcon className="text-foreground h-4 w-4" />,
  },
] as const;
