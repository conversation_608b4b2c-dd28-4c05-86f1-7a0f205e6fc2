'use client';

import { useEffect, useMemo, useRef, useState } from 'react';

import { createPortal } from 'react-dom';

import { ResourceMentionProps } from '@/components/chat/types';
import { TOOL_NAME_MAPPING } from '@/config/hardcoded-tools.config';
import { KBTypeBadge } from '@/features/kb';
import { cn } from '@/lib/utils';
import { KBAccessLevel } from '@/openapi-ts/gens';
import { BookOpen, ChevronRight } from 'lucide-react';

import { getToolIcon } from './tools/tool-config';

// Helper function to convert a string to a slug format
const toSlug = (text: string) => {
  return text.replace(/\s+/g, '-');
};

// Legacy mapping for existing tools (non-hardcoded)
const legacyToolNameMapping: Record<string, string> = {
  planning: 'plan',
  'push-alert': 'alert',
  recommendation: 'recommend',
  'search-internet': 'search',
  'schedule-task': 'schedule',
};

// Combined tool name mapping using global config + legacy
const toolNameMapping: Record<string, string> = {
  ...legacyToolNameMapping,
  ...TOOL_NAME_MAPPING,
};

// Helper function to get the shortened name from mapping
const getShortName = (title: string) => {
  const slug = toSlug(title.toLowerCase());
  // Check if it's a tool (starts with common tool prefixes)
  const toolSlug = slug.replace(/^(tools?[-/]?|#tools?[-/]?|#)/, '');
  return toolNameMapping[toolSlug] || slug;
};

// Helper function to get the mention format based on item type
const getMentionFormat = (item: {
  categoryId?: string;
  title: string;
  metadata?:
    | { access_level?: KBAccessLevel }
    | { access_level?: 'private' | 'shared' };
}) => {
  // Check if item is from KB/documents category
  if (item.categoryId === 'documents' || item.metadata?.access_level) {
    return `kbs/${getShortName(item.title)}`;
  }
  // For other items, use the regular shortened name
  return getShortName(item.title);
};

// Helper function to get icon for items - styled KB icon for all KB items
const getItemIcon = (item: {
  categoryId?: string;
  title: string;
  metadata?:
    | { access_level?: KBAccessLevel }
    | { access_level?: 'private' | 'shared' };
}) => {
  // Check if item is from KB/documents category
  if (item.categoryId === 'documents' || item.metadata?.access_level) {
    return <BookOpen className="size-4" />;
  }
  // For tools, use the tool icon
  return getToolIcon(item.title);
};

export function ResourceMentionDropdown({
  isVisible,
  position,
  filter,
  categories,
  onSelect,
  onClose,
  dropdownRef,
}: ResourceMentionProps) {
  const [highlightedCategoryIndex, setHighlightedCategoryIndex] = useState(0);
  const [highlightedItemIndex, setHighlightedItemIndex] = useState(0);
  const [focusedColumn, setFocusedColumn] = useState<'categories' | 'items'>(
    'categories',
  );
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categoryManuallySelected, setCategoryManuallySelected] =
    useState(false);

  const categoriesContainerRef = useRef<HTMLDivElement>(null);
  const itemsContainerRef = useRef<HTMLDivElement>(null);
  const categoryItemsRef = useRef<Array<HTMLDivElement | null>>([]);
  const resourceItemsRef = useRef<Array<HTMLDivElement | null>>([]);

  // Filter categories and items based on input
  // Updated filter logic to search across both categories and their items
  let filteredCategories = categories.filter((cat) => {
    if (filter.length === 0) {
      // When not searching, show all categories
      return true; // Show all categories when filter is empty
    } else {
      // When searching, only show categories with matching names OR with matching items
      const lowercaseFilter = filter.toLowerCase();
      const hasMatchingItems = cat.items.some((item) =>
        item.title.toLowerCase().includes(lowercaseFilter),
      );
      return (
        hasMatchingItems || cat.name.toLowerCase().includes(lowercaseFilter)
      );
    }
  });

  // Calculate matching item counts for each category
  const matchingItemCounts = filteredCategories.reduce(
    (counts, category) => {
      const lowercaseFilter = filter.toLowerCase();
      counts[category.id] =
        filter.length > 0
          ? category.items.filter((item) =>
              item.title.toLowerCase().includes(lowercaseFilter),
            ).length
          : category.items.length;
      return counts;
    },
    {} as Record<string, number>,
  );

  // Further filter to remove categories with zero matching items during search
  if (filter.length > 0) {
    const lowercaseFilter = filter.toLowerCase();
    filteredCategories = filteredCategories.filter((category) => {
      // Keep category if:
      // 1. It has matching items, OR
      // 2. Its name matches the search AND we want to show empty categories
      return (
        matchingItemCounts[category.id] > 0 ||
        (category.name.toLowerCase().includes(lowercaseFilter) && false)
      ); // Set to true if you want to show empty categories
    });
  }

  // Filter items based on the selected category and search filter
  const filteredItems = useMemo(() => {
    return selectedCategory
      ? categories
          .find((cat) => cat.id === selectedCategory)
          ?.items.filter((item) => {
            const lowercaseFilter = filter.toLowerCase();
            return item.title.toLowerCase().includes(lowercaseFilter);
          }) || []
      : [];
  }, [selectedCategory, categories, filter]);

  // Get all matching items across all categories when searching
  const allMatchingItems = useMemo(() => {
    return filter.length > 0
      ? filteredCategories.flatMap((cat) => {
          const lowercaseFilter = filter.toLowerCase();
          return cat.items
            .filter((item) =>
              item.title.toLowerCase().includes(lowercaseFilter),
            )
            .map((item) => ({
              ...item,
              categoryId: cat.id,
              categoryName: cat.name,
              categoryIcon: cat.icon,
            }));
        })
      : [];
  }, [filter, filteredCategories]);

  // Get category-specific matching items when a category is selected during search
  const categoryMatchingItems = useMemo(() => {
    return filter.length > 0 && selectedCategory
      ? categories
          .find((cat) => cat.id === selectedCategory)
          ?.items.filter((item) => {
            const lowercaseFilter = filter.toLowerCase();
            return item.title.toLowerCase().includes(lowercaseFilter);
          })
          .map((item) => ({
            ...item,
            categoryId: selectedCategory,
            categoryName:
              categories.find((cat) => cat.id === selectedCategory)?.name || '',
            categoryIcon: categories.find((cat) => cat.id === selectedCategory)
              ?.icon,
          })) || []
      : [];
  }, [filter, selectedCategory, categories]);

  // Auto-focus on items column when searching and there are matching items
  useEffect(() => {
    if (filter.length > 0 && allMatchingItems.length > 0) {
      setFocusedColumn('items');
      setHighlightedItemIndex(0);
      // Reset manual selection when filter changes
      setCategoryManuallySelected(false);
    } else if (filter.length === 0) {
      // Reset to categories focus when not searching
      setFocusedColumn('categories');
      setCategoryManuallySelected(false);
    }
  }, [filter, allMatchingItems.length]);

  // Reset highlight indexes when filtered data changes
  useEffect(() => {
    setHighlightedCategoryIndex(0);
    // Don't auto-select first category when filter changes
    // This prevents reverting back to first category
  }, [filteredCategories.length]);

  // Update selected category if the current one gets filtered out
  useEffect(() => {
    if (selectedCategory && filteredCategories.length > 0) {
      // Check if the currently selected category is still in the filtered list
      const categoryStillExists = filteredCategories.some(
        (cat) => cat.id === selectedCategory,
      );

      // If not, select the first category in the filtered list
      if (!categoryStillExists) {
        setSelectedCategory(filteredCategories[0].id);
        setHighlightedCategoryIndex(0);
      }
    }
  }, [filteredCategories, selectedCategory]);

  useEffect(() => {
    setHighlightedItemIndex(0);
  }, [filteredItems.length, allMatchingItems.length]);

  // Update selected category when navigating with arrow keys
  useEffect(() => {
    if (filteredCategories.length > 0) {
      setSelectedCategory(filteredCategories[highlightedCategoryIndex].id);
    }
  }, [highlightedCategoryIndex, filteredCategories]);

  // Initialize first category only when dropdown first becomes visible
  useEffect(() => {
    if (
      isVisible &&
      filteredCategories.length > 0 &&
      selectedCategory === null
    ) {
      setSelectedCategory(filteredCategories[0].id);
    }
  }, [isVisible, filteredCategories, selectedCategory]);

  // Ensure the highlighted category is visible
  useEffect(() => {
    if (
      !isVisible ||
      filteredCategories.length === 0 ||
      focusedColumn !== 'categories'
    )
      return;

    const highlightedElement =
      categoryItemsRef.current[highlightedCategoryIndex];
    const containerElement = categoriesContainerRef.current;

    if (highlightedElement && containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      const elementRect = highlightedElement.getBoundingClientRect();

      if (elementRect.bottom > containerRect.bottom) {
        containerElement.scrollTop += elementRect.bottom - containerRect.bottom;
      } else if (elementRect.top < containerRect.top) {
        containerElement.scrollTop -= containerRect.top - elementRect.top;
      }
    }
  }, [
    highlightedCategoryIndex,
    isVisible,
    filteredCategories.length,
    focusedColumn,
  ]);

  // Ensure the highlighted item is visible
  useEffect(() => {
    if (
      !isVisible ||
      (filteredItems.length === 0 && allMatchingItems.length === 0) ||
      focusedColumn !== 'items'
    )
      return;

    const highlightedElement = resourceItemsRef.current[highlightedItemIndex];
    const containerElement = itemsContainerRef.current;

    if (highlightedElement && containerElement) {
      const containerRect = containerElement.getBoundingClientRect();
      const elementRect = highlightedElement.getBoundingClientRect();

      if (elementRect.bottom > containerRect.bottom) {
        containerElement.scrollTop += elementRect.bottom - containerRect.bottom;
      } else if (elementRect.top < containerRect.top) {
        containerElement.scrollTop -= containerRect.top - elementRect.top;
      }
    }
  }, [
    highlightedItemIndex,
    isVisible,
    filteredItems.length,
    allMatchingItems.length,
    focusedColumn,
  ]);

  // Handle keyboard events
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        if (focusedColumn === 'categories') {
          const newIndex =
            highlightedCategoryIndex > 0
              ? highlightedCategoryIndex - 1
              : filteredCategories.length - 1;

          setHighlightedCategoryIndex(newIndex);
          // Auto-select the category and show its items
          if (filteredCategories.length > 0) {
            const selectedCat = filteredCategories[newIndex];
            setSelectedCategory(selectedCat.id);
            setCategoryManuallySelected(true);
          }
        } else {
          // If we're showing search results, use allMatchingItems, otherwise use filteredItems
          const itemsList =
            filter.length > 0 && !categoryManuallySelected
              ? allMatchingItems
              : filter.length > 0
                ? categoryMatchingItems
                : filteredItems;
          setHighlightedItemIndex((prev) =>
            prev > 0 ? prev - 1 : itemsList.length - 1,
          );
        }
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        if (focusedColumn === 'categories') {
          const newIndex =
            highlightedCategoryIndex < filteredCategories.length - 1
              ? highlightedCategoryIndex + 1
              : 0;

          setHighlightedCategoryIndex(newIndex);
          // Auto-select the category and show its items
          if (filteredCategories.length > 0) {
            const selectedCat = filteredCategories[newIndex];
            setSelectedCategory(selectedCat.id);
            setCategoryManuallySelected(true);
          }
        } else {
          // If we're showing search results, use allMatchingItems, otherwise use filteredItems
          const itemsList =
            filter.length > 0 && !categoryManuallySelected
              ? allMatchingItems
              : filter.length > 0
                ? categoryMatchingItems
                : filteredItems;
          setHighlightedItemIndex((prev) =>
            prev < itemsList.length - 1 ? prev + 1 : 0,
          );
        }
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        if (focusedColumn === 'categories' && selectedCategory) {
          setFocusedColumn('items');
          // Reset the item index when switching columns
          setHighlightedItemIndex(0);
        }
      } else if (e.key === 'ArrowLeft') {
        e.preventDefault();
        if (focusedColumn === 'items') {
          // Only switch back to categories if we're not searching or if explicitly allowed
          // Set to false if you want to disable switching during search
          setFocusedColumn('categories');
        }
      } else if (e.key === 'Enter') {
        e.preventDefault();

        if (focusedColumn === 'categories') {
          // Always set the selected category and switch focus to items
          const selectedCat = filteredCategories[highlightedCategoryIndex];
          if (selectedCat) {
            setSelectedCategory(selectedCat.id);
            // Keep focus on items column when searching
            setFocusedColumn('items');
            setHighlightedItemIndex(0);
          }
        } else if (focusedColumn === 'items') {
          if (filter.length > 0 && allMatchingItems.length > 0) {
            // Handle selecting from global search results
            const item = allMatchingItems[highlightedItemIndex];
            if (item) {
              const mentionFormat = getMentionFormat(item);
              onSelect(mentionFormat);
            }
          } else {
            // Handle selecting from category-specific items
            const category = categories.find(
              (cat) => cat.id === selectedCategory,
            );
            const item = filteredItems[highlightedItemIndex];
            if (category && item) {
              // Convert item title to mention format
              const mentionFormat = getMentionFormat(item);
              onSelect(mentionFormat);
            }
          }
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    isVisible,
    focusedColumn,
    filteredCategories,
    filteredItems,
    allMatchingItems,
    filter,
    highlightedCategoryIndex,
    highlightedItemIndex,
    selectedCategory,
    onSelect,
    onClose,
    categoryManuallySelected,
    categories,
    categoryMatchingItems,
  ]);

  if (!isVisible) return null;

  const dropdown = (
    <div
      ref={dropdownRef}
      data-resource-mention-dropdown
      className={cn(
        'bg-background/95 fixed z-100 backdrop-blur-xs',
        'border-muted-foreground/10 border',
        'rounded-lg shadow-lg',
        'max-h-[320px] w-[750px] overflow-hidden',
        'transition-all duration-200 ease-in-out',
        'animate-in fade-in-50 zoom-in-95',
      )}
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
    >
      <div className="border-muted-foreground/10 bg-muted/30 flex items-center justify-between border-b p-2">
        <span className="text-xs font-medium">Mention a resource</span>
        <div className="flex items-center gap-2">
          {filter.length > 0 && allMatchingItems.length > 0 && (
            <div className="rounded-full bg-blue-500/10 px-1.5 py-0.5 text-xs text-blue-500">
              {allMatchingItems.length}
            </div>
          )}
        </div>
      </div>

      <div className="flex h-[240px]">
        {/* Categories Column */}
        <div
          ref={categoriesContainerRef}
          className={cn(
            'custom-scrollbar border-muted-foreground/10 overflow-y-auto border-r',
            filter.length > 0 &&
              allMatchingItems.length > 0 &&
              focusedColumn === 'items'
              ? 'opacity-80'
              : '',
          )}
        >
          {filteredCategories.length > 0 ? (
            filteredCategories.map((category, index) => (
              <div
                key={category.id}
                ref={(el) => {
                  categoryItemsRef.current[index] = el;
                }}
                className={cn(
                  'flex items-center justify-between px-4 py-2',
                  'hover:bg-muted/50 cursor-pointer transition-colors duration-150',
                  'border-l-2',
                  focusedColumn === 'categories' &&
                    index === highlightedCategoryIndex
                    ? 'border-primary bg-muted/50'
                    : 'border-transparent',
                  selectedCategory === category.id &&
                    focusedColumn !== 'categories'
                    ? 'bg-muted/30'
                    : '',
                )}
                onClick={() => {
                  setHighlightedCategoryIndex(index);
                  setSelectedCategory(category.id);
                  // Mark that a category has been manually selected
                  setCategoryManuallySelected(true);
                  // When searching, only switch focus to items if not already focused
                  if (filter.length > 0 && allMatchingItems.length > 0) {
                    // Keep focus on items column during search
                    setFocusedColumn('items');
                  } else {
                    // Normal behavior when not searching
                    setFocusedColumn('items');
                  }
                  setHighlightedItemIndex(0);
                }}
              >
                <div className="flex items-center gap-3">
                  <div className="text-muted-foreground flex size-4 shrink-0 items-center justify-center">
                    {category.icon}
                  </div>
                  <span className="text-foreground text-xs font-medium">
                    {category.name}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <ChevronRight className="text-muted-foreground size-6 pl-2" />
                </div>
              </div>
            ))
          ) : (
            <div className="flex items-center justify-center py-4 text-center">
              <p className="text-muted-foreground text-xs">
                No categories found
              </p>
            </div>
          )}
        </div>

        {/* Items Column */}
        <div
          ref={itemsContainerRef}
          className={cn(
            'custom-scrollbar flex-1 overflow-y-auto',
            filter.length > 0 &&
              allMatchingItems.length > 0 &&
              focusedColumn === 'items'
              ? 'bg-muted/10'
              : '',
          )}
        >
          {selectedCategory && (
            <>
              {filter.length > 0 ? (
                categoryManuallySelected ? (
                  // Show category-specific matching items when a category is manually selected during search
                  categoryMatchingItems.length > 0 ? (
                    categoryMatchingItems.map((item, index) => (
                      <div
                        key={item.id}
                        ref={(el) => {
                          resourceItemsRef.current[index] = el;
                        }}
                        className={cn(
                          'flex items-center gap-3 px-4 py-2',
                          'hover:bg-muted/50 cursor-pointer transition-colors duration-150',
                          'border-l-2',
                          focusedColumn === 'items' &&
                            index === highlightedItemIndex
                            ? 'border-primary bg-muted/50'
                            : 'border-transparent',
                        )}
                        onClick={() => {
                          // Convert item title to mention format
                          const mentionFormat = getMentionFormat(item);
                          onSelect(mentionFormat);
                        }}
                      >
                        <div className="flex size-6 shrink-0 items-center justify-center">
                          {getItemIcon(item)}
                        </div>
                        <span className="text-foreground text-sm font-medium break-words">
                          {item.title}
                        </span>
                        {item.metadata?.access_level && (
                          <KBTypeBadge
                            accessLevel={
                              item.metadata.access_level as KBAccessLevel
                            }
                            showIcon={true}
                            size="sm"
                          />
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="flex h-full items-center justify-center py-4 text-center">
                      <p className="text-muted-foreground text-xs">
                        No matching items
                      </p>
                    </div>
                  )
                ) : // Show all matching items across categories when searching (default behavior)
                allMatchingItems.length > 0 ? (
                  allMatchingItems.map((item, index) => (
                    <div
                      key={item.id}
                      ref={(el) => {
                        resourceItemsRef.current[index] = el;
                      }}
                      className={cn(
                        'flex items-center gap-3 px-4 py-2',
                        'hover:bg-muted/50 cursor-pointer transition-colors duration-150',
                        'border-l-2',
                        focusedColumn === 'items' &&
                          index === highlightedItemIndex
                          ? 'border-primary bg-muted/50'
                          : 'border-transparent',
                      )}
                      onClick={() => {
                        // Convert item title to mention format
                        const mentionFormat = getMentionFormat(item);
                        onSelect(mentionFormat);
                      }}
                    >
                      <div className="flex size-6 shrink-0 items-center justify-center">
                        {getItemIcon(item)}
                      </div>
                      <span className="text-foreground truncate text-sm font-medium">
                        {item.title}
                      </span>
                      {item.metadata?.access_level && (
                        <KBTypeBadge
                          accessLevel={
                            item.metadata.access_level as KBAccessLevel
                          }
                          showIcon={true}
                          size="sm"
                        />
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex h-full items-center justify-center py-4 text-center">
                    <p className="text-muted-foreground text-xs">
                      No items found
                    </p>
                  </div>
                )
              ) : filteredItems.length > 0 ? (
                // Original item display for the selected category
                filteredItems.map((item, index) => {
                  const category = categories.find(
                    (cat) => cat.id === selectedCategory,
                  );
                  return (
                    <div
                      key={item.id}
                      ref={(el) => {
                        resourceItemsRef.current[index] = el;
                      }}
                      className={cn(
                        'flex items-center gap-3 px-4 py-2',
                        'hover:bg-muted/50 cursor-pointer transition-colors duration-150',
                        'border-l-2',
                        focusedColumn === 'items' &&
                          index === highlightedItemIndex
                          ? 'border-primary bg-muted/50'
                          : 'border-transparent',
                      )}
                      onClick={() => {
                        if (category) {
                          // Convert item title to mention format
                          const mentionFormat = getMentionFormat(item);
                          onSelect(mentionFormat);
                        }
                      }}
                    >
                      <div className="flex size-6 shrink-0 items-center justify-center">
                        {getItemIcon(item)}
                      </div>
                      <span className="text-foreground truncate text-sm font-medium">
                        {item.title}
                      </span>
                      {item.metadata?.access_level && (
                        <KBTypeBadge
                          accessLevel={
                            item.metadata.access_level as KBAccessLevel
                          }
                          showIcon={true}
                          size="sm"
                        />
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="flex h-full flex-col items-center justify-center py-6 text-center">
                  <div className="mb-2 flex size-10 items-center justify-center">
                    {
                      categories.find((cat) => cat.id === selectedCategory)
                        ?.icon
                    }
                  </div>
                  <p className="text-muted-foreground text-xs">
                    No items found
                  </p>
                  <p className="text-muted-foreground/70 mt-1 max-w-[80%] text-xs">
                    Try a different search term
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );

  return createPortal(dropdown, document.body);
}
