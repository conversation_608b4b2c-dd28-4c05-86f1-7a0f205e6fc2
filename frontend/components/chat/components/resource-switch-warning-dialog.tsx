'use client';

import { PropsWithChildren, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useNavigateAgentDetail } from '@/features/agent/hooks/use-navigate-agent-detail';
import { AlertTriangle, Zap } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

interface ResourceSwitchWarningDialogProps extends PropsWithChildren {
  currentResourceName: string;
  newResourceName: string;
  newResourceId: string;
  onConfirm: () => void;
  // New chat functionality
  onResourceSelect?: (resourceId: string | null) => void;
  // Optional external control
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ResourceSwitchWarningDialog({
  children,
  currentResourceName,
  newResourceName,
  newResourceId,
  onConfirm,
  onResourceSelect,
  isOpen: externalIsOpen,
  onOpenChange: externalOnOpenChange,
}: ResourceSwitchWarningDialogProps) {
  const [internalIsOpen, toggle] = useToggle(false);
  const navigateAgentDetail = useNavigateAgentDetail();
  const [isStartingNewConversation, setIsStartingNewConversation] =
    useState(false);

  // Use external control if provided, otherwise use internal state
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const onOpenChange = externalOnOpenChange || toggle;

  const handleStartNewConversation = async () => {
    setIsStartingNewConversation(true);

    try {
      // Step 1: Clear chat state (like clicking the new chat button)
      navigateAgentDetail({
        resource_id: newResourceId,
      });

      // Step 2: Pre-select the desired resource
      if (onResourceSelect) {
        onResourceSelect(newResourceId);
      }

      onOpenChange(false); // Close dialog
      onConfirm(); // Execute confirmation callback
      setIsStartingNewConversation(false);
    } catch (error) {
      console.error('Error starting new conversation:', error);
      setIsStartingNewConversation(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {children && <DialogTrigger asChild>{children}</DialogTrigger>}
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Switch Resource Context?
          </DialogTitle>
          <DialogDescription>
            You&apos;re currently in a conversation with{' '}
            <span className="text-foreground font-medium">
              {currentResourceName}
            </span>{' '}
            and trying to switch to{' '}
            <span className="text-foreground font-medium">
              {newResourceName}
            </span>
            .
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border border-amber-200 bg-amber-50 p-3 dark:border-amber-800/30 dark:bg-amber-950/20">
            <div className="flex items-start gap-2">
              <Zap className="mt-0.5 h-4 w-4 flex-shrink-0 text-amber-600 dark:text-amber-400" />
              <div className="text-xs text-amber-800 dark:text-amber-200">
                <div className="mb-1 font-medium">Performance Impact</div>
                <div>
                  Switching resources mid-conversation can affect the AI
                  agent&apos;s performance and context understanding. For best
                  results, we recommend starting a new conversation.
                </div>
              </div>
            </div>
          </div>

          <div className="text-muted-foreground text-sm">
            Would you like to start a new conversation with the selected
            resource?
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleStartNewConversation}
            disabled={isStartingNewConversation}
          >
            {isStartingNewConversation
              ? 'Starting...'
              : 'Start New Conversation'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
