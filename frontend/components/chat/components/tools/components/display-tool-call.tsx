import { useRef } from 'react';

import { useChatMobileActiveTab } from '@/components/chat/hooks/use-chat-mobile-active-tab';
import { usePanelActiveTab } from '@/components/chat/hooks/use-panel-active-tab';
import {
  EChatTabViewValue,
  EConversationTabValue,
} from '@/components/chat/models/chat.enum';
import { ToolCall } from '@/components/chat/types';
import { useConsoleCurrentIndexStore } from '@/components/console/hooks/console-current-index.store';
import { MarkdownCodeBlock } from '@/components/markdown';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { If } from '@/components/ui/common/if';
import { ScrollArea } from '@/components/ui/scroll-area';
import { TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { size } from 'lodash';
import { ChevronDownIcon } from 'lucide-react';

import { getToolType } from '../tool-config';
import {
  detectContentType,
  formatContent,
  formatJsonContent,
} from '../utils/content-formatter';
import {
  ToolCallButton,
  ToolCallIcon,
  ToolCallStatusBadge,
} from './tool-call-atomic';

interface Props {
  toolCall: ToolCall;
  className?: string;
}
const paddingClass = 'px-2 py-2 md:px-3 md:py-2';

export function DisplayToolCall({ toolCall, className = '' }: Props) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const toolType = getToolType(toolCall.name);
  const { setCurrentTab } = usePanelActiveTab();
  const setActiveTab = useChatMobileActiveTab((state) => state.setActiveTab);
  const setCurrentIndex = useConsoleCurrentIndexStore(
    (state) => state.setCurrentIndex,
  );

  const hasReasoning = !!(
    toolCall.reasoning &&
    typeof toolCall.reasoning === 'string' &&
    toolCall.reasoning.trim()
  );

  const { sql: sqlArg, ...restArgs } = toolCall.arguments;

  if (toolType.isExpandable) {
    return (
      <Collapsible className="rounded-lg border [&[data-state=open]_.chevron]:rotate-180">
        <CollapsibleTrigger asChild>
          <ToolCallButton
            variant="ghost"
            status={toolCall.status}
            className={paddingClass}
          >
            <div className="flex w-full min-w-0 items-center justify-between">
              <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
                {/* Tool Icon */}
                <ToolCallIcon toolType={toolType} />

                {/* Tool Info */}
                <div className="flex min-w-0 flex-1 flex-col items-start overflow-hidden">
                  <div className="flex w-full items-center gap-1 md:gap-2">
                    <span className="flex-1 truncate text-left text-xs font-medium md:text-sm">
                      {toolType.display}
                    </span>
                    <ToolCallStatusBadge status={toolCall.status} />
                    <ChevronDownIcon className="chevron size-4 transition-transform" />
                  </div>
                </div>
              </div>
            </div>
          </ToolCallButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className={cn('space-y-2 border-t', paddingClass)}>
            <If condition={hasReasoning}>
              <p className="text-muted-foreground text-xs italic md:text-sm">
                {toolCall.reasoning}
              </p>

              <If condition={sqlArg}>
                <MarkdownCodeBlock className="language-sql">
                  {formatJsonContent(sqlArg)}
                </MarkdownCodeBlock>
              </If>

              <If condition={size(restArgs)}>
                <ScrollArea className="max-h-[500px]">
                  <MarkdownCodeBlock
                    className={`language-${detectContentType(restArgs)}`}
                  >
                    {formatContent(restArgs)}
                  </MarkdownCodeBlock>
                </ScrollArea>
              </If>

              <If condition={toolCall.output}>
                {(output) => (
                  <div className="max-h-[500px] overflow-y-auto">
                    <MarkdownCodeBlock className="language-python">
                      {formatJsonContent(output)}
                    </MarkdownCodeBlock>
                  </div>
                )}
              </If>
            </If>
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  const handleClick = () => {
    if (!buttonRef.current) return;

    if (toolType.panelTabValue) {
      setActiveTab(EChatTabViewValue.Console);
      setCurrentTab(toolType.panelTabValue);
    }

    if (toolType.panelTabValue === EConversationTabValue.Console) {
      const buttons = Array.from(
        document.querySelectorAll<HTMLButtonElement>('.console-step'),
      );

      const index = buttons.indexOf(buttonRef.current);
      setTimeout(() => {
        setCurrentIndex(index);
      }, 100);
    }

    // onToggleExpand?.();
  };

  return (
    <TooltipProvider>
      <ToolCallButton
        ref={buttonRef}
        variant="outline"
        status={toolCall.status}
        className={cn(
          paddingClass,
          toolType.panelTabValue === EConversationTabValue.Console &&
            'console-step',
          className,
        )}
        onClick={handleClick}
      >
        <div className="flex w-full min-w-0 items-center justify-between">
          <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
            {/* Tool Icon */}
            <ToolCallIcon toolType={toolType} />

            {/* Tool Info */}
            <div className="flex min-w-0 flex-1 flex-col items-start overflow-hidden">
              <div className="flex w-full items-center gap-1 md:gap-2">
                <span className="flex-1 truncate text-left text-xs font-medium md:text-sm">
                  {toolType.display}
                </span>
                <ToolCallStatusBadge status={toolCall.status} />
              </div>

              {/* Reasoning or Agent Info */}
              <If condition={hasReasoning}>
                <div className="text-muted-foreground w-full text-left text-xs">
                  <span className="line-clamp-1 italic md:line-clamp-1">
                    {toolCall.reasoning}
                  </span>
                </div>
              </If>
            </div>
          </div>
        </div>
      </ToolCallButton>
    </TooltipProvider>
  );
}
