import { PropsWithChildren } from 'react';

import { ToolCallStatus } from '@/components/chat/types';
import { BorderBeam } from '@/components/magic/border-beam';
import { Badge } from '@/components/ui/badge';
import { Button, ButtonProps } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { cn } from '@/lib/utils';

import { ColoredToolIcon } from '../colored-tool-icon';
import { TOOL_CALL_STATUS_CONFIG } from '../constants/tool-call-status.config';
import { ToolType } from '../tool-config';

export function ToolCallIcon({ toolType }: { toolType: ToolType }) {
  return (
    <ColoredToolIcon
      icon={toolType.icon}
      size={20}
      className="shrink-0 rounded-lg"
    />
  );
}

export function ToolCallStatusBadge({
  status,
  showLabel = false,
}: {
  status: ToolCallStatus;
  showLabel?: boolean;
}) {
  if (
    status === ToolCallStatus.COMPLETED ||
    status === ToolCallStatus.RUNNING
  ) {
    return null;
  }

  const statusConfig = TOOL_CALL_STATUS_CONFIG[status];

  return (
    <Badge
      variant="ghost"
      className={cn(
        'size-4 shrink-0 border-none px-1 text-xs md:size-5',
        statusConfig.color,
        'transition-colors',
      )}
    >
      <span className="flex items-center gap-0.5 md:gap-1">
        {statusConfig.icon}
        <If condition={showLabel}>
          <span className="text-[9px] font-medium md:text-[10px]">
            {statusConfig.label}
          </span>
        </If>
      </span>
    </Badge>
  );
}

export function ToolCallButton({
  children,
  variant,
  status,
  className,
  ...props
}: PropsWithChildren<{
  variant: 'ghost' | 'outline';
  status: ToolCallStatus;
  className?: string;
}> &
  ButtonProps) {
  return (
    <Button
      {...props}
      variant={variant}
      className={cn(
        'relative h-auto w-full items-center overflow-hidden',
        status === ToolCallStatus.RUNNING &&
          'tool-shimmer-running relative overflow-hidden',
        className,
      )}
    >
      {/* Border beam for running status */}
      <If condition={status === ToolCallStatus.RUNNING}>
        <BorderBeam duration={4} size={70} />
      </If>
      {children}
    </Button>
  );
}
