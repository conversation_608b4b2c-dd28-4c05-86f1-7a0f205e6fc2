import { ToolCallStatus } from '@/components/chat/types';
import { CheckCircle2Icon, LoaderCircleIcon } from 'lucide-react';
import { XCircleIcon } from 'lucide-react';
import { AlertTriangleIcon } from 'lucide-react';

// Status configuration
export const TOOL_CALL_STATUS_CONFIG = {
  [ToolCallStatus.RUNNING]: {
    icon: <LoaderCircleIcon className="size-3.5 animate-spin" />,
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    border: 'border-blue-800/60',
    ariaLabel: 'Operation in progress',
    label: 'Running',
  },
  [ToolCallStatus.COMPLETED]: {
    icon: <CheckCircle2Icon className="size-3.5" />,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    border: 'border-green-800/60',
    ariaLabel: 'Operation completed successfully',
    label: 'Completed',
  },
  [ToolCallStatus.ERROR]: {
    icon: <XCircleIcon className="size-3.5" />,
    color: 'text-red-400',
    bgColor: 'bg-red-500/20',
    border: 'border-red-800/60',
    ariaLabel: 'Operation failed with error',
    label: 'Error',
  },
  [ToolCallStatus.TIMEOUT]: {
    icon: <XCircleIcon className="size-3.5" />,
    color: 'text-red-400',
    bgColor: 'bg-red-500/20',
    border: 'border-red-800/60',
    ariaLabel: 'Operation timed out',
    label: 'Timeout',
  },
  [ToolCallStatus.SUCCESS]: {
    icon: <CheckCircle2Icon className="size-3.5" />,
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    border: 'border-green-800/60',
    ariaLabel: 'Operation completed successfully',
    label: 'Success',
  },
  [ToolCallStatus.WAITING]: {
    icon: <AlertTriangleIcon className="h-3.5 w-3.5" />,
    color: 'dark:text-amber-400 border-none',
    bgColor: 'dark:bg-amber-500/20',
    border: 'dark:border-amber-800/60',
    ariaLabel: 'Operation awaiting approval',
    label: 'Awaiting Approval',
  },
} as const satisfies Record<ToolCallStatus, unknown>;
