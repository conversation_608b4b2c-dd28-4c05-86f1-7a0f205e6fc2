import React from 'react';

import AwsLogo from '@/public/aws-logo.svg';
import AzureLogo from '@/public/azure-logo.svg';
import GcpLogo from '@/public/gcp-logo.svg';
import K8sLogo from '@/public/k8s-logo.svg';
import PostgresqlLogo from '@/public/postgresql-icon.svg';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  BookOpen,
  Cpu,
  FileText,
  Globe,
  Grid,
  Lightbulb,
  ListChecks,
  Save,
  Search,
  User,
  Wrench,
} from 'lucide-react';

import { EConversationTabValue } from '../../models/chat.enum';

// MCP Icon Component
const McpIcon = () => (
  <svg
    fill="currentColor"
    fillRule="evenodd"
    height="1em"
    style={{ flex: 'none', lineHeight: 1 }}
    viewBox="0 0 24 24"
    width="1em"
    xmlns="http://www.w3.org/2000/svg"
  >
    <title>ModelContextProtocol</title>
    <path d="M15.688 2.343a2.588 2.588 0 00-3.61 0l-9.626 9.44a.863.863 0 01-1.203 0 .823.823 0 010-1.18l9.626-9.44a4.313 4.313 0 016.016 0 4.116 4.116 0 011.204 3.54 4.3 4.3 0 013.609 1.18l.05.05a4.115 4.115 0 010 5.9l-8.706 8.537a.274.274 0 000 .393l1.788 1.754a.823.823 0 010 1.18.863.863 0 01-1.203 0l-1.788-1.753a1.92 1.92 0 010-2.754l8.706-8.538a2.47 2.47 0 000-3.54l-.05-.049a2.588 2.588 0 00-3.607-.003l-7.172 7.034-.002.002-.098.097a.863.863 0 01-1.204 0 .823.823 0 010-1.18l7.273-7.133a2.47 2.47 0 00-.003-3.537z"></path>
    <path d="M14.485 4.703a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a4.115 4.115 0 000 5.9 4.314 4.314 0 006.016 0l7.12-6.982a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a2.588 2.588 0 01-3.61 0 2.47 2.47 0 010-3.54l7.12-6.982z"></path>
  </svg>
);

// Enhanced tool type interface
export type ToolType = {
  icon: React.ReactElement;
  display: string;
  description: string;
  isVisible: boolean;
  isExpandable: boolean;
  panelTabValue?: EConversationTabValue;
};

// Tool behavior options interface
interface ToolBehaviorOptions {
  isVisible?: boolean;
  isExpandable?: boolean;
}

// Simplified tool configuration - single source of truth
const TOOL_CONFIG: Record<
  string,
  {
    icon: React.ReactElement;
    display: string;
    description: string;
    patterns: readonly string[];
    exactMatch?: boolean;
    isExpandable: boolean;
    panelTabValue?: EConversationTabValue;
  }
> = {
  alert: {
    icon: <AlertCircle className="size-4" />,
    display: 'Alert',
    description: 'System notification',
    patterns: ['alert', 'push alert'],
    isExpandable: true,
  },

  // script: {
  //   icon: <Terminal className="size-4" />,
  //   display: 'Script',
  //   description: 'Command execution',
  //   patterns: ['script'],
  //   isExpandable: true,
  // },
  knowledge_base: {
    icon: <BookOpen className="size-4" />,
    display: 'Knowledge Base',
    description: 'Document search',
    patterns: ['knowledge'],
    isExpandable: true,
  },
  visualize: {
    icon: <BarChart className="size-4" />,
    display: 'Visualize',
    description: 'Data visualization',
    patterns: ['visualize'],
    isExpandable: true,
  },
  recommendation: {
    icon: <Lightbulb className="size-4" />,
    display: 'Recommendation',
    description: 'Optimization suggestions',
    patterns: ['recommendation', 'recommendations'],
    isExpandable: false,
    panelTabValue: EConversationTabValue.Resource,
  },
  create_memory: {
    icon: <Save className="size-4" />,
    display: 'Create Memory',
    description: 'Store memory / execution patterns',
    patterns: ['create_memory', 'memory'],
    isExpandable: true,
  },
  search: {
    icon: <Search className="size-4" />,
    display: 'Search',
    description: 'Internet search',
    patterns: ['search'],
    isExpandable: true,
  },
  planning: {
    icon: <ListChecks className="size-4" />,
    display: 'Planning',
    description: 'Task planning',
    patterns: ['planning', 'plan'],
    isExpandable: false,
  },
  // web: {
  //   icon: <Globe className="size-4" />,
  //   display: 'Web',
  //   description: 'Web operation',
  //   patterns: ['web'],
  //   isExpandable: true,
  // },
  group_chat: {
    icon: <User className="size-4" />,
    display: 'Group Chat',
    description: 'Group chat message',
    patterns: ['group chat', 'group_chat'],
    isExpandable: true,
  },
  computer_use: {
    icon: <Cpu className="size-4" />,
    display: 'Computer Use',
    description: 'Built-in connector for computer use',
    patterns: ['computer_use', 'computer use'],
    isExpandable: false,
  },
  // schedule: {
  //   icon: <Calendar className="size-4" />,
  //   display: 'Schedule',
  //   description: 'Schedule operation',
  //   patterns: ['schedule', 'schedule_task'],
  //   isExpandable: false,
  // },
  report: {
    icon: <FileText className="size-4" />,
    display: 'Report',
    description: 'Report operation',
    patterns: ['report'],
    exactMatch: true,
    isExpandable: false,
    panelTabValue: EConversationTabValue.Report,
  },
  dashboard: {
    icon: <Grid className="size-4" />,
    display: 'Dashboard',
    description: 'Dashboard operation',
    patterns: ['dashboard'],
    exactMatch: true,
    isExpandable: false,
    panelTabValue: EConversationTabValue.Dashboard,
  },
  mcp: {
    icon: <McpIcon />,
    display: 'MCP Tool',
    description: 'Model Context Protocol tool',
    patterns: ['mcp'],
    isExpandable: true,
  },
  // console: {
  //   icon: <Terminal className="size-4" />,
  //   display: 'Console',
  //   description: 'Console operation',
  //   patterns: ['console', 'terminal'],
  //   isExpandable: false,
  //   panelTabValue: EConversationTabValue.Console,
  // },
  aws: {
    icon: <AwsLogo className="w-4" />,
    display: 'AWS CLI',
    description: 'AWS operation',
    patterns: ['aws'],
    isExpandable: false,
    panelTabValue: EConversationTabValue.Console,
  },
  gcp: {
    icon: <GcpLogo className="w-4" />,
    display: 'GCP CLI',
    description: 'GCP operation',
    patterns: ['gcp'],
    isExpandable: false,
    panelTabValue: EConversationTabValue.Console,
  },
  k8s: {
    icon: <K8sLogo className="w-4" />,
    display: 'Kubernetes CLI',
    description: 'Kubernetes operation',
    patterns: ['k8s', 'kubernetes'],
    isExpandable: false,
    panelTabValue: EConversationTabValue.Console,
  },
  azure: {
    icon: <AzureLogo className="w-4" />,
    display: 'Azure CLI',
    description: 'Azure operation',
    patterns: ['azure'],
    isExpandable: false,
    panelTabValue: EConversationTabValue.Console,
  },
  fetch_url: {
    icon: <Globe className="size-4" />,
    display: 'Fetch URL',
    description: 'Fetch URL',
    patterns: ['fetch_url'],
    isExpandable: true,
  },
  default: {
    icon: <Wrench className="size-4" />,
    display: 'Tool',
    description: 'System tool',
    patterns: [],
    isExpandable: true,
  },
} as const;

// Generate TOOL_TYPES from simplified config
const TOOL_TYPES = Object.fromEntries(
  Object.entries(TOOL_CONFIG).map(([key, config]) => [
    key,
    {
      icon: config.icon,
      display: config.display,
      description: config.description,
      isVisible: true,
      isExpandable: config.isExpandable,
      panelTabValue: config.panelTabValue,
    },
  ]),
) as Record<keyof typeof TOOL_CONFIG, ToolType>;

// Function to format tool name (split by "_" and capitalize first characters)
function formatToolName(toolName: string): string {
  return toolName
    .split(/[-_]/)
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Unified tool type detection using functional approach
const detectToolByUnifiedConfig = (lowerName: string) => {
  return Object.entries(TOOL_CONFIG).find(([_, config]) => {
    const { patterns, exactMatch } = config;
    return exactMatch
      ? patterns.some((pattern) => lowerName === pattern)
      : patterns.some((pattern) => lowerName.includes(pattern));
  });
};

const handleSearchTools = (lowerName: string) =>
  lowerName.includes('search') && !lowerName.includes('memory')
    ? TOOL_TYPES.search
    : null;

// Functional approach for tool behavior detection
const getToolBehavior = (toolName: string): ToolBehaviorOptions => {
  const lowerName = toolName.toLowerCase();

  // Functional composition for behavior rules
  const isExpandable = !['cli', 'planning'].some((pattern) =>
    lowerName.includes(pattern),
  );
  const isVisible = true; // All tools are visible for now

  return { isVisible, isExpandable };
};

const createCustomMcpTool = (toolName: string): ToolType => {
  const formattedName = formatToolName(toolName);
  const behavior = getToolBehavior(toolName);

  if (toolName.includes('postgres')) {
    return {
      icon: <PostgresqlLogo className="size-4" />,
      display: formattedName,
      description: `${formattedName} tool`,
      isVisible: true,
      isExpandable: behavior.isExpandable ?? true,
    };
  }

  return {
    icon: <McpIcon />,
    display: formattedName,
    description: `${formattedName} tool`,
    isVisible: true,
    isExpandable: behavior.isExpandable ?? true,
  };
};

// Tool type detection function using unified configuration
export function getToolType(toolName: string) {
  const lowerName = toolName.toLowerCase();

  // General pattern matching using unified config
  const matchedTool = detectToolByUnifiedConfig(lowerName);
  if (matchedTool) {
    const [toolKey] = matchedTool;
    return TOOL_TYPES[toolKey as keyof typeof TOOL_TYPES];
  }

  // Special case for search tools (not memory search)
  const searchTool = handleSearchTools(lowerName);
  if (searchTool) {
    return searchTool;
  }

  // Default to custom MCP tool
  return createCustomMcpTool(toolName);
}

// Get icon for tool name
export function getToolIcon(toolName: string) {
  const toolType = getToolType(toolName);

  return toolType.icon;
}
