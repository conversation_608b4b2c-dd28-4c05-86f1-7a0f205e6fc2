'use client';

import { useRef, useState } from 'react';

import { agentQuery } from '@/features/agent/hooks/agent.query';

interface InactiveAgentInfo {
  id: string;
  name: string;
}

export function useAgentActivation() {
  // Agent activation dialog states
  const [showAgentActivationDialog, setShowAgentActivationDialog] =
    useState(false);
  const [inactiveAgentInfo, setInactiveAgentInfo] =
    useState<InactiveAgentInfo | null>(null);

  // Track recently activated agents to avoid re-checking them immediately
  const recentlyActivatedAgentsRef = useRef<Set<string>>(new Set());

  // Fetch all agents to check for inactive ones
  const { data: allAgentsResponse } = agentQuery.query.useList();

  // Function to check for inactive agent mentions
  const checkForInactiveAgents = (text: string): InactiveAgentInfo | null => {
    if (!allAgentsResponse?.data) return null;

    // Extract agent mentions using regex to find @agent_name patterns
    const agentMentionRegex = /@(\w+)/gi;
    const mentions = text.match(agentMentionRegex);

    if (!mentions) return null;

    const allAgents = allAgentsResponse.data.filter(
      (agent) => agent.type === 'conversation_agent',
    );

    for (const mention of mentions) {
      const mentionedName = mention.substring(1).toLowerCase(); // Remove @ and convert to lowercase

      // Find agent by name (case insensitive)
      const mentionedAgent = allAgents.find(
        (agent) => agent.title?.toLowerCase() === mentionedName,
      );

      // If agent exists but is inactive, and not recently activated, return it
      if (
        mentionedAgent &&
        !mentionedAgent.is_active &&
        !recentlyActivatedAgentsRef.current.has(mentionedAgent.id)
      ) {
        return {
          id: mentionedAgent.id,
          name: mentionedAgent.title || '',
        };
      }
    }

    return null;
  };

  // Function to handle activation confirmation
  const handleActivationConfirm = (onSubmit: () => void) => {
    // Track this agent as recently activated
    if (inactiveAgentInfo) {
      recentlyActivatedAgentsRef.current.add(inactiveAgentInfo.id);

      // Clear the tracking after 2 seconds
      setTimeout(() => {
        recentlyActivatedAgentsRef.current.delete(inactiveAgentInfo.id);
      }, 2000);
    }

    onSubmit();
  };

  // Function to show activation dialog
  const showActivationDialog = (agentInfo: InactiveAgentInfo) => {
    setInactiveAgentInfo(agentInfo);
    setShowAgentActivationDialog(true);
  };

  return {
    // States
    showAgentActivationDialog,
    setShowAgentActivationDialog,
    inactiveAgentInfo,

    // Functions
    checkForInactiveAgents,
    handleActivationConfirm,
    showActivationDialog,
  };
}
