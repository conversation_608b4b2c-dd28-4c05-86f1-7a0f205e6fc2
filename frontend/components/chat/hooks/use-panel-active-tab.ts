import { ChatAgentSearchParams } from '@/features/agent/models/agent.type';
import { useParseSearchParams } from '@/hooks/use-parse-search-params';
import { updateLRUCache } from '@/utils/object';
import { useLocalStorage } from 'usehooks-ts';

import { EConversationTabValue } from '../models/chat.enum';

export const usePanelActiveTab = () => {
  const searchParams = useParseSearchParams<ChatAgentSearchParams>();

  const currentConversationIdOrNew =
    searchParams.conversationId ?? 'new-conversation';
  const [activeTab, setActiveTab] = useLocalStorage<
    Record<string, EConversationTabValue>
  >('canvas-active-tab', {
    [currentConversationIdOrNew]: EConversationTabValue.Resource,
  });

  const setCurrentTab = (tab: EConversationTabValue) => {
    setActiveTab((prev) =>
      updateLRUCache(prev, currentConversationIdOrNew, tab),
    );
  };

  const setConversationActiveTab = (
    conversationId: string,
    tab: EConversationTabValue,
  ) => {
    setActiveTab((prev) => updateLRUCache(prev, conversationId, tab));
  };

  const currentTab = activeTab[currentConversationIdOrNew];

  return { setCurrentTab, currentTab, setConversationActiveTab };
};
