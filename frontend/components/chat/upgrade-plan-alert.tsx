import { UpgradePlanButtonLink } from '@/features/subcriptions/components/upgrade-plan-button-link';
import { AlertTriangleIcon } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '../ui/alert';

export const UpgradePlanAlert = () => {
  return (
    <Alert variant="destructive">
      <AlertTriangleIcon />
      <AlertTitle>Out of quota</AlertTitle>
      <AlertDescription>
        You’ve reached your usage limit. Please upgrade your plan to continue
        chatting without interruption.
      </AlertDescription>
      <div className="mt-4">
        <UpgradePlanButtonLink />
      </div>
    </Alert>
  );
};
