import { SchemaShortcutPublic } from '@/openapi-ts/gens';

import { getIncompleteAtCursor } from './mention-highlighting';

export interface ShortcutSelectParams {
  textareaRef: React.RefObject<HTMLTextAreaElement | null>;
  localValue: string;
  setLocalValue: (value: string) => void;
  onChange: (value: string) => void;
  setShowShortcutMention: (show: boolean) => void;
}

// Handle shortcut selection - insert /slug and highlight it
export const handleShortcutSelect = (
  shortcut: SchemaShortcutPublic,
  params: ShortcutSelectParams,
) => {
  const {
    textareaRef,
    localValue,
    setLocalValue,
    onChange,
    setShowShortcutMention,
  } = params;

  if (!textareaRef.current) return;

  const cursorPos = textareaRef.current.selectionStart;
  const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

  if (mentionInfo && mentionInfo.type === 'shortcut') {
    const textBeforeMention = localValue.substring(0, mentionInfo.startIndex);
    const textAfterCursor = localValue.substring(cursorPos);

    // Insert the /slug (it will be automatically colored by the mention highlighting system)
    const slugText = `/${shortcut.slug}`;
    const newText = textBeforeMention + slugText + textAfterCursor;

    setLocalValue(newText);
    onChange(newText);
    setShowShortcutMention(false);

    // Position cursor after the slug
    setTimeout(() => {
      if (textareaRef.current) {
        const newCursorPos = mentionInfo.startIndex + slugText.length;
        textareaRef.current.focus();
        textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
      }
    }, 0);
  }
};
