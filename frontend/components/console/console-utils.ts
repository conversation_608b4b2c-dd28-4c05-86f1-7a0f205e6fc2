import { ToolCall as OriginalToolCall } from '@/components/chat/types';
import { parseTimestamp } from '@/lib/date-utils';

import {
  extractErrorInfo,
  formatJSONOutput,
  isErrorOutput,
  isJSONOutput,
} from './bash-highlighter';
import { TERMINAL_CONFIG } from './config';

// ===== TYPE DEFINITIONS =====
export interface ProcessedToolCall {
  id: string;
  name: string;
  status: string;
  script?: string;
  output?: string;
  reasoning: string;
  timestamp?: string;
}

interface OutputData {
  outputLines: string[];
  errorInfo: {
    hasError: boolean;
    errorText: string;
    returnCode?: number;
  } | null;
  isJSON: boolean;
}

interface WrapOptions {
  preserveBlankLines: boolean;
  addContinuation: boolean;
  continuationIndent: string;
}

// ===== PURE UTILITY FUNCTIONS =====

/**
 * Generic line wrapping utility
 */
const wrapLines = (
  text: string,
  maxLineLength: number,
  options: WrapOptions,
): string[] => {
  if (!text) return [];

  const lines = text.split('\n');
  const result: string[] = [];

  lines.forEach((line) => {
    const trimmedLine = line.trim();

    if (!trimmedLine) {
      if (options.preserveBlankLines) result.push('');
      return;
    }

    if (line.length <= maxLineLength) {
      result.push(line);
      return;
    }

    const words = line.split(' ');
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;

      if (testLine.length <= maxLineLength) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          result.push(currentLine + (options.addContinuation ? ' \\' : ''));
          currentLine = options.continuationIndent + word;
        } else {
          result.push(word);
        }
      }
    }

    if (currentLine) {
      result.push(currentLine);
    }
  });

  return result;
};

/**
 * Format bash commands with continuation characters and preserve indentation
 */
export const formatBashCommand = (
  command: string,
  maxLineLength: number = 80,
): string[] => {
  if (!command) return [];

  let processedCommand = command;
  if (command.trim().startsWith('#!/')) {
    processedCommand = '\n' + command;
  }

  const lines = processedCommand.split('\n');
  const result: string[] = [];

  lines.forEach((line, lineIndex) => {
    const trimmedLine = line.trim();

    if (!trimmedLine) {
      result.push('');
      return;
    }

    // Preserve original indentation for lines starting with # (comments/headers)
    if (trimmedLine.startsWith('#')) {
      result.push(line);
      return;
    }

    // Check if the previous line ended with a continuation character
    const prevLine = lineIndex > 0 ? lines[lineIndex - 1].trim() : '';
    const isPreviousLineContinuation = prevLine.endsWith('\\');

    // Determine if this is a continuation line
    const isContinuationLine =
      isPreviousLineContinuation ||
      line.startsWith(' ') ||
      line.startsWith('\t');
    const isFirstLine = result.length === 0 || result[result.length - 1] === '';

    let formattedLine: string;

    if (isContinuationLine && !isFirstLine) {
      // This is a continuation line - apply tab indentation
      formattedLine = '\t' + trimmedLine;
    } else {
      // This is the start of a new command - no extra indentation
      formattedLine = trimmedLine;
    }

    // Handle line wrapping if needed
    if (formattedLine.length <= maxLineLength) {
      result.push(formattedLine);
    } else {
      // For long lines, split commands by semicolon first
      const commands = trimmedLine.split(';').filter((cmd) => cmd.trim());

      commands.forEach((singleCommand, cmdIndex) => {
        if (cmdIndex > 0) result.push('');

        const baseIndent = isContinuationLine && !isFirstLine ? '\t' : '';
        const indentedCommand = baseIndent + singleCommand.trim();

        const wrapped = wrapLines(indentedCommand, maxLineLength, {
          preserveBlankLines: false,
          addContinuation: true,
          continuationIndent: baseIndent + '\t',
        });
        result.push(...wrapped);
      });
    }
  });

  return result;
};

/**
 * Format output text with proper line breaks
 */
const formatOutputText = (
  output: string,
  maxLineLength: number = 80,
): string[] => {
  if (!output) return [];

  let processedOutput = output;
  if (output.trim() && !output.startsWith('\n') && !output.startsWith(' ')) {
    processedOutput = '\n' + output;
  }

  return wrapLines(processedOutput, maxLineLength, {
    preserveBlankLines: true,
    addContinuation: false,
    continuationIndent: '',
  });
};

/**
 * Calculate progress percentage and index from mouse position
 */
export const calculateProgressFromMouse = (
  clientX: number,
  progressBarRef: React.RefObject<HTMLDivElement | null>,
  totalItems: number,
): number | null => {
  if (!progressBarRef.current || totalItems === 0) return null;

  const rect = progressBarRef.current.getBoundingClientRect();
  const mouseX = clientX - rect.left;
  const percentage = Math.max(0, Math.min(1, mouseX / rect.width));

  // Calculate index based on percentage
  // The visual progress uses currentIndex / (totalItems - 1) for even distribution
  const newIndex =
    totalItems > 1 ? Math.round(percentage * (totalItems - 1)) : 0;

  // Ensure the index is within bounds
  return Math.max(0, Math.min(totalItems - 1, newIndex));
};

// ===== DATA PROCESSING FUNCTIONS =====

/**
 * Process tool call output data
 */
const processToolCallOutput = (
  toolCall: OriginalToolCall,
): ProcessedToolCall => {
  const args = toolCall.arguments || {};
  const script = args.script;

  let console_output = '';
  let hasError = false;

  try {
    const output =
      typeof toolCall.output === 'string'
        ? JSON.parse(toolCall.output)
        : toolCall.output;

    if (isErrorOutput(output)) {
      hasError = true;
      const errorInfo = extractErrorInfo(output);
      console_output = errorInfo.errorText;
    } else {
      console_output = output.stdout || '';
    }
  } catch {
    console_output =
      typeof toolCall.output === 'string'
        ? toolCall.output
        : 'Error processing output';
  }

  const reasoning = toolCall.reasoning || '';

  return {
    id: toolCall.id,
    name: toolCall.name,
    status: hasError ? 'error' : toolCall.status,
    script: script as string,
    output: console_output,
    reasoning: reasoning,
    timestamp: toolCall.createAt?.toISOString(),
  };
};

/**
 * Process output data and determine type/formatting
 */
export const processOutputData = (
  currentToolCall: ProcessedToolCall,
  toolCalls: OriginalToolCall[],
  responsiveLineLength: number,
): OutputData => {
  const rawOutput = currentToolCall.output || '';
  const isError = currentToolCall.status === 'error';
  const isJSON = !isError && isJSONOutput(rawOutput);

  let outputLines: string[] = [];
  let errorInfo: {
    hasError: boolean;
    errorText: string;
    returnCode?: number;
  } | null = null;

  if (isError) {
    const originalFromList = toolCalls.find(
      (tc) => tc.id === currentToolCall.id,
    );
    if (originalFromList?.output) {
      try {
        const parsedOutput =
          typeof originalFromList.output === 'string'
            ? JSON.parse(originalFromList.output)
            : originalFromList.output;
        errorInfo = extractErrorInfo(parsedOutput);

        // Display stdout as normal output, stderr as error
        if (parsedOutput.stdout && parsedOutput.stdout.trim()) {
          outputLines = formatOutputText(
            parsedOutput.stdout,
            responsiveLineLength,
          );
        } else {
          outputLines = [];
        }
      } catch {
        errorInfo = extractErrorInfo(originalFromList.output);
        outputLines = [];
      }
    } else {
      outputLines = [];
      errorInfo = { hasError: true, errorText: rawOutput };
    }
  } else if (isJSON) {
    outputLines = formatJSONOutput(rawOutput, responsiveLineLength);
  } else {
    outputLines = formatOutputText(rawOutput, responsiveLineLength);
  }

  return { outputLines, errorInfo, isJSON: isJSON && !isError };
};

/**
 * Process and filter tool calls to only show script-related tools
 */
export const processScriptToolCalls = (
  toolCalls: OriginalToolCall[],
): ProcessedToolCall[] => {
  return toolCalls
    .filter((toolCall) =>
      TERMINAL_CONFIG.toolCalls.scriptKeywords.some((keyword) =>
        toolCall.name.includes(keyword),
      ),
    )
    .map(processToolCallOutput)
    .sort((a, b) => {
      const dateA = parseTimestamp(a.timestamp);
      const dateB = parseTimestamp(b.timestamp);
      return (dateA?.getTime() || 0) - (dateB?.getTime() || 0);
    });
};

/**
 * Auto-scroll helper function
 */
export const scrollToBottom = (
  scrollAreaRef: React.RefObject<HTMLDivElement | null>,
) => {
  if (scrollAreaRef.current) {
    const scrollContainer = scrollAreaRef.current.querySelector(
      '[data-radix-scroll-area-viewport]',
    );
    if (scrollContainer) {
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }
};
