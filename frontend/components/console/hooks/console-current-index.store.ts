import { Dispatch, SetStateAction } from 'react';

import { create } from 'zustand';

export const useConsoleCurrentIndexStore = create<{
  currentIndex: number;
  setCurrentIndex: Dispatch<SetStateAction<number>>;
}>((set) => ({
  currentIndex: 0,
  setCurrentIndex: (value) => {
    if (typeof value === 'function') {
      set((state) => ({ currentIndex: value(state.currentIndex) }));
    } else {
      set({ currentIndex: value });
    }
  },
}));
