import { useCallback, useEffect, useRef, useState } from 'react';

interface UseConsoleScrollProps {
  isLoading?: boolean;
  itemCount: number;
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
}

// Reusable auto-scroll hook for the Console tab, adapted from chat useScrollHandling
export function useConsoleScroll({
  isLoading,
  itemCount,
  scrollAreaRef,
}: UseConsoleScrollProps) {
  const scrollContainerRef = useRef<HTMLElement | null>(null);
  const lastProcessedCountRef = useRef<number>(0);
  const userHasScrolledRef = useRef<boolean>(false);
  const autoScrollRef = useRef<boolean>(true);
  const rafRef = useRef<number>(undefined);
  const userScrollTimeoutRef = useRef<NodeJS.Timeout>(undefined);

  // Local UI state (optional for future extensions)
  const [isNearBottom, setIsNearBottom] = useState(true);

  const getScrollContainer = useCallback(() => {
    if (!scrollContainerRef.current && scrollAreaRef.current) {
      scrollContainerRef.current = scrollAreaRef.current.querySelector(
        '[data-radix-scroll-area-viewport]',
      ) as HTMLElement;
    }
    return scrollContainerRef.current;
  }, [scrollAreaRef]);

  const isAtBottom = useCallback(() => {
    const el = getScrollContainer();
    if (!el) return true;
    const distanceFromBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
    return distanceFromBottom <= 50; // threshold
  }, [getScrollContainer]);

  const scrollToBottom = useCallback(
    (behavior: ScrollBehavior = 'smooth', force: boolean = false) => {
      const el = getScrollContainer();
      if (!el) return;

      if (!force && userHasScrolledRef.current && behavior === 'smooth') {
        return;
      }

      const target = el.scrollHeight - el.clientHeight;

      if (behavior === 'auto') {
        if (rafRef.current) cancelAnimationFrame(rafRef.current);
        rafRef.current = requestAnimationFrame(() => {
          el.scrollTop = target;
        });
      } else {
        el.scrollTo({ top: target, behavior });
      }

      autoScrollRef.current = true;
      userHasScrolledRef.current = false;
    },
    [getScrollContainer],
  );

  // Attach scroll listener to respect user scroll and toggle auto-scroll
  useEffect(() => {
    const el = getScrollContainer();
    if (!el) return;

    let lastTop = el.scrollTop;
    let ticking = false;

    const onScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      const currentTop = target.scrollTop;
      const distanceFromBottom =
        target.scrollHeight - currentTop - target.clientHeight;
      const nearBottom = distanceFromBottom < 50;
      setIsNearBottom(nearBottom);

      if (Math.abs(currentTop - lastTop) > 5) {
        if (currentTop < lastTop - 5) {
          userHasScrolledRef.current = true;
          autoScrollRef.current = false;
          clearTimeout(userScrollTimeoutRef.current);
          userScrollTimeoutRef.current = setTimeout(() => {
            if (isAtBottom()) {
              userHasScrolledRef.current = false;
              autoScrollRef.current = true;
            }
          }, 1000);
        } else if (nearBottom) {
          clearTimeout(userScrollTimeoutRef.current);
          userHasScrolledRef.current = false;
          autoScrollRef.current = true;
        }
      }

      lastTop = currentTop;
    };

    const optimized = (e: Event) => {
      if (!ticking) {
        requestAnimationFrame(() => {
          onScroll(e);
          ticking = false;
        });
        ticking = true;
      }
    };

    el.addEventListener('scroll', optimized, { passive: true });

    // Initial bottom alignment
    requestAnimationFrame(() => scrollToBottom('auto', true));

    return () => {
      el.removeEventListener('scroll', optimized);
      clearTimeout(userScrollTimeoutRef.current);
      if (rafRef.current) cancelAnimationFrame(rafRef.current);
    };
  }, [getScrollContainer, scrollToBottom, isAtBottom]);

  // React to item count changes (new console entries)
  useEffect(() => {
    if (itemCount > lastProcessedCountRef.current) {
      if (autoScrollRef.current || isAtBottom()) {
        requestAnimationFrame(() => scrollToBottom('smooth'));
      }
    }
    lastProcessedCountRef.current = itemCount;
  }, [itemCount, scrollToBottom, isAtBottom]);

  // Continuous scroll during streaming
  useEffect(() => {
    if (isLoading && (autoScrollRef.current || isAtBottom())) {
      const id = setInterval(() => {
        if (autoScrollRef.current || isAtBottom()) {
          scrollToBottom('auto');
        }
      }, 100);
      return () => clearInterval(id);
    }
  }, [isLoading, scrollToBottom, isAtBottom]);

  return { scrollToBottom, isNearBottom };
}
