import { useEffect, useMemo } from 'react';

import { ToolCall } from '@/components/chat/types';

import { processScriptToolCalls, scrollToBottom } from '../console-utils';
import { useConsoleCurrentIndexStore } from './console-current-index.store';

/**
 * Hook for console state management and navigation
 */
export const useConsoleState = (
  toolCalls: ToolCall[],
  scrollAreaRef: React.RefObject<HTMLDivElement | null>,
) => {
  const { currentIndex, setCurrentIndex } = useConsoleCurrentIndexStore();

  // Process and filter tool calls
  const scriptToolCalls = useMemo(
    () => processScriptToolCalls(toolCalls),
    [toolCalls],
  );

  // Handle auto-scroll and index updates
  useEffect(() => {
    // Jump to latest when new tool calls are added
    if (scriptToolCalls.length > 0) {
      setCurrentIndex(scriptToolCalls.length - 1);
    }

    // Auto-scroll to bottom for any content changes
    scrollToBottom(scrollAreaRef);
  }, [scriptToolCalls.length, scrollAreaRef, setCurrentIndex]);

  const currentToolCall = scriptToolCalls[currentIndex];

  return {
    currentIndex,
    setCurrentIndex,
    scriptToolCalls,
    currentToolCall,
  };
};
