import { useEffect, useState } from 'react';

import {
  ProcessedToolCall,
  calculateProgressFromMouse,
} from '../console-utils';

/**
 * Hook for progress bar drag functionality
 */
export const useProgressBarDrag = (
  setCurrentIndex: (index: number) => void,
  progressBarRef: React.RefObject<HTMLDivElement | null>,
  scriptToolCalls: ProcessedToolCall[],
) => {
  const [isDragging, setIsDragging] = useState(false);
  const handleProgressBarClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const newIndex = calculateProgressFromMouse(
      event.clientX,
      progressBarRef,
      scriptToolCalls.length,
    );
    if (newIndex !== null) {
      setCurrentIndex(newIndex);
    }
  };

  const handleMouseDown = () => setIsDragging(true);
  const handleMouseUp = () => setIsDragging(false);

  // Add global mouse event listeners for dragging
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => {
      if (!isDragging) return;
      const newIndex = calculateProgressFromMouse(
        event.clientX,
        progressBarRef,
        scriptToolCalls.length,
      );
      if (newIndex !== null) setCurrentIndex(newIndex);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, progressBarRef, scriptToolCalls.length, setCurrentIndex]);

  return {
    handleProgressBarClick,
    handleMouseDown,
    handleMouseUp,
  };
};
