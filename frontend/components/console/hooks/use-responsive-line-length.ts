import { useEffect, useState } from 'react';

/**
 * Hook for responsive line length calculation
 */
export const useResponsiveLineLength = (
  containerRef: React.RefObject<HTMLDivElement | null>,
) => {
  const [lineLength, setLineLength] = useState(80);

  useEffect(() => {
    const calculateLineLength = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        // Estimate characters per line based on container width
        // Assuming monospace font with ~8px character width and accounting for padding
        const estimatedCharsPerLine = Math.floor((containerWidth - 32) / 8); // 32px for padding
        setLineLength(Math.max(40, Math.min(120, estimatedCharsPerLine))); // Min 40, max 120
      }
    };

    calculateLineLength();
    window.addEventListener('resize', calculateLineLength);

    return () => window.removeEventListener('resize', calculateLineLength);
  }, [containerRef]);

  return lineLength;
};
