'use client';

import { useCallback, useState } from 'react';
import { createElement } from 'react';

import { UsersService } from '@/client';
import { AlertModal } from '@/components/modal/alert-modal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useUserContext } from '@/features/user/provider/user-provider';
import { WORKSPACE_PROVIDER_CONFIG } from '@/features/workspaces/config/workspace-provider.config';
import { cn } from '@/lib/utils';
import { UserInfo } from '@/types/common.enum';
import clientCookie from 'js-cookie';
import { Check, ChevronsUpDown } from 'lucide-react';

interface WorkspaceSelectorProps {
  className?: string;
}

export function WorkspaceSelector({ className }: WorkspaceSelectorProps) {
  const { workspaces, currentWorkspace } = useUserContext();
  const [showAlert, setShowAlert] = useState(false);
  const [pendingWorkspaceId, setPendingWorkspaceId] = useState<string>();
  const [loading, setLoading] = useState(false);
  const currentWorkspaceId = clientCookie.get(UserInfo.WorkspacesID);

  // Switch workspace function
  const switchWorkspace = useCallback((workspace_id: string | undefined) => {
    setPendingWorkspaceId(workspace_id);
    setShowAlert(true);
  }, []);

  const handleConfirmSwitch = async () => {
    if (!pendingWorkspaceId) return;

    setLoading(true);
    clientCookie.set(UserInfo.WorkspacesID, pendingWorkspaceId);
    const response = await UsersService.switchWorkspace({
      workspaceId: pendingWorkspaceId,
    });

    clientCookie.set(UserInfo.AccessToken, response.access_token);
    window.location.href = '/';
    setLoading(false);
  };

  const provider =
    currentWorkspace?.provider &&
    WORKSPACE_PROVIDER_CONFIG.CONFIG[currentWorkspace.provider];

  return (
    <>
      <AlertModal
        isOpen={showAlert}
        onClose={() => setShowAlert(false)}
        onConfirm={handleConfirmSwitch}
        loading={loading}
        title="Switch Workspace"
        description="Are you sure you want to switch workspace? You will be redirected to the main page."
      />
      <div className={cn('group-data-[collapsible=icon]:hidden', className)}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="hover:bg-sidebar-accent flex w-full items-center justify-between gap-2 rounded-lg px-2 py-1.5 text-sm">
              <div className="flex items-center gap-2 overflow-hidden">
                <div className="shrink-0">
                  {provider &&
                    createElement(provider.icon, { className: 'size-4' })}
                </div>
                <span className="max-w-[60px] truncate font-medium sm:max-w-[200px]">
                  {currentWorkspace?.name || 'Select workspace'}
                </span>
              </div>
              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-70" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 space-y-2" align="start">
            {workspaces.map((workspace) => (
              <DropdownMenuItem
                key={workspace.id}
                onClick={() => switchWorkspace(workspace.id)}
                className={cn(
                  workspace.id === currentWorkspaceId &&
                    'bg-primary/10 pointer-events-none',
                )}
              >
                <span className="flex w-full items-center gap-2">
                  {workspace.provider &&
                    createElement(
                      WORKSPACE_PROVIDER_CONFIG.CONFIG[workspace.provider].icon,
                      {
                        className: 'size-4',
                      },
                    )}
                  {workspace.name}
                  {workspace.id === currentWorkspaceId && (
                    <Check className="ml-2 h-4 w-4" />
                  )}
                </span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </>
  );
}
