import React from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';

interface BrokenContentAlertProps {
  type: 'card' | 'table' | 'chart';
  title?: string;
  description?: string;
}

const BrokenContentAlert: React.FC<BrokenContentAlertProps> = ({
  type,
  title,
  description,
}) => {
  const getTypeInfo = () => {
    switch (type) {
      case 'card':
        return {
          title: title || 'Invalid Card Data',
          description:
            description ||
            'The card data is incomplete or invalid. Missing title or value.',
        };
      case 'table':
        return {
          title: title || 'Table Data Missing',
          description: description || 'No table data available to display.',
        };
      case 'chart':
        return {
          title: title || 'Chart Data Missing',
          description: description || 'No chart data available to display.',
        };
      default:
        return {
          title: 'Content Unavailable',
          description: 'The content data is invalid or missing.',
        };
    }
  };

  const { title: alertTitle, description: alertDescription } = getTypeInfo();

  return (
    <div className="my-4 w-full max-w-md">
      <Alert variant="warning">
        <AlertTriangle />
        <AlertTitle>{alertTitle}</AlertTitle>
        <AlertDescription>{alertDescription}</AlertDescription>
      </Alert>
    </div>
  );
};

export default BrokenContentAlert;
