'use client';

import { useUserContext } from '@/features/user/provider/user-provider';
import { cn } from '@/lib/utils';

import { Heading } from '../ui/heading';

type Props = {
  className?: string;
};

export function Greeting({ className }: Props) {
  const { user } = useUserContext();

  return (
    <div className={cn('space-y-2', className)}>
      <Heading level={1} className="text-gradient">
        Hello {user.full_name},
      </Heading>

      <p className="text-muted-foreground text-xl">
        How can we help you today?
      </p>
    </div>
  );
}
