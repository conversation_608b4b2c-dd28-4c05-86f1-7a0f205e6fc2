'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import { useToggle } from 'usehooks-ts';

type ExpandableTextProps = {
  text: string;
  maxLines?: number;
  showMoreText?: string;
  showLessText?: string;
  buttonClassName?: string;
};

export function ExpandableText({
  text,
  maxLines = 2,
  showMoreText = 'Show more',
  showLessText = 'Show less',
  buttonClassName,
}: ExpandableTextProps) {
  const [isExpanded, toggleExpanded] = useToggle(false);

  // Don't show expand button if text is short enough
  const shouldShowButton = text.length > 150; // Rough estimate, could be made more sophisticated

  const lineClampClass = `line-clamp-${maxLines}`;

  return (
    <div>
      <div
        className={cn(
          'text-sm leading-relaxed',
          !isExpanded && shouldShowButton && lineClampClass,
        )}
        aria-expanded={isExpanded}
      >
        {text}
      </div>

      {shouldShowButton && (
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleExpanded}
          className={cn(
            'h-auto p-2 text-xs font-medium transition-colors',
            'text-muted-foreground hover:text-foreground px-0',
            buttonClassName,
          )}
          aria-label={
            isExpanded ? `${showLessText} text` : `${showMoreText} text`
          }
        >
          {isExpanded ? (
            <>
              <ChevronUpIcon className="mr-1 size-3" />
              {showLessText}
            </>
          ) : (
            <>
              <ChevronDownIcon className="mr-1 size-3" />
              {showMoreText}
            </>
          )}
        </Button>
      )}
    </div>
  );
}
