import { FC, createElement } from 'react';

import { cn } from '@/lib/utils';

import { Input, InputProps } from './input';

type InputIconPrefixProps = InputProps & {
  Icon: FC<{ className: string }>;
  containerClassName?: string;
};

const InputIconPrefix: FC<InputIconPrefixProps> = ({
  className,
  containerClassName,
  Icon,
  type,
  ref,
  ...props
}) => {
  return (
    <div className={cn('relative flex items-center', containerClassName)}>
      {createElement(Icon, {
        className: 'absolute left-2 z-10 size-4 text-muted-foreground',
      })}

      <Input
        type={type}
        ref={ref}
        {...props}
        className={cn('pl-8', className)}
      />
    </div>
  );
};
InputIconPrefix.displayName = 'InputIconPrefix';

export { InputIconPrefix };
