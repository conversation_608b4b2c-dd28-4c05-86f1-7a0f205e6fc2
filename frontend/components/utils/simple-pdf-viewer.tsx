'use client';

import React, { Props<PERSON><PERSON><PERSON><PERSON>dren, ReactElement, isValidElement } from 'react';

import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';

interface SimplePDFViewerProps extends PropsWithChildren {
  url: string;
  fileName?: string;
}

export function SimplePDFViewer({
  url,
  fileName,
  children,
}: SimplePDFViewerProps) {
  const handleOpenExternal = () => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const getTriggerChild = (): ReactElement => {
    if (isValidElement(children)) {
      const child = children as ReactElement<{
        onClick?: React.MouseEventHandler;
      }>;
      const handleClick: React.MouseEventHandler = (event) => {
        if (typeof child.props.onClick === 'function') {
          child.props.onClick(event);
        }
        handleOpenExternal();
      };

      return React.cloneElement(child, { onClick: handleClick });
    }

    if (children === null || children === undefined || children === false) {
      return (
        <Button variant="outline" size="sm" onClick={handleOpenExternal}>
          <FileText className="mr-1 size-4" />
          {fileName ? `View ${fileName}` : 'View'}
        </Button>
      );
    }

    return (
      <span
        role="button"
        onClick={handleOpenExternal}
        className="inline-flex cursor-pointer items-center gap-2"
      >
        {children}
      </span>
    );
  };

  return getTriggerChild();
}
