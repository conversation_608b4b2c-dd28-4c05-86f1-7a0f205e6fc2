/**
 * Global configuration for hardcoded tools
 * Following frontend best practices with centralized configuration
 */

interface HardcodedTool {
  id: string;
  name: string;
  shortName: string;
  displayName: string;
  description?: string;
}

/**
 * Hardcoded tools configuration
 * These tools are always available and don't require dynamic loading
 */
export const HARDCODED_TOOLS: readonly HardcodedTool[] = [
  {
    id: 'visualize',
    name: 'create-chart',
    shortName: 'visualize',
    displayName: 'Visualize',
    description: 'Generate and visualize charts from data',
  },
  {
    id: 'dashboard',
    name: 'dashboard',
    shortName: 'dashboard',
    displayName: 'Dashboard',
    description: 'Access dashboard views and analytics',
  },
  {
    id: 'report',
    name: 'report',
    shortName: 'report',
    displayName: 'Report',
    description: 'Generate reports and analytics',
  },
] as const;

/**
 * Tool name mapping for backwards compatibility and mention system
 * Maps tool names/variations to their short names used in mentions
 */
export const TOOL_NAME_MAPPING: Record<string, string> = HARDCODED_TOOLS.reduce(
  (mapping, tool) => {
    // Add primary mappings
    mapping[tool.name] = tool.shortName;
    mapping[tool.shortName] = tool.shortName;
    mapping[tool.id] = tool.shortName;

    // Add display name mapping
    mapping[tool.displayName.toLowerCase().replace(/\s+/g, '-')] =
      tool.shortName;

    return mapping;
  },
  {} as Record<string, string>,
);
