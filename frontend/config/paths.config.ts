import { z } from 'zod';

const PathsSchema = z.object({
  signOut: z.string().min(1),
  auth: z.object({
    signIn: z.string().min(1),
    signUp: z.string().min(1),
    forgotPassword: z.string().min(1),
  }),
  slack: z.object({
    complete: z.string().min(1),
  }),
  app: z.object({
    home: z.string().min(1),
    dashboard: z.string().min(1),

    // Workspaces
    workspaces: z.string().min(1),

    // Resources
    resources: z.string().min(1),
    resourceRecommendations: z.string().min(1),
    wellArchitectedAssessment: z.string().min(1),

    // Tasks

    // Tasks v1
    tasks: z.string().min(1),

    // Agents
    agents: z.string().min(1),
    agentDetail: z.function().args(z.string()).returns(z.string()),

    profile: z.string().min(1),
    subscription: z.string().min(1),
    billing: z.string().min(1),
    purchase: z.string().min(1),

    // Knowledge Base
    knowledgeBase: z.string().min(1),
    knowledgeBaseDetail: z.function().args(z.string()).returns(z.string()),

    // Alerts
    alerts: z.string().min(1),
    alertDetail: z.function().args(z.string()).returns(z.string()),

    // Connectors
    connectors: z.string().min(1),
    connectorDetail: z.function().args(z.string()).returns(z.string()),

    // Integrations
    integrations: z.string().min(1),
  }),
  share: z.object({
    shareDetail: z.function().args(z.string()).returns(z.string()),
  }),
});

const pathsConfig = PathsSchema.parse({
  signOut: '/sign-out',
  auth: {
    signIn: '/auth/login',
    signUp: '/auth/signup',
    forgotPassword: '/auth/forgot-password',
  },
  slack: {
    complete: '/slack-redirect/complete',
  },
  app: {
    home: '/',
    dashboard: '/dashboard',

    // Workspaces
    workspaces: '/workspaces',

    // Resources
    resources: '/resources',

    // Recommendations
    resourceRecommendations: '/resources/recommendations',
    wellArchitectedAssessment: '/resources/well-architected-assessment',

    // Tasks

    // Tasks v1
    tasks: '/tasks',

    agents: '/agents',
    agentDetail: (id: string) => `/agents/${id}`,

    // Connectors
    connectors: '/connectors',
    connectorDetail: (id: string) => `/connectors/${id}`,

    profile: '/profile',
    subscription: '/subscription',
    billing: '/billing',
    purchase: '/purchase',

    // Knowledge Base
    knowledgeBase: '/kb',
    knowledgeBaseDetail: (id: string) => `/kb/${id}`,

    // Alerts
    alerts: '/alerts',
    alertDetail: (id: string) => `/alerts/${id}`,

    // Integrations
    integrations: '/integrations',
  },
  share: {
    shareDetail: (id: string) => `/share/${id}`,
  },
} satisfies z.infer<typeof PathsSchema>);

export default pathsConfig;
