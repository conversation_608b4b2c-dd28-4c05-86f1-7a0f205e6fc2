import { Dispatch, SetStateAction } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import { WithTooltip } from '@/components/ui/tooltip';
import { AgentType } from '@/openapi-ts/gens';
import { filter } from 'lodash';

import { agentQuery } from '../hooks/agent.query';

type Props = {
  setInputValue: Dispatch<SetStateAction<string>>;
};

export function QuickSelectAgents({ setInputValue }: Props) {
  const { data, isLoading } = agentQuery.query.useList();

  if (data) {
    const activeConversationalAgents = filter(data.data, {
      type: AgentType.conversation_agent,
      is_active: true,
    });
    return (
      <div className="flex gap-2">
        {activeConversationalAgents.map((agent) => (
          <WithTooltip
            key={agent.id}
            tooltip={
              <div>
                <p className="text-sm font-bold">
                  {agent.title} - {agent.role}
                </p>
                <p className="line-clamp-4 text-xs">{agent.instructions}</p>
              </div>
            }
            contentClassName="max-w-xs"
          >
            <Avatar
              className="size-8 hover:scale-120"
              onClick={() =>
                setInputValue((v) => (v + ` @${agent.alias}`).trim())
              }
            >
              <AvatarImage src={`/avatars/${agent.alias}.webp`} />
              <AvatarFallback>{agent.alias}</AvatarFallback>
            </Avatar>
          </WithTooltip>
        ))}
      </div>
    );
  }

  if (isLoading) {
    return <Skeleton className="h-10 w-20" />;
  }
}
