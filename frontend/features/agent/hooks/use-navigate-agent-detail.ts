import { useRouter } from 'next/navigation';

import pathsConfig from '@/config/paths.config';
import { useUserContext } from '@/features/user/provider/user-provider';
import { pickBy } from 'lodash';

import { ChatAgentSearchParams } from '../models/agent.type';

export const useNavigateAgentDetail = () => {
  const router = useRouter();
  const { agentId } = useUserContext();

  return (searchParams: ChatAgentSearchParams) => {
    router.push(agentDetailUrl(agentId, searchParams));
  };
};

export const agentDetailUrl = (
  agentId: string,
  searchParams: ChatAgentSearchParams,
) => {
  const params = new URLSearchParams(pickBy(searchParams));

  return `${pathsConfig.app.agentDetail(agentId)}?${params.toString()}`;
};
