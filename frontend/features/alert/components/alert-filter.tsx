'use client';

import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { useFormFilter } from '@/hooks/use-form-filter';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';

import { alertSeverityConfig } from '../config/alert-severity.config';
import { alertStatusConfig } from '../config/alert-status.config';
import { AlertQueryParams } from '../models/alert.type';

type FormFilter = WithPaginationDefaults<AlertQueryParams>;

type Props = {
  defaultValues: FormFilter;
};

export function AlertFilter({ defaultValues }: Props) {
  const { form, onSubmit } = useFormFilter<FormFilter>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    // debounceField: 'name',
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        {/* <FormField
      control={control}
      name="name"
      render={({ field }) => (
        <FormItem className="grow md:max-w-lg">
          <FormControl>
            <Input
              placeholder={`Search resource by name...`}
              className={'w-full'}
              {...field}
              value={field.value ?? ''}
            />
          </FormControl>
        </FormItem>
      )}
    /> */}
        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <FormField
            control={control}
            name="severity"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    options={alertSeverityConfig.LIST}
                    {...field}
                    name="severity"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete options={alertStatusConfig.LIST} {...field} />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </form>
    </Form>
  );
}
