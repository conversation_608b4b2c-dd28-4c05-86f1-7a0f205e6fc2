'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Heading } from '@/components/ui/heading';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertStatus } from '@/openapi-ts/gens';
import { BarChart3Icon, TrendingUpIcon } from 'lucide-react';

import { alertStatusConfig } from '../config/alert-status.config';
import { alertQuery } from '../hooks/alert.query';

export function AlertSummary() {
  const { data, isLoading, error } = alertQuery.query.useStatusSummary();

  if (data) {
    const { status_counts, total } = data;

    return (
      <div className="space-y-2">
        {/* Header Section */}
        <div className="flex items-center justify-between">
          <Heading level={4}>Alert Status Overview</Heading>

          <Badge variant="secondary">
            <div className="flex items-center gap-2">
              <TrendingUpIcon className="text-muted-foreground size-4" />
              <span className="text-muted-foreground text-sm font-medium">
                Total
              </span>
              <span className="text-lg font-bold">
                {total.toLocaleString()}
              </span>
            </div>
          </Badge>
        </div>

        {/* Status Cards Grid */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {alertStatusConfig.LIST.map((status) => {
            const count = status_counts[status.value];
            return (
              <StatusSummaryCard
                key={status.value}
                status={status.value}
                count={count}
                total={total}
              />
            );
          })}
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <BarChart3Icon className="text-muted-foreground size-5" />
          <h2 className="text-lg font-semibold">Alert Status Overview</h2>
        </div>
        <StatusSummarySkeletons />
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive/20 bg-destructive/5">
        <CardContent className="p-6">
          <div className="text-destructive flex items-center gap-2">
            <BarChart3Icon className="size-5" />
            <p className="font-medium">Failed to load alert summary</p>
          </div>
          <p className="text-muted-foreground mt-2 text-sm">
            Unable to fetch alert status data. Please try refreshing the page.
          </p>
        </CardContent>
      </Card>
    );
  }
}

interface StatusSummaryCardProps {
  status: AlertStatus;
  count: number;
  total: number;
  className?: string;
}

function StatusSummaryCard({
  status,
  count,
  total,
  className,
}: StatusSummaryCardProps) {
  const config = alertStatusConfig.CONFIG[status];
  const percentage = total > 0 ? (count / total) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{config.label}</CardTitle>
        <config.icon className="text-muted-foreground size-4" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count.toLocaleString()}</div>
        <p className="text-muted-foreground text-xs">
          {percentage.toFixed(1)}% of total alerts
        </p>
      </CardContent>
    </Card>
  );
}

function StatusSummarySkeletons() {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="size-4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="mb-1 h-8 w-12" />
            <Skeleton className="h-3 w-20" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
