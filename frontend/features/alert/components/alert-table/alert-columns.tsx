import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExpandableText } from '@/components/ui/common/expandable-text';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { SchemaAlertResponse } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import { Trash2Icon } from 'lucide-react';

import { alertSeverityConfig } from '../../config/alert-severity.config';
import { ConfirmDeleteAlert } from '../confirm-delete-alert';
import { UpdateAlertStatusDropdown } from '../update-alert-status-dropdown';

export const alertColumns: ColumnDef<SchemaAlertResponse>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    meta: {
      className: 'min-w-[280px]',
    },
    cell: ({ row }) => {
      const { title, id } = row.original;

      return (
        <Link
          href={pathsConfig.app.alertDetail(id)}
          className="text-primary hover:underline"
        >
          {title}
        </Link>
      );
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const { description } = row.original;

      return <ExpandableText text={description} />;
    },
    meta: {
      className: 'min-w-[300px]',
    },
  },
  {
    accessorKey: 'severity',
    header: 'Severity',
    cell: ({ row }) => {
      const { severity } = row.original;
      const { label, variant } = alertSeverityConfig.CONFIG[severity];

      return <Badge variant={variant}>{label}</Badge>;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const { id, status } = row.original;

      return <UpdateAlertStatusDropdown status={status} alertId={id} />;
    },
  },
  {
    header: 'Created At',
    accessorKey: 'created_at',
    cell: renderCellToFullDateTime,
    meta: {
      className: 'text-center whitespace-nowrap max-w-[140px]',
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated At',
    cell: renderCellToFullDateTime,
    meta: {
      className: 'text-center whitespace-nowrap max-w-[140px]',
    },
  },
  {
    header: 'Actions',
    cell: ({ row }) => {
      const { id } = row.original;

      return (
        <div className="flex items-center justify-end">
          <ConfirmDeleteAlert alertId={id}>
            <WithTooltip tooltip="Delete">
              <Button variant="outline" size="icon">
                <Trash2Icon className="size-4" />
              </Button>
            </WithTooltip>
          </ConfirmDeleteAlert>
        </div>
      );
    },
    meta: {
      className: 'text-right whitespace-nowrap ',
    },
  },
];
