'use client';

import { If } from '@/components/ui/common/if';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import { TableUI } from '@/components/ui/table/table-ui';
import { alertQuery } from '@/features/alert/hooks/alert.query';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { AlertQueryParams } from '../../models/alert.type';
import { alertColumns } from './alert-columns';

export function AlertTable({
  searchParams,
}: {
  searchParams: WithPaginationDefaults<AlertQueryParams>;
}) {
  const { data, isLoading, isRefetching } =
    alertQuery.query.useList(searchParams);

  return (
    <If
      condition={data}
      fallback={
        <If condition={isLoading}>
          <DataTableSkeleton columnCount={alertColumns.length} />
        </If>
      }
    >
      {(data) => (
        <TableUI
          data={data.data}
          columns={alertColumns}
          meta={data.meta}
          isRefetching={isRefetching}
        />
      )}
    </If>
  );
}
