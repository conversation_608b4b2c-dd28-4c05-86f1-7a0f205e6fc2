'use client';

import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { alertQuery } from '../hooks/alert.query';

type Props = PropsWithChildren<{
  alertId: string;
}>;

export function ConfirmDeleteAlert({ children, alertId }: Props) {
  const { mutate, isPending } = alertQuery.mutation.useDelete(alertId);

  const onConfirm = (toggle: () => void) => {
    mutate(undefined, {
      onSuccess: toggle,
    });
  };

  return (
    <DeleteConfirmAlert
      title="Delete Alert"
      loading={isPending}
      onConfirm={onConfirm}
    >
      {children}
    </DeleteConfirmAlert>
  );
}
