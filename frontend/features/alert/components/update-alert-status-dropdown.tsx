import { Badge } from '@/components/ui/badge';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AlertStatus } from '@/openapi-ts/gens';
import { ChevronDownIcon } from 'lucide-react';

import { alertStatusConfig } from '../config/alert-status.config';
import { alertQuery } from '../hooks/alert.query';

type Props = {
  status: AlertStatus;
  alertId: string;
};

export const UpdateAlertStatusDropdown = ({ status, alertId }: Props) => {
  const { label, variant } = alertStatusConfig.CONFIG[status];

  const { mutate, isPending } = alertQuery.mutation.useUpdateStatus(alertId);

  const handleUpdateStatus = (value: string) => {
    const newValue = value as AlertStatus;

    mutate({ status: newValue });
  };

  return (
    <UpdateWrapper isPending={isPending} className="justify-start">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Badge variant={variant} className="gap-2">
            {label}
            <ChevronDownIcon className="size-4" />
          </Badge>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuRadioGroup
            value={status}
            onValueChange={handleUpdateStatus}
          >
            {alertStatusConfig.LIST.map((statusConfig) => (
              <DropdownMenuRadioItem
                key={statusConfig.value}
                value={statusConfig.value}
                disabled={statusConfig.value === status}
              >
                <Badge variant={statusConfig.variant}>
                  {statusConfig.label}
                </Badge>
              </DropdownMenuRadioItem>
            ))}
          </DropdownMenuRadioGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </UpdateWrapper>
  );
};
