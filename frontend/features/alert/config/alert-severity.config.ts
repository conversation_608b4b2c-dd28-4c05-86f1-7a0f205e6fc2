import { BadgeProps } from '@/components/ui/badge';
import { AlertSeverity } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';
import {
  AlertCircleIcon,
  AlertOctagonIcon,
  AlertTriangleIcon,
  InfoIcon,
} from 'lucide-react';
import { LucideIcon } from 'lucide-react';

export const alertSeverityConfig = createUtilityConfig({
  [AlertSeverity.CRITICAL]: {
    label: 'Critical',
    icon: AlertOctagonIcon,
    variant: 'destructive',
    notificationCardClassName: 'bg-destructive/10 border-destructive/30',
  },
  [AlertSeverity.HIGH]: {
    label: 'High',
    icon: AlertTriangleIcon,
    variant: 'warning',
    notificationCardClassName: 'bg-warning/20 border-warning/30',
  },
  [AlertSeverity.MEDIUM]: {
    label: 'Medium',
    icon: AlertTriangleIcon,
    variant: 'warning',
    notificationCardClassName: 'bg-warning/20 border-warning/30',
  },
  [AlertSeverity.LOW]: {
    label: 'Low',
    icon: AlertCircleIcon,
    variant: 'success',
    notificationCardClassName: 'bg-success/20 border-success/30',
  },
  [AlertSeverity.INFO]: {
    label: 'Info',
    icon: InfoIcon,
    variant: 'info',
    notificationCardClassName: 'bg-info/20 border-info/30',
  },
} satisfies Record<
  AlertSeverity,
  {
    label: string;
    icon: LucideIcon;
    variant: BadgeProps['variant'];
    notificationCardClassName: string;
  }
>);
