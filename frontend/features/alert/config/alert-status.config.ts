import { BadgeProps } from '@/components/ui/badge';
import { AlertStatus } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';
import {
  CheckCircle2Icon,
  CircleIcon,
  ClockIcon,
  XCircleIcon,
} from 'lucide-react';
import { LucideIcon } from 'lucide-react';

export const alertStatusConfig = createUtilityConfig({
  [AlertStatus.OPEN]: {
    label: 'Open',
    icon: XCircleIcon,
    variant: 'destructive',
  },
  [AlertStatus.ACKNOWLEDGED]: {
    label: 'Acknowledged',
    icon: ClockIcon,
    variant: 'warning',
  },
  [AlertStatus.RESOLVED]: {
    label: 'Resolved',
    icon: CheckCircle2Icon,
    variant: 'success',
  },
  [AlertStatus.CLOSED]: {
    label: 'Closed',
    icon: CircleIcon,
    variant: 'secondary',
  },
} satisfies Record<
  AlertStatus,
  { label: string; icon: LucideIcon; variant: BadgeProps['variant'] }
>);
