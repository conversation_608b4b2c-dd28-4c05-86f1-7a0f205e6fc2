import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { AlertQueryParams } from '../models/alert.type';
import { alertApi } from '../services/alert.api';

const alertQueryKeys = createQueryKeys('alerts', {
  statusSummary: {
    queryKey: null,
    queryFn: () => alertApi.statusSummary(),
  },

  list: (params: WithPaginationDefaults<AlertQueryParams>) => ({
    queryKey: [params],
    queryFn: () => alertApi.list(handleSkipOfPagination(params)),
  }),
});

const useStatusSummary = () => {
  return useQuery(alertQueryKeys.statusSummary);
};

const useList = (params: WithPaginationDefaults<AlertQueryParams>) => {
  return useQuery({
    ...alertQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useUpdateStatus = (alertId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: alertApi.detail(alertId).updateStatus,
    onSuccess: () => {
      toast.success('Alert status updated');
      queryClient.invalidateQueries({
        queryKey: alertQueryKeys.list._def,
      });
      queryClient.invalidateQueries({
        queryKey: alertQueryKeys.statusSummary.queryKey,
      });
    },
  });
};

const useDelete = (alertId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: alertApi.detail(alertId).delete,
    onSuccess: () => {
      toast.success('Alert deleted');
      queryClient.invalidateQueries({
        queryKey: alertQueryKeys.list._def,
      });
      queryClient.invalidateQueries({
        queryKey: alertQueryKeys.statusSummary.queryKey,
      });
    },
  });
};

export const alertQuery = {
  query: {
    useList,
    useStatusSummary,
  },
  mutation: {
    useUpdateStatus,
    useDelete,
  },
};
