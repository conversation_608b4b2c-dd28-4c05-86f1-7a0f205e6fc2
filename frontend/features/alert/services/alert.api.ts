import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsPatchRequestBodyDto } from '@/openapi-ts/utils.type';

import { AlertQueryParams } from '../models/alert.type';

export const alertApi = {
  list: (query: AlertQueryParams) =>
    fetchData(api.GET('/api/v1/alerts/', { params: { query } })),

  statusSummary: () => fetchData(api.GET('/api/v1/alerts/summary/status')),

  markAllAcknowledged: () => api.POST('/api/v1/alerts/mark-all-acknowledged'),

  detail: (alertId: string) => ({
    updateStatus: (
      body: PathsPatchRequestBodyDto<'/api/v1/alerts/{alert_id}/status'>,
    ) =>
      api.PATCH('/api/v1/alerts/{alert_id}/status', {
        body,
        params: {
          path: { alert_id: alertId },
        },
      }),

    delete: () =>
      api.DELETE('/api/v1/alerts/{alert_id}', {
        params: {
          path: { alert_id: alertId },
        },
      }),
  }),
};
