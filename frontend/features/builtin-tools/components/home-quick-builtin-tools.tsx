import { Dispatch, SetStateAction } from 'react';

import Image from 'next/image';

import { Button } from '@/components/ui/button';
import { WithTooltip } from '@/components/ui/tooltip';
import { HOME_QUICK_BUILTIN_TOOLS_CATEGORIES } from '@/features/builtin-tools/data/builtin-tools.constants';

type Props = {
  inputValue: string;
  setInputValue: Dispatch<SetStateAction<string>>;
};

export function HomeQuickBuiltinTools({ inputValue, setInputValue }: Props) {
  return (
    <>
      {HOME_QUICK_BUILTIN_TOOLS_CATEGORIES.map((builtinTool) => {
        const isSelected = inputValue.includes(builtinTool.prompt);

        return (
          <WithTooltip
            key={builtinTool.name}
            tooltip={
              <div className="space-y-2">
                <p className="text-base font-bold">{builtinTool.displayName}</p>

                <Image
                  src={builtinTool.imageSrc}
                  alt={builtinTool.displayName}
                  width={400}
                  height={0}
                  className="rounded-lg object-cover"
                />
              </div>
            }
            // contentClassName="text-card-foreground bg-card"
          >
            <Button
              onClick={() => {
                setInputValue(builtinTool.prompt);
              }}
              variant={isSelected ? 'outlinePrimary' : 'outline'}
              className="gap-2"
              disabled={isSelected}
              size="sm"
            >
              <builtinTool.icon className="size-4 shrink-0" />
              <p className="whitespace-nowrap">{builtinTool.displayName}</p>
            </Button>
          </WithTooltip>
        );
      })}
    </>
  );
}
