import {
  ChartBarIcon,
  CircleAlertIcon,
  FileTextIcon,
  LayoutDashboardIcon,
  LightbulbIcon,
  LucideIcon,
} from 'lucide-react';

export const HOME_QUICK_BUILTIN_TOOLS_CATEGORIES = [
  {
    name: 'dashboard',
    icon: LayoutDashboardIcon,
    displayName: 'Dashboard',
    prompt: '@Anna please build #dashboard to analyze EC2 instance utilization',
    imageSrc: '/images/builtin-tools/dashboard.png',
  },
  {
    name: 'report',
    icon: FileTextIcon,
    displayName: 'Report',
    prompt: `@Alex let's make a #report about Inspector assessment findings and remediation status`,
    imageSrc: '/images/builtin-tools/report.png',
  },
  {
    name: 'visual',
    icon: ChartBarIcon,
    displayName: 'Visual',
    prompt:
      '@Kai can you recommend node group adjustments and #visualize resource allocation?',
    imageSrc: '/images/builtin-tools/visual.png',
  },
  {
    name: 'push_alert',
    icon: CircleAlertIcon,
    displayName: 'Push Alert',
    prompt:
      '@Oliver please audit OpsWorks security settings, access controls and #alert for vulnerabilities',
    imageSrc: '/images/builtin-tools/alert.png',
  },
  {
    name: 'recommendation',
    icon: LightbulbIcon,
    displayName: 'Recommendation',
    prompt:
      '@Tony can you validate database encoding consistency across tables and #recommend remediation steps?',
    imageSrc: '/images/builtin-tools/recommendation.png',
  },
] satisfies {
  icon: LucideIcon;
  name: string;
  displayName: string;
  prompt: string;
  imageSrc: string;
}[];
