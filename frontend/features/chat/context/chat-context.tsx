import { ReactNode, createContext, useContext } from 'react';

import { RecommendationPublic } from '@/client/types.gen';
import { StreamingRecommendation } from '@/components/chat/autonomous/recommendation-table/types';
import { InterruptConfirmation } from '@/hooks/message-stream';
import {
  SchemaDashboard,
  SchemaMessagePlanList,
  SchemaReport,
} from '@/openapi-ts/gens';
import { Message, Session } from '@/types/chat';

export type ChatType = 'resource' | 'agent';

export interface ChatContextType {
  isLoadingMessages: boolean;
  chatType: ChatType;
  messages: Message[];
  onSendMessage: (
    message: string,
    attachmentIds?: string[],
    resourceId?: string,
  ) => void;
  isStreaming: boolean;
  currentSession?: Session;
  onStopStreaming?: () => void;
  confirmation?: InterruptConfirmation | null;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<
    React.SetStateAction<RecommendationPublic[]>
  >;
  resourceId?: string;
  currentReport?: SchemaReport | null;
  currentDashboard?: SchemaDashboard | null;
  currentRecommendations?: StreamingRecommendation[];
  conversationId?: string;
  thinkingContent?: string | null;
  planningContent: SchemaMessagePlanList | null;
  conversationResourceId?: string | null;
  selectedResourceId?: string | null;
  setSelectedResourceId: (resourceId: string | null) => void;
  hasReport?: boolean;
  hasDashboard?: boolean;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
  value: ChatContextType;
}

export function ChatProvider({ children, value }: ChatProviderProps) {
  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}
