import { useCallback, useEffect, useReducer, useRef, useState } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { ChatAgentSearchParams } from '@/features/agent/models/agent.type';
import { useMessageStream } from '@/hooks/use-autonomous-message-stream';
import { pickBy } from 'lodash';

interface ChatState {
  selectedConversation: string | null;
  mounted: boolean;
  initialMessageSent: boolean;
}

type ChatAction =
  | { type: 'SET_MOUNTED'; payload: boolean }
  | { type: 'SET_SELECTED_CONVERSATION'; payload: string | null }
  | { type: 'SET_INITIAL_MESSAGE_SENT'; payload: boolean };

const initialState: ChatState = {
  selectedConversation: null,
  mounted: false,
  initialMessageSent: false,
};

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_MOUNTED':
      return { ...state, mounted: action.payload };
    case 'SET_SELECTED_CONVERSATION':
      return { ...state, selectedConversation: action.payload };
    case 'SET_INITIAL_MESSAGE_SENT':
      return { ...state, initialMessageSent: action.payload };
    default:
      return state;
  }
};

interface AutonomousChatProps {
  searchParams: ChatAgentSearchParams;
}

export const useAutonomousChat = ({ searchParams }: AutonomousChatProps) => {
  const router = useRouter();
  const pathname = usePathname();

  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    selectedConversation: searchParams.conversationId || null,
  });

  // Only update the URL, don't update selectedConversation state
  // This prevents triggering the useEffect that clears streaming messages
  const handleConversationCreated = useCallback(
    (newConversationId: string) => {
      // update search params

      const newSearchParams: ChatAgentSearchParams = {
        ...searchParams,
        conversationId: newConversationId,
        initialMessage: undefined,
      };
      const newUrl = `${pathname}?${new URLSearchParams(pickBy(newSearchParams)).toString()}`;
      router.replace(newUrl, { scroll: false });
    },
    [pathname, router, searchParams],
  );

  const messageStream = useMessageStream(state.selectedConversation, {
    onConversationCreated: handleConversationCreated,
  });

  const {
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    currentRecommendations,
    thinkingContent,
    planningContent,
    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,
    isLoadingMessages,
  } = messageStream;

  // Selected resource state for immediate tab display
  const [selectedResourceId, setSelectedResourceId] = useState<string | null>(
    searchParams.resource_id || null,
  );

  const setMounted = useCallback((mounted: boolean) => {
    dispatch({ type: 'SET_MOUNTED', payload: mounted });
  }, []);

  const setSelectedConversation = useCallback(
    (conversationId: string | null) => {
      dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: conversationId });
    },
    [],
  );

  // Clear streaming messages when selected conversation changes to a different conversation
  // Use ref to track previous conversation ID to avoid clearing on streaming state changes
  const prevConversationRef = useRef<string | null>(null);

  useEffect(() => {
    const currentConversation = state.selectedConversation;
    const prevConversation = prevConversationRef.current;

    // Only clear messages if we're switching to a genuinely different conversation
    // (not just from null to a conversation during new chat creation)
    if (
      prevConversation !== null &&
      currentConversation !== null &&
      prevConversation !== currentConversation
    ) {
      clearStreamingMessages();
      // Clear selected resource when conversation changes
      setSelectedResourceId(null);
    }

    // Update the ref for next time
    prevConversationRef.current = currentConversation;
  }, [state.selectedConversation, clearStreamingMessages]);

  const setInitialMessageSent = useCallback((sent: boolean) => {
    dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: sent });
  }, []);

  // Clear all state for new chat
  const clearAllState = useCallback(() => {
    // Clear selected conversation
    dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: null });

    // Clear streaming messages and other state
    clearStreamingMessages();

    // Clear selected resource (unless preserving)

    const newResourceId =
      searchParams.resource_id || selectedResourceId || conversationResourceId;

    setSelectedResourceId(newResourceId);

    // Reset initial message sent flag
    dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: false });

    const newUrl = `${pathname}?${new URLSearchParams(searchParams).toString()}`;
    router.replace(newUrl, { scroll: false });
  }, [
    clearStreamingMessages,
    conversationResourceId,
    pathname,
    router,
    searchParams,
    selectedResourceId,
  ]);

  const clearAllStateRef = useRef(clearAllState);
  // const stopStreamRef = useRef(stopStream);

  //  Clear streaming messages when new chat is created
  useEffect(() => {
    if (searchParams.conversationId) return;

    clearAllStateRef.current();
    // if (!isStreaming) return;

    // stopStream();
  }, [searchParams.conversationId]);

  return {
    // State
    selectedConversation: state.selectedConversation,
    mounted: state.mounted,
    initialMessageSent: state.initialMessageSent,

    // Actions
    setMounted,
    setSelectedConversation,
    setInitialMessageSent,

    // Message stream
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    currentRecommendations,
    thinkingContent,
    planningContent,

    // MessagePublicList fields
    conversationResourceId,
    hasReport,
    hasDashboard,

    // Selected resource for immediate tab display
    selectedResourceId,
    setSelectedResourceId,

    // Loading state
    isLoadingMessages,
  };
};
