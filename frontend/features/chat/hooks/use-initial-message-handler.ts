import { startTransition, useCallback, useEffect } from 'react';

import { usePathname, useRouter } from 'next/navigation';

import { ChatAgentSearchParams } from '@/features/agent/models/agent.type';
import { useParseSearchParams } from '@/hooks/use-parse-search-params';

import { ChatContextType } from '../context/chat-context';

interface InitialMessageHandlerProps {
  mounted: boolean;
  initialMessage?: string;
  initialMessageSent: boolean;
  handleSendMessage: ChatContextType['onSendMessage'];
  setInitialMessageSent: (sent: boolean) => void;
}

export const useInitialMessageHandler = ({
  mounted,
  initialMessage,
  initialMessageSent,
  handleSendMessage,
  setInitialMessageSent,
}: InitialMessageHandlerProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useParseSearchParams<ChatAgentSearchParams>();

  const removeInitialMessageFromUrl = useCallback(() => {
    delete searchParams.initialMessage;
    const newUrl = `${pathname}?${new URLSearchParams(searchParams).toString()}`;
    router.replace(newUrl, { scroll: false });
    console.log('searchParams', newUrl);
  }, [pathname, router, searchParams]);

  useEffect(() => {
    if (!mounted) return;

    const sendInitialMessage = () => {
      if (!initialMessage) return;

      const timer = setTimeout(() => {
        // Check for stored message data from quick-generate
        let attachmentIds: string[] | undefined;
        let resourceId: string | undefined;
        try {
          const storedData = sessionStorage.getItem(
            'quick-generate-message-data',
          );
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            // Only use data if it was stored recently (within 5 minutes)
            if (
              parsedData.timestamp &&
              Date.now() - parsedData.timestamp < 5 * 60 * 1000
            ) {
              attachmentIds = parsedData.attachmentIds;
              resourceId = parsedData.resourceId;
            }
            // Clean up the stored data
            sessionStorage.removeItem('quick-generate-message-data');
          }
        } catch (error) {
          console.warn('Failed to retrieve message data:', error);
        }

        handleSendMessage(
          initialMessage,
          attachmentIds,
          resourceId ?? searchParams.resource_id,
        );
        setInitialMessageSent(true);

        startTransition(() => removeInitialMessageFromUrl());
      }, 600);

      return () => clearTimeout(timer);
    };

    // Send initial message if present and not already sent
    // Conversation will be created implicitly by the backend when message is sent
    if (initialMessage && !initialMessageSent) {
      return sendInitialMessage();
    }
  }, [
    handleSendMessage,
    initialMessage,
    initialMessageSent,
    mounted,
    removeInitialMessageFromUrl,
    searchParams.resource_id,
    setInitialMessageSent,
  ]);
};
