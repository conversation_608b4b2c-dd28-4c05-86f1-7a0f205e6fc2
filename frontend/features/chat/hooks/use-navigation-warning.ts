import { useCallback, useEffect, useRef } from 'react';

import { useChatContext } from '@/features/chat/context/chat-context';

interface NavigationWarningOptions {
  /**
   * Whether to enable navigation warnings during streaming.
   * @default true
   */
  enabled?: boolean;
  /**
   * Custom message to show in the browser's beforeunload dialog.
   * @default "You have an active conversation in progress. Are you sure you want to leave?"
   */
  beforeUnloadMessage?: string;
  /**
   * Callback fired when user attempts to navigate away during streaming.
   */
  onNavigationAttempt?: () => void;
  /**
   * Callback fired when user confirms navigation despite warning.
   */
  onNavigationConfirmed?: () => void;
}

/**
 * Hook that provides navigation warning functionality during active streaming.
 * Handles both browser navigation (back/forward/refresh/close) and programmatic navigation.
 */
export function useNavigationWarning(options: NavigationWarningOptions = {}) {
  const {
    enabled = true,
    beforeUnloadMessage = 'You have an active conversation in progress. Are you sure you want to leave?',
    onNavigationAttempt,
    onNavigationConfirmed,
  } = options;

  const { isStreaming } = useChatContext();
  const isStreamingRef = useRef(isStreaming);
  const onNavigationAttemptRef = useRef(onNavigationAttempt);
  const onNavigationConfirmedRef = useRef(onNavigationConfirmed);

  // Update refs when values change
  useEffect(() => {
    isStreamingRef.current = isStreaming;
  }, [isStreaming]);

  useEffect(() => {
    onNavigationAttemptRef.current = onNavigationAttempt;
  }, [onNavigationAttempt]);

  useEffect(() => {
    onNavigationConfirmedRef.current = onNavigationConfirmed;
  }, [onNavigationConfirmed]);

  // Handle browser navigation events (refresh, close, back/forward)
  useEffect(() => {
    if (!enabled) return;

    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      // Only show warning if streaming is active
      if (isStreamingRef.current) {
        // Call the navigation attempt callback
        onNavigationAttemptRef.current?.();

        // Set the return value to trigger browser warning
        event.preventDefault();
        event.returnValue = beforeUnloadMessage;
        return beforeUnloadMessage;
      }
    };

    // Handle page visibility changes (tab switching, minimizing)
    const handleVisibilityChange = () => {
      if (document.hidden && isStreamingRef.current) {
        // User switched away from tab during streaming
        // This doesn't prevent navigation but can be used for analytics
        onNavigationAttemptRef.current?.();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, beforeUnloadMessage]);

  /**
   * Programmatically check if navigation should be allowed.
   * Returns true if navigation is safe, false if it should be blocked.
   */
  const checkNavigationSafety = useCallback(() => {
    if (!enabled || !isStreaming) {
      return true;
    }

    // Call the navigation attempt callback
    onNavigationAttempt?.();

    // Return false to indicate navigation should be blocked
    return false;
  }, [enabled, isStreaming, onNavigationAttempt]);

  /**
   * Confirm navigation and allow it to proceed.
   * Should be called after user confirms they want to navigate despite active streaming.
   */
  const confirmNavigation = useCallback(() => {
    onNavigationConfirmed?.();
  }, [onNavigationConfirmed]);

  /**
   * Get the current streaming status for external components.
   */
  const getStreamingStatus = useCallback(() => {
    return {
      isStreaming,
      shouldWarn: enabled && isStreaming,
    };
  }, [enabled, isStreaming]);

  return {
    isStreaming,
    shouldWarnOnNavigation: enabled && isStreaming,
    checkNavigationSafety,
    confirmNavigation,
    getStreamingStatus,
  };
}
