import { useEffect, useRef } from 'react';

import { InterruptConfirmation } from '@/hooks/message-stream';

interface UrlManagerProps {
  conversationId?: string;
  setSelectedConversation: (id: string | null) => void;
  setInterruptConfirmation: (
    confirmation: InterruptConfirmation | null,
  ) => void;
}

export const useUrlManager = ({
  conversationId,
  setSelectedConversation,
  setInterruptConfirmation,
}: UrlManagerProps) => {
  // Use refs to store the latest callback functions
  const setSelectedConversationRef = useRef(setSelectedConversation);
  const setInterruptConfirmationRef = useRef(setInterruptConfirmation);

  // Update refs when the callbacks change
  useEffect(() => {
    setSelectedConversationRef.current = setSelectedConversation;
    setInterruptConfirmationRef.current = setInterruptConfirmation;
  }, [setSelectedConversation, setInterruptConfirmation]);

  // Handle conversationId changes
  useEffect(() => {
    if (conversationId) {
      setSelectedConversationRef.current(conversationId);
      setInterruptConfirmationRef.current(null);
    }
  }, [conversationId]);

  // Handle URL updates
  // useEffect(() => {
  //   if (selectedConversation && mounted) {
  //     const newUrl = buildUrlWithParams(window.location.pathname, {
  //       conversationId: selectedConversation,
  //       // Don't preserve initialMessage when updating conversation URL
  //       // It should only be present initially and removed after message is sent
  //     });
  //     router.replace(newUrl, { scroll: false });
  //   }
  // }, [selectedConversation, mounted, router]);
};
