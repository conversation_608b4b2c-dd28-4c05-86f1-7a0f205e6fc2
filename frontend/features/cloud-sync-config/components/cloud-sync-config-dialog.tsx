'use client';

import { <PERSON>actNode, createElement, useEffect, useMemo, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { builtinConnectionQuery } from '@/features/connection-v2/hooks/builtin-connection.query';
import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '@/features/resources/config/resource-type.config';
import {
  BuiltinConnectionType,
  SchemaBuiltinConnectionPublic,
  SchemaCloudSync<PERSON>onfig<PERSON><PERSON>,
  SchemaCloudSyncConfigPublic,
  SchemaCloudSyncConfigUpdate,
} from '@/openapi-ts/gens';
import {
  BarChart3,
  Cloud,
  CloudIcon,
  Cpu,
  Database,
  Globe,
  HardDrive,
  Loader2,
  MessageSquare,
  RefreshCw,
  Server,
  Settings,
} from 'lucide-react';
import { toast } from 'sonner';

import { cloudSyncConfigQuery } from '../hooks/cloud-sync-config.query';

interface CloudSyncConfigDialogProps {
  children: ReactNode;
  editConfig?: SchemaCloudSyncConfigPublic | null;
}

interface FormData {
  connection_id: string;
  include_stopped_resources: boolean;
  refresh_interval: number;
  selected_resources: string[];
  is_enabled: boolean;
}

interface FormErrors {
  refresh_interval?: string;
}

const DEFAULT_FORM_DATA: FormData = {
  connection_id: '',
  include_stopped_resources: false,
  refresh_interval: 30,
  selected_resources: [],
  is_enabled: true,
};

export const CloudSyncConfigDialog = ({
  children,
  editConfig,
}: CloudSyncConfigDialogProps) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<FormData>(DEFAULT_FORM_DATA);
  const [refreshIntervalInput, setRefreshIntervalInput] = useState('30');
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [selectedConnection, setSelectedConnection] =
    useState<SchemaBuiltinConnectionPublic | null>(null);

  // Mutations
  const { mutate: createConfig, isPending: isCreating } =
    cloudSyncConfigQuery.mutation.useCreate();
  const { mutate: updateConfig, isPending: isUpdating } =
    cloudSyncConfigQuery.mutation.useUpdate();
  const { mutate: triggerSync, isPending: isSyncing } =
    cloudSyncConfigQuery.mutation.useTriggerSync();

  // Queries
  const { data: connectionsResponse, isLoading: isLoadingConnections } =
    builtinConnectionQuery.query.useList();
  const { data: configsResponse } = cloudSyncConfigQuery.query.useList();
  const { data: resourceTypesResponse, isLoading: isLoadingResourceTypes } =
    cloudSyncConfigQuery.query.useResourceTypes({
      cloud_provider: getCloudProviderFromConnection(selectedConnection),
    });

  const isLoading = isCreating || isUpdating;
  const connections = useMemo(
    () => connectionsResponse?.data || [],
    [connectionsResponse?.data],
  );
  const cloudConnections = useMemo(
    () =>
      connections.filter(
        (conn) => conn.builtin_connection_type === BuiltinConnectionType.cloud,
      ),
    [connections],
  );

  const existingConfigs = useMemo(
    () => configsResponse || [],
    [configsResponse],
  );
  const resourceTypes = resourceTypesResponse?.resource_types || [];

  // Initialize form when editing or when dialog opens
  useEffect(() => {
    if (editConfig) {
      setFormData({
        connection_id: editConfig.connection_id,
        include_stopped_resources: editConfig.include_stopped_resources,
        refresh_interval: editConfig.refresh_interval,
        selected_resources: editConfig.selected_resources || [],
        is_enabled: editConfig.is_enabled,
      });
      setRefreshIntervalInput(editConfig.refresh_interval.toString());
      setFormErrors({});

      // Find and set the selected connection
      const connection = connections.find(
        (conn) => conn.id === editConfig.connection_id,
      );
      setSelectedConnection(connection || null);
    } else if (
      open &&
      cloudConnections.length === 1 &&
      existingConfigs.length === 0
    ) {
      // Auto-select the only connection if there's no existing config
      const connection = cloudConnections[0];
      setSelectedConnection(connection);
      setFormData((prev) => ({
        ...prev,
        connection_id: connection.id,
      }));
    } else if (!editConfig) {
      setFormData(DEFAULT_FORM_DATA);
      setRefreshIntervalInput('30');
      setFormErrors({});
      setSelectedConnection(null);
    }
  }, [editConfig, connections, cloudConnections, existingConfigs, open]);

  // Handle connection selection
  const handleConnectionChange = (connectionId: string) => {
    const connection = connections.find((conn) => conn.id === connectionId);
    const existingConfig = existingConfigs.find(
      (config) => config.connection_id === connectionId,
    );

    setSelectedConnection(connection || null);

    if (existingConfig) {
      // Load existing configuration
      setFormData({
        connection_id: connectionId,
        include_stopped_resources: existingConfig.include_stopped_resources,
        refresh_interval: existingConfig.refresh_interval,
        selected_resources: existingConfig.selected_resources || [],
        is_enabled: existingConfig.is_enabled,
      });
      setRefreshIntervalInput(existingConfig.refresh_interval.toString());
      setFormErrors({});
    } else {
      // New configuration
      setFormData((prev) => ({
        ...prev,
        connection_id: connectionId,
        selected_resources: [], // Reset selected resources when connection changes
      }));
      setRefreshIntervalInput('30');
      setFormErrors({});
    }
  };

  // Handle resource selection
  const handleResourceToggle = (resourceType: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      selected_resources: checked
        ? [...prev.selected_resources, resourceType]
        : prev.selected_resources.filter((r) => r !== resourceType),
    }));
  };

  // Handle sync trigger
  const handleTriggerSync = (configId: string) => {
    triggerSync(configId);
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!formData.connection_id) {
      toast.error('Please select a cloud connection');
      return;
    }

    if (formData.selected_resources.length === 0) {
      toast.error('Please select at least one resource type');
      return;
    }

    // Check if we have an existing config for this connection
    const existingConfig = existingConfigs.find(
      (config) => config.connection_id === formData.connection_id,
    );

    if (existingConfig) {
      // Update existing configuration
      const updateData: SchemaCloudSyncConfigUpdate = {
        include_stopped_resources: formData.include_stopped_resources,
        refresh_interval: formData.refresh_interval,
        selected_resources: formData.selected_resources,
        is_enabled: formData.is_enabled,
      };

      updateConfig(
        { id: existingConfig.id, data: updateData },
        {
          onSuccess: () => {
            toast.success('Configuration updated successfully');
            setOpen(false);
            setFormData(DEFAULT_FORM_DATA);
            setRefreshIntervalInput('30');
            setFormErrors({});
            setSelectedConnection(null);
          },
        },
      );
    } else {
      // Create new configuration
      const createData: SchemaCloudSyncConfigCreate = {
        connection_id: formData.connection_id,
        include_stopped_resources: formData.include_stopped_resources,
        refresh_interval: formData.refresh_interval,
        selected_resources: formData.selected_resources,
        is_enabled: formData.is_enabled,
      };

      createConfig(createData, {
        onSuccess: () => {
          setOpen(false);
          setFormData(DEFAULT_FORM_DATA);
          setRefreshIntervalInput('30');
          setFormErrors({});
          setSelectedConnection(null);
        },
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Cloud Service Sync Configuration
          </DialogTitle>
          <p className="text-muted-foreground mt-1 text-sm">
            Configure resource synchronization from your connected cloud
            services
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Connected Cloud Provider */}
          <div className="space-y-3">
            <Label className="text-base font-medium">
              Connected Cloud Provider
            </Label>
            {isLoadingConnections ? (
              <Skeleton className="h-20 w-full" />
            ) : cloudConnections.length > 0 ? (
              <div className="space-y-2">
                {cloudConnections.map((connection) => {
                  const isSelected = formData.connection_id === connection.id;
                  const cloudProvider = connection.prefix.toUpperCase();
                  const existingConfig = existingConfigs.find(
                    (config) => config.connection_id === connection.id,
                  );
                  const hasExistingConfig = !!existingConfig;

                  return (
                    <Card
                      key={connection.id}
                      className={`cursor-pointer border-2 transition-colors ${
                        isSelected
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      } ${editConfig ? 'cursor-not-allowed opacity-60' : ''}`}
                      onClick={() =>
                        !editConfig && handleConnectionChange(connection.id)
                      }
                    >
                      <CardContent className="flex items-center justify-between p-4">
                        <div className="flex items-center gap-3">
                          {getCloudProviderIcon(cloudProvider)}
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">
                                {connection.name}
                              </span>
                              {hasExistingConfig && (
                                <span className="bg-primary/40 rounded px-2 py-0.5 text-xs">
                                  Configured
                                </span>
                              )}
                            </div>
                            <div className="text-muted-foreground text-sm">
                              {cloudProvider} • {connection.prefix}
                            </div>
                            <div className="text-muted-foreground text-xs">
                              {hasExistingConfig && existingConfig.last_sync_at
                                ? `Last synced: ${new Date(existingConfig.last_sync_at).toLocaleString()}`
                                : 'Never synced'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {isSelected && hasExistingConfig && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTriggerSync(existingConfig.id);
                              }}
                              disabled={isSyncing}
                              className="h-8"
                            >
                              {isSyncing ? (
                                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="mr-2 h-3 w-3" />
                              )}
                              Sync
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <p className="text-muted-foreground text-sm">
                No cloud connections available.
              </p>
            )}
          </div>

          {/* Select Resources to Sync */}
          {selectedConnection && (
            <div className="space-y-3">
              <Label className="text-base font-medium">
                Select Resources to Sync
              </Label>
              {isLoadingResourceTypes ? (
                <div className="grid grid-cols-2 gap-3">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                  ))}
                </div>
              ) : resourceTypes.length > 0 ? (
                <div className="grid grid-cols-2 gap-3">
                  {resourceTypes.map((resourceType) => {
                    const isSelected = formData.selected_resources.includes(
                      resourceType.resource_type,
                    );
                    const resourceIcon = getResourceTypeIcon(
                      resourceType.resource_type,
                    );

                    return (
                      <Card
                        key={resourceType.resource_type}
                        className={`cursor-pointer border transition-colors ${
                          isSelected
                            ? 'border-primary bg-primary/5'
                            : 'border-border hover:border-primary/50'
                        }`}
                        onClick={() =>
                          handleResourceToggle(
                            resourceType.resource_type,
                            !isSelected,
                          )
                        }
                      >
                        <CardContent className="flex items-center gap-3 p-4">
                          <div className="flex min-w-0 flex-1 items-center gap-3">
                            {resourceIcon}
                            <div className="min-w-0 flex-1">
                              <div className="text-sm font-medium">
                                {resourceType.display_name}
                              </div>
                              <div className="text-muted-foreground truncate text-xs">
                                {resourceType.description}
                              </div>
                            </div>
                          </div>
                          <Checkbox
                            checked={isSelected}
                            onCheckedChange={(checked) =>
                              handleResourceToggle(
                                resourceType.resource_type,
                                checked as boolean,
                              )
                            }
                            onClick={(e) => e.stopPropagation()}
                          />
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  No resource types available for this cloud provider.
                </p>
              )}
            </div>
          )}

          {/* Sync Options */}
          {selectedConnection && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Settings className="h-4 w-4" />
                  Sync Options
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Include Stopped Resources */}
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Include stopped resources</Label>
                    <p className="text-muted-foreground text-sm">
                      Sync resources that are currently stopped
                    </p>
                  </div>
                  <Switch
                    checked={formData.include_stopped_resources}
                    onCheckedChange={(checked) =>
                      setFormData((prev) => ({
                        ...prev,
                        include_stopped_resources: checked,
                      }))
                    }
                  />
                </div>

                {/* Auto-refresh Interval */}
                <div className="space-y-2">
                  <Label htmlFor="refresh-interval">
                    Auto-refresh interval
                  </Label>
                  <p className="text-muted-foreground mb-2 text-sm">
                    How often to automatically sync resources
                  </p>
                  <div className="flex items-center gap-2">
                    <Input
                      id="refresh-interval"
                      type="number"
                      min="5"
                      value={refreshIntervalInput}
                      onChange={(e) => setRefreshIntervalInput(e.target.value)}
                      onBlur={() => {
                        const interval = parseInt(refreshIntervalInput);
                        if (isNaN(interval) || interval < 5) {
                          setFormErrors((prev) => ({
                            ...prev,
                            refresh_interval:
                              'Refresh interval must be at least 30 minutes.',
                          }));
                          setFormData((prev) => ({
                            ...prev,
                            refresh_interval: 30, // Revert to default on blur
                          }));
                          setRefreshIntervalInput('30'); // Sync the input display
                        } else {
                          setFormErrors((prev) => ({
                            ...prev,
                            refresh_interval: undefined,
                          }));
                          setFormData((prev) => ({
                            ...prev,
                            refresh_interval: interval,
                          }));
                        }
                      }}
                      className="w-20"
                    />
                    <span className="text-muted-foreground text-sm">
                      minutes
                    </span>
                  </div>
                  {formErrors.refresh_interval && (
                    <p className="mt-1 text-xs text-red-500">
                      {formErrors.refresh_interval}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {editConfig ? 'Save Configuration' : 'Save Configuration'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to extract cloud provider from connection
function getCloudProviderFromConnection(
  connection: SchemaBuiltinConnectionPublic | null,
): string {
  if (!connection) return '';

  const providerMapping: Record<string, string> = {
    aws: 'AWS',
    gcp: 'GCP',
    azure: 'AZURE',
  };

  return providerMapping[connection.prefix] || '';
}

// Helper function to get cloud provider icon
function getCloudProviderIcon(provider: string) {
  switch (provider) {
    case 'AWS':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded bg-orange-500 text-xs font-bold text-white">
          AWS
        </div>
      );
    case 'GCP':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded bg-blue-500 text-xs font-bold text-white">
          GCP
        </div>
      );
    case 'AZURE':
      return (
        <div className="flex h-8 w-8 items-center justify-center rounded bg-cyan-500 text-xs font-bold text-white">
          AZ
        </div>
      );
    default:
      return <CloudIcon className="text-muted-foreground h-8 w-8" />;
  }
}

// Helper function to get resource type icon
function getResourceTypeIcon(resourceType: string) {
  // Try to get icon from AWS resource config
  const awsResourceType = resourceType as AWSResourceType;
  if (RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType]) {
    const iconComponent = RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType].icon;
    return createElement(iconComponent, { className: 'h-5 w-5 text-primary' });
  }

  // Fallback icons for other providers or unknown types
  const fallbackIcons: Record<
    string,
    React.ComponentType<{ className?: string }>
  > = {
    COMPUTE_ENGINE: Cpu,
    CLOUD_FUNCTIONS: Cpu,
    CLOUD_RUN: Server,
    GKE: Server,
    VIRTUAL_MACHINES: Cpu,
    AZURE_FUNCTIONS: Cpu,
    CONTAINER_INSTANCES: Server,
    AKS: Server,
    CLOUD_SQL: Database,
    FIRESTORE: Database,
    SQL_DATABASE: Database,
    COSMOS_DB: Database,
    CLOUD_STORAGE: HardDrive,
    BLOB_STORAGE: HardDrive,
    VPC_NETWORK: Globe,
    VIRTUAL_NETWORK: Globe,
    PUB_SUB: MessageSquare,
    SERVICE_BUS: MessageSquare,
    CLOUD_MONITORING: BarChart3,
    MONITOR: BarChart3,
  };

  const IconComponent = fallbackIcons[resourceType] || Server;
  return <IconComponent className="text-primary h-5 w-5" />;
}
