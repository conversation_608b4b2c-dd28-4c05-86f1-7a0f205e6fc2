import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Heading } from '@/components/ui/heading';
import { cn } from '@/lib/utils';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { SettingsIcon, WrenchIcon } from 'lucide-react';

import { ConnectionLogo } from '../config';
import { builtinConnectionEnvTypeConfig } from '../config/builtin-connection-env-type.config';
import { BuiltinConnectedActions } from './builtin-connected-actions';
import { BuiltinConnectionConfigToolDialog } from './builtin-connection-config-tool-dialog';

type Props = {
  connection: SchemaBuiltinConnectionPublic;
};

export function BuiltinConnectionCard({ connection }: Props) {
  const envTypeConfig =
    builtinConnectionEnvTypeConfig[connection.connection_env_type];

  return (
    <Card key={connection.id} className="flex flex-col space-y-2 p-4">
      <div className="space-y-1">
        <div className="flex items-center justify-between gap-1">
          <div className="flex items-center gap-2">
            <ConnectionLogo prefix={connection.prefix} />
            <Heading level={4} className="truncate">
              {connection.name}
            </Heading>
          </div>
          <If condition={connection.is_connected}>
            <Badge variant={envTypeConfig.variant} className="gap-1">
              <envTypeConfig.icon className="size-4" />
              {envTypeConfig.label}
            </Badge>
          </If>
        </div>

        <p className="text-muted-foreground text-sm">
          {connection.description}
        </p>
      </div>
      <div className="grow space-y-1">
        <span className="text-muted-foreground flex items-center gap-1 text-sm whitespace-nowrap">
          <WrenchIcon className="size-4" /> Tools ({connection.tools.length})
        </span>

        <div className="flex flex-wrap items-center gap-2">
          <If
            condition={connection.tools.length}
            fallback={
              <span className="text-muted-foreground text-sm italic">
                No tools available
              </span>
            }
          >
            {connection.tools.slice(0, 3).map((tool) => (
              <Badge
                variant="outline"
                className="bg-primary/10 gap-2"
                key={tool.name}
              >
                <div
                  className={cn(
                    'size-2 rounded-full',
                    tool.is_enabled ? 'bg-success' : 'bg-warning',
                  )}
                />
                {tool.name}
              </Badge>
            ))}
            <If condition={connection.tools.length > 3}>
              <span className="text-muted-foreground text-sm italic">
                +{connection.tools.length - 3} more
              </span>
            </If>
          </If>
        </div>
      </div>
      <div className="flex items-center justify-end gap-4">
        <If condition={connection.is_connected}>
          <BuiltinConnectionConfigToolDialog builtinConnection={connection}>
            <Button variant="outlinePrimary" size="sm" className="gap-2">
              <SettingsIcon className="size-4" />
              Tool settings
            </Button>
          </BuiltinConnectionConfigToolDialog>
        </If>
        <BuiltinConnectedActions connection={connection} />
      </div>
    </Card>
  );
}
