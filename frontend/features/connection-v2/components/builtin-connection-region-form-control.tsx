import { Autocomplete } from '@/components/ui/common/autocomplete';
import { FormControl } from '@/components/ui/form';
import { constantsQuery } from '@/features/constants/hooks/constants.query';
import { CloudProvider } from '@/openapi-ts/gens';

type Props = {
  provider: CloudProvider;
  value: string;
  onChange: (value: string | null | undefined) => void;
};

export const BuiltinConnectionRegionFormControl = ({
  provider,
  value,
  onChange,
}: Props) => {
  const { data: regions } = constantsQuery.query.useRegions({
    provider,
  });
  return (
    <FormControl>
      <Autocomplete
        name="region"
        clearable={false}
        options={regions}
        value={value}
        onChange={onChange}
      />
    </FormControl>
  );
};
