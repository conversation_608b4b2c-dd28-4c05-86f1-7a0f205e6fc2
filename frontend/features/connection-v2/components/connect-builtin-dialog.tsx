'use client';

import { useEffect, useRef } from 'react';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  FileUpload,
  FileUploadDropzone,
  FileUploadItem,
  FileUploadItemDelete,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
  useFileUpload,
} from '@/components/ui/file-upload';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/password-input';
import { YamlPreview } from '@/components/ui/yaml-preview';
import {
  ConnectionEnvType,
  SchemaBuiltinConnectionPublic,
} from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { CloudUploadIcon, Loader2Icon, PlugIcon, XIcon } from 'lucide-react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import {
  connectionPrefixConfig,
  isPrefixInConnection,
} from '../config/connection-prefix.config';
import { builtinConnectionQuery } from '../hooks/builtin-connection.query';
import {
  ConnectionEnv,
  connectionEnvSchema,
} from '../schema/connection-env.schema';
import { BuiltinConnectionRegionFormControl } from './builtin-connection-region-form-control';

type Props = {
  builtinConnection: SchemaBuiltinConnectionPublic;
};

export function ConnectBuiltinDialog({ builtinConnection }: Props) {
  const [open, toggle] = useToggle(false);

  const form = useForm<ConnectionEnv>({
    resolver: zodResolver(connectionEnvSchema),
    defaultValues: {
      env: builtinConnection.env,
    },
  });

  const { control, handleSubmit } = form;
  const { fields } = useFieldArray({
    control,
    name: 'env',
  });

  const { mutate, isPending } = builtinConnectionQuery.mutation.useConnect(
    builtinConnection.id,
  );

  const onSubmit: SubmitHandler<ConnectionEnv> = (data) => {
    mutate(data, {
      onSuccess: toggle,
    });
  };

  return (
    <AlertDialog open={open} onOpenChange={toggle}>
      <AlertDialogTrigger asChild>
        <Button className="gap-2" size="sm" disabled={isPending}>
          <If condition={isPending} fallback={<PlugIcon className="size-4" />}>
            <Loader2Icon className="size-4 animate-spin" />
          </If>
          Connect
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent
        onEscapeKeyDown={(e) => e.preventDefault()}
        className="flex max-h-[90dvh] max-w-3xl flex-col overflow-hidden"
      >
        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex h-full flex-col space-y-4 overflow-hidden"
          >
            <AlertDialogHeader>
              <AlertDialogTitle>
                Connect {builtinConnection.name}
              </AlertDialogTitle>
              <AlertDialogDescription>
                Configure the environment variables for {builtinConnection.name}
                .
              </AlertDialogDescription>
            </AlertDialogHeader>
            <div className="overflow-thin-auto grow space-y-4 pr-2 pl-0.5">
              {fields.map((field, index) => (
                <FormField
                  key={field.id}
                  control={control}
                  name={`env.${index}.value`}
                  render={({ field: inputField }) => (
                    <FormItem>
                      <FormLabel required>{field.key}</FormLabel>

                      <If condition={field.type === ConnectionEnvType.string}>
                        <FormControl>
                          <PasswordInput
                            {...inputField}
                            placeholder={`Enter ${field.key}`}
                          />
                        </FormControl>
                      </If>
                      <If condition={field.type === ConnectionEnvType.region}>
                        {() => {
                          if (!isPrefixInConnection(builtinConnection.prefix)) {
                            throw new Error(
                              `Unsupported prefix: ${builtinConnection.prefix}`,
                            );
                          }

                          const prefixConfig =
                            connectionPrefixConfig[builtinConnection.prefix];

                          if (!('cloudProvider' in prefixConfig)) {
                            throw new Error(
                              `${builtinConnection.prefix} does not support regions`,
                            );
                          }

                          return (
                            <BuiltinConnectionRegionFormControl
                              provider={prefixConfig.cloudProvider}
                              value={inputField.value}
                              onChange={inputField.onChange}
                            />
                          );
                        }}
                      </If>
                      <If
                        condition={
                          field.type === ConnectionEnvType.yaml ||
                          field.type === ConnectionEnvType.json
                        }
                      >
                        {() => {
                          const typeConfig = {
                            [ConnectionEnvType.yaml]: {
                              accept: '.yaml',
                              title: 'YAML',
                            },
                            [ConnectionEnvType.json]: {
                              accept: '.json',
                              title: 'JSON',
                            },
                          } as const;

                          const fileType =
                            typeConfig[field.type as keyof typeof typeConfig];

                          return (
                            <>
                              <FormDescription>
                                Upload a {fileType.title} file to configure the
                                environment variables.
                              </FormDescription>

                              <FileUpload
                                accept={fileType.accept}
                                maxFiles={1}
                                onUpload={async (files) => {
                                  const file = files[0];
                                  const reader = new FileReader();

                                  reader.onload = () => {
                                    const content = reader.result as string;
                                    inputField.onChange(content);
                                  };

                                  reader.readAsText(file);
                                }}
                              >
                                <EnvUploader
                                  onChange={inputField.onChange}
                                  fileTitle={fileType.title}
                                />

                                <If condition={inputField.value}>
                                  {(content) => (
                                    <div className="mt-4">
                                      <YamlPreview
                                        content={content}
                                        title="Configuration Preview"
                                      />
                                    </div>
                                  )}
                                </If>
                              </FileUpload>
                            </>
                          );
                        }}
                      </If>

                      <FormMessage />
                    </FormItem>
                  )}
                />
              ))}
            </div>

            <AlertDialogFooter>
              <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>

              <Button type={'submit'} className="gap-2" disabled={isPending}>
                <If condition={isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                Connect
              </Button>
            </AlertDialogFooter>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
}

const EnvUploader = ({
  onChange,
  fileTitle,
}: {
  onChange: (value: string) => void;
  fileTitle: string;
}) => {
  const onChangeRef = useRef(onChange);
  const files = useFileUpload((state) => Array.from(state.files.values()));

  const currentFile = files[0]?.file;

  useEffect(() => {
    if (!currentFile) {
      onChangeRef.current('');
    }
  }, [currentFile]);

  return (
    <If
      condition={currentFile}
      fallback={
        <FileUploadDropzone className="flex-row flex-wrap border-dotted text-center">
          <CloudUploadIcon className="size-4" />
          Drag and drop or
          <FileUploadTrigger asChild>
            <Button variant="link" size="sm" className="p-0">
              choose a {fileTitle} file
            </Button>
          </FileUploadTrigger>
          to configure the environment variables.
        </FileUploadDropzone>
      }
    >
      {(file) => (
        <FileUploadList>
          <FileUploadItem value={file}>
            <FileUploadItemPreview />
            <FileUploadItemMetadata />
            <FileUploadItemDelete asChild>
              <Button variant="ghost" size="icon" className="size-7">
                <XIcon />
                <span className="sr-only">Delete</span>
              </Button>
            </FileUploadItemDelete>
          </FileUploadItem>
        </FileUploadList>
      )}
    </If>
  );
};
