import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Skeleton } from '@/components/ui/skeleton';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { useGetNavigationConfig } from '@/hooks/use-get-navigation-config';
import { cn } from '@/lib/utils';
import { PlusIcon } from 'lucide-react';

import { ConnectionLogo } from '../config';
import { builtinConnectionQuery } from '../hooks/builtin-connection.query';

export function HomeChatQuickConnection() {
  const { data, isLoading } = builtinConnectionQuery.query.useList();
  const { getRoutePath } = useGetNavigationConfig();

  const connectionRouter = getRoutePath(pathsConfig.app.connectors);

  return (
    <>
      <If condition={data}>
        {(data) => (
          <div className="flex items-center gap-4">
            {data.data.map((connection) => (
              <Button
                className="relative gap-1 px-1"
                key={connection.id}
                variant="ghost"
                size="sm"
                asChild
              >
                <Link href={pathsConfig.app.connectors}>
                  <div
                    className={cn(
                      'absolute -right-1 bottom-1 flex size-2 items-center justify-center rounded-full',
                      connection.is_connected ? 'bg-success' : 'bg-destructive',
                    )}
                  />
                  <ConnectionLogo
                    prefix={connection.prefix}
                    className={cn(!connection.is_connected && 'opacity-50')}
                  />
                </Link>
              </Button>
            ))}
            <WithTooltip tooltip="Setup connections">
              <Button variant="outline" asChild size="icon" className="size-8">
                <Link href={connectionRouter.path}>
                  <PlusIcon className="size-4" />
                </Link>
              </Button>
            </WithTooltip>
          </div>
        )}
      </If>

      <If condition={isLoading}>
        <Skeleton className="h-10 w-full" />
      </If>
    </>
  );
}
