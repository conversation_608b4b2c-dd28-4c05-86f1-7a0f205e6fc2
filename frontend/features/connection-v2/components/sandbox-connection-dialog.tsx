'use client';

import { useState } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { If } from '@/components/ui/common/if';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Heading } from '@/components/ui/heading';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { SchemaBuiltinConnectionPublic } from '@/openapi-ts/gens';
import { Loader2Icon, SettingsIcon, ShieldCheckIcon } from 'lucide-react';
import pluralize from 'pluralize';
import { toast } from 'sonner';

import { ConnectionLogo } from '../config';
import { builtinConnectionQuery } from '../hooks/builtin-connection.query';

type Props = {
  connections: SchemaBuiltinConnectionPublic[];
};

export function SandboxConnectionDialog({ connections }: Props) {
  const [open, setOpen] = useState(false);
  const [selectedConnections, setSelectedConnections] = useState<string[]>([]);

  const { mutate: installSandbox, isPending } =
    builtinConnectionQuery.mutation.useInstallSandbox();

  // Filter connections that support sandbox
  const sandboxConnections = connections.filter(
    (connection) => connection.is_sandbox_supported,
  );

  const handleConnectionToggle = (connectionId: string) => {
    setSelectedConnections((prev) =>
      prev.includes(connectionId)
        ? prev.filter((id) => id !== connectionId)
        : [...prev, connectionId],
    );
  };

  const handleConnect = () => {
    if (selectedConnections.length === 0) {
      toast.error('Please select at least one connection to set up');
      return;
    }

    installSandbox(
      { sandbox_conn_ids: selectedConnections },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success(
            `Successfully set up ${selectedConnections.length} sandbox connection(s)`,
          );
        },
      },
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" className="gap-2" disabled={isPending}>
          <If
            condition={isPending}
            fallback={<SettingsIcon className="size-4" />}
          >
            <Loader2Icon className="size-4 animate-spin" />
          </If>
          Setup
        </Button>
      </DialogTrigger>

      <DialogContent className="flex max-h-[90vh] max-w-4xl flex-col overflow-hidden">
        <DialogHeader>
          <DialogTitle>Setup Your Sandbox Connections</DialogTitle>
          <p className="text-muted-foreground text-sm">
            Choose which demo connections to initialize. You can add more later
            or start with our recommended setup.
          </p>
        </DialogHeader>

        <div className="overflow-thin-auto grow">
          <div className="space-y-4">
            <Alert variant="info">
              <ShieldCheckIcon />
              <AlertTitle>Safe Sandbox Environment</AlertTitle>
              <AlertDescription>
                {`All sandbox connections use isolated demo data and won't
                    affect your production systems. Perfect for learning and
                    testing CloudThinker's capabilities.`}
              </AlertDescription>
            </Alert>

            {/* All Sandbox Connections */}
            <If condition={sandboxConnections.length > 0}>
              <div className="grid gap-3 @2xl:grid-cols-2">
                {sandboxConnections.map((connection) => {
                  return (
                    <Label key={connection.id}>
                      <Card
                        className={cn(
                          'hover:bg-card/80 relative transition-all',
                          {
                            'cursor-not-allowed opacity-50':
                              connection.is_connected,
                          },
                        )}
                      >
                        <div className="flex items-start gap-3 p-4">
                          <Checkbox
                            checked={selectedConnections.includes(
                              connection.id,
                            )}
                            disabled={connection.is_connected}
                            className="mt-1"
                            onCheckedChange={() =>
                              handleConnectionToggle(connection.id)
                            }
                          />

                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <ConnectionLogo prefix={connection.prefix} />
                              <Heading level={5} className="text-sm">
                                {connection.name}
                              </Heading>
                              <If condition={connection.is_connected}>
                                <Badge variant="info">Connected</Badge>
                              </If>
                            </div>

                            <p className="text-muted-foreground text-xs">
                              {connection.description}
                            </p>
                          </div>
                        </div>
                      </Card>
                    </Label>
                  );
                })}
              </div>
            </If>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="text-muted-foreground text-xs">
              {pluralize('connection', selectedConnections.length, true)}{' '}
              selected
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleConnect}
                disabled={selectedConnections.length === 0 || isPending}
                className="gap-2"
              >
                <If condition={isPending}>
                  <Loader2Icon className="size-4 animate-spin" />
                </If>
                Setup{' '}
                {pluralize('connection', selectedConnections.length, true)}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
