import { BadgeProps } from '@/components/ui/badge';
import { BuiltinConnectionEnvType } from '@/openapi-ts/gens';
import { FlaskConicalIcon, GlobeIcon, LucideIcon } from 'lucide-react';

export const builtinConnectionEnvTypeConfig = {
  [BuiltinConnectionEnvType.sandbox]: {
    label: 'Sandbox',
    icon: FlaskConicalIcon,
    variant: 'beta',
  },
  [BuiltinConnectionEnvType.production]: {
    label: 'Production',
    icon: GlobeIcon,
    variant: 'success',
  },
} as const satisfies Record<
  BuiltinConnectionEnvType,
  { label: string; icon: LucideIcon; variant: BadgeProps['variant'] }
>;
