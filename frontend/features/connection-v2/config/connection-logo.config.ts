import { createElement } from 'react';

import { cn } from '@/lib/utils';

import {
  connectionPrefixConfig,
  isPrefixInConnection,
} from './connection-prefix.config';

/**
 * Get the logo path for a given connection prefix
 * @param prefix - The connection prefix (e.g., 'aws', 'gcp', etc.)
 * @returns The logo path or null if not found
 */
export const ConnectionLogo = ({
  prefix,
  className,
}: {
  prefix: string;
  className?: string;
}) => {
  if (isPrefixInConnection(prefix)) {
    return createElement(connectionPrefixConfig[prefix].icon, {
      className: cn('w-6', className),
    });
  }

  throw new Error(`Connection logo not found for prefix: ${prefix}`);
};
