import { FC } from 'react';

import { CloudProvider } from '@/openapi-ts/gens';
import AwsLogo from '@/public/aws-logo.svg';
import Azure<PERSON>ogo from '@/public/azure-logo.svg';
import GcpLogo from '@/public/gcp-logo.svg';
import Grafana<PERSON>ogo from '@/public/grafana-logo.svg';
import K8sLogo from '@/public/k8s-logo.svg';
import PostgresqlLogo from '@/public/postgresql-icon.svg';

export const connectionPrefixConfig = {
  aws: {
    icon: AwsLogo,
    cloudProvider: CloudProvider.AWS,
  },
  gcp: {
    icon: GcpLogo,
    cloudProvider: CloudProvider.GCP,
  },
  azure: {
    icon: AzureLogo,
    cloudProvider: CloudProvider.AZURE,
  },
  k8s: {
    icon: K8sLogo,
  },
  grafana: {
    icon: GrafanaLogo,
  },
  postgres: {
    icon: PostgresqlLogo,
  },
} as const satisfies Record<
  string,
  {
    icon: FC<{ className?: string }>;
    cloudProvider?: CloudProvider;
  }
>;

export const isPrefixInConnection = (
  prefix: string,
): prefix is keyof typeof connectionPrefixConfig => {
  return prefix in connectionPrefixConfig;
};
