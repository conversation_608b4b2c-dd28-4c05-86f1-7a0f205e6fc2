import { SchemaBuiltinConnectionToolUpdateRequest } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import { PathsRequestBodyDto } from '@/openapi-ts/utils.type';

export const builtinConnectionApi = {
  list: () => fetchData(api.GET('/api/v1/builtin-connection')),
  detail: (builtin_id: string) => ({
    disconnect: () =>
      api.DELETE('/api/v1/builtin-connection/{builtin_id}', {
        params: { path: { builtin_id } },
      }),

    connect: (
      body: PathsRequestBodyDto<'/api/v1/builtin-connection/{builtin_id}'>,
    ) =>
      api.POST('/api/v1/builtin-connection/{builtin_id}', {
        params: { path: { builtin_id } },
        body,
      }),

    updateTool: (body: SchemaBuiltinConnectionToolUpdateRequest) =>
      api.PATCH('/api/v1/builtin-connection/{builtin_id}/tool', {
        params: { path: { builtin_id } },
        body,
      }),
  }),
  sandbox: {
    install: (
      body: PathsRequestBodyDto<'/api/v1/builtin-connection/sandbox/install'>,
    ) =>
      api.POST('/api/v1/builtin-connection/sandbox/install', {
        body,
      }),
  },
};
