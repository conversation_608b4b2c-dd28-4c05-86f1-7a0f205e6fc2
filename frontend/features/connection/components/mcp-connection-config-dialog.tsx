'use client';

import { use<PERSON>allback, useEffect, useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  MCPTransportType,
  SchemaMcpConnectionCreate,
  SchemaMcpConnectionPublic,
  SchemaMcpConnectionUpdate,
} from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';
import { z } from 'zod';

import {
  type EnvVariable,
  EnvVariablesManager,
} from './shared/env-variables-manager';

// Schema for MCP connection configuration
const mcpConfigSchema = z.object({
  name: z.string().min(1, 'Connection name is required'),
  prefix: z.string().min(1, 'Prefix is required'),
  mcp_transport_type: z.nativeEnum(MCPTransportType),
  url: z.string().url('Please enter a valid URL'),
  timeout: z
    .number()
    .min(0.1, 'Timeout must be at least 0.1 seconds')
    .max(300, 'Timeout cannot exceed 300 seconds'),
  sse_read_timeout: z
    .number()
    .min(1, 'SSE read timeout must be at least 1 second')
    .max(600, 'SSE read timeout cannot exceed 600 seconds'),
  headers: z.record(z.string(), z.string()),
  env: z.array(
    z.object({
      key: z.string().min(1, 'Environment variable name is required'),
      value: z.string().min(1, 'Environment variable value is required'),
    }),
  ),
});

type McpConfigFormData = z.infer<typeof mcpConfigSchema>;

/**
 * Props for the MCPConnectionConfigDialog component
 */
interface MCPConnectionConfigDialogProps {
  children: React.ReactNode | ((toggle: () => void) => React.ReactNode);
  connection?: SchemaMcpConnectionPublic; // undefined for create, defined for edit
  onSave?: (data: SchemaMcpConnectionCreate) => Promise<void>;
  onUpdate?: (
    connectionId: string,
    data: SchemaMcpConnectionUpdate,
  ) => Promise<void>;
  mode: 'create' | 'edit';
}

export function MCPConnectionConfigDialog({
  children,
  connection,
  onSave,
  onUpdate,
  mode,
}: MCPConnectionConfigDialogProps) {
  const [open, toggle] = useToggle(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<McpConfigFormData>({
    resolver: zodResolver(mcpConfigSchema),
    defaultValues: {
      name: '',
      prefix: '',
      mcp_transport_type: MCPTransportType.sse,
      url: '',
      timeout: 5.0,
      sse_read_timeout: 30.0,
      headers: {},
      env: [],
    },
  });

  const { handleSubmit, setValue, watch, reset } = form;

  // Initialize form data when dialog opens
  useEffect(() => {
    if (!open) return;

    if (connection && mode === 'edit') {
      const config = (connection.config as Record<string, unknown>) || {};
      const existingHeaders = (config.headers as Record<string, string>) || {};

      const envEntries = (connection.env || []).flatMap((envObj) =>
        Object.entries(envObj).map(([key, value]) => ({
          key,
          value: String(value),
        })),
      );

      reset({
        name: connection.name,
        prefix: connection.prefix,
        mcp_transport_type: connection.mcp_transport_type,
        url: String(config.url || ''),
        timeout: Number(config.timeout) || 5.0,
        sse_read_timeout: Number(config.sse_read_timeout) || 30.0,
        headers: existingHeaders,
        env: envEntries,
      });
    } else {
      reset({
        name: '',
        prefix: '',
        mcp_transport_type: MCPTransportType.sse,
        url: '',
        timeout: 5.0,
        sse_read_timeout: 30.0,
        headers: {},
        env: [],
      });
    }
  }, [open, connection, mode, reset]);

  const handleEnvVariablesChange = useCallback(
    (envVariables: EnvVariable[]) => {
      setValue('env', envVariables);
    },
    [setValue],
  );

  const addEnvVariable = useCallback(() => {
    const currentEnv = watch('env') || [];
    setValue('env', [...currentEnv, { key: '', value: '' }]);
  }, [setValue, watch]);

  const onSubmit = useCallback(
    async (data: McpConfigFormData) => {
      setIsSubmitting(true);
      try {
        if (mode === 'edit' && connection?.id && onUpdate) {
          const apiData: SchemaMcpConnectionUpdate = {
            name: data.name,
            prefix: data.prefix,
            mcp_transport_type: data.mcp_transport_type,
            config: {
              url: data.url,
              timeout: data.timeout,
              sse_read_timeout: data.sse_read_timeout,
              headers: data.headers as Record<string, never>,
            },
            env: data.env.map((item) => ({
              [item.key]: item.value,
            })),
          };
          await onUpdate(connection.id, apiData);
        } else if (mode === 'create' && onSave) {
          const apiData: SchemaMcpConnectionCreate = {
            name: data.name,
            prefix: data.prefix,
            mcp_transport_type: data.mcp_transport_type,
            config: {
              url: data.url,
              timeout: data.timeout,
              sse_read_timeout: data.sse_read_timeout,
              headers: data.headers as Record<string, never>,
            },
            env: data.env.map((item) => ({
              [item.key]: item.value,
            })),
          };
          await onSave(apiData);
        }
        toggle();
        reset();
      } catch (error) {
        console.error('MCP connection operation failed:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [mode, connection?.id, onSave, onUpdate, toggle, reset],
  );

  const handleCancel = useCallback(() => {
    toggle();
    reset();
  }, [toggle, reset]);

  const child = typeof children === 'function' ? children(toggle) : children;
  const title =
    mode === 'create' ? 'Create MCP Connection' : `Edit ${connection?.name}`;

  return (
    <Dialog open={open} onOpenChange={toggle}>
      <DialogTrigger asChild>{child}</DialogTrigger>

      <DialogContent
        className="flex max-h-[90vh] flex-col sm:max-w-[600px]"
        onEscapeKeyDown={(e) => isSubmitting && e.preventDefault()}
      >
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg font-semibold">{title}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex min-h-0 flex-1 flex-col"
          >
            <div className="flex-1 overflow-y-auto px-1">
              <div className="space-y-6 pb-4">
                {/* Basic Information */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">
                      Basic Information
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Configure the basic connection details
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Connection Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter connection name"
                              disabled={isSubmitting}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="prefix"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Prefix</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter prefix (will be prefix__tool_name)"
                              disabled={isSubmitting}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Connection Configuration */}
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">
                      Connection Configuration
                    </CardTitle>
                    <CardDescription className="text-xs">
                      Configure the MCP transport and connection settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="mcp_transport_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Transport Type</FormLabel>
                          <Select
                            disabled={isSubmitting}
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select transport type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem
                                value={MCPTransportType.streamable_http}
                              >
                                Streamable HTTP
                              </SelectItem>
                              <SelectItem value={MCPTransportType.sse}>
                                Server-Sent Events (SSE)
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://example.com/mcp"
                              disabled={isSubmitting}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="timeout"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Timeout (seconds)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0.1"
                                max="300"
                                step="0.1"
                                disabled={isSubmitting}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    parseFloat(e.target.value) || 0,
                                  )
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="sse_read_timeout"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SSE Read Timeout (seconds)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="1"
                                max="600"
                                step="1"
                                disabled={isSubmitting}
                                {...field}
                                onChange={(e) =>
                                  field.onChange(parseInt(e.target.value) || 0)
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Environment Variables */}
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-sm font-medium">
                          Environment Variables
                        </CardTitle>
                        <CardDescription className="text-xs">
                          Environment variables that will be available to the
                          MCP connection
                        </CardDescription>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addEnvVariable}
                        disabled={isSubmitting}
                        className="h-8"
                      >
                        <Plus className="mr-1 h-4 w-4" />
                        Add Variable
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div
                      className="bg-muted/30 max-h-[200px] overflow-y-scroll rounded-md border p-3"
                      style={{ minHeight: '120px' }}
                    >
                      <EnvVariablesManager
                        envVariables={watch('env') || []}
                        onEnvVariablesChange={handleEnvVariablesChange}
                        canEditKeys
                        title=""
                        emptyStateMessage="No environment variables set."
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <DialogFooter className="mt-4 border-t pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="h-9"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="h-9 min-w-[120px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === 'create' ? 'Creating...' : 'Updating...'}
                  </>
                ) : mode === 'create' ? (
                  'Create Connection'
                ) : (
                  'Update Connection'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
