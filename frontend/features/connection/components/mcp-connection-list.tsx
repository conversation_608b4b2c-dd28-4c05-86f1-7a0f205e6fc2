'use client';

import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  SchemaMcpConnectionCreate,
  SchemaMcpConnectionUpdate,
} from '@/openapi-ts/gens';
import { Loader2, Plus } from 'lucide-react';

import { mcpConnectionQuery } from '../hooks/mcp-connection.query';
import { MCPConnectionCardUI } from './mcp-connection-card';
import { MCPConnectionConfigDialog } from './mcp-connection-config-dialog';
import { ConnectionGrid } from './shared/connection-grid';

export function MCPConnectionList() {
  const {
    data: mcpConnections,
    isLoading,
    error,
  } = mcpConnectionQuery.query.useList();
  const { mutateAsync: createMcpConnection } =
    mcpConnectionQuery.mutation.useCreate();
  const { mutateAsync: updateMcpConnection } =
    mcpConnectionQuery.mutation.useUpdate();
  const { mutateAsync: deleteMcpConnection } =
    mcpConnectionQuery.mutation.useDelete();
  const { mutateAsync: updateMcpConnectionTool } =
    mcpConnectionQuery.mutation.useUpdateTool();

  // Track which tools are currently being toggled
  const [togglingTools, setTogglingTools] = useState<Set<string>>(new Set());

  // Handle create new connection
  const handleCreate = async (data: SchemaMcpConnectionCreate) => {
    await createMcpConnection({ mcp_conn_create: data });
  };

  // Handle update connection
  const handleUpdate = async (
    connectionId: string,
    data: SchemaMcpConnectionUpdate,
  ) => {
    await updateMcpConnection({
      mcp_conn_id: connectionId,
      mcp_conn_update: data,
    });
  };

  // Handle delete connection
  const handleDelete = async (connectionId: string) => {
    await deleteMcpConnection({ mcp_conn_id: connectionId });
  };

  // Handle individual tool toggle
  const handleToolToggle = async (
    connectionId: string,
    toolName: string,
    enabled: boolean,
  ) => {
    // Mark tool as toggling
    setTogglingTools((prev) => new Set(prev).add(toolName));

    try {
      await updateMcpConnectionTool({
        mcp_conn_id: connectionId,
        tool_name: toolName,
        is_enabled: enabled,
        is_required_permission: false,
      });
    } finally {
      // Remove tool from toggling set
      setTogglingTools((prev) => {
        const next = new Set(prev);
        next.delete(toolName);
        return next;
      });
    }
  };

  // Handle permission toggle
  const handlePermissionToggle = async (
    connectionId: string,
    toolName: string,
    requiresPermission: boolean,
  ) => {
    try {
      await updateMcpConnectionTool({
        mcp_conn_id: connectionId,
        tool_name: toolName,
        is_enabled: true, // Keep enabled when changing permissions
        is_required_permission: requiresPermission,
      });
    } catch (error) {
      console.error('Failed to update MCP connection tool permissions:', error);
    }
  };

  if (mcpConnections) {
    // Main content
    return (
      <div>
        {/* Header with connection count and create button */}
        <div className="mt-2 mb-3 space-y-3">
          <div className="flex flex-wrap items-center justify-between gap-3">
            <Badge variant="ghost-primary" className="px-3 py-1">
              {mcpConnections.data.length} MCP Connections
            </Badge>
            <MCPConnectionConfigDialog onSave={handleCreate} mode="create">
              <Button className="flex items-center gap-2" size="sm">
                <Plus className="h-4 w-4" />
                New Connection
              </Button>
            </MCPConnectionConfigDialog>
          </div>
        </div>

        {/* Connection Grid */}
        <div className="space-y-4">
          <If
            condition={mcpConnections.data.length > 0}
            fallback={
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <h3 className="text-muted-foreground mb-2 text-lg font-medium">
                  No MCP connections found
                </h3>
                <p className="text-muted-foreground mb-4 text-sm">
                  Create your first MCP server connection to get started.
                </p>
              </div>
            }
          >
            <ConnectionGrid>
              {mcpConnections.data.map((connection) => (
                <MCPConnectionCardUI
                  key={connection.id}
                  connection={connection}
                  onUpdate={handleUpdate}
                  onDelete={handleDelete}
                  onToolToggle={handleToolToggle}
                  onPermissionToggle={handlePermissionToggle}
                  togglingTools={togglingTools}
                />
              ))}
            </ConnectionGrid>
          </If>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="space-y-2 text-center">
          <h4 className="text-destructive text-lg font-medium">
            Error loading connections
          </h4>
          <p className="text-muted-foreground text-sm">
            {error instanceof Error
              ? error.message
              : 'An unexpected error occurred'}
          </p>
        </div>
      </div>
    );
  }
}
