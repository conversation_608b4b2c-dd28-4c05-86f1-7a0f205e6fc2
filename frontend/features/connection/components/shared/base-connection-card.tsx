'use client';

import { ReactNode } from 'react';

import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Types for the base connection card
interface BaseConnectionCardProps {
  readonly header: ReactNode;
  readonly footer: ReactNode;
  readonly children?: ReactNode;
  readonly className?: string;
}

export function BaseConnectionCard({
  header,
  footer,
  children,
  className,
}: BaseConnectionCardProps) {
  return (
    <Card className={cn('relative flex h-full flex-col', className)}>
      {/* Card Header - Customizable */}
      <CardHeader className="flex-shrink-0 p-0">{header}</CardHeader>

      <CardContent className="flex flex-grow flex-col px-4 pt-0 pb-3">
        {/* Main Content Area */}
        <div className="flex-grow">{children}</div>

        {/* Card Footer - Customizable */}
        <div className="mt-auto flex items-center justify-end pt-3">
          <div className="flex items-center gap-2">{footer}</div>
        </div>
      </CardContent>
    </Card>
  );
}
