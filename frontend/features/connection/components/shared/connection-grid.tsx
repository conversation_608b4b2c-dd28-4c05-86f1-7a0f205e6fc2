'use client';

import { ReactNode } from 'react';

// Shared grid component that both builtin and MCP connection lists can use
interface ConnectionGridProps {
  readonly children: ReactNode;
  readonly className?: string;
}

export function ConnectionGrid({
  children,
  className = '',
}: ConnectionGridProps) {
  return (
    <div
      className={`grid auto-rows-min grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 ${className}`}
    >
      {children}
    </div>
  );
}
