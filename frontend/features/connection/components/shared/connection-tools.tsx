'use client';

import { use<PERSON><PERSON>back, useMemo, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import {
  ChevronDown,
  ChevronUp,
  Eye,
  Loader2,
  Settings,
  Wrench,
} from 'lucide-react';

/**
 * Tool type based on list[dict[str, str|bool]] format
 * Updated to support is_enabled and is_required_permission
 */
export type ConnectionTool = {
  name: string;
  is_enabled?: boolean;
  is_required_permission?: boolean;
  description?: string;
};

interface ConnectionToolsProps {
  readonly tools: ConnectionTool[];
  readonly isConnected?: boolean;
  readonly showExpandButton?: boolean;
  readonly maxToolsDisplay?: number;
  readonly onToolToggle?: (toolName: string, enabled: boolean) => void;
  readonly onPermissionToggle?: (
    toolName: string,
    requiresPermission: boolean,
  ) => void;
  readonly isTogglingTool?: (toolName: string) => boolean;
  readonly className?: string;
  readonly connectionName?: string;
}

/**
 * A simplified, reusable tools component for connections
 *
 * Supports tools in the format: list[dict[str, str|bool]]
 * Example: [{"name": "database_query", "enabled": true, "description": "Query database"}]
 */
export function ConnectionTools({
  tools,
  isConnected = false,
  showExpandButton = true,
  maxToolsDisplay = 6,
  onToolToggle,
  onPermissionToggle,
  isTogglingTool,
  className,
  connectionName,
}: ConnectionToolsProps) {
  const [showAllTools, setShowAllTools] = useState(false);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [tempPermissions, setTempPermissions] = useState<
    Record<string, boolean>
  >({});

  // Determine tools to show based on showAllTools state
  const toolsToShow = useMemo(() => {
    return showAllTools ? tools : tools.slice(0, maxToolsDisplay);
  }, [tools, showAllTools, maxToolsDisplay]);

  // Only show expand/collapse button if there are more tools than max display
  const shouldShowExpandButton =
    showExpandButton && tools.length > maxToolsDisplay;

  // Initialize temp permissions when dialog opens
  const handleOpenPermissionsDialog = useCallback(() => {
    const permissions: Record<string, boolean> = {};
    tools.forEach((tool) => {
      permissions[tool.name] = tool.is_required_permission || false;
    });
    setTempPermissions(permissions);
    setShowPermissionsDialog(true);
  }, [tools]);

  // Handle permission toggle in dialog
  const handlePermissionToggle = useCallback(
    (toolName: string, requiresPermission: boolean) => {
      setTempPermissions((prev) => ({
        ...prev,
        [toolName]: requiresPermission,
      }));
    },
    [],
  );

  // Save permissions
  const handleSavePermissions = useCallback(() => {
    if (onPermissionToggle) {
      Object.entries(tempPermissions).forEach(
        ([toolName, requiresPermission]) => {
          const currentTool = tools.find((t) => t.name === toolName);
          if (
            currentTool &&
            currentTool.is_required_permission !== requiresPermission
          ) {
            onPermissionToggle(toolName, requiresPermission);
          }
        },
      );
    }
    setShowPermissionsDialog(false);
  }, [tempPermissions, onPermissionToggle, tools]);

  // Don't render if no tools
  if (!tools.length) return null;

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <div className="text-muted-foreground flex items-center gap-1 text-xs font-medium">
          <Wrench className="h-3 w-3" />
          Tools ({tools.length})
          <If condition={!isConnected}>
            <span className="text-muted-foreground text-xs font-normal">
              (Connect to enable)
            </span>
          </If>
        </div>
        <div className="flex items-center gap-1">
          <If condition={isConnected && onPermissionToggle}>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleOpenPermissionsDialog}
                    className="text-muted-foreground hover:text-foreground h-6 px-2 text-xs"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top" className="text-xs">
                  Manage Permissions
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </If>
          <If condition={shouldShowExpandButton}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAllTools(!showAllTools)}
              className="text-muted-foreground hover:text-foreground h-6 px-2 text-xs"
            >
              <If
                condition={showAllTools}
                fallback={
                  <>
                    <ChevronDown className="mr-1 h-3 w-3" />
                    Show All
                  </>
                }
              >
                <ChevronUp className="mr-1 h-3 w-3" />
                Show Less
              </If>
            </Button>
          </If>
        </div>
      </div>

      <div
        className={cn(
          'flex flex-wrap gap-1.5',
          showAllTools &&
            shouldShowExpandButton &&
            'scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent max-h-32 overflow-y-auto',
        )}
      >
        {toolsToShow.map((tool, index) => {
          const isEnabled = tool.is_enabled ?? false;
          const requiresPermission = tool.is_required_permission ?? false;
          const isToggling = isTogglingTool?.(tool.name) || false;
          const canToggle = isConnected && onToolToggle && !isToggling;

          return (
            <TooltipProvider key={index}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={isEnabled ? 'secondary' : 'outline'}
                    size="sm"
                    onClick={() =>
                      canToggle && onToolToggle(tool.name, !isEnabled)
                    }
                    disabled={!canToggle}
                    className={cn(
                      'h-auto min-h-[24px] px-2 py-1 text-xs transition-all duration-200 ease-in-out',
                      'relative hover:scale-[1.02] active:scale-[0.98]',
                      // Enabled state with permission variants
                      isEnabled &&
                        !requiresPermission && [
                          'border-primary/30 bg-primary/10 text-primary-foreground',
                          'hover:border-primary/40 hover:bg-primary/15',
                          'shadow-xs hover:shadow-md',
                        ],
                      // Enabled state with permissions - amber/yellow styling
                      isEnabled &&
                        requiresPermission && [
                          'border-amber-300 bg-amber-50 dark:border-amber-700 dark:bg-amber-950/30',
                          'text-amber-800 dark:text-amber-200',
                          'hover:bg-amber-100 dark:hover:bg-amber-950/50',
                          'hover:border-amber-400 dark:hover:border-amber-600',
                          'shadow-xs shadow-amber-200/50 dark:shadow-amber-900/20',
                          'hover:shadow-md hover:shadow-amber-200/60 dark:hover:shadow-amber-900/30',
                        ],
                      // Disabled state
                      !isEnabled && [
                        'bg-muted/30 text-muted-foreground opacity-60',
                        'border-muted-foreground/20 hover:opacity-80',
                        'hover:border-muted-foreground/30 hover:bg-muted/40',
                      ],
                      // Loading state
                      isToggling && 'pointer-events-none animate-pulse',
                      // Not connected state
                      !isConnected && 'cursor-not-allowed opacity-50',
                    )}
                  >
                    <div className="flex items-center gap-1.5">
                      <div
                        className={cn(
                          'flex items-center justify-center',
                          isToggling && 'animate-spin',
                        )}
                      >
                        <If
                          condition={isToggling}
                          fallback={
                            <div
                              className={cn(
                                'h-2 w-2 rounded-full transition-all duration-200',
                                isEnabled
                                  ? requiresPermission
                                    ? 'bg-amber-500 shadow-xs shadow-amber-500/30'
                                    : 'bg-green-500 shadow-xs shadow-green-500/30'
                                  : 'border-muted-foreground/30 bg-muted-foreground/40 border',
                              )}
                            />
                          }
                        >
                          <Loader2 className="h-3 w-3" />
                        </If>
                      </div>
                      <span
                        className={cn(
                          'max-w-[80px] truncate font-medium transition-all duration-200',
                          isEnabled
                            ? requiresPermission
                              ? 'text-amber-800 dark:text-amber-200'
                              : 'text-foreground'
                            : 'text-muted-foreground',
                        )}
                      >
                        {tool.name}
                      </span>
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  align="center"
                  className="border-border bg-popover max-w-[300px] rounded-lg border px-3 py-2 shadow-lg"
                  sideOffset={8}
                >
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          'h-2.5 w-2.5 rounded-full',
                          isEnabled
                            ? requiresPermission
                              ? 'bg-amber-500'
                              : 'bg-green-500'
                            : 'bg-muted-foreground/40',
                        )}
                      />
                      <p className="text-popover-foreground text-sm font-medium">
                        {tool.name}
                      </p>
                      <If condition={requiresPermission}>
                        <div className="inline-flex items-center gap-1 rounded bg-amber-100 px-1.5 py-0.5 text-xs font-medium text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                          <Eye className="h-3 w-3" />
                          Protected
                        </div>
                      </If>
                    </div>
                    <div className="space-y-1">
                      <If condition={Boolean(tool.description)}>
                        <p className="text-muted-foreground text-xs">
                          {tool.description}
                        </p>
                      </If>
                      <p className="text-muted-foreground text-xs">
                        <span className="font-medium">Status:</span>{' '}
                        {isEnabled ? 'Active' : 'Inactive'}
                      </p>
                      <If condition={requiresPermission && isEnabled}>
                        <p className="text-xs text-amber-600 dark:text-amber-400">
                          <span className="font-medium">Permission:</span> User
                          approval required before execution
                        </p>
                      </If>
                      <If condition={isConnected}>
                        <p className="text-muted-foreground text-xs">
                          <span className="font-medium">Action:</span> Click to{' '}
                          {isEnabled ? 'deactivate' : 'activate'} this tool
                        </p>
                      </If>
                      <If condition={!isConnected}>
                        <p className="text-xs text-amber-600 dark:text-amber-400">
                          Connection must be active to toggle tools
                        </p>
                      </If>
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>

      {/* Tool Permissions Dialog */}
      <Dialog
        open={showPermissionsDialog}
        onOpenChange={setShowPermissionsDialog}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Tool Permissions{connectionName ? ` - ${connectionName}` : ''}
            </DialogTitle>
            <DialogDescription>
              Choose which tools require approval before execution.
            </DialogDescription>
          </DialogHeader>
          <div className="max-h-96 space-y-4 overflow-y-auto">
            {tools.map((tool) => {
              const requiresPermission = tempPermissions[tool.name] || false;

              return (
                <div
                  key={tool.name}
                  className={cn(
                    'flex items-center justify-between rounded-lg border p-3 transition-colors',
                    requiresPermission
                      ? 'border-amber-500 bg-amber-50/50 dark:bg-amber-950/20'
                      : 'border-gray-200 dark:border-gray-700',
                  )}
                >
                  <div className="flex flex-1 items-center gap-3">
                    <div
                      className={cn(
                        'h-2 w-2 rounded-full',
                        requiresPermission ? 'bg-amber-500' : 'bg-gray-400',
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="text-sm font-medium">{tool.name}</span>
                      <span className="text-muted-foreground text-xs">
                        {requiresPermission
                          ? 'Requires approval'
                          : 'No approval needed'}
                      </span>
                    </div>
                    <If condition={requiresPermission}>
                      <Badge
                        variant="outline"
                        className="ml-2 border-amber-500 text-xs text-amber-600"
                      >
                        Protected
                      </Badge>
                    </If>
                  </div>
                  <Switch
                    checked={requiresPermission}
                    onCheckedChange={(checked) =>
                      handlePermissionToggle(tool.name, checked)
                    }
                    className="ml-4"
                  />
                </div>
              );
            })}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPermissionsDialog(false)}
            >
              Cancel
            </Button>
            <Button onClick={handleSavePermissions}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
