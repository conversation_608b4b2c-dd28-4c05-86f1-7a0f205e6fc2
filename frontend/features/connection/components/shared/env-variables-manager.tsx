'use client';

import { useCallback, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ConnectionEnvType } from '@/openapi-ts/gens';
import { Eye, EyeOff, X } from 'lucide-react';

// Types
export interface EnvVariable {
  key: string;
  value: string;
  type: ConnectionEnvType;
}

interface EnvVariablesManagerProps {
  readonly envVariables: EnvVariable[];
  readonly onEnvVariablesChange: (envVariables: EnvVariable[]) => void;
  readonly canEditKeys?: boolean;
  readonly canDelete?: boolean;
  readonly title?: string;
  readonly emptyStateMessage?: string;
}

export function EnvVariablesManager({
  envVariables,
  onEnvVariablesChange,
  canEditKeys = false,
  canDelete = true,
  title = 'Environment Variables',
  emptyStateMessage = 'No environment variables configured.',
}: EnvVariablesManagerProps) {
  const [visibilityState, setVisibilityState] = useState<
    Record<number, boolean>
  >({});

  const updateEnvVariable = useCallback(
    (index: number, field: 'key' | 'value', value: string) => {
      const newEnvVariables = [...envVariables];
      newEnvVariables[index] = { ...newEnvVariables[index], [field]: value };
      onEnvVariablesChange(newEnvVariables);
    },
    [envVariables, onEnvVariablesChange],
  );

  const removeEnvVariable = useCallback(
    (index: number) => {
      const newEnvVariables = envVariables.filter((_, i) => i !== index);
      onEnvVariablesChange(newEnvVariables);
    },
    [envVariables, onEnvVariablesChange],
  );

  const handleValuePaste = useCallback(
    (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
      const pastedText = e.clipboardData.getData('text');
      if (pastedText.includes('\n')) {
        e.preventDefault();
        updateEnvVariable(index, 'value', pastedText);
      }
    },
    [updateEnvVariable],
  );

  const toggleVisibility = useCallback((index: number) => {
    setVisibilityState((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  }, []);

  const getInputComponent = useCallback(
    (envVar: EnvVariable, index: number) => {
      const isMultiline =
        envVar.type === ConnectionEnvType.yaml ||
        envVar.type === ConnectionEnvType.json;

      if (isMultiline) {
        return (
          <div className="relative">
            <textarea
              placeholder={
                envVar.type === ConnectionEnvType.yaml
                  ? 'key: value\narray:\n  - item1\n  - item2'
                  : envVar.type === ConnectionEnvType.json
                    ? '{\n  "key": "value",\n  "array": ["item1", "item2"]\n}'
                    : 'variable_value'
              }
              value={envVar.value}
              onChange={(e) =>
                updateEnvVariable(index, 'value', e.target.value)
              }
              className="border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring min-h-[120px] w-full resize-none rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
              style={{
                filter: visibilityState[index] ? 'none' : 'blur(4px)',
                fontFamily: 'monospace',
                resize: 'vertical',
              }}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => toggleVisibility(index)}
              className="text-muted-foreground hover:text-foreground absolute top-2 right-1 h-6 w-6 p-1"
              aria-label={visibilityState[index] ? 'Hide value' : 'Show value'}
            >
              {visibilityState[index] ? (
                <EyeOff className="h-3 w-3" />
              ) : (
                <Eye className="h-3 w-3" />
              )}
            </Button>
          </div>
        );
      }

      return (
        <>
          <Input
            placeholder="variable_value"
            value={envVar.value}
            onChange={(e) => updateEnvVariable(index, 'value', e.target.value)}
            onPaste={(e) => handleValuePaste(index, e)}
            className="h-8 pr-10 font-mono"
            type={visibilityState[index] ? 'text' : 'password'}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => toggleVisibility(index)}
            className="text-muted-foreground hover:text-foreground absolute top-1/2 right-1 h-6 w-6 -translate-y-1/2 p-1"
            aria-label={visibilityState[index] ? 'Hide value' : 'Show value'}
          >
            {visibilityState[index] ? (
              <EyeOff className="h-3 w-3" />
            ) : (
              <Eye className="h-3 w-3" />
            )}
          </Button>
        </>
      );
    },
    [visibilityState, updateEnvVariable, handleValuePaste, toggleVisibility],
  );

  return (
    <div className="space-y-2">
      {title && (
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium">{title}</h4>
        </div>
      )}

      <div className="space-y-2">
        {envVariables.length === 0 ? (
          <div className="text-muted-foreground rounded-lg border border-dashed p-4 text-center text-sm">
            {emptyStateMessage}
          </div>
        ) : (
          <div className="space-y-3">
            {envVariables.map((envVar, index) => (
              <div key={index} className="space-y-3 rounded-lg border p-4">
                {/* Header: Key and Type */}
                <div className="flex items-center gap-2">
                  {/* Key Input */}
                  <div className="flex-1">
                    <Input
                      placeholder="VARIABLE_NAME"
                      value={envVar.key}
                      onChange={
                        canEditKeys
                          ? (e) =>
                              updateEnvVariable(index, 'key', e.target.value)
                          : undefined
                      }
                      className="text-muted-foreground h-8 font-mono text-xs"
                      readOnly={!canEditKeys}
                      disabled={!canEditKeys}
                    />
                  </div>

                  {/* Type Badge */}
                  <div className="flex items-center">
                    <span className="bg-muted text-muted-foreground inline-flex items-center rounded-md px-2 py-1 text-xs font-medium">
                      {envVar.type.toUpperCase()}
                    </span>
                  </div>

                  {/* Remove Button */}
                  {canDelete && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeEnvVariable(index)}
                      className="text-muted-foreground hover:text-destructive h-8 w-8 p-0"
                      aria-label="Remove variable"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                {/* Value Input */}
                <div className="relative">
                  {getInputComponent(envVar, index)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
