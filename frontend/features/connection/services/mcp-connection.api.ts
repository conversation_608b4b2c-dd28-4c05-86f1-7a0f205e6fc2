import {
  SchemaMcpConnectionCreate,
  SchemaMcpConnectionUpdate,
} from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export const mcpConnectionApi = {
  list: () => fetchData(api.GET('/api/v1/mcp-connection')),
  create: (mcp_conn_create: SchemaMcpConnectionCreate) =>
    fetchData(
      api.POST('/api/v1/mcp-connection', {
        body: mcp_conn_create,
      }),
    ),
  update: (mcp_conn_id: string, mcp_conn_update: SchemaMcpConnectionUpdate) =>
    fetchData(
      api.PUT('/api/v1/mcp-connection/{mcp_conn_id}', {
        params: { path: { mcp_conn_id } },
        body: mcp_conn_update,
      }),
    ),
  delete: (mcp_conn_id: string) =>
    fetchData(
      api.DELETE('/api/v1/mcp-connection/{mcp_conn_id}', {
        params: { path: { mcp_conn_id } },
      }),
    ),
  updateTool: (
    mcp_conn_id: string,
    tool_name: string,
    is_enabled: boolean,
    is_required_permission: boolean,
  ) =>
    fetchData(
      api.PATCH('/api/v1/mcp-connection/{mcp_conn_id}/tool', {
        params: { path: { mcp_conn_id } },
        body: { tool_name, is_enabled, is_required_permission },
      }),
    ),
};
