import { createQueryKeys } from '@lukemorales/query-key-factory';
import { keepPreviousData, useQuery } from '@tanstack/react-query';

import { RegionQueryParams } from '../models/constants.type';
import { constantsApi } from '../services/constants.api';

const constantsQueryKeys = createQueryKeys('constants', {
  regions: (query: RegionQueryParams) => ({
    queryKey: [query],
    queryFn: () => constantsApi.getRegions(query),
  }),
});

const useRegions = (query: RegionQueryParams) => {
  return useQuery({
    ...constantsQueryKeys.regions(query),
    placeholderData: keepPreviousData,
  });
};

export const constantsQuery = {
  query: {
    useRegions,
  },
};
