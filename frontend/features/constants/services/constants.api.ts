import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { RegionQueryParams } from '../models/constants.type';

export const constantsApi = {
  //   getStartTemplates: () =>
  //     fetchData(
  //       api.GET('/api/v1/utils/constants/quick_start_templates', {
  //         next: { tags: [ETagCache.CONSTANTS] },
  //       }),
  //     ),

  //   getExamplePrompts: (
  //     query: PathsRequestQueryDto<'/api/v1/utils/constants/example_prompts'>,
  //   ) =>
  //     fetchData(
  //       api.GET('/api/v1/utils/constants/example_prompts', {
  //         params: { query },
  //       }),
  //     ),
  getRegions: (query: RegionQueryParams) =>
    fetchData(
      api.GET('/api/v1/utils/constants/cloud_regions', {
        params: {
          query,
        },
      }),
    ).then((res) =>
      res.data.map((v) => ({
        value: v,
        label: v,
      })),
    ),
};
