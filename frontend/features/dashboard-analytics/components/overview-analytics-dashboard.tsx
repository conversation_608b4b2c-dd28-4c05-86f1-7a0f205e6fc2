import React from 'react';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { formatUSD } from '@/lib/currency';

import { dashboardAnalyticsQuery } from '../hooks/dashboard-analytics.query';
import { HighConsumingServices } from './high-consuming-services';
import { TopRecommendations } from './top-recommendations';

interface OverviewAnalyticsDashboardProps {
  topServicesLimit?: number;
}

export const OverviewAnalyticsDashboard: React.FC<
  OverviewAnalyticsDashboardProps
> = ({ topServicesLimit = 10 }) => {
  const { data: analyticsData, isLoading } =
    dashboardAnalyticsQuery.query.useAnalytics();

  return (
    <div className="space-y-6">
      {/* Cost Overview Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Monthly Savings ($)
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : analyticsData?.cost_overview ? (
              <>
                <div className="text-2xl font-bold">
                  {formatUSD(
                    analyticsData.cost_overview.total_estimated_saving_amount ||
                      0,
                  )}
                </div>
                <p className="text-muted-foreground text-xs">
                  Potential monthly savings
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Saving Opportunities
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
              <circle cx="9" cy="7" r="4" />
              <path d="M22 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : analyticsData?.cost_overview ? (
              <>
                <div className="text-2xl font-bold">
                  {Math.round(
                    analyticsData.cost_overview
                      .total_optimization_opportunities,
                  )}
                </div>
                <p className="text-muted-foreground text-xs">
                  Available optimization opportunities
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Well Optimized
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <path d="M2 10h20" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : analyticsData?.cost_overview ? (
              <>
                <div className="text-2xl font-bold">
                  {Math.round(analyticsData.cost_overview.total_well_optimized)}
                </div>
                <p className="text-muted-foreground text-xs">
                  Well-optimized resources
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Resources Scanned
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <>
                <Skeleton className="mb-2 h-8 w-[100px]" />
                <Skeleton className="h-4 w-[140px]" />
              </>
            ) : analyticsData?.cost_overview ? (
              <>
                <div className="text-2xl font-bold">
                  {Math.round(
                    analyticsData.cost_overview.total_resource_scanned,
                  )}
                </div>
                <p className="text-muted-foreground text-xs">
                  Resources scanned for optimization
                </p>
              </>
            ) : null}
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Content */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-7">
        <div className="col-span-4">
          <HighConsumingServices topServicesLimit={topServicesLimit} />
        </div>
        <div className="col-span-4 md:col-span-3">
          <TopRecommendations />
        </div>
      </div>
    </div>
  );
};
