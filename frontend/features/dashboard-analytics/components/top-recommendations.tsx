import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { dashboardAnalyticsQuery } from '@/features/dashboard-analytics';
import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '@/features/resources/config/resource-type.config';
import { formatUSD } from '@/lib/currency';
import { CloudIcon } from 'lucide-react';

export function TopRecommendations() {
  const { data: recommendationsData, isLoading } =
    dashboardAnalyticsQuery.query.useTopRecommendations();

  const content = isLoading ? (
    Array(5)
      .fill(0)
      .map((_, i) => (
        <div key={i} className="mb-4 flex items-center space-x-4 p-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <div className="flex space-x-2">
              <Skeleton className="h-5 w-12 rounded-full" />
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
            <Skeleton className="h-3 w-[150px]" />
          </div>
          <div className="space-y-1 text-right">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-3 w-[60px]" />
          </div>
        </div>
      ))
  ) : (
    <div className="space-y-3">
      {recommendationsData?.recommendations?.length ? (
        recommendationsData.recommendations.map((recommendation) => (
          <div
            key={recommendation.id}
            className="hover:border-border hover:bg-muted/50 flex items-center space-x-4 rounded-lg border border-transparent p-3 transition-colors"
          >
            <div className="flex h-12 w-12 shrink-0 items-center justify-center overflow-hidden rounded-lg">
              {(() => {
                const resourceType = recommendation.resource_type.toUpperCase();
                const config =
                  RESOURCE_TYPE_CONFIG.CONFIG[resourceType as AWSResourceType];
                if (config?.icon) {
                  const IconComponent = config.icon;
                  return (
                    <IconComponent size={28} className="text-foreground" />
                  );
                }
                return <CloudIcon size={28} className="text-foreground" />;
              })()}
            </div>

            <div className="flex-1 space-y-2">
              <p className="text-sm leading-none font-medium">
                {recommendation.title}
              </p>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="px-2 py-0.5 text-xs">
                  Effort: {recommendation.effort}
                </Badge>
                <Badge variant="secondary" className="px-2 py-0.5 text-xs">
                  Risk: {recommendation.risk}
                </Badge>
              </div>
            </div>

            <div className="text-right">
              <div className="text-sm font-medium text-green-600">
                {formatUSD(recommendation.potential_monthly_savings)}
              </div>
              <div className="text-muted-foreground text-xs">/ month</div>
            </div>
          </div>
        ))
      ) : (
        <div className="py-8 text-center">
          <p className="text-muted-foreground text-sm">
            No recommendations found
          </p>
        </div>
      )}
    </div>
  );

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Top 5 Potential Monthly Savings</CardTitle>
        <CardDescription>
          Recommendations with effort and risk assessment
        </CardDescription>
      </CardHeader>
      <CardContent className="overflow-thin-auto h-[400px] pt-0">
        {content}
      </CardContent>
    </Card>
  );
}
