import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { DashboardAnalyticsQueryParams } from '../models/dashboard-analytics.type';

export const dashboardAnalyticsApi = {
  getAnalytics: (query?: DashboardAnalyticsQueryParams) =>
    fetchData(
      api.GET('/api/v1/dashboard-analytics/analytics', {
        params: {
          query: query || {},
        },
      }),
    ),

  getHighConsumingServices: (query?: DashboardAnalyticsQueryParams) =>
    fetchData(
      api.GET('/api/v1/dashboard-analytics/high-consuming-services', {
        params: {
          query: query || {},
        },
      }),
    ),

  getTopRecommendations: (query?: DashboardAnalyticsQueryParams) =>
    fetchData(
      api.GET('/api/v1/dashboard-analytics/top-recommendations', {
        params: {
          query: query || {},
        },
      }),
    ),
};
