'use client';

import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useFormFilter } from '@/hooks/use-form-filter';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';

import { DocumentQueryParams, DocumentType } from '../models/document.type';

interface DocumentFilterProps {
  defaultValues: WithPaginationDefaults<DocumentQueryParams>;
}

export function DocumentFilter({ defaultValues }: DocumentFilterProps) {
  const { form, onSubmit } = useFormFilter<
    WithPaginationDefaults<DocumentQueryParams>
  >({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'search',
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        <FormField
          control={control}
          name="search"
          render={({ field }) => (
            <FormItem className="grow md:max-w-lg">
              <FormControl>
                <Input
                  placeholder="Search documents..."
                  className="w-full"
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="type"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Select
                  value={String(
                    (field.value as DocumentType | null | undefined) ?? 'all',
                  )}
                  onValueChange={(val) =>
                    field.onChange(val === 'all' ? undefined : val)
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="file">File</SelectItem>
                    <SelectItem value="url">Website</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
