'use client';

import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { DocumentQueryParams } from '../models/document.type';
import { DocumentTable } from './document-tables/document-table';

interface DocumentListProps {
  kbId: string;
  searchParams: WithPaginationDefaults<DocumentQueryParams>;
  uploadTrigger?: React.ReactNode;
}

export function DocumentList({
  kbId,
  searchParams,
  uploadTrigger,
}: DocumentListProps) {
  return (
    <div className="flex flex-col gap-4">
      {uploadTrigger && <div className="flex justify-end">{uploadTrigger}</div>}

      <div className="overflow-thin-auto grow">
        <DocumentTable kbId={kbId} searchParams={searchParams} />
      </div>
    </div>
  );
}
