'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { formatBytes } from '@/lib/utils/format-bytes';
import { components } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import {
  Eye,
  FileText,
  Globe,
  Loader2,
  MoreHorizontal,
  Trash2,
  Type,
} from 'lucide-react';

import { ASYNC_TASK_STATUS_CONFIG } from '../../config/document-type.config';

type DocumentKBRead = components['schemas']['DocumentKBRead'];

interface DocumentActionsProps {
  document: DocumentKBRead;
  onView: (document: DocumentKBRead) => void;
  onDelete: (document: DocumentKBRead) => void;
  loadingDocumentId?: string;
}

function DocumentActions({
  document,
  onView,
  onDelete,
  loadingDocumentId,
}: DocumentActionsProps) {
  const isLoadingView = loadingDocumentId === document.id;
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="hover:bg-muted h-8 w-8 rounded-full"
          aria-label="Actions"
        >
          <MoreHorizontal className="size-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        <DropdownMenuItem
          onClick={() => onView(document)}
          disabled={isLoadingView}
        >
          <>
            {isLoadingView ? (
              <Loader2 className="mr-2 size-4 animate-spin" />
            ) : (
              <Eye className="mr-2 size-4" />
            )}
            <span>View</span>
          </>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => onDelete(document)}
          className="text-destructive"
          disabled={!document.object_name}
        >
          <>
            <Trash2 className="mr-2 size-4" />
            <span>Delete</span>
          </>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

const getDocumentIcon = (type: 'url' | 'text' | 'file') => {
  switch (type) {
    case 'url':
      return <Globe className="size-4" />;
    case 'text':
      return <Type className="size-4" />;
    default:
      return <FileText className="size-4" />;
  }
};

export const createDocumentColumns = (
  onView: (document: DocumentKBRead) => void,
  onDelete: (document: DocumentKBRead) => void,
  loadingDocumentId?: string,
): ColumnDef<DocumentKBRead>[] => [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => {
      const { name, url } = row.original;
      return (
        <div className="space-y-1">
          <div className="max-w-[300px] truncate font-medium">{name}</div>
          {url && (
            <div className="text-muted-foreground max-w-[300px] truncate text-xs">
              {url}
            </div>
          )}
        </div>
      );
    },
    minSize: 200,
    maxSize: 400,
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const { type } = row.original;
      return (
        <div className="flex items-center justify-center">
          {getDocumentIcon(type)}
        </div>
      );
    },
    size: 120,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'embed_status',
    header: 'Status',
    cell: ({ row }) => {
      const { embed_status } = row.original;
      const statusConfig = ASYNC_TASK_STATUS_CONFIG[embed_status];

      return (
        <Badge variant={statusConfig?.variant || 'outline'}>
          {statusConfig?.label || embed_status}
        </Badge>
      );
    },
    size: 120,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'file_size',
    header: 'Size',
    cell: ({ row }) => {
      const { file_size } = row.original;
      if (!file_size) {
        return <span className="text-muted-foreground text-sm">N/A</span>;
      }

      return <span className="text-sm">{formatBytes(file_size)}</span>;
    },
    size: 100,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: renderCellToFullDateTime,
    size: 200,
    meta: {
      className: 'text-center whitespace-nowrap',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      return (
        <DocumentActions
          document={row.original}
          onView={onView}
          onDelete={onDelete}
          loadingDocumentId={loadingDocumentId}
        />
      );
    },
    size: 90,
    meta: {
      className: 'text-right',
      header: {
        className:
          'sticky right-0 z-20 bg-background text-right shadow-[inset_-1px_0_0_0_var(--border)]',
      },
      cell: {
        className:
          'sticky right-0 z-10 bg-background shadow-[inset_-1px_0_0_0_var(--border)]',
      },
    },
  },
];
