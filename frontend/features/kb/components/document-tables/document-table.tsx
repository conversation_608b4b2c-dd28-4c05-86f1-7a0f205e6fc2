'use client';

import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ErrorAlert } from '@/components/ui/error-alert';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import { TableUI } from '@/components/ui/table/table-ui';
import { documentUrlCache } from '@/lib/document-url-cache';
import { components } from '@/openapi-ts/gens';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { toast } from 'sonner';

import { documentQuery } from '../../hooks/document.query';
import { DocumentQueryParams } from '../../models/document.type';
import { createDocumentColumns } from './document-columns';

type DocumentKBRead = components['schemas']['DocumentKBRead'];

interface DocumentTableProps {
  kbId: string;
  searchParams: WithPaginationDefaults<DocumentQueryParams>;
}

export function DocumentTable({ kbId, searchParams }: DocumentTableProps) {
  const [deletingDocument, setDeletingDocument] =
    useState<DocumentKBRead | null>(null);
  const [loadingDocumentId, setLoadingDocumentId] = useState<string | null>(
    null,
  );

  const { data, isLoading, isRefetching, isError } =
    documentQuery.query.useList(kbId, searchParams);
  const deleteMutation = documentQuery.mutation.useDelete(kbId);

  const handleView = async (document: DocumentKBRead) => {
    if (!document.object_name) {
      toast.error('Cannot view document: missing storage information');
      return;
    }

    if (document.type === 'url' && document.url) {
      window.open(document.url, '_blank', 'noopener,noreferrer');
      return;
    }

    setLoadingDocumentId(document.id);
    try {
      // Open a blank tab synchronously to avoid popup blockers, then navigate
      const newWindow = window.open('', '_blank', 'noopener,noreferrer');
      const url = await documentUrlCache.getViewUrl(kbId, document.object_name);
      if (newWindow) {
        newWindow.location.href = url;
      } else {
        // Fallback
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    } catch (error) {
      console.error('Error getting document URL:', error);
      toast.error('Failed to load document for viewing');
    } finally {
      setLoadingDocumentId(null);
    }
  };

  const handleDelete = (document: DocumentKBRead) => {
    setDeletingDocument(document);
  };

  const confirmDelete = () => {
    if (deletingDocument) {
      if (!deletingDocument.object_name) {
        toast.error('Cannot delete document: missing storage information');
        setDeletingDocument(null);
        return;
      }

      deleteMutation.mutate(
        {
          documentId: deletingDocument.id,
          objectName: deletingDocument.object_name,
        },
        {
          onSuccess: () => {
            setDeletingDocument(null);
          },
        },
      );
    }
  };

  const documentColumns = createDocumentColumns(
    handleView,
    handleDelete,
    loadingDocumentId || undefined,
  );

  if (data) {
    return (
      <>
        <TableUI
          data={data.data}
          columns={documentColumns}
          meta={data.meta}
          isRefetching={isRefetching}
        />

        {/* Delete Confirmation */}
        <AlertDialog
          open={!!deletingDocument}
          onOpenChange={() => setDeletingDocument(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Document</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete &quot;{deletingDocument?.name}
                &quot;? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  }

  if (isLoading) {
    return <DataTableSkeleton columnCount={documentColumns.length} />;
  }

  if (isError) {
    return (
      <ErrorAlert
        title="Failed to Load Documents"
        description="Unable to fetch documents. Please try again."
      />
    );
  }
}
