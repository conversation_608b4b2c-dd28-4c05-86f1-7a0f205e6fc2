'use client';

import { PropsWith<PERSON>hildren, useCallback, useRef, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SchemaConfirmUploadsRequest } from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  AlertCircle,
  CheckCircle,
  FileText,
  Globe,
  Upload,
  X,
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import {
  ACCEPTED_FILE_TYPES,
  MAX_FILES_PER_UPLOAD,
  MAX_FILE_SIZE,
  SUPPORTED_FILE_TYPES,
} from '../config/document-type.config';
import { documentQuery } from '../hooks/document.query';
import {
  FileUploadInfo,
  PresignedUploadInfo,
  WebsiteIngestionData,
} from '../models/document.type';

const websiteSchema = z.object({
  url: z.string().url('Please enter a valid URL'),
  deep_crawl: z.boolean(),
});

type WebsiteFormValues = z.infer<typeof websiteSchema>;

interface DocumentUploadProps extends PropsWithChildren {
  kbId: string;
}

export function DocumentUpload({ kbId, children }: DocumentUploadProps) {
  const [activeTab, setActiveTab] = useState<'files' | 'website'>('files');
  type SelectedFileInfo = FileUploadInfo & { tempId: string };
  const [selectedFiles, setSelectedFiles] = useState<SelectedFileInfo[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>(
    {},
  );
  const [isOpen, setIsOpen] = useState(false);

  // Track active uploads to support cancel/abort
  const xhrMapRef = useRef<Record<string, XMLHttpRequest>>({});

  const getPresignedMutation = documentQuery.mutation.useGetPresignedUrls(kbId);
  const confirmUploadMutation = documentQuery.mutation.useConfirmUploads(kbId);
  const websiteMutation = documentQuery.mutation.useIngestWebsite(kbId);

  const websiteForm = useForm<WebsiteFormValues>({
    resolver: zodResolver(websiteSchema),
    defaultValues: {
      url: '',
      deep_crawl: false,
    },
  });

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      const validFiles = acceptedFiles.filter((file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast.error(`${file.name} is too large. Maximum size is 10MB.`);
          return false;
        }
        return true;
      });

      if (selectedFiles.length + validFiles.length > MAX_FILES_PER_UPLOAD) {
        toast.error(
          `Maximum ${MAX_FILES_PER_UPLOAD} files allowed per upload.`,
        );
        return;
      }

      const fileInfos: SelectedFileInfo[] = validFiles.map((file) => ({
        file,
        name: file.name,
        type: file.type,
        size: file.size,
        tempId: crypto.randomUUID(),
      }));

      setSelectedFiles((prev) => [...prev, ...fileInfos]);
    },
    [selectedFiles.length],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: MAX_FILE_SIZE,
  });

  const removeFile = (tempId: string) => {
    setSelectedFiles((prev) => prev.filter((file) => file.tempId !== tempId));
    setUploadProgress((prev) => {
      const newProgress = { ...prev } as Record<string, number>;
      delete newProgress[tempId];
      return newProgress;
    });
  };

  const uploadFiles = async () => {
    if (selectedFiles.length === 0) return;

    try {
      // Get presigned URLs
      const presignedData: PresignedUploadInfo[] = selectedFiles.map(
        (fileInfo) => ({
          file_name: fileInfo.name,
          content_type: fileInfo.type,
          file_size: fileInfo.size,
        }),
      );

      const presignedResponse =
        await getPresignedMutation.mutateAsync(presignedData);

      // Helper to upload with progress via XHR with timeout and abort
      const uploadWithProgress = (
        tempId: string,
        url: string,
        file: File,
        contentType: string,
        onProgress: (pct: number) => void,
      ) => {
        return new Promise<void>((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open('PUT', url);
          xhr.setRequestHeader('Content-Type', contentType);
          // 2 minutes timeout per file
          xhr.timeout = 120_000;
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const pct = Math.round((event.loaded / event.total) * 100);
              onProgress(pct);
            }
          };
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              onProgress(100);
              resolve();
            } else {
              reject(new Error(`Upload failed with status ${xhr.status}`));
            }
          };
          xhr.onerror = () => reject(new Error('Network error during upload'));
          xhr.ontimeout = () => reject(new Error('Upload timed out'));
          xhr.onabort = () => reject(new Error('Upload canceled'));
          // store XHR for cancellation
          xhrMapRef.current[tempId] = xhr;
          xhr.send(file);
        }).finally(() => {
          delete xhrMapRef.current[tempId];
        });
      };

      // Upload files to S3
      const uploadPromises = selectedFiles.map(async (fileInfo, index) => {
        const presignedInfo = presignedResponse.presigned_urls[index];

        setUploadProgress((prev) => ({
          ...prev,
          [fileInfo.tempId]: 0,
        }));

        await uploadWithProgress(
          fileInfo.tempId,
          presignedInfo.presigned_url,
          fileInfo.file,
          fileInfo.type,
          (pct) =>
            setUploadProgress((prev) => ({
              ...prev,
              [fileInfo.tempId]: pct,
            })),
        );

        const payload: SchemaConfirmUploadsRequest['uploaded_files'][number] = {
          file_id: presignedInfo.file_id,
          filename: presignedInfo.filename,
          storage_key: presignedInfo.storage_key,
          content_type: fileInfo.type,
          file_size: fileInfo.size,
        };

        return payload;
      });

      const uploadedFiles: SchemaConfirmUploadsRequest['uploaded_files'] =
        await Promise.all(uploadPromises);

      // Confirm uploads with backend
      await confirmUploadMutation.mutateAsync(uploadedFiles);

      // Reset state
      setSelectedFiles([]);
      setUploadProgress({});
      setIsOpen(false);
    } catch (error) {
      console.error('Upload failed:', error);
      toast.error('Failed to upload files');
    }
  };

  const cancelUpload = (tempId: string) => {
    const xhr = xhrMapRef.current[tempId];
    if (xhr) {
      try {
        xhr.abort();
      } catch {
        // ignore
      }
    }
    setSelectedFiles((prev) => prev.filter((file) => file.tempId !== tempId));
    setUploadProgress((prev) => {
      const copy = { ...prev } as Record<string, number>;
      delete copy[tempId];
      return copy;
    });
  };

  const onWebsiteSubmit = (data: WebsiteFormValues) => {
    websiteMutation.mutate(data as WebsiteIngestionData, {
      onSuccess: () => {
        websiteForm.reset();
        setIsOpen(false);
      },
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add Documents</DialogTitle>
          <DialogDescription>
            Upload files or add website content to your knowledge base
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as 'files' | 'website')}
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="files">
              <FileText className="mr-2 size-4" />
              Files
            </TabsTrigger>
            <TabsTrigger value="website">
              <Globe className="mr-2 size-4" />
              Website
            </TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="space-y-4">
            {/* File Upload Area */}
            <div
              {...getRootProps()}
              className={`cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                isDragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="text-muted-foreground mx-auto mb-4 size-8" />
              <div className="space-y-2">
                <p className="text-sm font-medium">
                  {isDragActive
                    ? 'Drop the files here...'
                    : 'Drag & drop files here, or click to select'}
                </p>
                <p className="text-muted-foreground text-xs">
                  Supported formats: {SUPPORTED_FILE_TYPES.join(', ')}
                </p>
                <p className="text-muted-foreground text-xs">
                  Maximum file size: 10MB | Maximum files:{' '}
                  {MAX_FILES_PER_UPLOAD}
                </p>
              </div>
            </div>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Selected Files</h4>
                <div className="max-h-40 space-y-2 overflow-y-auto">
                  {selectedFiles.map((fileInfo, index) => {
                    const progress = uploadProgress[fileInfo.tempId];
                    const isUploading =
                      progress !== undefined && progress < 100;
                    const isCompleted = progress === 100;

                    return (
                      <div
                        key={index}
                        className="bg-muted/50 flex items-center gap-3 rounded-lg p-2"
                      >
                        <FileText className="text-muted-foreground size-4" />
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center justify-between">
                            <span className="truncate text-sm font-medium">
                              {fileInfo.name}
                            </span>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {formatFileSize(fileInfo.size)}
                              </Badge>
                              {isCompleted ? (
                                <CheckCircle className="size-4 text-green-500" />
                              ) : isUploading ? (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => cancelUpload(fileInfo.tempId)}
                                >
                                  Cancel
                                </Button>
                              ) : (
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFile(fileInfo.tempId)}
                                >
                                  <X className="size-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                          {isUploading && (
                            <Progress value={progress} className="mt-1 h-1" />
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="website" className="space-y-4">
            <Form {...websiteForm}>
              <form
                onSubmit={websiteForm.handleSubmit(onWebsiteSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={websiteForm.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website URL</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={websiteForm.control}
                  name="deep_crawl"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Deep Crawl</FormLabel>
                        <div className="text-muted-foreground text-sm">
                          Crawl all linked pages within the same domain
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <div className="flex items-start gap-2 rounded-lg bg-blue-50 p-3 dark:bg-blue-950/30">
                  <AlertCircle className="mt-0.5 size-4 text-blue-500" />
                  <div className="text-sm text-blue-700 dark:text-blue-300">
                    Website content will be processed in the background. You can
                    check the status in the documents list.
                  </div>
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={websiteMutation.isPending}>
                    {websiteMutation.isPending
                      ? 'Processing...'
                      : 'Add Website'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>

        {activeTab === 'files' && (
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={uploadFiles}
              disabled={
                selectedFiles.length === 0 ||
                getPresignedMutation.isPending ||
                confirmUploadMutation.isPending
              }
            >
              {getPresignedMutation.isPending || confirmUploadMutation.isPending
                ? 'Uploading...'
                : `Upload ${selectedFiles.length} file${selectedFiles.length !== 1 ? 's' : ''}`}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
