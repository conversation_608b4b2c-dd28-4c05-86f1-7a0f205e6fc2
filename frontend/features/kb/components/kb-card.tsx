import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { components } from '@/openapi-ts/gens';
import { Lock, MoreHorizontal, Users } from 'lucide-react';

import { KB_ACCESS_LEVELS, KB_USAGE_MODES } from '../config';

type KBPublic = components['schemas']['KBPublic'];

interface KBCardProps {
  kb: KBPublic;
  onEdit?: (kb: KBPublic) => void;
  onDelete?: (kb: KBPublic) => void;
  onView?: (kb: KBPublic) => void;
}

export function KBCard({ kb, onEdit, onDelete, onView }: KBCardProps) {
  const accessConfig =
    KB_ACCESS_LEVELS[kb.access_level as keyof typeof KB_ACCESS_LEVELS];
  const usageConfig =
    KB_USAGE_MODES[kb.usage_mode as keyof typeof KB_USAGE_MODES];

  return (
    <Card className="group transition-shadow hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="min-w-0 flex-1">
            <h3 className="mb-1 truncate text-lg font-semibold">{kb.title}</h3>
            {kb.description && (
              <p className="text-muted-foreground line-clamp-2 text-sm">
                {kb.description}
              </p>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 opacity-0 transition-opacity group-hover:opacity-100"
              >
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {onView && (
                <DropdownMenuItem onClick={() => onView(kb)}>
                  View
                </DropdownMenuItem>
              )}
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(kb)}>
                  Edit
                </DropdownMenuItem>
              )}
              {onDelete && (
                <DropdownMenuItem
                  onClick={() => onDelete(kb)}
                  className="text-destructive"
                >
                  Delete
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="mb-3 flex flex-wrap gap-2">
          <Badge variant="outline" className="text-xs">
            {kb.access_level === 'private' ? (
              <Lock className="mr-1 size-3" />
            ) : (
              <Users className="mr-1 size-3" />
            )}
            {accessConfig?.label}
          </Badge>

          <Badge variant="secondary" className="text-xs">
            {usageConfig?.icon && <usageConfig.icon className="mr-1 size-3" />}
            {usageConfig?.label}
          </Badge>
        </div>

        {kb.tags && kb.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {kb.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {kb.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{kb.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="border-t pt-3">
        <div className="text-muted-foreground flex w-full items-center justify-between text-xs">
          <span>{kb.total_document || 0} documents</span>
          <span>
            {kb.created_at ? new Date(kb.created_at).toLocaleDateString() : ''}
          </span>
        </div>
      </CardFooter>
    </Card>
  );
}
