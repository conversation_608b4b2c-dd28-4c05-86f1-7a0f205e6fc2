'use client';

import { PropsWithChildren, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  KBAccessLevel,
  KBUsageMode,
  SchemaKbCreate,
  SchemaKbUpdate,
} from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { X } from 'lucide-react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { z } from 'zod';

import { KB_ACCESS_LEVEL_OPTIONS, KB_USAGE_MODE_OPTIONS } from '../config';
import { kbQuery } from '../hooks/kb.query';

// Using generated API types directly

const kbFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  access_level: z.nativeEnum(KBAccessLevel),
  // Restrict create/edit UI to allowed subset, but keep typing aligned with API
  usage_mode: z.nativeEnum(KBUsageMode),
  tags: z.array(z.string()).optional(),
  allowed_users: z.array(z.string()).optional(),
});

type KBFormValues = z.infer<typeof kbFormSchema>;

interface KBCreateDialogProps extends PropsWithChildren {
  initialData?: Partial<KBFormValues>;
  mode?: 'create' | 'edit';
  kbId?: string; // Required for edit mode
}

export function KBCreateDialog({
  children,
  initialData,
  mode = 'create',
  kbId,
}: KBCreateDialogProps) {
  const [newTag, setNewTag] = useState('');

  const form = useForm<KBFormValues>({
    resolver: zodResolver(kbFormSchema),
    defaultValues: {
      title: initialData?.title || '',
      description: initialData?.description || '',
      access_level: initialData?.access_level || KBAccessLevel.private,
      usage_mode: initialData?.usage_mode || KBUsageMode.manual,
      tags: initialData?.tags || [],
      allowed_users: initialData?.allowed_users || [],
    },
  });

  // Instantiate only the necessary mutation to avoid unused hooks
  const createMutation =
    mode === 'create' ? kbQuery.mutation.useCreate() : undefined;
  const updateMutation =
    mode === 'edit' && kbId ? kbQuery.mutation.useUpdate(kbId) : undefined;

  const [isOpen, setIsOpen] = useState(false);

  const onSubmit: SubmitHandler<KBFormValues> = (data) => {
    if (mode === 'edit' && kbId && updateMutation) {
      updateMutation.mutate(data as SchemaKbUpdate, {
        onSuccess: () => {
          setIsOpen(false);
          form.reset();
        },
      });
    } else {
      createMutation?.mutate(data as SchemaKbCreate, {
        onSuccess: () => {
          setIsOpen(false);
          form.reset();
        },
      });
    }
  };

  const addTag = () => {
    if (newTag.trim() && !form.getValues('tags')?.includes(newTag.trim())) {
      const currentTags = form.getValues('tags') || [];
      form.setValue('tags', [...currentTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags') || [];
    form.setValue(
      'tags',
      currentTags.filter((tag) => tag !== tagToRemove),
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create'
              ? 'Create Knowledge Base'
              : 'Edit Knowledge Base'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new knowledge base to organize your documents and information.'
              : 'Update your knowledge base settings and configuration.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter knowledge base title"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the purpose of this knowledge base"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="access_level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Access Level</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select access level">
                            {field.value
                              ? KB_ACCESS_LEVEL_OPTIONS.find(
                                  (option) => option.value === field.value,
                                )?.label
                              : 'Select access level'}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {KB_ACCESS_LEVEL_OPTIONS.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value}
                            disabled={option.disabled}
                            className={option.disabled ? 'opacity-50' : ''}
                          >
                            <div className="w-full text-left">
                              <div className="flex items-center gap-2 font-medium">
                                {option.label}
                                {option.comingSoon && (
                                  <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700">
                                    Coming Soon
                                  </span>
                                )}
                              </div>
                              <div className="text-muted-foreground text-xs">
                                {option.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="usage_mode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Usage Mode</FormLabel>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select usage mode">
                            {field.value
                              ? KB_USAGE_MODE_OPTIONS.find(
                                  (option) => option.value === field.value,
                                )?.label
                              : 'Select usage mode'}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {KB_USAGE_MODE_OPTIONS.map((option) => (
                          <SelectItem
                            key={option.value}
                            value={option.value}
                            disabled={option.disabled}
                            className={option.disabled ? 'opacity-50' : ''}
                          >
                            <div className="w-full py-1 text-left">
                              <div className="flex items-center gap-2 text-sm font-medium">
                                {option.label}
                                {option.comingSoon && (
                                  <span className="rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-700">
                                    Coming Soon
                                  </span>
                                )}
                              </div>
                              <div className="text-muted-foreground text-xs leading-relaxed">
                                {option.description}
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add a tag"
                          value={newTag}
                          onChange={(e) => setNewTag(e.target.value)}
                          onKeyDown={handleKeyDown}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={addTag}
                        >
                          Add
                        </Button>
                      </div>
                      {field.value && field.value.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {field.value.map((tag) => (
                            <Badge
                              key={tag}
                              variant="secondary"
                              className="text-xs"
                            >
                              {tag}
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="ml-1 h-auto p-0"
                                onClick={() => removeTag(tag)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Add tags to help organize and find your knowledge bases
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  Boolean(createMutation?.isPending) ||
                  Boolean(updateMutation?.isPending)
                }
              >
                {createMutation?.isPending || updateMutation?.isPending
                  ? mode === 'create'
                    ? 'Creating...'
                    : 'Updating...'
                  : mode === 'create'
                    ? 'Create'
                    : 'Update'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
