'use client';

import { Button } from '@/components/ui/button';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { Separator } from '@/components/ui/separator';
import { components } from '@/openapi-ts/gens';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { Upload } from 'lucide-react';

import { kbQuery } from '../hooks/kb.query';
import { DocumentQueryParams } from '../models/document.type';
import { DocumentFilter } from './document-filter';
import { DocumentList } from './document-list';
import { DocumentUpload } from './document-upload';
import { KBDetailStatusSummary } from './kb-status-summary';

interface KBDetailProps {
  kbId: string;
  searchParams: WithPaginationDefaults<DocumentQueryParams>;
}

export function KBDetail({ kbId, searchParams }: KBDetailProps) {
  const { data, isLoading } = kbQuery.query.useDetail(kbId);
  type KBPublic = components['schemas']['KBPublic'];
  const kb = data as KBPublic | undefined;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (!kb) {
    return (
      <div className="py-16 text-center">
        <h3 className="mb-2 text-lg font-semibold">Knowledge Base Not Found</h3>
        <p className="text-muted-foreground">
          The requested knowledge base could not be found.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">{kb.title}</h1>
        {kb.description && (
          <p className="text-muted-foreground max-w-3xl text-lg">
            {kb.description}
          </p>
        )}
      </div>

      {/* KB Detail Status Summary */}
      <div className="max-md:hidden">
        <KBDetailStatusSummary kbId={kbId} />
        <Separator />
      </div>

      {/* Documents filters and list */}
      <div className="flex flex-col gap-4">
        <div className="flex flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
          <div className="max-w-4xl flex-1">
            <DocumentFilter defaultValues={searchParams} />
          </div>

          <DocumentUpload kbId={kbId}>
            <Button>
              <span className="flex items-center">
                <Upload className="mr-2 h-4 w-4" />
                Add Documents
              </span>
            </Button>
          </DocumentUpload>
        </div>

        <DocumentList kbId={kbId} searchParams={searchParams} />
      </div>
    </div>
  );
}
