'use client';

import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useFormFilter } from '@/hooks/use-form-filter';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { omit } from 'lodash';

import { KB_ACCESS_LEVEL_OPTIONS, KB_USAGE_MODE_OPTIONS } from '../config';
import { KBQueryParams } from '../models/kb.type';

interface KBFilterProps {
  defaultValues: WithPaginationDefaults<KBQueryParams>;
}

type FormFilter = WithPaginationDefaults<KBQueryParams>;

export function KBFilter({ defaultValues }: KBFilterProps) {
  const { form, onSubmit } = useFormFilter<FormFilter>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'search',
  });

  const { control, handleSubmit, watch, setValue } = form;

  const handleAccessLevelChange = (value: string) => {
    const access_level = value === 'all' ? undefined : value;
    setValue('access_level', access_level);
  };

  const handleUsageModeChange = (value: string) => {
    const usage_mode = value === 'all' ? undefined : value;
    setValue('usage_mode', usage_mode);
  };

  const currentAccessLevel = watch('access_level') || 'all';
  const currentUsageMode = watch('usage_mode') || 'all';

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        <FormField
          control={control}
          name="search"
          render={({ field }) => (
            <FormItem className="grow md:max-w-lg">
              <FormControl>
                <Input
                  placeholder="Search knowledge bases..."
                  className="w-full"
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <Select
            value={currentAccessLevel}
            onValueChange={handleAccessLevelChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Access Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Access Levels</SelectItem>
              {KB_ACCESS_LEVEL_OPTIONS.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  className={option.disabled ? 'opacity-50' : ''}
                >
                  {option.comingSoon
                    ? `${option.label} (Coming Soon)`
                    : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={currentUsageMode}
            onValueChange={handleUsageModeChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Usage Mode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Usage Modes</SelectItem>
              {KB_USAGE_MODE_OPTIONS.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  className={option.disabled ? 'opacity-50' : ''}
                >
                  {option.comingSoon
                    ? `${option.label} (Coming Soon)`
                    : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </form>
    </Form>
  );
}
