'use client';

import { But<PERSON> } from '@/components/ui/button';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';
import { Plus } from 'lucide-react';

import { KBQueryParams } from '../models/kb.type';
import { KBCreateDialog } from './kb-create-dialog';
import { KBFilter } from './kb-filter';
import { KBTable } from './kb-tables/kb-table';

interface KBListProps {
  searchParams: WithPaginationDefaults<KBQueryParams>;
}

export function KBList({ searchParams }: KBListProps) {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-wrap items-center justify-between gap-4 max-md:flex-col max-md:items-start">
        <div className="max-w-4xl flex-1">
          <KBFilter defaultValues={searchParams} />
        </div>

        <KBCreateDialog>
          <Button className="gap-2">
            <Plus className="size-4" />
            Create KB
          </Button>
        </KBCreateDialog>
      </div>

      <div className="overflow-thin-auto grow">
        <KBTable searchParams={searchParams} />
      </div>
    </div>
  );
}
