'use client';

import DashboardCard from '@/components/dashboard-card';
import { Card, CardContent } from '@/components/ui/card';
import { kbQuery } from '@/features/kb/hooks/kb.query';
import {
  Activity,
  BookOpen,
  CheckCircle2,
  Clock,
  Database,
  FileText,
  HardDrive,
} from 'lucide-react';

export function KBStatusSummary() {
  const { data: overview, isLoading: overviewLoading } =
    kbQuery.query.useOverview();

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (overviewLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Loading..."
            value="..."
            subtitle="Loading data..."
            icon={<div className="h-4 w-4 animate-pulse rounded bg-gray-200" />}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-4">
      <DashboardCard
        title="Total Knowledge Bases"
        value={`${overview?.total_knowledge_bases || 0}`}
        subtitle={`Active: ${overview?.active_kbs_count || 0}`}
        icon={<BookOpen className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="Total Documents"
        value={`${overview?.total_documents || 0}`}
        subtitle="Across all knowledge bases"
        icon={<FileText className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="Total Storage"
        value={formatBytes(overview?.total_size_bytes || 0)}
        subtitle="Used storage space"
        icon={<HardDrive className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="KB Activity"
        value={`${overview?.active_kbs_count || 0}`}
        subtitle={`Total: ${overview?.total_knowledge_bases || 0}`}
        icon={<Activity className="text-muted-foreground size-4" />}
      />
    </div>
  );
}

interface KBDetailStatusSummaryProps {
  kbId: string;
}

export function KBDetailStatusSummary({ kbId }: KBDetailStatusSummaryProps) {
  const { data: kb, isLoading: kbLoading } = kbQuery.query.useDetail(kbId);
  const { data: documentsOverview, isLoading: documentsLoading } =
    kbQuery.query.useDocumentsOverview(kbId);

  const isLoading = kbLoading || documentsLoading;

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Loading..."
            value="..."
            subtitle="Loading data..."
            icon={<div className="h-4 w-4 animate-pulse rounded bg-gray-200" />}
          />
        ))}
      </div>
    );
  }

  const documentTypes = documentsOverview?.documents_by_type || {};
  const totalProcessed = documentsOverview?.total_documents_processed || 0;
  const successRate = documentsOverview?.processing_success_rate || 0;
  const recentActivity = documentsOverview?.recent_activity_count || 0;

  return (
    <div className="grid gap-4 md:grid-cols-4">
      <DashboardCard
        title="Total Documents"
        value={`${kb?.total_document || 0}`}
        subtitle="In this knowledge base"
        icon={<FileText className="text-muted-foreground size-4" />}
      />

      <DashboardCard
        title="Processing Status"
        value={`${totalProcessed}`}
        subtitle={`${successRate.toFixed(1)}% success rate`}
        icon={<CheckCircle2 className="text-muted-foreground size-4" />}
      />

      {/* Document Types Card - keeping as Card due to complex content */}
      <Card className="min-w-[220px] flex-1">
        <CardContent className="flex h-full flex-col p-4">
          <div className="mb-3 flex shrink-0 items-center justify-between">
            <p className="text-muted-foreground text-sm font-medium">
              Document Types
            </p>
            <Database className="text-muted-foreground size-4" />
          </div>
          <div className="grid flex-1 grid-cols-2 content-start gap-x-2 gap-y-1">
            {Object.entries(documentTypes).length > 0 ? (
              Object.entries(documentTypes).map(([type, count]) => (
                <div
                  key={type}
                  className="flex min-w-0 items-center justify-between text-sm"
                >
                  <span className="truncate text-xs font-medium capitalize">
                    {type}
                  </span>
                  <span className="text-muted-foreground shrink-0 text-xs font-semibold">
                    {count}
                  </span>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground col-span-2 text-sm">
                No documents yet
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      <DashboardCard
        title="Recent Activity"
        value={`${recentActivity}`}
        subtitle="Documents added (7 days)"
        icon={<Clock className="text-muted-foreground size-4" />}
      />
    </div>
  );
}
