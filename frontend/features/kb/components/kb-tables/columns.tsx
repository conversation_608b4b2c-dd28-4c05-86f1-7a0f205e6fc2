'use client';

import { useState } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/navigation';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { formatBytes } from '@/lib/utils/format-bytes';
import { components } from '@/openapi-ts/gens';
import { KBAccessLevel, KBUsageMode } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import {
  BookOpen,
  Eye,
  Lock,
  MoreHorizontal,
  Settings,
  Trash2,
  Users,
} from 'lucide-react';

import { KB_ACCESS_LEVELS, KB_USAGE_MODES } from '../../config';
import { kbQuery } from '../../hooks/kb.query';
import { KBCreateDialog } from '../kb-create-dialog';

type KBPublic = components['schemas']['KBPublic'];
type KBRow = KBPublic;

export const kbColumns: ColumnDef<KBRow>[] = [
  {
    accessorKey: 'title',
    header: 'Knowledge Base',
    cell: ({ row }) => {
      const { title, description, id } = row.original;

      return (
        <div className="flex gap-3">
          <div className="mt-1 flex-shrink-0">
            <BookOpen className="size-5" />
          </div>
          <div className="min-w-0 flex-1 space-y-1">
            <div className="text-foreground leading-tight font-medium">
              <Link href={`/kb/${id}`} className="hover:underline">
                {title}
              </Link>
            </div>
            {description && (
              <div className="text-muted-foreground line-clamp-2 text-sm break-words">
                {description}
              </div>
            )}
          </div>
        </div>
      );
    },
    minSize: 300,
    maxSize: 400,
  },
  {
    accessorKey: 'access_level',
    header: 'Access',
    cell: ({ row }) => {
      const { access_level } = row.original;
      const config =
        KB_ACCESS_LEVELS[access_level as keyof typeof KB_ACCESS_LEVELS];

      return (
        <div className="flex items-center gap-2">
          {access_level === 'private' ? (
            <Lock className="text-muted-foreground size-4" />
          ) : (
            <Users className="text-muted-foreground size-4" />
          )}
          <span className="text-sm">{config?.label || access_level}</span>
        </div>
      );
    },
    size: 120,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'usage_mode',
    header: 'Usage Mode',
    cell: ({ row }) => {
      const { usage_mode } = row.original;
      const config = KB_USAGE_MODES[usage_mode as keyof typeof KB_USAGE_MODES];

      return (
        <div className="flex items-center gap-2">
          <span className="text-sm">{config?.label || usage_mode}</span>
        </div>
      );
    },
    size: 140,
  },
  {
    accessorKey: 'total_document',
    header: 'Documents',
    cell: ({ row }) => {
      const count = (row.original.total_document ?? 0) as number;
      return <span className="font-medium">{count}</span>;
    },
    size: 100,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'total_size',
    header: 'Size',
    cell: ({ row }) => {
      const bytes = (row.original.total_size ?? 0) as number;
      return <span className="font-medium">{formatBytes(bytes)}</span>;
    },
    size: 100,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'tags',
    header: 'Tags',
    cell: ({ row }) => {
      const tags = row.original.tags || [];

      if (tags.length === 0) {
        return <span className="text-muted-foreground text-sm">No tags</span>;
      }

      if (tags.length <= 2) {
        return (
          <div className="flex flex-wrap gap-1">
            {tags.map((tag: string) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        );
      }

      return (
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs">
            {tags[0]}
          </Badge>
          <span className="text-muted-foreground text-xs">
            +{tags.length - 1} more
          </span>
        </div>
      );
    },
    size: 150,
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    cell: renderCellToFullDateTime,
    size: 200,
    meta: {
      className: 'text-center whitespace-nowrap',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      return <RowActions kb={row.original} />;
    },
    size: 90,
    meta: {
      className: 'text-right',
      header: {
        className:
          'sticky right-0 z-20 bg-background text-right shadow-[inset_-1px_0_0_0_var(--border)]',
      },
      cell: {
        className:
          'sticky right-0 z-10 bg-background shadow-[inset_-1px_0_0_0_var(--border)]',
      },
    },
  },
];

interface RowActionsProps {
  kb: KBRow;
}

function RowActions({ kb }: RowActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const deleteMutation = kbQuery.mutation.useDelete(kb.id);
  const router = useRouter();

  const handleView = () => {
    router.push(`/kb/${kb.id}`);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    deleteMutation.mutate(undefined, {
      onSuccess: () => {
        setShowDeleteDialog(false);
      },
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-muted h-8 w-8 rounded-full"
            aria-label="Actions"
          >
            <MoreHorizontal className="size-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          <DropdownMenuItem onClick={handleView}>
            <span className="flex items-center">
              <Eye className="mr-2 size-4" />
              View
            </span>
          </DropdownMenuItem>
          <KBCreateDialog
            mode="edit"
            kbId={kb.id}
            initialData={{
              title: kb.title,
              description: kb.description || '',
              access_level: kb.access_level as KBAccessLevel,
              usage_mode: kb.usage_mode as KBUsageMode,
              tags: kb.tags || [],
              allowed_users: kb.allowed_users || [],
            }}
          >
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <span className="flex items-center">
                <Settings className="mr-2 size-4" />
                Settings
              </span>
            </DropdownMenuItem>
          </KBCreateDialog>
          <DropdownMenuItem onClick={handleDelete} className="text-destructive">
            <>
              <Trash2 className="mr-2 size-4" />
              <span>Delete</span>
            </>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Knowledge Base</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{kb.title}&quot;? This
              action cannot be undone and will permanently delete all documents
              in this knowledge base.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
