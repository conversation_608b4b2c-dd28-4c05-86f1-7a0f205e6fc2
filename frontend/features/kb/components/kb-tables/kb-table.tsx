'use client';

import { ErrorAlert } from '@/components/ui/error-alert';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import { TableUI } from '@/components/ui/table/table-ui';
import { SchemaKBsPublic } from '@/openapi-ts/gens';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { kbQuery } from '../../hooks/kb.query';
import { KBQueryParams } from '../../models/kb.type';
import { kbColumns } from './columns';

interface KBTableProps {
  searchParams: WithPaginationDefaults<KBQueryParams>;
}

export function KBTable({ searchParams }: KBTableProps) {
  const { data, isLoading, isRefetching, isError } =
    kbQuery.query.useList(searchParams);

  if (data) {
    const kbList = data as SchemaKBsPublic;
    return (
      <TableUI
        data={kbList.data}
        columns={kbColumns}
        meta={kbList.meta}
        isRefetching={isRefetching}
      />
    );
  }

  if (isLoading) {
    return <DataTableSkeleton columnCount={kbColumns.length} />;
  }

  if (isError) {
    return (
      <ErrorAlert
        title="Failed to Load Knowledge Bases"
        description="Unable to fetch the knowledge bases. Please check your connection and try again."
      />
    );
  }
}
