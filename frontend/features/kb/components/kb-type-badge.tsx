import { Badge } from '@/components/ui/badge';
import { KBAccessLevel } from '@/openapi-ts/gens';
import { Lock, Users } from 'lucide-react';

import { KB_ACCESS_LEVELS } from '../config';

interface KBTypeBadgeProps {
  accessLevel: KBAccessLevel;
  variant?: 'default' | 'outline' | 'secondary';
  size?: 'sm' | 'default';
  showIcon?: boolean;
}

export function KBTypeBadge({
  accessLevel,
  variant = 'outline',
  size = 'default',
  showIcon = true,
}: KBTypeBadgeProps) {
  const config = KB_ACCESS_LEVELS[accessLevel];

  if (!config) return null;

  const Icon = accessLevel === 'private' ? Lock : Users;

  return (
    <Badge variant={variant} className={size === 'sm' ? 'text-xs' : undefined}>
      {showIcon && <Icon className="mr-1 size-3" />}
      {config.label}
    </Badge>
  );
}
