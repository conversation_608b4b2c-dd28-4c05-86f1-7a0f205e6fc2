import { AsyncTaskStatus } from '@/openapi-ts/gens';

export const ASYNC_TASK_STATUS_CONFIG: Record<
  AsyncTaskStatus,
  {
    label: string;
    variant:
      | 'default'
      | 'secondary'
      | 'destructive'
      | 'outline'
      | 'success'
      | 'warning'
      | 'info';
  }
> = {
  [AsyncTaskStatus.PENDING]: {
    label: 'Pending',
    variant: 'warning',
  },
  [AsyncTaskStatus.PROGRESS]: {
    label: 'Processing',
    variant: 'info',
  },
  [AsyncTaskStatus.SUCCESS]: {
    label: 'Completed',
    variant: 'success',
  },
  [AsyncTaskStatus.FAILURE]: {
    label: 'Failed',
    variant: 'destructive',
  },
};

export const SUPPORTED_FILE_TYPES = [
  '.pdf',
  '.doc',
  '.docx',
  '.txt',
  '.md',
  '.csv',
  '.xlsx',
  '.xls',
] as const;

export const MAX_FILE_SIZE = 10 * 1024 * 1024;
export const MAX_FILES_PER_UPLOAD = 10;

// Centralized mapping for react-dropzone `accept` prop
export const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
  'text/plain': ['.txt'],
  'text/markdown': ['.md'],
  'text/csv': ['.csv'],
  'application/vnd.ms-excel': ['.xls'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
} as const;
