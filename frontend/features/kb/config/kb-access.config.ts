import { KBAccessLevel } from '@/openapi-ts/gens';

export const KB_ACCESS_LEVELS: Record<
  KBAccessLevel,
  {
    label: string;
    description: string;
    disabled?: boolean;
    comingSoon?: boolean;
  }
> = {
  private: {
    label: 'Private',
    description: 'Only you can access this knowledge base',
  },
  shared: {
    label: 'Shared',
    description: 'Selected users can access this knowledge base',
    disabled: true,
    comingSoon: true,
  },
};

export const KB_ACCESS_LEVEL_OPTIONS = Object.entries(KB_ACCESS_LEVELS).map(
  ([value, config]) => ({
    value: value as KBAccessLevel,
    label: config.label,
    description: config.description,
    disabled: config.disabled,
    comingSoon: config.comingSoon,
  }),
);
