import { KBUsageMode } from '@/openapi-ts/gens';
import { <PERSON><PERSON>, <PERSON>ideIcon, <PERSON><PERSON>ointer, Zap } from 'lucide-react';

export const KB_USAGE_MODES: Record<
  KBUsageMode,
  {
    label: string;
    description: string;
    icon: LucideIcon;
    disabled?: boolean;
    comingSoon?: boolean;
  }
> = {
  manual: {
    label: 'Manual',
    description: 'Use this knowledge base only when manually selected',
    icon: MousePointer,
  },
  agent_requested: {
    label: 'Agent Requested',
    description: 'Agent can request to use this knowledge base when needed',
    icon: Bo<PERSON>,
    disabled: true,
    comingSoon: true,
  },
  always: {
    label: 'Always',
    description: 'Always include this knowledge base in agent context',
    icon: Zap,
  },
};

export const KB_USAGE_MODE_OPTIONS = Object.entries(KB_USAGE_MODES).map(
  ([value, config]) => ({
    value: value as KBUsageMode,
    label: config.label,
    description: config.description,
    disabled: config.disabled,
    comingSoon: config.comingSoon,
  }),
);
