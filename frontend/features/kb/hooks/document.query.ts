import { SchemaConfirmUploadsRequest } from '@/openapi-ts/gens';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import {
  DocumentQueryParams,
  PresignedUploadInfo,
  WebsiteIngestionData,
} from '../models/document.type';
import { documentApi } from '../services/document.api';

const documentQueryKeys = createQueryKeys('documents', {
  list: (
    kbId: string,
    params: WithPaginationDefaults<DocumentQueryParams>,
  ) => ({
    queryKey: [kbId, params],
    queryFn: () => documentApi.list(kbId, handleSkipOfPagination(params)),
  }),

  detail: (kbId: string, documentId: string) => ({
    queryKey: [kbId, documentId],
    queryFn: () => documentApi.detail(kbId, documentId).get(),
  }),
});

// Query hooks
const useList = (
  kbId: string,
  params: WithPaginationDefaults<DocumentQueryParams>,
) => {
  return useQuery({
    ...documentQueryKeys.list(kbId, params),
    placeholderData: keepPreviousData,
    enabled: !!kbId, // Only fetch when kbId is available
  });
};

const useDetail = (kbId: string, documentId: string) => {
  return useQuery({
    ...documentQueryKeys.detail(kbId, documentId),
    enabled: !!kbId && !!documentId,
  });
};

const useEnsureDetail = (kbId: string, documentId: string) => {
  return useSuspenseQuery(documentQueryKeys.detail(kbId, documentId));
};

// Mutation hooks
const useGetPresignedUrls = (kbId: string) => {
  return useMutation({
    mutationFn: (files: PresignedUploadInfo[]) =>
      documentApi.getPresignedUrls(kbId, files),
  });
};

const useConfirmUploads = (kbId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      uploadedFiles: SchemaConfirmUploadsRequest['uploaded_files'],
    ) => documentApi.confirmUploads(kbId, uploadedFiles),
    onSuccess: () => {
      toast.success('Files uploaded successfully');

      queryClient.invalidateQueries({
        queryKey: documentQueryKeys.list._def,
      });
    },
  });
};

const useIngestWebsite = (kbId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: WebsiteIngestionData) =>
      documentApi.ingestWebsite(kbId, data),
    onSuccess: () => {
      toast.success('Website ingestion started');

      queryClient.invalidateQueries({
        queryKey: documentQueryKeys.list._def,
      });
    },
  });
};

const useDelete = (kbId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      documentId,
      objectName,
    }: {
      documentId: string;
      objectName: string;
    }) => documentApi.detail(kbId, documentId).delete(objectName),
    onSuccess: (_, variables) => {
      toast.success('Document deleted successfully');
      // Remove from detail queries
      queryClient.removeQueries({
        queryKey: documentQueryKeys.detail(kbId, variables.documentId).queryKey,
      });

      // Invalidate document list to refresh
      queryClient.invalidateQueries({
        queryKey: documentQueryKeys.list._def,
      });
    },
  });
};

export const documentQuery = {
  query: {
    useList,
    useDetail,
    useEnsureDetail,
  },
  mutation: {
    useGetPresignedUrls,
    useConfirmUploads,
    useIngestWebsite,
    useDelete,
  },
};
