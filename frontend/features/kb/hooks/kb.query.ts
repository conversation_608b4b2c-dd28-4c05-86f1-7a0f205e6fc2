import { SchemaKbCreate, SchemaKbUpdate } from '@/openapi-ts/gens';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { KBQueryParams } from '../models/kb.type';
import { kbApi } from '../services/kb.api';

const kbQueryKeys = createQueryKeys('kb', {
  overview: () => ({
    queryKey: ['overview'],
    queryFn: () => kbApi.overview(),
  }),

  list: (params: WithPaginationDefaults<KBQueryParams>) => ({
    queryKey: ['list', params],
    queryFn: () => kbApi.list(handleSkipOfPagination(params)),
  }),

  detail: (kbId: string) => ({
    queryKey: ['detail', kbId],
    queryFn: () => kbApi.detail(kbId).get(),
  }),

  documentsOverview: (kbId: string) => ({
    queryKey: ['documents-overview', kbId],
    queryFn: () => kbApi.detail(kbId).documentsOverview(),
  }),
});

// Query hooks
const useOverview = () => {
  return useQuery({
    ...kbQueryKeys.overview(),
    placeholderData: keepPreviousData,
  });
};

const useDocumentsOverview = (kbId: string) => {
  return useQuery({
    ...kbQueryKeys.documentsOverview(kbId),
    placeholderData: keepPreviousData,
    enabled: !!kbId, // Only fetch when kbId is available
  });
};

const useList = (params: WithPaginationDefaults<KBQueryParams>) => {
  return useQuery({
    ...kbQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useDetail = (kbId: string) => {
  return useQuery({
    ...kbQueryKeys.detail(kbId),
    placeholderData: keepPreviousData,
    enabled: !!kbId, // Only fetch when kbId is available
  });
};

const useEnsureDetail = (kbId: string) => {
  if (!kbId) {
    throw new Error('kbId is required for useEnsureDetail');
  }
  return useSuspenseQuery(kbQueryKeys.detail(kbId));
};

// Mutation hooks
const useCreate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SchemaKbCreate) => kbApi.create(data),
    onSuccess: (response) => {
      toast.success('Knowledge base created successfully');

      // Cache the new KB in detail queries
      if (response.id) {
        queryClient.setQueryData(
          kbQueryKeys.detail(response.id).queryKey,
          response,
        );
      }

      // Invalidate list queries to refresh
      queryClient.invalidateQueries({
        queryKey: ['kb', 'list'],
      });
    },
  });
};

const useUpdate = (kbId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SchemaKbUpdate) => kbApi.detail(kbId).update(data),
    onSuccess: (response) => {
      toast.success('Knowledge base updated successfully');

      // Update cached detail data
      queryClient.setQueryData(kbQueryKeys.detail(kbId).queryKey, response);

      // Invalidate list queries to refresh
      queryClient.invalidateQueries({
        queryKey: ['kb', 'list'],
      });
    },
  });
};

const useDelete = (kbId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => kbApi.detail(kbId).delete(),
    onSuccess: () => {
      toast.success('Knowledge base deleted successfully');

      queryClient.removeQueries({
        queryKey: kbQueryKeys.detail(kbId).queryKey,
      });

      queryClient.invalidateQueries({
        queryKey: ['kb', 'list'],
      });
    },
  });
};

export const kbQuery = {
  query: {
    useOverview,
    useDocumentsOverview,
    useList,
    useDetail,
    useEnsureDetail,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
  },
};
