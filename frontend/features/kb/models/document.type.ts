import { PathsRequestQueryDto } from '@/openapi-ts/utils.type';

// Document Query Parameters
export type DocumentQueryParams = NonNullable<
  PathsRequestQueryDto<'/api/v1/knowledge_base/kbs/{kb_id}/documents'>
>;

// Document Content Query Parameters
export type DocumentContentQueryParams = NonNullable<
  PathsRequestQueryDto<'/api/v1/knowledge_base/kbs/{kb_id}/documents/content'>
>;

// File Upload Types
export interface FileUploadInfo {
  file: File;
  name: string;
  type: string;
  size: number;
}

export interface PresignedUploadInfo {
  file_name: string;
  content_type: string;
  file_size: number;
}

// Website Ingestion Types
export interface WebsiteIngestionData {
  url: string;
  deep_crawl: boolean;
}

// Document Status Types
export type DocumentStatus = 'pending' | 'processing' | 'completed' | 'failed';

// Document Type Enum
export type DocumentType = 'file' | 'url' | 'text';

// Document Filter Options
export interface DocumentFilters {
  type?: DocumentType;
  status?: DocumentStatus;
  search?: string;
}
