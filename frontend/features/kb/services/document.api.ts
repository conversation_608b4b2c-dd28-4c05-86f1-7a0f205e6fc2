import { SchemaConfirmUploadsRequest } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import {
  DocumentQueryParams,
  PresignedUploadInfo,
  WebsiteIngestionData,
} from '../models/document.type';

export const documentApi = {
  // Get presigned URLs for file upload
  getPresignedUrls: (kbId: string, files: PresignedUploadInfo[]) =>
    fetchData(
      api.POST('/api/v1/knowledge_base/kbs/{kb_id}/presigned-urls', {
        params: {
          path: { kb_id: kbId },
        },
        body: {
          kb_id: kbId,
          files: files.map((file) => ({
            file_id:
              globalThis.crypto?.randomUUID?.() ??
              `${Date.now()}_${Math.random().toString(36).slice(2)}`,
            filename: file.file_name,
            content_type: file.content_type,
            file_size: file.file_size,
          })),
        },
      }),
    ),

  // Confirm file uploads after successful S3 upload
  confirmUploads: (
    kbId: string,
    uploadedFiles: SchemaConfirmUploadsRequest['uploaded_files'],
  ) =>
    fetchData(
      api.POST(
        '/api/v1/knowledge_base/kbs/{kb_id}/ingest-from-presigned-urls',
        {
          params: {
            path: { kb_id: kbId },
          },
          body: {
            uploaded_files: uploadedFiles,
          },
        },
      ),
    ),

  // Ingest website URL
  ingestWebsite: (kbId: string, data: WebsiteIngestionData) =>
    fetchData(
      api.POST('/api/v1/knowledge_base/kbs/{kb_id}/ingest-from-urls', {
        params: {
          path: { kb_id: kbId },
        },
        body: {
          urls: [data.url],
          deep_crawls: [data.deep_crawl],
        },
      }),
    ),

  // List documents in KB
  list: (kbId: string, query: DocumentQueryParams) =>
    fetchData(
      api.GET('/api/v1/knowledge_base/kbs/{kb_id}/documents', {
        params: {
          path: { kb_id: kbId },
          query: {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            ...query,
          },
        },
      }),
    ),

  detail: (kbId: string, documentId: string) => ({
    // Get document detail
    get: () =>
      fetchData(
        api.GET('/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}', {
          params: {
            path: {
              kb_id: kbId,
              document_id: documentId,
            },
          },
        }),
      ),
    // Delete document (requires object_name in query)
    delete: (objectName: string) =>
      fetchData(
        api.DELETE(
          '/api/v1/knowledge_base/kbs/{kb_id}/documents/{document_id}',
          {
            params: {
              path: {
                kb_id: kbId,
                document_id: documentId,
              },
              query: {
                object_name: objectName,
              },
            },
          },
        ),
      ),
  }),

  // Get document content (for viewing/downloading)
  getContent: (kbId: string, objectName: string) =>
    fetchData(
      api.GET('/api/v1/knowledge_base/kbs/{kb_id}/documents/content', {
        params: {
          path: { kb_id: kbId },
          query: { object_name: objectName },
        },
      }),
    ),
};
