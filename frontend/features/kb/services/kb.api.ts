import { SchemaKbCreate, SchemaKbUpdate } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { KBQueryParams } from '../models/kb.type';

export const kbApi = {
  // Get KB overview statistics
  overview: () => fetchData(api.GET('/api/v1/knowledge_base/kbs/overview')),

  // List KBs with pagination and filters
  list: (query: KBQueryParams) =>
    fetchData(
      api.GET('/api/v1/knowledge_base/kbs', {
        params: {
          query: {
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            ...query,
          },
        },
      }),
    ),

  // Create new KB
  create: (body: SchemaKbCreate) =>
    fetchData(
      api.POST('/api/v1/knowledge_base/kbs', {
        body: {
          title: body.title,
          description: body.description,
          tags: body.tags || [],
          access_level: body.access_level,
          usage_mode: body.usage_mode,
        },
      }),
    ),

  detail: (kbId: string) => ({
    // Get KB by ID
    get: () =>
      fetchData(
        api.GET('/api/v1/knowledge_base/kbs/{kb_id}', {
          params: {
            path: { kb_id: kbId },
          },
        }),
      ),

    // Get document overview for specific KB
    documentsOverview: () =>
      fetchData(
        api.GET('/api/v1/knowledge_base/kbs/{kb_id}/documents/overview', {
          params: {
            path: { kb_id: kbId },
          },
        }),
      ),

    // Update KB
    update: (body: SchemaKbUpdate) =>
      fetchData(
        api.PUT('/api/v1/knowledge_base/kbs/{kb_id}', {
          params: {
            path: { kb_id: kbId },
          },
          body: {
            title: body.title,
            description: body.description,
            tags: body.tags,
            access_level: body.access_level,
            usage_mode: body.usage_mode,
            allowed_users: body.allowed_users,
          },
        }),
      ),

    // Delete KB
    delete: () =>
      fetchData(
        api.DELETE('/api/v1/knowledge_base/kbs/{kb_id}', {
          params: {
            path: { kb_id: kbId },
          },
        }),
      ),
  }),
};
