'use client';

import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import { Loader2Icon, SkipForwardIcon } from 'lucide-react';

import { onboardingQuery } from '../hooks/onboarding.query';

export function OnboardingSkipButton() {
  const { mutate, isPending } = onboardingQuery.mutation.useSkip();

  const onSkip = () => {
    mutate();
  };

  return (
    <Button
      variant="outline"
      type="button"
      disabled={isPending}
      className="gap-2"
      onClick={onSkip}
    >
      <If
        condition={isPending}
        fallback={<SkipForwardIcon className="size-4" />}
      >
        <Loader2Icon className="size-4 animate-spin" />
      </If>
      <span>Skip</span>
    </Button>
  );
}
