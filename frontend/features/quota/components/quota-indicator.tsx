import { Alert } from '@/components/ui/alert';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useUserContext } from '@/features/user/provider/user-provider';
import { cn } from '@/lib/utils';

import { quotaQuery } from '../hooks/quota.query';

interface QuotaIndicatorProps {
  className?: string;
}

/**
 * Simple quota indicator component showing total credits remaining
 * with a tooltip displaying premium and daily credits breakdown
 *
 * @param className - Additional CSS classes to apply to the component
 */
export function QuotaIndicator({ className }: QuotaIndicatorProps) {
  const { user } = useUserContext();

  const { data: creditQuota, isLoading } = quotaQuery.query.useCreditQuota(
    user?.id || '',
  );

  // Check data first, then loading state
  if (creditQuota) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={cn('cursor-help', className)}
              data-test="quota-indicator"
            >
              <Alert variant="info">
                <span className="text-sm font-semibold">
                  {creditQuota.total_credits_remaining.toLocaleString()}{' '}
                  remaining credits
                </span>
              </Alert>
            </div>
          </TooltipTrigger>
          <TooltipContent className="max-w-xs" data-test="quota-tooltip">
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="pr-2">Premium Credits:</span>
                <span className="font-medium">
                  {creditQuota.premium_credits_remaining.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="pr-2">Daily Credits:</span>
                <span className="font-medium">
                  {creditQuota.daily_credits_remaining.toLocaleString()}
                </span>
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-2', className)}>
        <span className="text-muted-foreground text-xs font-medium">
          Loading...
        </span>
      </div>
    );
  }

  // Show error state
  return (
    <div className={cn('flex items-center justify-center p-2', className)}>
      <span className="text-destructive text-xs font-medium">
        Error loading quota
      </span>
    </div>
  );
}
