import { useUserContext } from '@/features/user/provider/user-provider';
import { PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod } from '@/openapi-ts/gens';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useQuery } from '@tanstack/react-query';

import { UseQuotaReturn } from '../models/quota.type';
import { quotaApi } from '../services/quota.api';

const quotaQueryKeys = createQueryKeys('quota', {
  creditQuota: (user_id: string) => ({
    queryKey: [user_id, 'credit-quota'],
    queryFn: () => quotaApi.getCreditQuota(user_id),
  }),

  dashboardOverview: (user_id: string) => ({
    queryKey: [user_id, 'dashboard-overview'],
    queryFn: () => quotaApi.getDashboardOverview(user_id),
  }),

  usageOverTime: (
    user_id: string,
    params?: {
      period?: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod;
      start_date?: string;
      end_date?: string;
    },
  ) => ({
    queryKey: [user_id, 'usage-over-time', params],
    queryFn: () => quotaApi.getUsageOverTime(user_id, params),
  }),
});

/**
 * Hook to fetch credit quota information for a user
 * @param user_id - The user ID to fetch credit quota for
 * @returns Credit quota information with loading states
 */
const useCreditQuota = (user_id: string) =>
  useQuery({
    ...quotaQueryKeys.creditQuota(user_id),
    staleTime: 120000, // 2 minutes - data stays fresh
    gcTime: 300000, // 5 minutes - data stays in cache
    refetchOnWindowFocus: false,
    enabled: !!user_id,
  });

/**
 * Hook to fetch dashboard overview for a user
 * @param user_id - The user ID to fetch dashboard overview for
 * @returns Dashboard overview information with loading states
 */
const useDashboardOverview = (user_id: string) =>
  useQuery({
    ...quotaQueryKeys.dashboardOverview(user_id),
    staleTime: 120000, // 2 minutes - data stays fresh
    gcTime: 300000, // 5 minutes - data stays in cache
    refetchOnWindowFocus: false,
    enabled: !!user_id,
  });

/**
 * Hook to fetch usage over time data
 * @param user_id - The user ID to fetch usage data for
 * @param params - Optional query parameters for period and date range
 * @returns Usage over time data with loading states
 */
const useUsageOverTime = (
  user_id: string,
  params?: {
    period?: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod;
    start_date?: string;
    end_date?: string;
  },
) =>
  useQuery({
    ...quotaQueryKeys.usageOverTime(user_id, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!user_id,
  });

/**
 * Main quota hook that uses the dashboard overview API
 * Uses user context to automatically get user_id
 * @returns UseQuotaReturn object with quotaInfo, refreshQuota, isLoading, etc.
 */
export function useQuota(): UseQuotaReturn {
  const { user } = useUserContext();

  // Use the dashboard overview API which has all the quota data we need
  const {
    data: dashboardData,
    isLoading,
    isError,
    error,
    refetch,
  } = useDashboardOverview(user?.id || '');

  // Use the dashboard overview data directly - no transformation needed
  const quotaInfo = dashboardData;

  // Async refreshQuota function
  const refreshQuota = async () => {
    await refetch();
  };

  return {
    quotaInfo,
    refreshQuota,
    isLoading,
    isError,
    error: error as Error | null,
  };
}

export const quotaQuery = {
  query: {
    useCreditQuota,
    useDashboardOverview,
    useUsageOverTime,
  },
};
