import { PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export const quotaApi = {
  /**
   * Get credit quota information for a user
   * @param user_id - The user ID to fetch credit quota for
   * @returns Promise resolving to CreditResponse
   */
  getCreditQuota: (user_id: string) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/credit-quota', {
        params: {
          path: {
            user_id,
          },
        },
      }),
    ),

  /**
   * Get comprehensive dashboard overview for a user
   * @param user_id - The user ID to fetch dashboard overview for
   * @returns Promise resolving to DashboardOverviewResponse
   */
  getDashboardOverview: (user_id: string) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/dashboard-overview', {
        params: {
          path: {
            user_id,
          },
        },
      }),
    ),

  /**
   * Get usage statistics over time with period grouping
   * @param user_id - The user ID to fetch usage data for
   * @param params - Optional query parameters for period and date range
   * @returns Promise resolving to UsageOvertimeResponse
   */
  getUsageOverTime: (
    user_id: string,
    params?: {
      period?: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod;
      start_date?: string;
      end_date?: string;
    },
  ) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/usage-over-time', {
        params: {
          path: {
            user_id,
          },
          query: params || {},
        },
      }),
    ),
};
