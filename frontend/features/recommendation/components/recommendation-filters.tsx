'use client';

import { Autocomplete } from '@/components/ui/common/autocomplete';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import { InputIconPrefix } from '@/components/ui/input-icon-prefix';
import { ResourceAutocomplete } from '@/features/resources/components/resource-autocomplete';
import { RESOURCE_TYPE_CONFIG } from '@/features/resources/config/resource-type.config';
import { useFormFilter } from '@/hooks/use-form-filter';
import { omit } from 'lodash';
import { SearchIcon } from 'lucide-react';

import { RECOMMENDATION_STATUS_CONFIG } from '../config/recommendation-status.config';
import { RecommendationQueryParams } from '../models/recommendation.type';

type Props = {
  defaultValues: RecommendationQueryParams;
};

export function RecommendationFilters({ defaultValues }: Props) {
  const { form, onSubmit } = useFormFilter<RecommendationQueryParams>({
    defaultValues: omit(defaultValues, ['page', 'limit']),
    debounceField: 'search',
  });

  const { control, handleSubmit } = form;

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-wrap items-center gap-4 pl-[1px] max-md:flex-col max-md:items-start max-md:[&>*]:w-full"
      >
        <FormField
          control={control}
          name="search"
          render={({ field }) => (
            <FormItem className="grow md:max-w-lg">
              <FormControl>
                <InputIconPrefix
                  Icon={SearchIcon}
                  placeholder={`Search by title, description...`}
                  className={'w-full'}
                  {...field}
                  value={field.value ?? ''}
                />
              </FormControl>
            </FormItem>
          )}
        />
        <div className="flex flex-wrap items-center gap-4 max-md:flex-col max-md:[&>*]:w-full">
          <FormField
            control={control}
            name="resource_id"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <ResourceAutocomplete {...field} name="resource name" />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="resource_type"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    mode="multiple"
                    options={RESOURCE_TYPE_CONFIG.LIST}
                    {...field}
                    PopoverContentProps={{
                      className: 'md:w-[340px]',
                    }}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Autocomplete
                    mode="multiple"
                    options={RECOMMENDATION_STATUS_CONFIG.LIST}
                    PopoverContentProps={{
                      className: 'md:w-[240px]',
                    }}
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        {/* <Button variant="default" className="gap-2">
          <XIcon className="size-4" />
          Clear Filters
        </Button> */}
      </form>
    </Form>
  );
}
