import { createElement } from 'react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SeverityBadge } from '@/components/ui/severity-badge';
import { StatusBadge } from '@/components/ui/status-badge';
import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '@/features/resources/config/resource-type.config';
import { SchemaRecommendationPublic } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontalIcon } from 'lucide-react';

import { ConfirmDeleteRecommendationDialog } from './confirm-delete-recommendation-dialog';
import { EditRecommendationDialog } from './edit-recommendation-dialog';

type RecommendationColumnOptions = {
  canEdit?: (recommendation: SchemaRecommendationPublic) => boolean;
  canDelete?: (recommendation: SchemaRecommendationPublic) => boolean;
};

export const createRecommendationColumns = (
  options?: RecommendationColumnOptions,
): ColumnDef<SchemaRecommendationPublic>[] => [
  {
    accessorKey: 'title',
    header: 'Title',
    size: 200,
    minSize: 150,
    meta: {
      className: 'min-w-[200px]',
    },
  },
  {
    accessorKey: 'description',
    header: 'Description',
    size: 400,
    cell: ({ row }) => {
      const value = row.original.description;
      return (
        <div className="max-w-none min-w-[200px] break-words whitespace-normal">
          {value}
        </div>
      );
    },
  },
  {
    accessorKey: 'type',
    header: 'Recommendation Type',
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.type;
      return value?.replace(/_/g, ' ')?.toUpperCase();
    },
  },
  {
    accessorKey: 'resource_name',
    header: 'Resource Name',
    size: 180,
    minSize: 150,
  },
  {
    accessorKey: 'resource_type',
    header: 'Resource Type',
    size: 140,
    minSize: 120,
    cell: ({ row }) => {
      const { resource_type, type } = row.original;

      // Get the icon for the resource type
      const getResourceTypeIcon = () => {
        // Try to get icon from AWS resource config first
        if (
          resource_type &&
          RESOURCE_TYPE_CONFIG.CONFIG[resource_type as AWSResourceType]
        ) {
          const iconComponent =
            RESOURCE_TYPE_CONFIG.CONFIG[resource_type as AWSResourceType].icon;
          return createElement(iconComponent, {
            className: 'size-6 text-primary shrink-0',
          });
        }

        // Fallback to a default icon if not found
        return (
          <div className="bg-muted flex size-6 shrink-0 items-center justify-center rounded text-xs font-medium">
            {type.charAt(0)}
          </div>
        );
      };

      return (
        <div className="flex items-center gap-2">
          {getResourceTypeIcon()}
          {resource_type}
        </div>
      );
    },
  },
  {
    accessorKey: 'potential_savings',
    header: () => <div className="text-left">Monthly Savings ($)</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.potential_savings;
      return (
        <div className="text-left">
          {value !== null && value !== undefined ? `$${value}` : '-'}
        </div>
      );
    },
  },
  {
    accessorKey: 'effort',
    header: () => <div className="text-left">Effort</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.effort;
      return (
        <div className="justify-left flex">
          {value ? (
            <SeverityBadge severity={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'risk',
    header: () => <div className="text-left">Risk</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.risk;
      return (
        <div className="justify-left flex">
          {value ? (
            <SeverityBadge severity={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    size: 120,
    minSize: 100,
    cell: ({ row }) => {
      const value = row.original.status;
      return (
        <div className="justify-left flex">
          {value ? (
            <StatusBadge status={value} className="p-2 opacity-80" />
          ) : null}
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    size: 80,
    cell: ({ row }) => {
      const recommendation = row.original;
      const { canEdit, canDelete } = options || {};

      const canUserEdit = canEdit ? canEdit(recommendation) : true;
      const canUserDelete = canDelete ? canDelete(recommendation) : true;

      if (!canUserEdit && !canUserDelete) {
        return null;
      }

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontalIcon className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent onClick={(e) => e.stopPropagation()}>
            {canUserEdit && (
              <EditRecommendationDialog recommendation={recommendation}>
                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                  Edit
                </DropdownMenuItem>
              </EditRecommendationDialog>
            )}
            {canUserDelete && (
              <ConfirmDeleteRecommendationDialog
                recommendation={recommendation}
              >
                <DropdownMenuItem
                  onSelect={(e) => e.preventDefault()}
                  className="text-destructive/80 hover:!text-destructive"
                >
                  Delete
                </DropdownMenuItem>
              </ConfirmDeleteRecommendationDialog>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
