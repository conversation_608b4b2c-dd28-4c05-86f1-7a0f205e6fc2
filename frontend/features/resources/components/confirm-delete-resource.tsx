'use client';

import { PropsWithChildren } from 'react';

import { DeleteConfirmAlert } from '@/components/ui/common/delete-confirm-alert';

import { resourceQuery } from '../hooks/resource.query';

type Props = PropsWithChildren<{
  resourceId: string;
}>;

export function ConfirmDeleteResource({ children, resourceId }: Props) {
  const { mutate, isPending } = resourceQuery.mutation.useDelete(resourceId);

  const onConfirm = (close: () => void) => {
    mutate(undefined, {
      onSuccess: close,
    });
  };
  return (
    <DeleteConfirmAlert
      title="Delete Resource"
      loading={isPending}
      onConfirm={onConfirm}
    >
      {children}
    </DeleteConfirmAlert>
  );
}
