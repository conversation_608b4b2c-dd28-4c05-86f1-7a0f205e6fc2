import { useDeferredValue, useState } from 'react';

import {
  Autocomplete,
  MultipleAutocompleteProps,
} from '@/components/ui/common/autocomplete';
import { Skeleton } from '@/components/ui/skeleton';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import { castArray } from 'lodash';

import { resourceQuery } from '../hooks/resource.query';

type Props = Omit<MultipleAutocompleteProps, 'options' | 'mode'>;

export function ResourceAutocomplete(props: Props) {
  const [valueSearch, setValueSearch] = useState('');
  const debouncedValueSearch = useDeferredValue(valueSearch);

  const { data, isLoading, isRefetching } = resourceQuery.query.useList({
    page: 1,
    limit: 100,
    name: debouncedValueSearch,
  });

  if (data) {
    const options = data.data.map((item) => ({
      label: item.name,
      value: item.id,
    }));

    return (
      <SpinnerContainer loading={isRefetching}>
        <Autocomplete
          {...props}
          mode="multiple"
          CommandInputProps={{
            onValueChange: setValueSearch,
          }}
          options={options}
          PopoverContentProps={{
            className: 'md:w-[240px]',
          }}
          value={props.value ? castArray(props.value) : []}
        />
      </SpinnerContainer>
    );
  }

  if (isLoading) {
    return <Skeleton className="h-10 w-20" />;
  }
}
