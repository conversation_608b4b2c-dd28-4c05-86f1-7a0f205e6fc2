'use client';

import { createElement } from 'react';

import Link from 'next/link';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { If } from '@/components/ui/common/if';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { WithTooltip } from '@/components/ui/tooltip';
import pathsConfig from '@/config/paths.config';
import { agentDetailUrl } from '@/features/agent/hooks/use-navigate-agent-detail';
import { RecommendationQueryParams } from '@/features/recommendation/models/recommendation.type';
import { useUserContext } from '@/features/user/provider/user-provider';
import { formatUSD } from '@/lib/currency';
import { renderCellToFullDateTime } from '@/lib/date-utils';
import { SchemaResourcePublic } from '@/openapi-ts/gens';
import { ColumnDef } from '@tanstack/react-table';
import { flow } from 'lodash';
import {
  Activity,
  ShieldCheck,
  SparklesIcon,
  Trash2Icon,
  Wallet,
} from 'lucide-react';

import { RESOURCE_CATEGORY_CONFIG } from '../../config/resource-category.config';
import {
  AWSResourceType,
  RESOURCE_TYPE_CONFIG,
} from '../../config/resource-type.config';
import { ConfirmDeleteResource } from '../confirm-delete-resource';

export const resourceColumns: ColumnDef<SchemaResourcePublic>[] = [
  {
    accessorKey: 'name',
    header: 'Resource',
    cell: ({ row }) => {
      const { name, resource_id, type } = row.original;

      // Get the icon for the resource type
      const getResourceTypeIcon = () => {
        // Try to get icon from AWS resource config first
        const awsResourceType = type as AWSResourceType;
        if (RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType]) {
          const iconComponent =
            RESOURCE_TYPE_CONFIG.CONFIG[awsResourceType].icon;
          return createElement(iconComponent, {
            className: 'size-6 text-primary shrink-0',
          });
        }

        // Fallback to a default icon if not found
        return (
          <div className="bg-muted flex size-6 shrink-0 items-center justify-center rounded text-xs font-medium">
            {type.charAt(0)}
          </div>
        );
      };

      return (
        <div className="flex gap-3">
          <div className="flex-shrink-0">{getResourceTypeIcon()}</div>
          <div className="-mt-0.5 min-w-0 flex-1 space-y-1">
            <div className="text-foreground leading-tight font-medium break-words">
              {name}
            </div>
            <div className="text-muted-foreground text-sm break-all">
              {resource_id}
            </div>
          </div>
        </div>
      );
    },
    minSize: 350,
    maxSize: 400,
    meta: {
      className: 'min-w-[350px]',
    },
  },
  {
    accessorKey: 'region',
    header: 'Region',
    cell: ({ row }) => {
      const { region } = row.original;
      return <span className="text-sm">{region}</span>;
    },
    size: 130,
    meta: {
      className: 'whitespace-nowrap',
    },
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: ({ row }) => {
      const { category } = row.original;
      const { label, variant } = RESOURCE_CATEGORY_CONFIG.CONFIG[category];
      return <Badge variant={variant}>{label}</Badge>;
    },
    size: 120,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: ({ row }) => {
      const { type } = row.original;
      return <span className="text-sm font-medium">{type}</span>;
    },
    size: 100,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const { status } = row.original;

      return <span className="text-sm">{status}</span>;
    },
    size: 110,
    meta: {
      className: 'text-center',
    },
  },
  {
    accessorKey: 'total_recommendation',
    header: 'Total Recommendations',
    cell: ({ row }) => {
      const { total_recommendation, id } = row.original;
      // Using lodash flow for functional composition:
      // 1. Takes resourceId and transforms it into RecommendationQueryParams object
      // 2. Transforms the params object into a URL string with query parameters
      // 3. Finally applies the composed function to data.id
      const allRecommendationsHref = flow(
        (resourceId: string): RecommendationQueryParams => ({
          resource_id: [resourceId],
        }),
        (params) =>
          `${pathsConfig.app.resourceRecommendations}?${new URLSearchParams(params as URLSearchParams).toString()}`,
      )(id);

      return (
        <If
          condition={total_recommendation}
          fallback={<p>{total_recommendation}</p>}
        >
          <WithTooltip tooltip="View all recommendations">
            <Button asChild variant="link" className="p-0">
              <Link href={allRecommendationsHref}>{total_recommendation}</Link>
            </Button>
          </WithTooltip>
        </If>
      );
    },
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'total_potential_saving',
    header: 'Potential Saving',
    cell: ({ row }) => {
      const value = row.original.total_potential_saving;
      if (typeof value !== 'number') {
        return 'N/A';
      }

      return <p>{formatUSD(value)}</p>;
    },
    meta: {
      className: 'text-right',
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Last Updated',
    cell: renderCellToFullDateTime,
    size: 230,
    meta: {
      className: 'text-center whitespace-nowrap',
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => {
      return <RowActions resource={row.original} />;
    },
    meta: {
      className: 'text-right',
    },
  },
];

function RowActions({ resource }: { resource: SchemaResourcePublic }) {
  const { agentId } = useUserContext();

  const getAgentDetailUrl = (prefix: string) => {
    return agentDetailUrl(agentId, {
      initialMessage: prefix,
      resource_id: resource.id,
    });
  };

  return (
    <div className="flex items-center justify-end gap-4">
      {/* Sparkles icon with dropdown for optimize options */}
      <DropdownMenu>
        <WithTooltip tooltip="Open AI Actions Menu">
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="outlinePrimary">
              <SparklesIcon className="size-4" />
            </Button>
          </DropdownMenuTrigger>
        </WithTooltip>
        <DropdownMenuContent align="end" className="w-48">
          {ROW_ACTIONS.map(({ key, label, prefix, icon: Icon }) => (
            <DropdownMenuItem key={key} onSelect={(e) => e.preventDefault()}>
              <Link
                href={getAgentDetailUrl(prefix)}
                className="flex items-center gap-2"
              >
                <Icon className="size-4" />
                <span>{label}</span>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmDeleteResource resourceId={resource.id}>
        <WithTooltip tooltip="Delete Resource">
          <Button variant="outline" size="icon">
            <Trash2Icon className="size-4" />
          </Button>
        </WithTooltip>
      </ConfirmDeleteResource>
    </div>
  );
}

type RowActionKey = 'optimize_cost' | 'scan_security' | 'check_performance';

const ROW_ACTIONS: Array<{
  key: RowActionKey;
  label: string;
  prefix: string;
  icon: React.ComponentType<{ className?: string }>;
}> = [
  {
    key: 'optimize_cost',
    label: 'Optimize Cost',
    prefix: '@Alex please optimize the cost of this resource',
    icon: Wallet,
  },
  {
    key: 'scan_security',
    label: 'Scan Security',
    prefix: '@Alex please perform a security scan on this resource',
    icon: ShieldCheck,
  },
  {
    key: 'check_performance',
    label: 'Check Performance',
    prefix: '@Alex please check the performance of this resource',
    icon: Activity,
  },
];
