'use client';

import { If } from '@/components/ui/common/if';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import { TableUI } from '@/components/ui/table/table-ui';
import { resourceQuery } from '@/features/resources/hooks/resource.query';
import { WithPaginationDefaults } from '@/utils/with-pagination-defaults';

import { ResourceQueryParams } from '../../models/resource.type';
import { resourceColumns } from './columns';

export function ResourceTable({
  searchParams,
}: {
  searchParams: WithPaginationDefaults<ResourceQueryParams>;
}) {
  const { data, isLoading, isRefetching } =
    resourceQuery.query.useList(searchParams);

  return (
    <If
      condition={data}
      fallback={
        <If condition={isLoading}>
          <DataTableSkeleton columnCount={resourceColumns.length} />
        </If>
      }
    >
      {(data) => (
        <TableUI
          data={data.data}
          columns={resourceColumns}
          meta={data.meta}
          isRefetching={isRefetching}
        />
      )}
    </If>
  );
}
