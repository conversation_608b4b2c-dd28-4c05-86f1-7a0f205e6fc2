import { BadgeProps } from '@/components/ui/badge';
import { CloudProvider } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const RESOURCE_PROVIDER_CONFIG = createUtilityConfig({
  [CloudProvider.AWS]: {
    label: 'AWS',
    variant: 'beta',
  },
  [CloudProvider.GCP]: {
    label: 'GCP',
    variant: 'alpha',
  },
  [CloudProvider.AZURE]: {
    label: 'Azure',
    variant: 'default',
  },
} satisfies Record<
  CloudProvider,
  {
    label: string;
    variant: BadgeProps['variant'];
  }
>);
