import { FC } from 'react';

import {
  AppRunnerIcon,
  BackupIcon,
  BatchIcon,
  CloudFormationIcon,
  CloudwatchIcon,
  DocumentdbIcon,
  DynamodbIcon,
  EbsIcon,
  Ec2AutoScalingIcon,
  Ec2Icon,
  EcsIcon,
  EfsIcon,
  EksIcon,
  ElasticBeanstalkIcon,
  ElasticacheIcon,
  ElbIcon,
  LambdaIcon,
  NeptuneIcon,
  OpensearchIcon,
  RdsIcon,
  RedshiftIcon,
  S3Icon,
  SnsIcon,
  SqsIcon,
  VpcIcon,
} from '@/components/aws-icons';
import { createUtilityConfig } from '@/utils/option-config';
import { LayersIcon } from 'lucide-react';

// AWS-specific resource types (hardcoded based on backend enum)
export enum AWSResourceType {
  // Compute Services
  EC2 = 'EC2',
  LAMBDA = 'LAMBDA',
  ECS = 'ECS',
  EKS = 'EKS',
  BATCH = 'BATCH',
  EC2_AUTO_SCALING = 'EC2_AUTO_SCALING',
  ELASTIC_BEANSTALK = 'ELASTIC_BEANSTALK',
  APP_RUNNER = 'APP_RUNNER',

  // Database Services
  RDS = 'RDS',
  DYNAMODB = 'DYNAMODB',
  ELASTICACHE = 'ELASTICACHE',
  NEPTUNE = 'NEPTUNE',
  DOCUMENTDB = 'DOCUMENTDB',
  OPENSEARCH = 'OPENSEARCH',
  REDSHIFT = 'REDSHIFT',

  // Storage Services
  S3 = 'S3',
  EBS = 'EBS',
  EFS = 'EFS',
  BACKUP = 'BACKUP',

  // Networking & Content Delivery
  VPC = 'VPC',
  ELB = 'ELB',

  // Management & Governance
  CLOUDFORMATION = 'CLOUDFORMATION',
  CLOUDWATCH = 'CLOUDWATCH',
  SQS = 'SQS',
  SNS = 'SNS',

  // General/Unknown Resource Types
  GENERAL = 'General',
}

export const RESOURCE_TYPE_CONFIG = createUtilityConfig({
  [AWSResourceType.EC2]: {
    label: 'EC2',
    icon: Ec2Icon,
  },
  [AWSResourceType.LAMBDA]: {
    label: 'Lambda',
    icon: LambdaIcon,
  },
  [AWSResourceType.ECS]: {
    label: 'ECS',
    icon: EcsIcon,
  },
  [AWSResourceType.EKS]: {
    label: 'EKS',
    icon: EksIcon,
  },
  [AWSResourceType.BATCH]: {
    label: 'Batch',
    icon: BatchIcon,
  },
  [AWSResourceType.EC2_AUTO_SCALING]: {
    label: 'EC2 Auto Scaling',
    icon: Ec2AutoScalingIcon,
  },
  [AWSResourceType.ELASTIC_BEANSTALK]: {
    label: 'Elastic Beanstalk',
    icon: ElasticBeanstalkIcon,
  },
  [AWSResourceType.APP_RUNNER]: {
    label: 'App Runner',
    icon: AppRunnerIcon,
  },
  [AWSResourceType.RDS]: {
    label: 'RDS',
    icon: RdsIcon,
  },
  [AWSResourceType.DYNAMODB]: {
    label: 'DynamoDB',
    icon: DynamodbIcon,
  },
  [AWSResourceType.ELASTICACHE]: {
    label: 'ElastiCache',
    icon: ElasticacheIcon,
  },
  [AWSResourceType.NEPTUNE]: {
    label: 'Neptune',
    icon: NeptuneIcon,
  },
  [AWSResourceType.DOCUMENTDB]: {
    label: 'DocumentDB',
    icon: DocumentdbIcon,
  },
  [AWSResourceType.OPENSEARCH]: {
    label: 'OpenSearch',
    icon: OpensearchIcon,
  },
  [AWSResourceType.REDSHIFT]: {
    label: 'Redshift',
    icon: RedshiftIcon,
  },
  [AWSResourceType.S3]: {
    label: 'S3',
    icon: S3Icon,
  },
  [AWSResourceType.EBS]: {
    label: 'EBS',
    icon: EbsIcon,
  },
  [AWSResourceType.EFS]: {
    label: 'EFS',
    icon: EfsIcon,
  },
  [AWSResourceType.BACKUP]: {
    label: 'Backup',
    icon: BackupIcon,
  },
  [AWSResourceType.VPC]: {
    label: 'VPC',
    icon: VpcIcon,
  },
  [AWSResourceType.ELB]: {
    label: 'ELB',
    icon: ElbIcon,
  },
  [AWSResourceType.CLOUDFORMATION]: {
    label: 'CloudFormation',
    icon: CloudFormationIcon,
  },
  [AWSResourceType.CLOUDWATCH]: {
    label: 'CloudWatch',
    icon: CloudwatchIcon,
  },
  [AWSResourceType.SQS]: {
    label: 'SQS',
    icon: SqsIcon,
  },
  [AWSResourceType.SNS]: {
    label: 'SNS',
    icon: SnsIcon,
  },
  [AWSResourceType.GENERAL]: {
    label: 'General',
    icon: LayersIcon,
  },
} satisfies Record<
  AWSResourceType,
  { label: string; icon: FC<{ className?: string }> }
>);
