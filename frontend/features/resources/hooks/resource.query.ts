import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';

import {
  ChatWithResourceFilter,
  ResourceQueryParams,
} from '../models/resource.type';
import { resourceApi } from '../services/resource.api';

const resourceQueryKeys = createQueryKeys('resources', {
  list: (params: WithPaginationDefaults<ResourceQueryParams>) => ({
    queryKey: [params],
    queryFn: () => resourceApi.list(handleSkipOfPagination(params)),
  }),

  infiniteList: (params: ChatWithResourceFilter) => ({
    queryKey: [params],
  }),

  byId: (id: string) => ({
    queryKey: [id],
    queryFn: () => resourceApi.getById(id),
  }),

  statistics: () => ({
    queryKey: ['statistics'],
    queryFn: () => resourceApi.getStatistics(),
  }),
});

const useList = (params: WithPaginationDefaults<ResourceQueryParams>) => {
  return useQuery({
    ...resourceQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useInfiniteList = (params: ChatWithResourceFilter) => {
  return useInfiniteQuery({
    queryKey: resourceQueryKeys.infiniteList(params).queryKey,
    queryFn: ({ pageParam = 0 }) =>
      resourceApi.list({
        ...params,
        skip: pageParam * 20,
        limit: 20,
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, __, lastPageParam) => {
      return lastPage.data.length === 20 ? lastPageParam + 1 : undefined;
    },
    placeholderData: keepPreviousData,
  });
};

const useById = (id: string, options?: { enabled?: boolean }) => {
  return useQuery({
    ...resourceQueryKeys.byId(id),
    enabled: options?.enabled,
  });
};

const useStatistics = () => {
  return useQuery({
    ...resourceQueryKeys.statistics(),
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

const useDelete = (resourceId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => resourceApi.delete(resourceId),
    onSuccess: () => {
      // Remove cached detail and invalidate lists/statistics
      queryClient.removeQueries({
        queryKey: resourceQueryKeys.byId(resourceId).queryKey,
      });
      queryClient.invalidateQueries({ queryKey: resourceQueryKeys._def });
    },
  });
};

export const resourceQuery = {
  query: {
    useList,
    useInfiniteList,
    useById,
    useStatistics,
  },
  mutation: {
    useDelete,
  },
};
