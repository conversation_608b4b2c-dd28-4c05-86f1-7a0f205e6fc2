import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { ResourceQueryParams } from '../models/resource.type';

export const resourceApi = {
  list: (query: ResourceQueryParams) =>
    fetchData(
      api.GET('/api/v1/resources/', {
        params: {
          query,
        },
      }),
    ),

  getById: (id: string) =>
    fetchData(
      api.GET('/api/v1/resources/{resource_id}', {
        params: {
          path: { resource_id: id },
        },
      }),
    ),

  getStatistics: () => fetchData(api.GET('/api/v1/resources/statistics')),

  delete: (id: string) =>
    fetchData(
      api.DELETE('/api/v1/resources/{resource_id}', {
        params: {
          path: { resource_id: id },
        },
      }),
    ),
};
