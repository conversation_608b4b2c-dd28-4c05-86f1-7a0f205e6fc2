'use client';

import { PropsWithChildren } from 'react';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { SchemaShortcutCreate } from '@/openapi-ts/gens';
import { SubmitHandler } from 'react-hook-form';
import { useToggle } from 'usehooks-ts';

import { shortcutQuery } from '../hooks/shortcut.query';
import { ShortcutForm } from './shortcut-form';

export function CreateShortcutDialog({ children }: PropsWithChildren) {
  const [isOpen, toggle] = useToggle(false);
  const { mutate, isPending } = shortcutQuery.mutation.useCreate();

  const onSubmit: SubmitHandler<SchemaShortcutCreate> = (data) => {
    mutate(data, {
      onSuccess: () => {
        toggle();
      },
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create Shortcut</DialogTitle>
        </DialogHeader>
        <ShortcutForm onSubmit={onSubmit} isPending={isPending} />
      </DialogContent>
    </Dialog>
  );
}
