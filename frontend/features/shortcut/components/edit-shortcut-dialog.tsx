'use client';

import { useState } from 'react';

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { SchemaShortcutPublic, SchemaShortcutUpdate } from '@/openapi-ts/gens';

import { shortcutQuery } from '../hooks/shortcut.query';
import { ShortcutForm } from './shortcut-form';

type EditShortcutDialogProps = {
  shortcut: SchemaShortcutPublic | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export function EditShortcutDialog({
  shortcut,
  open,
  onOpenChange,
}: EditShortcutDialogProps) {
  const [isPending, setIsPending] = useState(false);

  const updateShortcut = shortcutQuery.mutation.useUpdate();

  const handleSubmit = async (data: SchemaShortcutUpdate) => {
    if (!shortcut) return;

    setIsPending(true);
    try {
      await updateShortcut.mutateAsync({
        shortcutId: shortcut.id,
        data,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to update shortcut:', error);
    } finally {
      setIsPending(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!isPending) {
      onOpenChange(newOpen);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Shortcut</DialogTitle>
        </DialogHeader>

        {shortcut && (
          <ShortcutForm
            onSubmit={handleSubmit}
            defaultValues={{
              title: shortcut.title,
              slug: shortcut.slug,
              content: shortcut.content,
            }}
            isPending={isPending}
            isEdit={true}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
