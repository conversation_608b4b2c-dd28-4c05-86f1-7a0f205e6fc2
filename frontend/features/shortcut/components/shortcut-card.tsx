'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { WithTooltip } from '@/components/ui/tooltip';
import { SchemaShortcutPublic } from '@/openapi-ts/gens';
import { EditIcon, MoreHorizontalIcon, TrashIcon } from 'lucide-react';

type Props = {
  shortcut: SchemaShortcutPublic;
  onEdit?: (shortcut: SchemaShortcutPublic) => void;
  onDelete?: (shortcut: SchemaShortcutPublic) => void;
  onUse?: (shortcut: SchemaShortcutPublic) => void;
};

export function ShortcutCard({ shortcut, onEdit, onDelete, onUse }: Props) {
  return (
    <Card
      className="group border-muted/40 hover:border-muted relative transition-colors"
      data-test="shortcut-card"
    >
      <CardContent className="p-3">
        <div className="flex items-start gap-3">
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <code className="bg-muted text-muted-foreground rounded px-2 py-0.5 text-xs tracking-wide">
                /{shortcut.slug}
              </code>
            </div>

            <WithTooltip
              tooltip={
                <div className="whitespace-pre-line">
                  {shortcut.content.replace(/\\n/g, '\n')}
                </div>
              }
            >
              <p className="text-muted-foreground mt-2 line-clamp-2 cursor-pointer text-sm">
                {shortcut.content}
              </p>
            </WithTooltip>

            <If condition={onUse}>
              <div className="mt-3">
                <Button
                  size="sm"
                  onClick={() => onUse?.(shortcut)}
                  data-test="shortcut-use"
                >
                  Use Shortcut
                </Button>
              </div>
            </If>
            {/* Footer with category badge and actions */}
            <div className="mt-3 flex items-center justify-between">
              <If condition={shortcut.category}>
                {(category) => (
                  <Badge
                    variant="secondary"
                    className="truncate px-1.5 py-0 text-xs"
                  >
                    {category}
                  </Badge>
                )}
              </If>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-muted-foreground hover:text-foreground size-7 opacity-0 transition-opacity group-hover:opacity-100"
                    aria-label="Open actions"
                    data-test="shortcut-actions"
                  >
                    <MoreHorizontalIcon className="size-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <If condition={onEdit}>
                    <DropdownMenuItem onClick={() => onEdit?.(shortcut)}>
                      <EditIcon className="mr-2 size-4" />
                      Edit
                    </DropdownMenuItem>
                  </If>
                  <If condition={onDelete}>
                    <DropdownMenuItem
                      onClick={() => onDelete?.(shortcut)}
                      className="text-destructive focus:text-destructive"
                    >
                      <TrashIcon className="mr-2 size-4" />
                      Delete
                    </DropdownMenuItem>
                  </If>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
