'use client';

import { But<PERSON> } from '@/components/ui/button';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { SchemaShortcutCreate } from '@/openapi-ts/gens';
import { zodResolver } from '@hookform/resolvers/zod';
import { SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';

const shortcutSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().min(1, 'Slug is required'),
  content: z.string().min(1, 'Content is required'),
  category: z.string().nullable().optional(),
  is_default: z.boolean(),
});

type Props = {
  onSubmit: SubmitHandler<SchemaShortcutCreate>;
  defaultValues?: Partial<SchemaShortcutCreate>;
  isPending?: boolean;
  isEdit?: boolean;
};

export function ShortcutForm({
  onSubmit,
  defaultValues,
  isPending,
  isEdit,
}: Props) {
  const form = useForm<SchemaShortcutCreate>({
    resolver: zodResolver(shortcutSchema),
    defaultValues: {
      title: '',
      slug: '',
      content: '',
      category: null,
      is_default: false,
      ...defaultValues,
    },
  });

  const { control, handleSubmit, setValue } = form;

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
      .substring(0, 100);
  };

  // Auto-update slug when title changes (only for new shortcuts)
  const handleTitleChange = (title: string) => {
    if (!isEdit && title) {
      const slug = generateSlug(title);
      setValue('slug', slug);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Title</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter shortcut title"
                  onChange={(e) => {
                    field.onChange(e);
                    handleTitleChange(e.target.value);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="slug"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Slug</FormLabel>
              <FormControl>
                <Input {...field} placeholder="enter-slug-here" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Content</FormLabel>
              <FormDescription>
                The text content that will be inserted when using this shortcut
              </FormDescription>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Enter the shortcut content..."
                  className="min-h-48"
                  value={field.value ?? ''}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button type="submit" disabled={isPending}>
            <UpdateWrapper isPending={isPending}>
              {isEdit ? 'Update' : 'Create'} Shortcut
            </UpdateWrapper>
          </Button>
        </div>
      </form>
    </Form>
  );
}
