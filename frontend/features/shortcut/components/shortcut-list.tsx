'use client';

import { useState } from 'react';

import { If } from '@/components/ui/common/if';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { SchemaShortcutPublic } from '@/openapi-ts/gens';

import { shortcutQuery } from '../hooks/shortcut.query';
import { EditShortcutDialog } from './edit-shortcut-dialog';
import { ShortcutCard } from './shortcut-card';

type Props = {
  onUseShortcut?: (shortcut: SchemaShortcutPublic) => void;
  filterMode?: 'all' | 'defaults' | 'user';
};

export function ShortcutList({ onUseShortcut, filterMode = 'all' }: Props) {
  // Edit dialog state
  const [editingShortcut, setEditingShortcut] =
    useState<SchemaShortcutPublic | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const { data, isLoading, error } = shortcutQuery.query.useList({
    page: 1,
    page_size: 50,
    include_defaults: filterMode !== 'user',
  });

  const { mutate: deleteShortcut } = shortcutQuery.mutation.useDelete();

  const handleDelete = (shortcut: SchemaShortcutPublic) => {
    if (confirm(`Are you sure you want to delete "${shortcut.title}"?`)) {
      deleteShortcut(shortcut.id);
    }
  };

  const handleEdit = (shortcut: SchemaShortcutPublic) => {
    setEditingShortcut(shortcut);
    setIsEditDialogOpen(true);
  };

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false);
    setEditingShortcut(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-destructive p-8 text-center">
        Failed to load shortcuts. Please try again.
      </div>
    );
  }

  let shortcuts = data?.data || [];
  if (filterMode === 'defaults') {
    shortcuts = shortcuts.filter((s) => s.is_default);
  } else if (filterMode === 'user') {
    shortcuts = shortcuts.filter((s) => !s.is_default);
  }

  return (
    <div className="space-y-4">
      <If condition={shortcuts.length === 0}>
        <div className="p-12 text-center">
          <h3 className="mb-2 text-lg font-semibold">No shortcuts yet</h3>
          <p className="text-muted-foreground">
            Create your first shortcut to get started
          </p>
        </div>
      </If>

      <If condition={shortcuts.length > 0}>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-1 lg:grid-cols-2">
          {shortcuts.map((shortcut) => (
            <ShortcutCard
              key={shortcut.id}
              shortcut={shortcut}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onUse={onUseShortcut}
            />
          ))}
        </div>
      </If>

      {/* Edit Shortcut Dialog */}
      <EditShortcutDialog
        shortcut={editingShortcut}
        open={isEditDialogOpen}
        onOpenChange={handleCloseEditDialog}
      />
    </div>
  );
}
