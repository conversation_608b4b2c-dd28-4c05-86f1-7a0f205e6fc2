'use client';

import { useEffect, useMemo, useRef, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { If } from '@/components/ui/common/if';
import { cn } from '@/lib/utils';
import { SchemaShortcutPublic } from '@/openapi-ts/gens';
import { ZapIcon } from 'lucide-react';

import { shortcutQuery } from '../hooks/shortcut.query';

type ShortcutMentionDropdownProps = {
  isVisible: boolean;
  position: { top: number; left: number };
  filter: string;
  onSelect: (shortcut: SchemaShortcutPublic) => void;
  onClose?: () => void;
};

export function ShortcutMentionDropdown({
  isVisible,
  position,
  filter,
  onSelect,
  onClose,
}: ShortcutMentionDropdownProps) {
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const commandRef = useRef<HTMLDivElement>(null);
  const shortcutItemsRef = useRef<Array<HTMLDivElement | null>>([]);
  const listRef = useRef<HTMLDivElement>(null);

  // Fetch shortcuts based on filter
  const { data: shortcutsData, isLoading } = shortcutQuery.query.useList({
    search: filter || undefined,
    page: 1,
    page_size: 20,
    include_defaults: true,
  });

  // Filter shortcuts based on input - search both slug, title, and content
  const filteredShortcuts = useMemo(() => {
    const shortcuts = shortcutsData?.data || [];
    return shortcuts.filter((shortcut) => {
      if (!filter) return true;
      const searchTerm = filter.toLowerCase();
      return (
        shortcut.slug.toLowerCase().includes(searchTerm) ||
        shortcut.title.toLowerCase().includes(searchTerm) ||
        shortcut.content.toLowerCase().includes(searchTerm) ||
        (shortcut.category &&
          shortcut.category.toLowerCase().includes(searchTerm))
      );
    });
  }, [shortcutsData?.data, filter]);

  // Reset highlighted index when filtered data changes
  useEffect(() => {
    setHighlightedIndex(0);
  }, [filteredShortcuts.length]);

  // Initialize when dropdown becomes visible
  useEffect(() => {
    if (isVisible) {
      setHighlightedIndex(0);
    }
  }, [isVisible]);

  // Handle keyboard events
  useEffect(() => {
    if (!isVisible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev > 0 ? prev - 1 : filteredShortcuts.length - 1,
        );
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setHighlightedIndex((prev) =>
          prev < filteredShortcuts.length - 1 ? prev + 1 : 0,
        );
      } else if (e.key === 'Enter') {
        e.preventDefault();
        const shortcut = filteredShortcuts[highlightedIndex];
        if (shortcut) {
          onSelect(shortcut);
        }
      } else if (e.key === 'Escape') {
        e.preventDefault();
        onClose?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isVisible, filteredShortcuts, highlightedIndex, onSelect, onClose]);

  // Ensure the highlighted item is visible within the scrollable list
  useEffect(() => {
    if (!isVisible || filteredShortcuts.length === 0) return;

    const highlightedElement = shortcutItemsRef.current[highlightedIndex];
    const containerElement = listRef.current;

    if (highlightedElement) {
      highlightedElement.scrollIntoView({ block: 'nearest' });
    } else if (containerElement) {
      containerElement.scrollTop = 0;
    }
  }, [highlightedIndex, isVisible, filteredShortcuts.length]);

  if (!isVisible) return null;

  return (
    <div
      className="fixed z-50"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
      }}
    >
      <Command
        ref={commandRef}
        className="w-[500px] rounded-lg border shadow-md"
        tabIndex={-1}
        value={filteredShortcuts[highlightedIndex]?.slug || ''}
      >
        <div className="flex items-center gap-2 border-b px-3 py-2">
          <span className="text-sm font-medium">Insert a shortcut</span>
          <Badge variant="secondary" className="text-xs">
            {filteredShortcuts.length}{' '}
            {filteredShortcuts.length === 1 ? 'shortcut' : 'shortcuts'}
          </Badge>
        </div>

        <CommandList
          ref={listRef}
          className="max-h-[180px]"
          onKeyDown={(e) => e.preventDefault()}
        >
          <If condition={isLoading}>
            <div className="flex items-center justify-center py-6">
              <div className="bg-muted size-8 animate-pulse rounded-full" />
            </div>
          </If>

          <If condition={!isLoading && filteredShortcuts.length === 0}>
            <CommandEmpty>
              <div className="flex flex-col items-center justify-center py-6 text-center">
                <div className="bg-muted mb-2 flex size-8 items-center justify-center rounded-full">
                  <ZapIcon className="text-muted-foreground size-4" />
                </div>
                <p className="text-sm font-medium">No shortcuts found</p>
                <p className="text-muted-foreground mt-1 text-xs">
                  Try a different search term or create a new shortcut
                </p>
              </div>
            </CommandEmpty>
          </If>

          <If condition={!isLoading && filteredShortcuts.length > 0}>
            <CommandGroup>
              {filteredShortcuts.map((shortcut, index) => (
                <CommandItem
                  key={shortcut.id}
                  ref={(el) => {
                    shortcutItemsRef.current[index] = el;
                  }}
                  value={shortcut.slug}
                  onSelect={() => onSelect(shortcut)}
                  className={cn(
                    'flex items-center gap-2',
                    index === highlightedIndex && 'bg-accent',
                  )}
                  data-selected={index === highlightedIndex}
                >
                  <code className="bg-muted text-muted-foreground rounded px-2 py-0.5 text-[10px] tracking-wide">
                    /{shortcut.slug}
                  </code>
                  <div className="flex items-center gap-2">
                    <If condition={shortcut.category}>
                      <span className="text-muted-foreground truncate text-[10px]">
                        {shortcut.category}
                      </span>
                    </If>
                    <If condition={shortcut.is_default}>
                      <Badge
                        variant="secondary"
                        className="h-5 px-1.5 text-[10px]"
                      >
                        Default
                      </Badge>
                    </If>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </If>
        </CommandList>
      </Command>
    </div>
  );
}
