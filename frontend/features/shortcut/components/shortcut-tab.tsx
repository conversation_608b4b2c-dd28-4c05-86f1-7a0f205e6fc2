'use client';

import { But<PERSON> } from '@/components/ui/button';
import { CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SchemaShortcutPublic } from '@/openapi-ts/gens';
import { PlusIcon } from 'lucide-react';

import { CreateShortcutDialog } from './create-shortcut-dialog';
import { ShortcutList } from './shortcut-list';

type Props = {
  onUseShortcut?: (shortcut: SchemaShortcutPublic) => void;
};

export function ShortcutTab({ onUseShortcut }: Props) {
  return (
    <div>
      <CardContent className="h-[100vh] overflow-y-auto p-0">
        <Tabs defaultValue="user" className="w-full">
          <div className="mb-4 flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="user">Your Shortcuts</TabsTrigger>
              <TabsTrigger value="defaults">Default</TabsTrigger>
            </TabsList>

            <CreateShortcutDialog>
              <Button>
                <PlusIcon className="mr-2 size-4" />
                New Shortcut
              </Button>
            </CreateShortcutDialog>
          </div>

          <TabsContent value="user">
            <ShortcutList filterMode="user" onUseShortcut={onUseShortcut} />
          </TabsContent>
          <TabsContent value="defaults">
            <ShortcutList filterMode="defaults" onUseShortcut={onUseShortcut} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </div>
  );
}
