import { SchemaShortcutUpdate } from '@/openapi-ts/gens';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { ShortcutQueryParams } from '../models/shortcut.type';
import { shortcutApi } from '../services/shortcut.api';

const shortcutQueryKeys = createQueryKeys('shortcuts', {
  list: (query: ShortcutQueryParams) => ({
    queryKey: [query],
    queryFn: () => shortcutApi.list(query),
  }),
  detail: (shortcutId: string) => ({
    queryKey: [shortcutId],
    queryFn: () => shortcutApi.getById(shortcutId),
  }),
  bySlug: (slug: string) => ({
    queryKey: [slug],
    queryFn: () => shortcutApi.getBySlug(slug),
  }),
});

const useList = (params: ShortcutQueryParams) => {
  return useQuery({
    ...shortcutQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useDetail = (shortcutId: string) => {
  return useQuery({
    ...shortcutQueryKeys.detail(shortcutId),
    enabled: !!shortcutId,
  });
};

const useBySlug = (slug: string) => {
  return useQuery({
    ...shortcutQueryKeys.bySlug(slug),
    enabled: !!slug,
  });
};

const useCreate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shortcutApi.create,
    onSuccess: () => {
      toast.success('Shortcut created successfully');
      queryClient.invalidateQueries({
        queryKey: shortcutQueryKeys._def,
      });
    },
  });
};

const useUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      shortcutId,
      data,
    }: {
      shortcutId: string;
      data: SchemaShortcutUpdate;
    }) => shortcutApi.update(shortcutId, data),
    onSuccess: () => {
      toast.success('Shortcut updated successfully');
      queryClient.invalidateQueries({
        queryKey: shortcutQueryKeys._def,
      });
    },
  });
};

const useDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: shortcutApi.delete,
    onSuccess: () => {
      toast.success('Shortcut deleted successfully');
      queryClient.invalidateQueries({
        queryKey: shortcutQueryKeys._def,
      });
    },
  });
};

export const shortcutQuery = {
  query: {
    useList,
    useDetail,
    useBySlug,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
  },
};
