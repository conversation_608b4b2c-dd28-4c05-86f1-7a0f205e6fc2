import { SchemaShortcutCreate, SchemaShortcutUpdate } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

import { ShortcutQueryParams } from '../models/shortcut.type';

export const shortcutApi = {
  list: (query: ShortcutQueryParams) =>
    fetchData(
      api.GET('/api/v1/shortcuts/', {
        params: {
          query,
        },
      }),
    ),

  create: (body: SchemaShortcutCreate) =>
    fetchData(
      api.POST('/api/v1/shortcuts/', {
        body,
      }),
    ),

  getById: (shortcutId: string) =>
    fetchData(
      api.GET('/api/v1/shortcuts/{shortcut_id}', {
        params: {
          path: {
            shortcut_id: shortcutId,
          },
        },
      }),
    ),

  getBySlug: (slug: string) =>
    fetchData(
      api.GET('/api/v1/shortcuts/slug/{slug}', {
        params: {
          path: {
            slug,
          },
        },
      }),
    ),

  update: (shortcutId: string, body: SchemaShortcutUpdate) =>
    fetchData(
      api.PATCH('/api/v1/shortcuts/{shortcut_id}', {
        params: {
          path: {
            shortcut_id: shortcutId,
          },
        },
        body,
      }),
    ),

  delete: (shortcutId: string) =>
    fetchData(
      api.DELETE('/api/v1/shortcuts/{shortcut_id}', {
        params: {
          path: {
            shortcut_id: shortcutId,
          },
        },
      }),
    ),
};
