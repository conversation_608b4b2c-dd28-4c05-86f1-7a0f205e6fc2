import { shortcutApi } from '../services/shortcut.api';

/**
 * Expands shortcut patterns (/slug) in a message with their actual content
 * @param message The message containing potential /slug patterns
 * @returns Promise resolving to the message with shortcuts expanded
 */
export async function expandShortcuts(message: string): Promise<string> {
  if (!message.trim()) {
    return message;
  }

  // Pattern to match /slug (including hyphens, no spaces or special chars)
  const shortcutPattern = /\/([^@#/\s:;,.!?()[\]{}]+)/g;

  // Find all unique shortcut patterns in the message
  const matches = Array.from(message.matchAll(shortcutPattern));
  const uniqueSlugs = [...new Set(matches.map((match) => match[1]))];

  if (uniqueSlugs.length === 0) {
    return message;
  }

  // Fetch all shortcuts concurrently
  const shortcutPromises = uniqueSlugs.map(async (slug) => {
    try {
      const shortcut = await shortcutApi.getBySlug(slug);
      return { slug, shortcut };
    } catch (error) {
      // If shortcut doesn't exist or API fails, return null
      console.warn(`Failed to fetch shortcut for slug "${slug}":`, error);
      return { slug, shortcut: null };
    }
  });

  const shortcutResults = await Promise.all(shortcutPromises);

  // Create a map of slug to shortcut content
  const shortcutMap = new Map<string, string>();
  shortcutResults.forEach(({ slug, shortcut }) => {
    if (shortcut) {
      shortcutMap.set(slug, shortcut.content);
    }
  });

  // Replace all shortcut patterns with their content
  let expandedMessage = message;
  matches.forEach((match) => {
    const fullMatch = match[0]; // e.g., "/help"
    const slug = match[1]; // e.g., "help"
    const content = shortcutMap.get(slug);

    if (content) {
      // Replace the /slug with the actual content
      expandedMessage = expandedMessage.replace(fullMatch, content);
    }
    // If no content found, leave the /slug as-is
  });

  return expandedMessage;
}

/**
 * Checks if a message contains any shortcut patterns
 * @param message The message to check
 * @returns boolean indicating if shortcuts are present
 */
export function hasShortcuts(message: string): boolean {
  const shortcutPattern = /\/([^@#/\s:;,.!?()[\]{}]+)/g;
  return shortcutPattern.test(message);
}

/**
 * Extracts all shortcut slugs from a message
 * @param message The message to parse
 * @returns Array of unique slugs found in the message
 */
export function extractShortcutSlugs(message: string): string[] {
  const shortcutPattern = /\/([^@#/\s:;,.!?()[\]{}]+)/g;
  const matches = Array.from(message.matchAll(shortcutPattern));
  return [...new Set(matches.map((match) => match[1]))];
}
