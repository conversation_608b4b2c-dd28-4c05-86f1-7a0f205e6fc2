import Link from 'next/link';

import { Button } from '@/components/ui/button';
import pathsConfig from '@/config/paths.config';
import { MoveUpRightIcon } from 'lucide-react';

export const UpgradePlanButtonLink = () => {
  return (
    <Button size="sm" className="gap-2" asChild>
      <Link href={pathsConfig.app.purchase}>
        Upgrade plan
        <MoveUpRightIcon className="size-4" />
      </Link>
    </Button>
  );
};
