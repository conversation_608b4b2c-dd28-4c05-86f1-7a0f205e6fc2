'use client';

import { PageSkeleton } from '@/components/ui/common/page';
import { ErrorAlert } from '@/components/ui/error-alert';
import { Pagination } from '@/components/ui/pagination/pagination';
import { useNavigateToNewPage } from '@/components/ui/pagination/use-navigate-to-new-page';
import { SpinnerContainer } from '@/components/ui/spinner-container';
import {
  DEFAULT_LIMIT,
  WithPaginationDefaults,
} from '@/utils/with-pagination-defaults';

import { taskQuery } from '../hooks/task.query';
import { TaskQueryParams } from '../models/task.type';
import { TaskCard } from './task-card';

type TaskCardListProps = {
  searchParams: WithPaginationDefaults<TaskQueryParams>;
};

export function TaskCardList({ searchParams }: TaskCardListProps) {
  const { data, isLoading, isRefetching, isError, error, refetch } =
    taskQuery.query.useList(searchParams);
  const navigateToNewPage = useNavigateToNewPage();

  if (data) {
    return (
      <div className="space-y-2">
        <SpinnerContainer
          loading={isRefetching}
          className="@9xl:grid-cols-4 grid grid-cols-1 gap-4 @4xl:grid-cols-2 @7xl:grid-cols-3"
        >
          {data.data?.map((task) => (
            <TaskCard key={task.id} task={task} />
          ))}
        </SpinnerContainer>
        <Pagination
          currentPage={Number(searchParams.page ?? 1)}
          totalPages={Math.ceil(data.total / DEFAULT_LIMIT)}
          onPageChange={(page) => navigateToNewPage(page - 1)}
          total={data.total}
          unit="task"
        />
      </div>
    );
  }

  if (isLoading) return <PageSkeleton />;

  if (isError) {
    return (
      <ErrorAlert
        title="Failed to Load Tasks"
        description={
          error?.message ||
          'Unable to fetch the task list. Please check your connection and try again.'
        }
        action={{
          label: 'Retry',
          onClick: () => refetch(),
        }}
      />
    );
  }
}
