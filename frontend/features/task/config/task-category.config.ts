import { TaskCategoryEnum } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';
import {
  DollarSignIcon,
  LucideIcon,
  SaveIcon,
  SettingsIcon,
  ShieldIcon,
  TrendingUpIcon,
} from 'lucide-react';

export const taskCategoryConfig = createUtilityConfig({
  [TaskCategoryEnum.COST_OPTIMIZE]: {
    label: 'Cost Optimization',
    icon: DollarSignIcon,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
  },
  [TaskCategoryEnum.OPERATIONAL]: {
    label: 'Operational',
    icon: SettingsIcon,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
  },
  [TaskCategoryEnum.SCALABILITY]: {
    label: 'Scalability',
    icon: TrendingUpIcon,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
  },
  [TaskCategoryEnum.SECURITY]: {
    label: 'Security',
    icon: ShieldIcon,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
  },
  [TaskCategoryEnum.OPERATIONAL_EFFICIENCY]: {
    label: 'Operational Efficiency',
    icon: SettingsIcon,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
  },
  [TaskCategoryEnum.OTHER]: {
    label: 'Saved Task',
    icon: SaveIcon,
    color: 'text-gray-600',
    bgColor: 'bg-gray-50',
  },
} satisfies Record<
  TaskCategoryEnum,
  {
    label: string;
    icon: LucideIcon;
    color: string;
    bgColor: string;
  }
>);
