import { Progress } from '@/components/ui/progress';
import { useQuota } from '@/features/quota';
import { UpgradePlanButtonLink } from '@/features/subcriptions/components/upgrade-plan-button-link';

export function QuotaUsageDisplay() {
  const { quotaInfo, isLoading } = useQuota();

  if (isLoading || !quotaInfo) {
    return (
      <div className="bg-card rounded-lg border p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Quota Usage</h3>
            <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
          </div>
          <div className="space-y-2">
            <div className="h-2 w-full animate-pulse rounded bg-gray-200" />
            <div className="h-3 w-32 animate-pulse rounded bg-gray-200" />
          </div>
        </div>
      </div>
    );
  }

  const usagePercentage = quotaInfo.premium_usage_percentage || 0;

  return (
    <div className="bg-card rounded-lg border p-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Credits Usage</h3>
          <div className="flex items-center gap-3">
            <span className="text-primary text-2xl font-bold">
              {usagePercentage.toFixed(1)}%
            </span>
            <UpgradePlanButtonLink />
          </div>
        </div>

        <div className="space-y-2">
          <Progress value={usagePercentage} className="h-3" />
          <div className="text-muted-foreground flex justify-between text-sm">
            <span>
              {quotaInfo.premium_messages_used} /{' '}
              {quotaInfo.premium_messages_limit} credits used
            </span>
            <span>
              {quotaInfo.premium_messages_remaining} credits remaining
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
