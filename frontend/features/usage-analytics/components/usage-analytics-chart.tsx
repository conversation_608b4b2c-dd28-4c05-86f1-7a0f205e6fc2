import React, { useState } from 'react';

import type {
  EnhancedChartData,
  ProcessedChartData,
} from '@/components/chat/components/message/charts/common/types';
import { LineChart } from '@/components/chat/components/message/charts/line-chart';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useUserContext } from '@/features/user/provider/user-provider';
import { PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod } from '@/openapi-ts/gens';
import { Calendar, TrendingUp } from 'lucide-react';

import { useUsageOverTime } from '../hooks/usage-analytics.query';

type TimePeriod = PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod;

const formatDate = (dateString: string, period: TimePeriod): string => {
  const date = new Date(dateString);

  switch (period) {
    case PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.day:
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    case PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.week: {
      const weekEnd = new Date(date);
      weekEnd.setDate(date.getDate() + 6);
      return `${date.getMonth() + 1}/${date.getDate()} - ${weekEnd.getMonth() + 1}/${weekEnd.getDate()}`;
    }
    case PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.month:
      return date.toLocaleDateString('en-US', {
        month: 'short',
        year: 'numeric',
      });
    case PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.year:
      return date.getFullYear().toString();
    default:
      return date.toLocaleDateString();
  }
};

export function UsageAnalyticsChart() {
  const { user } = useUserContext();
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>(
    PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.day,
  );

  const {
    data: usageData,
    isLoading,
    error,
  } = useUsageOverTime(user?.id || '', selectedPeriod);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="size-5" />
            Usage Over Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-80 items-center justify-center">
            <div className="text-muted-foreground flex items-center gap-2">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-r-transparent" />
              Loading chart data...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !usageData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="size-5" />
            Usage Over Time
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-80 items-center justify-center">
            <div className="text-center">
              <div className="mb-2 text-red-500">Failed to load chart data</div>
              <div className="text-muted-foreground text-sm">
                Please try refreshing the page
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Transform data for the LineChart component
  const processedChartData: ProcessedChartData[] = usageData.data.map(
    (point) => ({
      name: formatDate(point.date, selectedPeriod),
      value: point.total_messages, // Primary metric
      color: 'hsl(217, 91%, 60%)', // Will be overridden by gradient
      originalValue: point.total_messages,
    }),
  );

  // Enhanced chart configuration
  const enhancedChartData: EnhancedChartData = {
    labels: usageData.data.map((point) =>
      formatDate(point.date, selectedPeriod),
    ),
    datasets: [
      {
        data: usageData.data.map((point) => point.total_messages),
        label: 'Total Messages',
        backgroundColor: 'hsl(217, 91%, 60%)',
      },
    ],
    x_axis: {
      title: 'Time Period',
      type: 'category',
    },
    y_axis: {
      title: 'Credit Count',
      type: 'linear',
    },
    display_options: {
      show_legend: false,
      show_grid: true,
    },
  };

  const periods: { key: TimePeriod; label: string; icon: React.ReactNode }[] = [
    {
      key: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.day,
      label: 'Daily',
      icon: <Calendar className="size-4" />,
    },
    {
      key: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.week,
      label: 'Weekly',
      icon: <Calendar className="size-4" />,
    },
    {
      key: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.month,
      label: 'Monthly',
      icon: <Calendar className="size-4" />,
    },
    {
      key: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.year,
      label: 'Yearly',
      icon: <Calendar className="size-4" />,
    },
  ];

  return (
    <Card>
      <CardHeader className="space-y-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="size-5" />
            Usage Over Time
          </CardTitle>

          <div className="flex gap-1">
            {periods.map((period) => (
              <Button
                key={period.key}
                variant={selectedPeriod === period.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedPeriod(period.key)}
                className="flex items-center gap-1"
              >
                {period.icon}
                {period.label}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="h-80">
          <LineChart
            data={processedChartData}
            chartData={enhancedChartData}
            height="100%"
            width="100%"
          />
        </div>
      </CardContent>
    </Card>
  );
}
