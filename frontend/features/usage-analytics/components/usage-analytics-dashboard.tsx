import React from 'react';

import {
  QuotaUsageDisplay,
  UsageAnalyticsChart,
  UsageAnalyticsHeroCards,
} from './index';

interface UsageAnalyticsDashboardProps {
  userId: string;
}

export const UsageAnalyticsDashboard: React.FC<
  UsageAnalyticsDashboardProps
> = () => {
  return (
    <div className="space-y-6">
      {/* Hero Metrics */}
      <UsageAnalyticsHeroCards />

      {/* Quota Usage Display */}
      <QuotaUsageDisplay />

      {/* Usage Over Time Chart */}
      <UsageAnalyticsChart />
    </div>
  );
};
