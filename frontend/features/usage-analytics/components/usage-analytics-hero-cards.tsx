import DashboardCard from '@/components/dashboard-card';
import { useQuota } from '@/features/quota';
import { Building, Calendar, Database, Users } from 'lucide-react';

function formatStorageValue(value: number): string {
  if (value >= 10000) {
    // Convert to GB if more than 5 digits
    const gbValue = (value / 1024).toFixed(1);
    return `${gbValue} GB`;
  }
  return `${value} MB`;
}

export function UsageAnalyticsHeroCards() {
  const { quotaInfo, isLoading, isError, error } = useQuota();

  if (quotaInfo) {
    return (
      <div className="grid gap-4 md:grid-cols-4">
        <DashboardCard
          title="Workspaces"
          value={`${quotaInfo.workspaces_used}`}
          subtitle={`${quotaInfo.workspaces_remaining} remaining • ${quotaInfo.workspaces_limit} total`}
          icon={<Building className="text-muted-foreground size-4" />}
        />

        <DashboardCard
          title="Team Members"
          value={`${quotaInfo.members_used}`}
          subtitle={`${quotaInfo.members_remaining} remaining • ${quotaInfo.members_limit} total`}
          icon={<Users className="text-muted-foreground size-4" />}
        />

        <DashboardCard
          title="Scheduled Tasks"
          value={`${quotaInfo.scheduled_tasks_used}`}
          subtitle={`${quotaInfo.scheduled_tasks_remaining} remaining • ${quotaInfo.scheduled_tasks_limit} total`}
          icon={<Calendar className="text-muted-foreground size-4" />}
        />

        <DashboardCard
          title="Knowledge Base Storage"
          value={formatStorageValue(quotaInfo.kb_storage_used)}
          subtitle={`${formatStorageValue(quotaInfo.kb_storage_remaining)} remaining • ${formatStorageValue(quotaInfo.kb_storage_limit)} total`}
          icon={<Database className="text-muted-foreground size-4" />}
        />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Loading..."
            value="..."
            subtitle="Loading quota info..."
            icon={<div className="size-4 animate-pulse rounded bg-gray-200" />}
          />
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="grid gap-4 md:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <DashboardCard
            key={i}
            title="Error"
            value="..."
            subtitle={error?.message || 'Failed to load quota info'}
            icon={<div className="size-4 rounded bg-red-200" />}
          />
        ))}
      </div>
    );
  }
}
