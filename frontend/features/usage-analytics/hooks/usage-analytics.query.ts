import { PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod } from '@/openapi-ts/gens';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useQuery } from '@tanstack/react-query';

import {
  UsageAnalyticsQueryParams,
  usageAnalyticsApi,
} from '../services/usage-analytics.api';

const usageAnalyticsQueryKeys = createQueryKeys('usage-analytics', {
  usageOverTime: (
    user_id: string,
    period: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod,
    params?: UsageAnalyticsQueryParams,
  ) => ({
    queryKey: [user_id, period, params],
    queryFn: () => usageAnalyticsApi.getUsageOverTime(user_id, period, params),
  }),
});

const useUsageOverTime = (
  user_id: string,
  period: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod = PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.day,
  params?: UsageAnalyticsQueryParams,
) =>
  useQuery({
    ...usageAnalyticsQueryKeys.usageOverTime(user_id, period, params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!user_id,
  });

export { useUsageOverTime };
