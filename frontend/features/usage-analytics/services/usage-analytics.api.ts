import { PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod } from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

export interface UsageAnalyticsQueryParams {
  start_date?: string;
  end_date?: string;
}

export const usageAnalyticsApi = {
  getUsageOverTime: (
    user_id: string,
    period: PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod = PathsApiV1QuotasUser_idUsageOverTimeGetParametersQueryPeriod.day,
    query?: UsageAnalyticsQueryParams,
  ) =>
    fetchData(
      api.GET('/api/v1/quotas/{user_id}/usage-over-time', {
        params: {
          path: {
            user_id,
          },
          query: {
            period,
            ...(query || {}),
          },
        },
      }),
    ),
};
