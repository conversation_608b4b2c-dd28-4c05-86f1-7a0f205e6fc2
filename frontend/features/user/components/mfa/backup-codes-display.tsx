'use client';

import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { DownloadIcon } from 'lucide-react';

interface BackupCodesDisplayProps {
  codes: string[];
  title?: string;
  description?: string;
  rows?: number;
}

export function BackupCodesDisplay({
  codes,
  title = 'Backup Codes',
  description = 'Save these backup codes in a safe place. Each code can only be used once.',
  rows = 8,
}: BackupCodesDisplayProps) {
  const handleDownload = () => {
    const content = codes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="font-semibold">{title}</h3>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>
      <Textarea
        value={codes.join('\n')}
        readOnly
        className="font-mono text-sm"
        rows={rows}
      />
      <div className="flex justify-end">
        <Button
          onClick={handleDownload}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <DownloadIcon className="size-4" />
          Download as TXT
        </Button>
      </div>
      <p className="text-muted-foreground text-xs">
        Copy these codes and store them securely. You can use them to access
        your account if you lose your device.
      </p>
    </div>
  );
}
