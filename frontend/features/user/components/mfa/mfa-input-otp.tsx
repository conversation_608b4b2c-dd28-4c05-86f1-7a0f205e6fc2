'use client';

import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from '@/components/ui/input-otp';
import { Control, Controller, FieldPath, FieldValues } from 'react-hook-form';

interface MFAInputOTPProps<T extends FieldValues> {
  control: Control<T>;
  name: FieldPath<T>;
  label?: string;
  disabled?: boolean;
  required?: boolean;
}

export function MFAInputOTP<T extends FieldValues>({
  control,
  name,
  label = 'Authentication Code',
  disabled = false,
  required = true,
}: MFAInputOTPProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <FormItem>
          <FormLabel required={required}>{label}</FormLabel>
          <FormControl>
            <InputOTP
              maxLength={6}
              value={field.value || ''}
              disabled={disabled}
              containerClassName="justify-center"
              onChange={(val) => {
                const numeric = (val ?? '').replace(/\D/g, '').slice(0, 6);
                field.onChange(numeric);
              }}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
