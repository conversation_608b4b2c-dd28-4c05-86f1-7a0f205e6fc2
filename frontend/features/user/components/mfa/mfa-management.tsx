'use client';

import { useState } from 'react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  BackupCodesDisplay,
  PasswordConfirmationDialog,
} from '@/features/user/components/mfa';
import { SchemaMfaStatusResponse } from '@/openapi-ts/gens';

import { mfaQuery } from '../../hooks/mfa.query';

interface MFAManagementProps {
  mfaStatus: SchemaMfaStatusResponse;
  onStatusChange: () => void;
}

export function MFAManagement({
  mfaStatus,
  onStatusChange,
}: MFAManagementProps) {
  const [newBackupCodes, setNewBackupCodes] = useState<string[] | null>(null);

  // Use the new MFA mutation hooks
  const disableMutation = mfaQuery.mutation.useDisable();
  const regenerateMutation = mfaQuery.mutation.useRegenerateBackupCodes();

  const handleDisableMFA = async (password: string) => {
    try {
      await disableMutation.mutateAsync({ password });
      onStatusChange();
    } catch (error) {
      // Error handling is done by the mutation hook
      console.error('Disable MFA error:', error);
    }
  };

  const handleRegenerateBackupCodes = async (password: string) => {
    try {
      const result = await regenerateMutation.mutateAsync({ password });
      setNewBackupCodes(result.backup_codes);
      onStatusChange();
    } catch (error) {
      // Error handling is done by the mutation hook
      console.error('Regenerate backup codes error:', error);
    }
  };

  const handleCloseNewCodes = () => {
    setNewBackupCodes(null);
  };

  if (mfaStatus.mfa_enabled) {
    return (
      <>
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Multi-Factor Authentication</CardTitle>
            <CardDescription>
              Your account is protected with two-factor authentication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Status</span>
                <span className="text-sm font-medium text-green-600">
                  Enabled
                </span>
              </div>

              <div className="bg-muted flex items-center justify-between rounded-lg p-3">
                <div>
                  <p className="font-medium">Backup Codes</p>
                  <p className="text-muted-foreground text-sm">
                    {mfaStatus.backup_codes_remaining} codes remaining
                  </p>
                </div>
                <PasswordConfirmationDialog
                  trigger={
                    <Button variant="outline" size="sm">
                      Regenerate
                    </Button>
                  }
                  title="Regenerate Backup Codes"
                  description="This will generate new backup codes. Your existing codes will no longer work."
                  confirmButtonText="Regenerate Codes"
                  onConfirm={handleRegenerateBackupCodes}
                  isLoading={regenerateMutation.isPending}
                />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <PasswordConfirmationDialog
              trigger={
                <Button variant="destructive" className="w-full">
                  Disable Multi-Factor Authentication
                </Button>
              }
              title="Disable Multi-Factor Authentication"
              description="This will remove two-factor authentication from your account. Your account will only be protected by your password."
              confirmButtonText="Disable MFA"
              onConfirm={handleDisableMFA}
              isLoading={disableMutation.isPending}
              variant="destructive"
            />
          </CardFooter>
        </Card>

        {/* New Backup Codes Dialog */}
        <Dialog open={!!newBackupCodes} onOpenChange={handleCloseNewCodes}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>New Backup Codes</DialogTitle>
              <DialogDescription>
                Save these backup codes in a safe place. Each code can only be
                used once.
              </DialogDescription>
            </DialogHeader>
            <BackupCodesDisplay
              codes={newBackupCodes || []}
              title="New Backup Codes"
            />
            <DialogFooter>
              <Button onClick={handleCloseNewCodes}>
                I&apos;ve Saved These Codes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return null;
}
