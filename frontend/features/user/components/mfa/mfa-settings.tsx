'use client';

import { useState } from 'react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertTriangleIcon, XCircleIcon } from 'lucide-react';

import { mfaQuery } from '../../hooks/mfa.query';
import { MFAManagement } from './mfa-management';
import { MFASetup } from './mfa-setup';

export function MFASettings() {
  const [showSetup, setShowSetup] = useState(false);

  // Use the new MFA query hook
  const { data: mfaStatus, isLoading: loading } = mfaQuery.query.useStatus();

  const handleSetupComplete = () => {
    setShowSetup(false);
    // Query will automatically refetch due to cache invalidation
  };

  const handleStatusChange = () => {
    // Query will automatically refetch due to cache invalidation
  };

  if (loading) {
    return (
      <Card className="max-w-md">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-muted-foreground text-sm">
            Loading security settings...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {showSetup ? (
        <MFASetup onSetupComplete={handleSetupComplete} />
      ) : (
        <>
          {mfaStatus && (
            <MFAManagement
              mfaStatus={mfaStatus}
              onStatusChange={handleStatusChange}
            />
          )}

          {!mfaStatus?.mfa_enabled && (
            <Card className="max-w-md">
              <CardHeader>
                <CardTitle>Enable Multi-Factor Authentication</CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account with two-factor
                  authentication using an authenticator app.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowSetup(true)}>
                  Setup Multi-Factor Authentication
                </Button>
              </CardContent>
            </Card>
          )}

          <Card className="max-w-md">
            <CardHeader>
              <CardTitle>Security Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!mfaStatus?.mfa_enabled && (
                <Alert variant="warning">
                  <AlertTriangleIcon />
                  <AlertTitle>Enable Two-Factor Authentication</AlertTitle>
                  <AlertDescription>
                    Add an extra layer of security to your account by enabling
                    multi-factor authentication.
                  </AlertDescription>
                </Alert>
              )}

              {mfaStatus?.mfa_enabled &&
                mfaStatus.backup_codes_remaining === 0 && (
                  <Alert variant="destructive">
                    <XCircleIcon />
                    <AlertTitle>No Backup Codes Remaining</AlertTitle>
                    <AlertDescription>
                      You have no backup codes remaining. Generate new ones to
                      ensure you can access your account if you lose your
                      device.
                    </AlertDescription>
                  </Alert>
                )}

              {mfaStatus?.mfa_enabled &&
                mfaStatus.backup_codes_remaining !== undefined &&
                mfaStatus.backup_codes_remaining <= 3 &&
                mfaStatus.backup_codes_remaining > 0 && (
                  <Alert variant="warning">
                    <AlertTriangleIcon />
                    <AlertTitle>Low Backup Codes</AlertTitle>
                    <AlertDescription>
                      You only have {mfaStatus.backup_codes_remaining} backup
                      code{mfaStatus.backup_codes_remaining === 1 ? '' : 's'}{' '}
                      remaining. Consider generating new ones.
                    </AlertDescription>
                  </Alert>
                )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
