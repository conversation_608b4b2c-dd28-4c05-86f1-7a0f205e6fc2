'use client';

import { useState } from 'react';

import Image from 'next/image';

import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/password-input';
import {
  BackupCodesDisplay,
  MFAInputOTP,
} from '@/features/user/components/mfa';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { mfaQuery } from '../../hooks/mfa.query';

interface SchemaMfaSetupResponse {
  qr_code_data_uri: string;
  secret: string;
  backup_codes: string[];
}

const _setupSchema = z.object({
  password: z.string().min(8, { message: 'Password is required' }),
});

const _verifySchema = z.object({
  code: z.string().length(6, { message: 'MFA code must be 6 digits' }),
});

interface MFASetupProps {
  onSetupComplete: () => void;
}

export function MFASetup({ onSetupComplete }: MFASetupProps) {
  const [step, setStep] = useState<'password' | 'setup' | 'verify'>('password');
  const [setupData, setSetupData] = useState<SchemaMfaSetupResponse | null>(
    null,
  );

  // Use the new MFA mutation hooks
  const setupMutation = mfaQuery.mutation.useSetup();
  const verifySetupMutation = mfaQuery.mutation.useVerifySetup();

  const passwordForm = useForm<z.infer<typeof _setupSchema>>({
    defaultValues: {
      password: '',
    },
  });

  const verifyForm = useForm<z.infer<typeof _verifySchema>>({
    defaultValues: {
      code: '',
    },
  });

  const handlePasswordSubmit = async (data: z.infer<typeof _setupSchema>) => {
    try {
      const result = await setupMutation.mutateAsync({
        password: data.password,
      });
      setSetupData(result);
      setStep('setup');
    } catch (error) {
      // Error handling is done by the mutation hook
      console.error('MFA setup error:', error);
    }
  };

  const handleSetupNext = () => {
    setStep('verify');
  };

  const handleVerifySubmit = async (data: z.infer<typeof _verifySchema>) => {
    try {
      await verifySetupMutation.mutateAsync({ code: data.code });
      onSetupComplete();
    } catch (error) {
      // Error handling is done by the mutation hook
      console.error('MFA verification error:', error);
    }
  };

  const handleBack = () => {
    if (step === 'verify') {
      setStep('setup');
    } else if (step === 'setup') {
      setStep('password');
    }
  };

  if (step === 'password') {
    return (
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle>Setup Multi-Factor Authentication</CardTitle>
          <CardDescription>
            Add an extra layer of security to your account by enabling
            two-factor authentication.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...passwordForm}>
            <form
              onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)}
              className="space-y-4"
            >
              <FormField
                control={passwordForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Current Password</FormLabel>
                    <FormControl>
                      <PasswordInput
                        {...field}
                        disabled={setupMutation.isPending}
                        placeholder="Enter your current password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button
                disabled={setupMutation.isPending}
                className="w-full"
                type="submit"
              >
                {setupMutation.isPending && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Continue
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  if (step === 'setup' && setupData) {
    return (
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle>Scan QR Code</CardTitle>
          <CardDescription>
            Scan this QR code with your authenticator app (Google Authenticator,
            Authy, etc.)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center">
            <Image
              src={setupData.qr_code_data_uri}
              alt="MFA QR Code"
              className="rounded-lg border"
              width={200}
              height={200}
            />
          </div>

          <div className="text-center">
            <p className="text-muted-foreground mb-2 text-sm">
              Can&apos;t scan the code?
            </p>
            <p className="bg-muted rounded p-2 font-mono text-xs">
              {setupData.secret}
            </p>
          </div>

          <BackupCodesDisplay codes={setupData.backup_codes} rows={5} />

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={setupMutation.isPending}
            >
              Back
            </Button>
            <Button onClick={handleSetupNext} className="flex-1">
              I&apos;ve Scanned the QR Code
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'verify') {
    return (
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle>Verify Setup</CardTitle>
          <CardDescription>
            Enter the 6-digit code from your authenticator app to complete the
            setup.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...verifyForm}>
            <form
              onSubmit={verifyForm.handleSubmit(handleVerifySubmit)}
              className="space-y-4"
            >
              <MFAInputOTP
                control={verifyForm.control}
                name="code"
                disabled={verifySetupMutation.isPending}
              />
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handleBack}
                  disabled={verifySetupMutation.isPending}
                >
                  Back
                </Button>
                <Button
                  disabled={
                    verifySetupMutation.isPending ||
                    verifyForm.watch('code').length !== 6
                  }
                  className="flex-1"
                  type="submit"
                >
                  {verifySetupMutation.isPending && (
                    <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Verify & Enable MFA
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  }

  return null;
}
