import { createQueryKeys } from '@lukemorales/query-key-factory';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Types are available from the API service if needed
import { mfaApi } from '../services/mfaService';

// Query keys for MFA operations
const mfaQueryKeys = createQueryKeys('mfa', {
  status: {
    queryKey: null,
    queryFn: mfaApi.getStatus,
  },
});

// Query hook for getting MFA status
const useStatus = () => {
  return useQuery(mfaQueryKeys.status);
};

// Mutation hook for setting up MFA
const useSetup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mfaApi.setup,
    onSuccess: (_data) => {
      toast.success('MFA setup initiated successfully');
      queryClient.invalidateQueries({ queryKey: mfaQueryKeys.status.queryKey });
    },
  });
};

// Mutation hook for verifying MFA setup
const useVerifySetup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mfaApi.verifySetup,
    onSuccess: () => {
      toast.success('MFA enabled successfully!');
      queryClient.invalidateQueries({ queryKey: mfaQueryKeys.status.queryKey });
    },
  });
};

// Mutation hook for disabling MFA
const useDisable = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mfaApi.disable,
    onSuccess: () => {
      toast.success('MFA disabled successfully');
      queryClient.invalidateQueries({ queryKey: mfaQueryKeys.status.queryKey });
    },
  });
};

// Mutation hook for regenerating backup codes
const useRegenerateBackupCodes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: mfaApi.regenerateBackupCodes,
    onSuccess: (_data) => {
      toast.success('Backup codes regenerated successfully');
      queryClient.invalidateQueries({ queryKey: mfaQueryKeys.status.queryKey });
    },
  });
};

// Mutation hook for verifying MFA during login
const useVerifyLogin = () => {
  return useMutation({
    mutationFn: mfaApi.verifyLogin,
    onSuccess: () => {
      toast.success('Successfully logged in with MFA');
    },
  });
};

// Export all MFA query hooks organized by type
export const mfaQuery = {
  query: {
    useStatus,
  },
  mutation: {
    useSetup,
    useVerifySetup,
    useDisable,
    useRegenerateBackupCodes,
    useVerifyLogin,
  },
};
