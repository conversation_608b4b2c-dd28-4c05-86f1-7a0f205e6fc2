import {
  SchemaMfaLoginRequest,
  SchemaMfaSetupRequest,
  SchemaMfaVerifyRequest,
} from '@/openapi-ts/gens';
import { api, fetchData } from '@/openapi-ts/openapi-fetch';

// MFA API service following the established pattern
export const mfaApi = {
  // Get current MFA status
  getStatus: () => fetchData(api.GET('/api/v1/mfa/status')),

  // Setup MFA for the first time
  setup: (body: SchemaMfaSetupRequest) =>
    fetchData(api.POST('/api/v1/mfa/setup', { body })),

  // Verify and enable MFA after setup
  verifySetup: (body: SchemaMfaVerifyRequest) =>
    fetchData(api.POST('/api/v1/mfa/verify-setup', { body })),

  // Disable MFA
  disable: (body: SchemaMfaSetupRequest) =>
    fetchData(api.POST('/api/v1/mfa/disable', { body })),

  // Regenerate backup codes
  regenerateBackupCodes: (body: SchemaMfaSetupRequest) =>
    fetchData(api.POST('/api/v1/mfa/regenerate-backup-codes', { body })),

  // Verify MFA during login
  verifyLogin: (body: SchemaMfaLoginRequest) =>
    fetchData(api.POST('/api/v1/login/mfa-verify', { body })),
};
