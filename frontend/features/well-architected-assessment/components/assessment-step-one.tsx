'use client';

import { useState } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowRightIcon } from 'lucide-react';
import { SubmitHandler, useForm } from 'react-hook-form';

import {
  WELL_ARCHITECTED_CONFIG,
  WellArchitectedPillar,
} from '../config/well-architected-pillars.config';
import {
  SelectPillarsSchema,
  selectPillarsSchema,
} from '../schema/well-architected-assessment.schema';
import { PillarCard } from './pillar-card';

export function AssessmentStepOne() {
  const form = useForm<SelectPillarsSchema>({
    resolver: zodResolver(selectPillarsSchema),
    defaultValues: {
      selectedPillars: ['cost-optimization', 'security'],
    },
  });

  const { goToNextStep } = useStepContext();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { control, handleSubmit, watch, setValue } = form;
  const selectedPillars = watch('selectedPillars') || [];

  const togglePillar = (pillarId: WellArchitectedPillar) => {
    const current = selectedPillars;
    const isSelected = current.includes(pillarId);

    if (isSelected) {
      setValue(
        'selectedPillars',
        current.filter((id) => id !== pillarId),
      );
    } else {
      setValue('selectedPillars', [...current, pillarId]);
    }
  };

  const onSubmit: SubmitHandler<SelectPillarsSchema> = async (data) => {
    setIsSubmitting(true);

    // Simulate API call or store data
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Store selected pillars in session storage or context
    sessionStorage.setItem(
      'well-architected-pillars',
      JSON.stringify(data.selectedPillars),
    );

    setIsSubmitting(false);
    goToNextStep();
  };

  const totalTasks = selectedPillars.reduce((total, pillarId) => {
    const pillar = WELL_ARCHITECTED_CONFIG.getPillarById(pillarId);
    return total + (pillar?.tasks.length || 0);
  }, 0);

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Select Pillars</CardTitle>
            <CardDescription>
              Choose which Well-Architected pillars you want to include in your
              assessment
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={control}
              name="selectedPillars"
              render={() => (
                <FormItem>
                  <FormLabel>Available Pillars</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                      {WELL_ARCHITECTED_CONFIG.PILLARS.map((pillar) => (
                        <PillarCard
                          key={pillar.id}
                          pillar={pillar}
                          selected={selectedPillars.includes(pillar.id)}
                          onClick={() => togglePillar(pillar.id)}
                        />
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <If condition={selectedPillars.length > 0}>
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="space-y-1">
                      <p className="font-medium">Assessment Summary</p>
                      <p className="text-muted-foreground">
                        {selectedPillars.length} pillar
                        {selectedPillars.length !== 1 ? 's' : ''} • {totalTasks}{' '}
                        task{totalTasks !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </If>
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <div></div>
          <Button
            type="submit"
            className="gap-2"
            disabled={isSubmitting || selectedPillars.length === 0}
          >
            <UpdateWrapper isPending={isSubmitting}>Select Tasks</UpdateWrapper>
            <ArrowRightIcon className="size-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
