'use client';

import { useEffect, useState } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import {
  ArrowLeftIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon,
  PlayIcon,
  SquareIcon,
} from 'lucide-react';

import {
  WELL_ARCHITECTED_CONFIG,
  WellArchitectedPillar,
  WellArchitectedTask,
} from '../config/well-architected-pillars.config';

type AssessmentProgress = {
  currentTask: string | null;
  completedTasks: string[];
  isRunning: boolean;
  progress: number;
};

export function AssessmentStepThree() {
  const { goToNextStep, goToPrevStep } = useStepContext();
  const [selectedPillars, setSelectedPillars] = useState<
    WellArchitectedPillar[]
  >([]);
  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);
  const [assessmentProgress, setAssessmentProgress] =
    useState<AssessmentProgress>({
      currentTask: null,
      completedTasks: [],
      isRunning: false,
      progress: 0,
    });

  // Load data from previous steps
  useEffect(() => {
    const storedPillars = sessionStorage.getItem('well-architected-pillars');
    const storedTasks = sessionStorage.getItem('well-architected-tasks');

    if (storedPillars) {
      try {
        setSelectedPillars(JSON.parse(storedPillars));
      } catch (error) {
        console.error('Failed to load selected pillars:', error);
      }
    }

    if (storedTasks) {
      try {
        setSelectedTasks(JSON.parse(storedTasks));
      } catch (error) {
        console.error('Failed to load selected tasks:', error);
      }
    }
  }, []);

  const allTasks = selectedTasks
    .map((taskId) => WELL_ARCHITECTED_CONFIG.getTaskById(taskId))
    .filter(Boolean) as WellArchitectedTask[];

  const startAssessment = async () => {
    setAssessmentProgress((prev) => ({
      ...prev,
      isRunning: true,
      progress: 0,
    }));

    for (let i = 0; i < allTasks.length; i++) {
      const task = allTasks[i];

      // Set current task
      setAssessmentProgress((prev) => ({
        ...prev,
        currentTask: task.id,
        progress: (i / allTasks.length) * 100,
      }));

      // Simulate task execution time
      await new Promise((resolve) =>
        setTimeout(resolve, 2000 + Math.random() * 1000),
      );

      // Mark task as completed
      setAssessmentProgress((prev) => ({
        ...prev,
        completedTasks: [...prev.completedTasks, task.id],
        currentTask: i === allTasks.length - 1 ? null : prev.currentTask,
        progress: ((i + 1) / allTasks.length) * 100,
      }));
    }

    // Assessment completed
    setAssessmentProgress((prev) => ({
      ...prev,
      isRunning: false,
      currentTask: null,
    }));

    // Store completion status
    sessionStorage.setItem('well-architected-completed', 'true');
  };

  const stopAssessment = () => {
    setAssessmentProgress((prev) => ({
      ...prev,
      isRunning: false,
      currentTask: null,
    }));
  };

  const isCompleted =
    assessmentProgress.progress === 100 && !assessmentProgress.isRunning;
  const canProceed = isCompleted;

  const getTaskStatus = (taskId: string) => {
    if (assessmentProgress.completedTasks.includes(taskId)) return 'completed';
    if (assessmentProgress.currentTask === taskId) return 'running';
    return 'pending';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Run Assessment</CardTitle>
          <CardDescription>
            Execute selected tasks and analyze your AWS architecture
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Assessment Summary */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card className="bg-muted/50">
              <CardContent className="p-4 text-center">
                <p className="text-2xl font-bold">{selectedPillars.length}</p>
                <p className="text-muted-foreground text-sm">
                  Pillar{selectedPillars.length !== 1 ? 's' : ''}
                </p>
              </CardContent>
            </Card>
            <Card className="bg-muted/50">
              <CardContent className="p-4 text-center">
                <p className="text-2xl font-bold">{allTasks.length}</p>
                <p className="text-muted-foreground text-sm">
                  Task{allTasks.length !== 1 ? 's' : ''}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Assessment Progress */}
          <If condition={assessmentProgress.isRunning || isCompleted}>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Assessment Progress</h3>
                <span className="text-muted-foreground text-sm">
                  {Math.round(assessmentProgress.progress)}%
                </span>
              </div>
              <Progress value={assessmentProgress.progress} className="h-2" />

              <If condition={assessmentProgress.currentTask}>
                {(currentTaskId) => {
                  const currentTask =
                    WELL_ARCHITECTED_CONFIG.getTaskById(currentTaskId);
                  return currentTask ? (
                    <div className="flex items-center gap-2 text-sm">
                      <ClockIcon className="size-4 animate-pulse text-blue-500" />
                      <span>Running: {currentTask.title}</span>
                    </div>
                  ) : null;
                }}
              </If>

              <If condition={isCompleted}>
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <CheckCircleIcon className="size-4" />
                  <span>Assessment completed successfully!</span>
                </div>
              </If>
            </div>
          </If>

          {/* Task List */}
          <div className="space-y-4">
            <h3 className="font-medium">Tasks to Execute</h3>
            {selectedPillars.map((pillarId) => {
              const pillar = WELL_ARCHITECTED_CONFIG.getPillarById(pillarId);
              if (!pillar) return null;

              const pillarTasks = pillar.tasks.filter((task) =>
                selectedTasks.includes(task.id),
              );
              if (pillarTasks.length === 0) return null;

              const Icon = pillar.icon;

              return (
                <div key={pillar.id} className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="bg-muted flex size-6 items-center justify-center rounded">
                      <Icon className="size-3" />
                    </div>
                    <span className="text-sm font-medium">{pillar.title}</span>
                  </div>

                  <div className="ml-9 space-y-2">
                    {pillarTasks.map((task) => {
                      const status = getTaskStatus(task.id);
                      return (
                        <div
                          key={task.id}
                          className={cn(
                            'flex items-center justify-between rounded-lg border p-3',
                            status === 'completed' &&
                              'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/10',
                            status === 'running' &&
                              'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/10',
                            status === 'pending' && 'bg-muted/30',
                          )}
                        >
                          <div className="flex items-center gap-3">
                            <div
                              className={cn(
                                'flex size-4 items-center justify-center rounded-full',
                                status === 'completed' && 'bg-green-500',
                                status === 'running' &&
                                  'animate-pulse bg-blue-500',
                                status === 'pending' && 'bg-muted-foreground',
                              )}
                            >
                              <If condition={status === 'completed'}>
                                <CheckCircleIcon className="size-3 text-white" />
                              </If>
                            </div>
                            <div>
                              <p className="text-sm font-medium">
                                {task.title}
                              </p>
                            </div>
                          </div>
                          <div
                            className={cn(
                              'rounded px-2 py-1 text-xs',
                              status === 'completed' &&
                                'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
                              status === 'running' &&
                                'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
                              status === 'pending' &&
                                'bg-muted text-muted-foreground',
                            )}
                          >
                            {status === 'completed' && 'Completed'}
                            {status === 'running' && 'Running'}
                            {status === 'pending' && 'Pending'}
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <Separator />
                </div>
              );
            })}
          </div>

          {/* Control Buttons */}
          <div className="flex justify-center">
            <If condition={!assessmentProgress.isRunning && !isCompleted}>
              <Button onClick={startAssessment} className="gap-2">
                <PlayIcon className="size-4" />
                Start Assessment
              </Button>
            </If>

            <If condition={assessmentProgress.isRunning}>
              <Button
                onClick={stopAssessment}
                variant="destructive"
                className="gap-2"
              >
                <SquareIcon className="size-4" />
                Stop Assessment
              </Button>
            </If>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button
          type="button"
          variant="outline"
          onClick={goToPrevStep}
          className="gap-2"
        >
          <ArrowLeftIcon className="size-4" />
          Previous
        </Button>
        <Button onClick={goToNextStep} className="gap-2" disabled={!canProceed}>
          View Results
          <ArrowRightIcon className="size-4" />
        </Button>
      </div>
    </div>
  );
}
