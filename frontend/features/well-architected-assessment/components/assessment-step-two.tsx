'use client';

import { useEffect, useState } from 'react';

import { useStepContext } from '@/components/providers/step-provider';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { ArrowLeftIcon, ArrowRightIcon } from 'lucide-react';
import { SubmitHandler, useForm } from 'react-hook-form';

import {
  WELL_ARCHITECTED_CONFIG,
  WellArchitectedPillar,
} from '../config/well-architected-pillars.config';
import {
  SelectTasksSchema,
  selectTasksSchema,
} from '../schema/well-architected-assessment.schema';
import { TaskCard } from './task-card';

export function AssessmentStepTwo() {
  const form = useForm<SelectTasksSchema>({
    resolver: zodResolver(selectTasksSchema),
    defaultValues: {
      selectedTasks: [],
    },
  });

  const { goToNextStep, goToPrevStep } = useStepContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPillars, setSelectedPillars] = useState<
    WellArchitectedPillar[]
  >([]);

  const { control, handleSubmit, watch, setValue } = form;
  const selectedTasks = watch('selectedTasks') || [];

  // Load selected pillars from previous step
  useEffect(() => {
    const stored = sessionStorage.getItem('well-architected-pillars');
    if (stored) {
      try {
        const pillars = JSON.parse(stored) as WellArchitectedPillar[];
        setSelectedPillars(pillars);
      } catch (error) {
        console.error('Failed to load selected pillars:', error);
      }
    }
  }, []);

  const toggleTask = (taskId: string) => {
    const current = selectedTasks;
    const isSelected = current.includes(taskId);

    if (isSelected) {
      setValue(
        'selectedTasks',
        current.filter((id) => id !== taskId),
      );
    } else {
      setValue('selectedTasks', [...current, taskId]);
    }
  };

  const selectAllTasksForPillar = (pillarId: WellArchitectedPillar) => {
    const pillar = WELL_ARCHITECTED_CONFIG.getPillarById(pillarId);
    if (!pillar) return;

    const pillarTaskIds = pillar.tasks.map((task) => task.id);
    const currentTasks = selectedTasks;
    const allPillarTasksSelected = pillarTaskIds.every((id) =>
      currentTasks.includes(id),
    );

    if (allPillarTasksSelected) {
      // Deselect all tasks for this pillar
      setValue(
        'selectedTasks',
        currentTasks.filter((id) => !pillarTaskIds.includes(id)),
      );
    } else {
      // Select all tasks for this pillar
      const newTasks = [...new Set([...currentTasks, ...pillarTaskIds])];
      setValue('selectedTasks', newTasks);
    }
  };

  const onSubmit: SubmitHandler<SelectTasksSchema> = async (data) => {
    setIsSubmitting(true);

    // Store selected tasks
    sessionStorage.setItem(
      'well-architected-tasks',
      JSON.stringify(data.selectedTasks),
    );

    await new Promise((resolve) => setTimeout(resolve, 500));

    setIsSubmitting(false);
    goToNextStep();
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Select Tasks</CardTitle>
            <CardDescription>
              Choose specific tasks within each selected pillar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormField
              control={control}
              name="selectedTasks"
              render={() => (
                <FormItem>
                  <FormLabel>Available Tasks</FormLabel>
                  <FormControl>
                    <div className="space-y-6">
                      {selectedPillars.map((pillarId) => {
                        const pillar =
                          WELL_ARCHITECTED_CONFIG.getPillarById(pillarId);
                        if (!pillar) return null;

                        const Icon = pillar.icon;
                        const pillarTaskIds = pillar.tasks.map(
                          (task) => task.id,
                        );
                        const selectedPillarTasks = selectedTasks.filter((id) =>
                          pillarTaskIds.includes(id),
                        );
                        const allSelected = pillarTaskIds.every((id) =>
                          selectedTasks.includes(id),
                        );

                        return (
                          <div key={pillar.id} className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className="bg-muted flex size-8 items-center justify-center rounded-lg">
                                  <Icon className="size-4" />
                                </div>
                                <div>
                                  <h3 className="font-medium">
                                    {pillar.title}
                                  </h3>
                                  <p className="text-muted-foreground text-sm">
                                    {selectedPillarTasks.length} of{' '}
                                    {pillar.tasks.length} tasks selected
                                  </p>
                                </div>
                              </div>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  selectAllTasksForPillar(pillar.id)
                                }
                              >
                                {allSelected ? 'Deselect All' : 'Select All'}
                              </Button>
                            </div>

                            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                              {pillar.tasks.map((task) => (
                                <TaskCard
                                  key={task.id}
                                  task={task}
                                  checked={selectedTasks.includes(task.id)}
                                  onCheckedChange={() => toggleTask(task.id)}
                                />
                              ))}
                            </div>

                            <Separator />
                          </div>
                        );
                      })}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <If condition={selectedTasks.length > 0}>
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="space-y-1">
                      <p className="font-medium">Selected Tasks</p>
                      <p className="text-muted-foreground">
                        {selectedTasks.length} task
                        {selectedTasks.length !== 1 ? 's' : ''} selected
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </If>
          </CardContent>
        </Card>

        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={goToPrevStep}
            className="gap-2"
          >
            <ArrowLeftIcon className="size-4" />
            Previous
          </Button>
          <Button
            type="submit"
            className="gap-2"
            disabled={isSubmitting || selectedTasks.length === 0}
          >
            <UpdateWrapper isPending={isSubmitting}>
              Run Assessment
            </UpdateWrapper>
            <ArrowRightIcon className="size-4" />
          </Button>
        </div>
      </form>
    </Form>
  );
}
