'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

import { WellArchitectedPillarConfig } from '../config/well-architected-pillars.config';

interface PillarCardProps {
  pillar: WellArchitectedPillarConfig;
  selected?: boolean;
  disabled?: boolean;
  onClick?: () => void;
}

export function PillarCard({
  pillar,
  selected = false,
  disabled = false,
  onClick,
}: PillarCardProps) {
  const Icon = pillar.icon;

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-md',
        selected && 'ring-primary border-primary ring-2',
        disabled && 'cursor-not-allowed opacity-50',
        !disabled && !selected && 'hover:border-muted-foreground/50',
      )}
      onClick={disabled ? undefined : onClick}
    >
      <CardContent className="relative p-4">
        <Badge variant="secondary" className="absolute top-2 right-2 text-xs">
          {pillar.tasks.length} tasks
        </Badge>
        <div className="flex items-center gap-3 pr-8">
          <Icon className={cn('size-5 flex-shrink-0', pillar.color)} />
          <div className="min-w-0 flex-1">
            <h3 className="truncate text-sm font-medium">{pillar.title}</h3>
            <p className="text-muted-foreground mt-1 line-clamp-2 text-xs">
              {pillar.description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
