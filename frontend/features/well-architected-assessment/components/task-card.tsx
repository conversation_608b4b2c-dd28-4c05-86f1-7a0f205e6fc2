'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';

import { WellArchitectedTask } from '../config/well-architected-pillars.config';

interface TaskCardProps {
  task: WellArchitectedTask;
  checked?: boolean;
  disabled?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}

export function TaskCard({
  task,
  checked = false,
  disabled = false,
  onCheckedChange,
}: TaskCardProps) {
  const priorityColors = {
    high: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    medium:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    low: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
  };

  const handleCardClick = () => {
    if (!disabled) {
      onCheckedChange?.(!checked);
    }
  };

  const handleCheckboxChange = (value: boolean) => {
    onCheckedChange?.(value);
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <Card
      className={cn(
        'cursor-pointer transition-all duration-200 hover:shadow-md',
        checked && 'ring-primary border-primary ring-2',
        disabled && 'cursor-not-allowed opacity-50',
        !disabled && !checked && 'hover:border-muted-foreground/50',
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3 pr-1">
          <div onClick={handleCheckboxClick}>
            <Checkbox
              checked={checked}
              disabled={disabled}
              onCheckedChange={handleCheckboxChange}
              className="mt-0.5"
            />
          </div>
          <div className="min-w-0 flex-1 space-y-2">
            <div className="flex items-start justify-between gap-2">
              <h4 className="truncate text-sm leading-none font-medium">
                {task.title}
              </h4>
              <Badge
                variant="secondary"
                className={cn('text-xs', priorityColors[task.priority])}
              >
                {task.priority}
              </Badge>
            </div>
            <p className="text-muted-foreground mt-1 line-clamp-2 text-xs">
              {task.description}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
