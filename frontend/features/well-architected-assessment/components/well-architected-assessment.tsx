'use client';

import {
  useStepContext,
  withStepProvider,
} from '@/components/providers/step-provider';
import {
  Stepper,
  StepperContent,
  StepperDescription,
  StepperIndicator,
  StepperItem,
  StepperNav,
  StepperPanel,
  StepperSeparator,
  <PERSON>per<PERSON><PERSON>le,
  StepperTrigger,
} from '@/components/ui/stepper';
import {
  Check,
  ClipboardListIcon,
  LoaderCircleIcon,
  PlayIcon,
  SettingsIcon,
} from 'lucide-react';

import { AssessmentStepOne } from './assessment-step-one';
import { AssessmentStepThree } from './assessment-step-three';
import { AssessmentStepTwo } from './assessment-step-two';

const ASSESSMENT_STEPS = [
  {
    step: 1,
    title: 'Select Pillars',
    description: 'Configure assessment scope and pillar priorities',
    icon: SettingsIcon,
    component: AssessmentStepOne,
  },
  {
    step: 2,
    title: 'Select Tasks',
    description: 'Define granular evaluation criteria and checks',
    icon: ClipboardListIcon,
    component: AssessmentStepTwo,
  },
  {
    step: 3,
    title: 'Run Assessment',
    description: 'Generate comprehensive architecture analysis report',
    icon: PlayIcon,
    component: AssessmentStepThree,
  },
];

function WellArchitectedAssessmentComponent() {
  const { currentStep } = useStepContext();

  return (
    <div className="container mx-auto max-w-4xl space-y-8 py-8">
      {/* Header */}
      <div className="space-y-2 text-center">
        <h1 className="text-3xl font-bold">Well-Architected Assessment</h1>
        <p className="text-muted-foreground">
          Evaluate your architecture against AWS best practices across all
          pillars
        </p>
      </div>

      <Stepper
        value={currentStep}
        indicators={{
          completed: <Check className="size-4" />,
          loading: <LoaderCircleIcon className="size-4 animate-spin" />,
        }}
        className="space-y-8"
      >
        <StepperNav>
          {ASSESSMENT_STEPS.map((stepConfig, index) => (
            <StepperItem
              key={stepConfig.step}
              step={stepConfig.step}
              className="relative flex-1 items-start"
            >
              <StepperTrigger className="flex flex-col gap-2.5">
                <StepperIndicator>{stepConfig.step}</StepperIndicator>
                <StepperTitle>{stepConfig.title}</StepperTitle>
                <StepperDescription className="p-2">
                  {stepConfig.description}
                </StepperDescription>
              </StepperTrigger>

              {ASSESSMENT_STEPS.length > index + 1 && (
                <StepperSeparator className="group-data-[state=completed]/step:bg-primary absolute inset-x-0 top-3 left-[calc(50%+0.875rem)] m-0 group-data-[orientation=horizontal]/stepper-nav:w-[calc(100%-2rem+0.225rem)] group-data-[orientation=horizontal]/stepper-nav:flex-none" />
              )}
            </StepperItem>
          ))}
        </StepperNav>

        <StepperPanel className="text-sm">
          {ASSESSMENT_STEPS.map((stepConfig) => {
            const StepComponent = stepConfig.component;

            return (
              <StepperContent
                key={stepConfig.step}
                value={stepConfig.step}
                className="flex items-center justify-center"
              >
                <StepComponent />
              </StepperContent>
            );
          })}
        </StepperPanel>
      </Stepper>
    </div>
  );
}

// Export with step provider HOC
export const WellArchitectedAssessment = withStepProvider(
  WellArchitectedAssessmentComponent,
  3,
);
