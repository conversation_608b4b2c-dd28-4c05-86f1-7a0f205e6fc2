import { createUtilityConfig } from '@/utils/option-config';
import {
  CheckCircleIcon,
  DollarSignIcon,
  LeafIcon,
  LockIcon,
  ShieldIcon,
  TrendingUpIcon,
} from 'lucide-react';

export interface PillarConfigItem {
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  tasks: Array<{
    id: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}

export const WELL_ARCHITECTED_PILLAR_CONFIG = createUtilityConfig({
  'cost-optimization': {
    label: 'Cost Optimization',
    description: 'Avoid or eliminate unneeded cost or suboptimal resources',
    icon: DollarSignIcon,
    color: 'text-orange-500',
    tasks: [
      {
        id: 'cost-right-sizing',
        title: 'Resource Right-sizing',
        description: 'Analyze and optimize resource sizing and utilization',
        priority: 'high',
      },
      {
        id: 'cost-allocation',
        title: 'Cost Allocation Review',
        description: 'Review cost allocation and tagging strategies',
        priority: 'medium',
      },
      {
        id: 'cost-reserved-instances',
        title: 'Reserved Instance Analysis',
        description: 'Evaluate Reserved Instance and Savings Plan usage',
        priority: 'medium',
      },
    ],
  },
  security: {
    label: 'Security',
    description:
      'Protect information, systems, and assets while delivering business value',
    icon: LockIcon,
    color: 'text-red-500',
    tasks: [
      {
        id: 'sec-iam',
        title: 'Identity and Access Management',
        description: 'Review IAM policies, roles, and access controls',
        priority: 'high',
      },
      {
        id: 'sec-data-protection',
        title: 'Data Protection Assessment',
        description: 'Evaluate data encryption and protection mechanisms',
        priority: 'high',
      },
      {
        id: 'sec-infrastructure',
        title: 'Infrastructure Protection',
        description: 'Assess network security and infrastructure controls',
        priority: 'medium',
      },
    ],
  },
  'operational-excellence': {
    label: 'Operational Excellence',
    description:
      'Run and monitor systems to deliver business value and continuously improve',
    icon: CheckCircleIcon,
    color: 'text-blue-500',
    tasks: [
      {
        id: 'ops-monitoring',
        title: 'Monitoring and Observability',
        description:
          'Evaluate monitoring, logging, and observability practices',
        priority: 'high',
      },
      {
        id: 'ops-automation',
        title: 'Automation and Deployment',
        description: 'Review automation and deployment processes',
        priority: 'medium',
      },
      {
        id: 'ops-incident-response',
        title: 'Incident Response',
        description: 'Assess incident response and recovery procedures',
        priority: 'medium',
      },
    ],
  },
  reliability: {
    label: 'Reliability',
    description:
      'Ensure a workload performs its intended function correctly and consistently',
    icon: ShieldIcon,
    color: 'text-green-500',
    tasks: [
      {
        id: 'rel-fault-tolerance',
        title: 'Fault Tolerance',
        description: 'Evaluate fault tolerance and recovery mechanisms',
        priority: 'high',
      },
      {
        id: 'rel-backup-recovery',
        title: 'Backup and Recovery',
        description: 'Review backup strategies and recovery procedures',
        priority: 'high',
      },
      {
        id: 'rel-monitoring',
        title: 'Reliability Monitoring',
        description: 'Assess monitoring and alerting for reliability',
        priority: 'medium',
      },
    ],
  },
  'performance-efficiency': {
    label: 'Performance Efficiency',
    description:
      'Use IT and computing resources efficiently to meet system requirements',
    icon: TrendingUpIcon,
    color: 'text-purple-500',
    tasks: [
      {
        id: 'perf-resource-selection',
        title: 'Resource Selection',
        description: 'Evaluate resource types and sizing decisions',
        priority: 'medium',
      },
      {
        id: 'perf-scaling',
        title: 'Scaling and Elasticity',
        description: 'Review scaling strategies and elasticity patterns',
        priority: 'high',
      },
      {
        id: 'perf-optimization',
        title: 'Performance Optimization',
        description: 'Assess performance optimization techniques',
        priority: 'medium',
      },
    ],
  },
  sustainability: {
    label: 'Sustainability',
    description: 'Minimize environmental impacts of running cloud workloads',
    icon: LeafIcon,
    color: 'text-emerald-500',
    tasks: [
      {
        id: 'sust-carbon-footprint',
        title: 'Carbon Footprint Analysis',
        description: 'Evaluate carbon footprint and sustainability metrics',
        priority: 'medium',
      },
      {
        id: 'sust-resource-efficiency',
        title: 'Resource Efficiency',
        description: 'Review resource efficiency and utilization patterns',
        priority: 'medium',
      },
      {
        id: 'sust-optimization',
        title: 'Sustainability Optimization',
        description: 'Assess sustainability optimization opportunities',
        priority: 'low',
      },
    ],
  },
} satisfies Record<string, PillarConfigItem>);
