import { WELL_ARCHITECTED_PILLAR_CONFIG } from './well-architected-pillar.config';

export type WellArchitectedPillar =
  | 'operational-excellence'
  | 'security'
  | 'reliability'
  | 'performance-efficiency'
  | 'cost-optimization'
  | 'sustainability';

export type WellArchitectedTask = {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
};

export type WellArchitectedPillarConfig = {
  id: WellArchitectedPillar;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  tasks: WellArchitectedTask[];
};

// Convert the utility config to the expected format
export const WELL_ARCHITECTED_PILLARS: WellArchitectedPillarConfig[] =
  WELL_ARCHITECTED_PILLAR_CONFIG.LIST.map((item) => ({
    id: item.value as WellArchitectedPillar,
    title: item.label,
    description: item.description,
    icon: item.icon,
    color: item.color,
    tasks: item.tasks,
  }));

export const WELL_ARCHITECTED_CONFIG = {
  PILLARS: WELL_ARCHITECTED_PILLARS,
  getPillarById: (id: WellArchitectedPillar) =>
    WELL_ARCHITECTED_PILLARS.find((pillar) => pillar.id === id),
  getTaskById: (taskId: string) =>
    WELL_ARCHITECTED_PILLARS.flatMap((pillar) => pillar.tasks).find(
      (task) => task.id === taskId,
    ),
};
