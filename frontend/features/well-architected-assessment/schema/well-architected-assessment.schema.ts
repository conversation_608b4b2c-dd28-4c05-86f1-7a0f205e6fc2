import { z } from 'zod';

import { WellArchitectedPillar } from '../config/well-architected-pillars.config';

export const selectPillarsSchema = z.object({
  selectedPillars: z
    .array(
      z.enum([
        'operational-excellence',
        'security',
        'reliability',
        'performance-efficiency',
        'cost-optimization',
        'sustainability',
      ] as const),
    )
    .min(1, 'Please select at least one pillar'),
});

export type SelectPillarsSchema = z.infer<typeof selectPillarsSchema>;

export const selectTasksSchema = z.object({
  selectedTasks: z.array(z.string()).min(1, 'Please select at least one task'),
});

export type SelectTasksSchema = z.infer<typeof selectTasksSchema>;

export const assessmentConfigSchema = z.object({
  name: z.string().min(1, 'Assessment name is required'),
  description: z.string().optional(),
  selectedPillars: z
    .array(
      z.enum([
        'operational-excellence',
        'security',
        'reliability',
        'performance-efficiency',
        'cost-optimization',
        'sustainability',
      ] as const),
    )
    .min(1, 'Please select at least one pillar'),
  selectedTasks: z.array(z.string()).min(1, 'Please select at least one task'),
});

export type AssessmentConfigSchema = z.infer<typeof assessmentConfigSchema>;

export type AssessmentStep = 1 | 2 | 3 | 4 | 5;

export type AssessmentState = {
  currentStep: AssessmentStep;
  config: Partial<AssessmentConfigSchema>;
  isRunning: boolean;
  progress: number;
  results?: AssessmentResults;
};

export type AssessmentResults = {
  id: string;
  name: string;
  pillars: PillarResult[];
  overallScore: number;
  completedAt: string;
  recommendations: Recommendation[];
};

export type PillarResult = {
  pillar: WellArchitectedPillar;
  score: number;
  tasks: TaskResult[];
};

export type TaskResult = {
  taskId: string;
  score: number;
  findings: string[];
  recommendations: string[];
};

export type Recommendation = {
  id: string;
  pillar: WellArchitectedPillar;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  estimatedEffort: string;
  potentialSavings?: string;
};
