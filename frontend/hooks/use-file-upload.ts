'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import {
  AttachmentFileInfo,
  AttachmentMetadataResponse,
  AttachmentsService,
  UploadedAttachmentInfo,
} from '@/client';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

// Constants
const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
const MAX_TOTAL_SIZE = 20 * 1024 * 1024; // 20MB
const MAX_FILES = 5; // Maximum 5 files at a time
const POLL_INTERVAL = 2000; // 2 seconds
const MAX_POLL_TIME = 60000; // 60 seconds

// Supported file types
const SUPPORTED_MIME_TYPES = new Set([
  // Images
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
  'image/bmp',
  'image/tiff',
  'image/x-icon',
  // Documents
  'application/pdf',
  'text/plain',
  'text/csv',
  'text/markdown',
  'text/rtf',
  'application/json',
  'application/xml',
  'application/yaml',
  'application/x-yaml',
  // Office documents
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
  'application/msword', // .doc
  'application/vnd.ms-excel', // .xls
  'application/vnd.ms-powerpoint', // .ppt
  'application/vnd.oasis.opendocument.text', // .odt
  'application/vnd.oasis.opendocument.spreadsheet', // .ods
  'application/vnd.oasis.opendocument.presentation', // .odp
  // Code files
  'application/javascript',
  'application/typescript',
  'text/javascript',
  'text/typescript',
  'text/html',
  'text/css',
  'text/x-python',
  'application/x-python-code',
  'text/x-sh',
  'application/x-shellscript',
  // Archives (for reference, may need special handling)
  'application/zip',
  'application/x-tar',
  'application/gzip',
  'application/x-7z-compressed',
]);

type FileUploadStatus =
  | 'idle'
  | 'validating'
  | 'uploading'
  | 'processing'
  | 'completed'
  | 'failed';

export interface UploadingFile {
  id: string;
  file: File;
  status: FileUploadStatus;
  progress: number;
  error?: string;
  attachmentId?: string;
  storageKey?: string;
  taskId?: string;
}

interface UseFileUploadResult {
  files: UploadingFile[];
  addFiles: (files: File[]) => void;
  removeFile: (fileId: string) => void;
  clearAll: () => void;
  isUploading: boolean;
  hasValidationErrors: boolean;
  completedAttachmentIds: string[];
  fetchAttachmentMetadata: (
    attachmentId: string,
  ) => Promise<AttachmentMetadataResponse>;
}

export function useFileUpload(): UseFileUploadResult {
  const [files, setFiles] = useState<UploadingFile[]>([]);
  const pollTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const abortControllersRef = useRef<Map<string, AbortController>>(new Map());

  // Cleanup function
  const cleanup = useCallback(() => {
    // Clear all timeouts
    pollTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
    pollTimeoutsRef.current.clear();

    // Abort all ongoing requests
    abortControllersRef.current.forEach((controller) => controller.abort());
    abortControllersRef.current.clear();
  }, []);

  // Cleanup on unmount or navigation
  useEffect(() => {
    const handleBeforeUnload = () => {
      cleanup();
      // Auto-fail any in-progress uploads
      setFiles((prev) =>
        prev.map((file) =>
          file.status === 'uploading' || file.status === 'processing'
            ? {
                ...file,
                status: 'failed',
                error: 'Upload cancelled due to navigation',
              }
            : file,
        ),
      );
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      cleanup();
    };
  }, [cleanup]);

  // Validate file
  const validateFile = useCallback((file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File size exceeds 25MB limit (${(file.size / 1024 / 1024).toFixed(1)}MB)`;
    }

    if (!SUPPORTED_MIME_TYPES.has(file.type)) {
      return `File type "${file.type}" is not supported`;
    }

    return null;
  }, []);

  // Poll task status
  const pollTaskStatus = useCallback(async (fileId: string, taskId: string) => {
    try {
      const response = await AttachmentsService.getAttachmentTaskStatus({
        taskId,
      });

      setFiles((prev) =>
        prev.map((file) => {
          if (file.id !== fileId) return file;

          switch (response.status) {
            case 'SUCCESS':
              return {
                ...file,
                status: 'completed',
                progress: 100,
                attachmentId: (response.result as { attachment_id?: string })
                  ?.attachment_id,
              };
            case 'FAILURE':
              return {
                ...file,
                status: 'failed',
                error: response.error || 'Validation failed',
              };
            case 'PROGRESS':
              return {
                ...file,
                status: 'processing',
                progress: response.progress || 50,
              };
            default: {
              // Continue polling for PENDING
              const timeout = setTimeout(
                () => pollTaskStatus(fileId, taskId),
                POLL_INTERVAL,
              );
              pollTimeoutsRef.current.set(fileId, timeout);
              return file;
            }
          }
        }),
      );

      // Clear timeout for completed/failed files
      if (response.status === 'SUCCESS' || response.status === 'FAILURE') {
        const timeout = pollTimeoutsRef.current.get(fileId);
        if (timeout) {
          clearTimeout(timeout);
          pollTimeoutsRef.current.delete(fileId);
        }
      }
    } catch {
      setFiles((prev) =>
        prev.map((file) =>
          file.id === fileId
            ? {
                ...file,
                status: 'failed',
                error: 'Failed to check upload status',
              }
            : file,
        ),
      );
    }
  }, []);

  // Upload single file
  const uploadFile = useCallback(
    async (uploadingFile: UploadingFile) => {
      const { id, file } = uploadingFile;

      try {
        // Step 1: Get presigned URL
        setFiles((prev) =>
          prev.map((f) =>
            f.id === id ? { ...f, status: 'uploading', progress: 10 } : f,
          ),
        );

        const fileInfo: AttachmentFileInfo = {
          file_id: id,
          filename: file.name,
          content_type: file.type,
          file_size: file.size,
        };

        const presignedResponse =
          await AttachmentsService.generateAttachmentPresignedUrls({
            requestBody: { files: [fileInfo] },
          });

        const presignedUrl = presignedResponse.presigned_urls[0];

        // Step 2: Upload to S3
        setFiles((prev) =>
          prev.map((f) =>
            f.id === id
              ? { ...f, progress: 30, storageKey: presignedUrl.storage_key }
              : f,
          ),
        );

        const controller = new AbortController();
        abortControllersRef.current.set(id, controller);

        const uploadResponse = await fetch(presignedUrl.presigned_url, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': file.type,
          },
          signal: controller.signal,
        });

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload file to storage');
        }

        // Step 3: Confirm upload and start validation
        setFiles((prev) =>
          prev.map((f) =>
            f.id === id ? { ...f, progress: 60, status: 'processing' } : f,
          ),
        );

        const uploadedFileInfo: UploadedAttachmentInfo = {
          file_id: id,
          filename: file.name,
          storage_key: presignedUrl.storage_key,
          content_type: file.type,
          file_size: file.size,
        };

        const confirmResponse =
          await AttachmentsService.confirmAttachmentUploads({
            requestBody: { uploaded_files: [uploadedFileInfo] },
          });

        // Step 4: Start polling for validation status
        setFiles((prev) =>
          prev.map((f) =>
            f.id === id ? { ...f, taskId: confirmResponse.task_id } : f,
          ),
        );

        // Start polling with timeout
        setTimeout(
          () => pollTaskStatus(id, confirmResponse.task_id),
          POLL_INTERVAL,
        );

        // Set max polling timeout
        setTimeout(() => {
          const timeout = pollTimeoutsRef.current.get(id);
          if (timeout) {
            clearTimeout(timeout);
            pollTimeoutsRef.current.delete(id);
            setFiles((prev) =>
              prev.map((f) =>
                f.id === id && f.status === 'processing'
                  ? { ...f, status: 'failed', error: 'Validation timeout' }
                  : f,
              ),
            );
          }
        }, MAX_POLL_TIME);
      } catch (error) {
        setFiles((prev) =>
          prev.map((f) =>
            f.id === id
              ? {
                  ...f,
                  status: 'failed',
                  error:
                    error instanceof Error ? error.message : 'Upload failed',
                }
              : f,
          ),
        );
      } finally {
        abortControllersRef.current.delete(id);
      }
    },
    [pollTaskStatus],
  );

  // Add files to upload queue
  const addFiles = useCallback(
    (newFiles: File[]) => {
      // Check if adding these files would exceed the maximum number of files
      if (files.length + newFiles.length > MAX_FILES) {
        toast.error(
          `You can only upload a maximum of ${MAX_FILES} files at a time.`,
        );
        return;
      }

      // Calculate total size of existing files
      const existingTotalSize = files.reduce(
        (total, file) => total + file.file.size,
        0,
      );

      // Calculate total size of new files
      const newTotalSize = newFiles.reduce(
        (total, file) => total + file.size,
        0,
      );

      // Check if adding these files would exceed the maximum total size
      if (existingTotalSize + newTotalSize > MAX_TOTAL_SIZE) {
        toast.error(
          `Total file size cannot exceed ${MAX_TOTAL_SIZE / 1024 / 1024}MB.`,
        );
        return;
      }

      const uploadingFiles: UploadingFile[] = newFiles.map((file) => {
        const id = uuidv4();
        const error = validateFile(file);

        return {
          id,
          file,
          status: error ? 'failed' : 'validating',
          progress: 0,
          error: error || undefined,
        };
      });

      setFiles((prev) => [...prev, ...uploadingFiles]);

      // Start uploading valid files individually (maintaining current behavior)
      uploadingFiles.forEach((uploadingFile) => {
        if (!uploadingFile.error) {
          // Small delay to show validating state
          setTimeout(() => uploadFile(uploadingFile), 100);
        }
      });
    },
    [files, validateFile, uploadFile],
  );

  // Remove file
  const removeFile = useCallback((fileId: string) => {
    // Cancel any ongoing operations for this file
    const timeout = pollTimeoutsRef.current.get(fileId);
    if (timeout) {
      clearTimeout(timeout);
      pollTimeoutsRef.current.delete(fileId);
    }

    const controller = abortControllersRef.current.get(fileId);
    if (controller) {
      controller.abort();
      abortControllersRef.current.delete(fileId);
    }

    setFiles((prev) => prev.filter((f) => f.id !== fileId));
  }, []);

  // Clear all files
  const clearAll = useCallback(() => {
    cleanup();
    setFiles([]);
  }, [cleanup]);

  // Derived state
  const isUploading = files.some(
    (f) =>
      f.status === 'validating' ||
      f.status === 'uploading' ||
      f.status === 'processing',
  );

  const hasValidationErrors = files.some((f) => f.status === 'failed');

  const completedAttachmentIds = files
    .filter((f) => f.status === 'completed' && f.attachmentId)
    .map((f) => f.attachmentId!);

  // Function to fetch attachment metadata
  const fetchAttachmentMetadata = useCallback(
    async (attachmentId: string): Promise<AttachmentMetadataResponse> => {
      try {
        const response = await AttachmentsService.getAttachmentMetadata({
          attachmentId,
        });
        return response;
      } catch (error) {
        console.error('Error fetching attachment metadata:', error);
        throw error;
      }
    },
    [],
  );

  return {
    files,
    addFiles,
    removeFile,
    clearAll,
    isUploading,
    hasValidationErrors,
    completedAttachmentIds,
    fetchAttachmentMetadata,
  };
}
