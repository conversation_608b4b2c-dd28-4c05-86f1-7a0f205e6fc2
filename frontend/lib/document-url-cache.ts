// lib/document-url-cache.ts
import { documentApi } from '@/features/kb/services/document.api';

interface CachedUrl {
  url: string;
  expiresAt: number; // timestamp
}

const CACHE_KEY_PREFIX = 'document_url_';
const DEFAULT_CACHE_DURATION = 50 * 60 * 1000; // 50 minutes (presigned URLs typically expire in 1 hour)

class DocumentUrlCache {
  private pendingRequests = new Map<string, Promise<string>>();

  private getCacheKey(documentId: string): string {
    return `${CACHE_KEY_PREFIX}${documentId}`;
  }

  private isExpired(cachedUrl: CachedUrl): boolean {
    return Date.now() >= cachedUrl.expiresAt;
  }

  private getCachedUrl(documentId: string): string | null {
    try {
      const cached = localStorage.getItem(this.getCacheKey(documentId));
      if (!cached) return null;

      const cachedUrl: CachedUrl = JSON.parse(cached);
      if (this.isExpired(cachedUrl)) {
        // Remove expired entry
        localStorage.removeItem(this.getCacheKey(documentId));
        return null;
      }

      return cachedUrl.url;
    } catch (error) {
      console.error('Error reading cached document URL:', error);
      return null;
    }
  }

  private setCachedUrl(documentId: string, url: string): void {
    try {
      const cachedUrl: CachedUrl = {
        url,
        expiresAt: Date.now() + DEFAULT_CACHE_DURATION,
      };
      localStorage.setItem(
        this.getCacheKey(documentId),
        JSON.stringify(cachedUrl),
      );
    } catch (error) {
      console.error('Error caching document URL:', error);
    }
  }

  async getViewUrl(kbId: string, objectName: string): Promise<string> {
    const documentId = objectName; // Use object_name as document ID for caching

    // Check if there's already a pending request for this document
    if (this.pendingRequests.has(documentId)) {
      console.log(`[DocumentCache] Using pending request for ${documentId}`);
      return this.pendingRequests.get(documentId)!;
    }

    // Try to get from cache first
    const cachedUrl = this.getCachedUrl(documentId);
    if (cachedUrl) {
      console.log(`[DocumentCache] Using cached URL for ${documentId}`);
      return cachedUrl;
    }

    // Create a promise for the backend request
    const requestPromise = this.fetchFromBackend(kbId, objectName);
    this.pendingRequests.set(documentId, requestPromise);

    try {
      const url = await requestPromise;
      return url;
    } finally {
      // Clean up the pending request
      this.pendingRequests.delete(documentId);
    }
  }

  private async fetchFromBackend(
    kbId: string,
    objectName: string,
  ): Promise<string> {
    const documentId = objectName;
    console.log(
      `[DocumentCache] Fetching new URL from backend for ${documentId}`,
    );

    // Fetch new URL from backend
    const url = await documentApi.getContent(kbId, objectName);

    // Cache the URL
    this.setCachedUrl(documentId, url);

    return url;
  }

  // Clear cache for a specific document
  clearCache(documentId: string): void {
    localStorage.removeItem(this.getCacheKey(documentId));
  }

  // Clear all cached document URLs
  clearAllCache(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach((key) => {
        if (key.startsWith(CACHE_KEY_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error clearing document URL cache:', error);
    }
  }
}

// Export a singleton instance
export const documentUrlCache = new DocumentUrlCache();
