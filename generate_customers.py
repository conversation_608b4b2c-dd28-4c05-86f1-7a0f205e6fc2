#!/usr/bin/env python3
"""
Script to generate random AWS cloud infrastructure data for a startup
Generates 500 lines of realistic cloud cost and usage information
"""

import csv
import random
from datetime import datetime, timedelta

from faker import Faker


def generate_aws_cloud_data(num_records=500):
    """Generate random AWS cloud infrastructure data for a startup"""
    fake = Faker()

    # AWS-specific data lists
    aws_services = [
        "EC2",
        "S3",
        "RDS",
        "Lambda",
        "CloudFront",
        "API Gateway",
        "DynamoDB",
        "ElastiCache",
        "ECS",
        "EKS",
        "CloudWatch",
        "SNS",
        "SQS",
        "Route 53",
        "VPC",
        "IAM",
        "CloudFormation",
        "CodePipeline",
        "CodeBuild",
        "X-Ray",
    ]

    instance_types = [
        "t3.micro",
        "t3.small",
        "t3.medium",
        "t3.large",
        "m5.large",
        "m5.xlarge",
        "c5.large",
        "c5.xlarge",
        "r5.large",
        "r5.xlarge",
        "i3.large",
        "i3.xlarge",
    ]

    aws_regions = [
        "us-east-1",
        "us-west-2",
        "us-west-1",
        "eu-west-1",
        "eu-central-1",
        "ap-southeast-1",
        "ap-northeast-1",
        "sa-east-1",
        "ca-central-1",
    ]

    startup_stages = ["Seed", "Series A", "Series B", "Series C", "Growth", "Scale-up"]

    team_sizes = ["1-5", "6-10", "11-25", "26-50", "51-100", "100+"]

    cost_optimization_status = [
        "Optimized",
        "Partially Optimized",
        "Needs Optimization",
        "Critical",
    ]

    # Generate cloud infrastructure data
    records = []

    for i in range(1, num_records + 1):
        # Generate random dates within last 12 months
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        usage_date = fake.date_between(start_date=start_date, end_date=end_date)

        # Generate realistic AWS costs based on service
        service = random.choice(aws_services)
        base_cost = get_base_cost_for_service(service)
        cost_variation = random.uniform(0.5, 2.0)
        monthly_cost = round(base_cost * cost_variation, 2)

        # Generate usage metrics
        if service == "EC2":
            instance_type = random.choice(instance_types)
            instance_count = random.randint(1, 20)
            hours_used = random.randint(720, 744)  # Monthly hours
            data_transfer_gb = random.randint(10, 1000)
        elif service == "S3":
            instance_type = ""
            instance_count = 0
            hours_used = 0
            storage_gb = random.randint(100, 10000)
            data_transfer_gb = random.randint(50, 5000)
        elif service == "RDS":
            instance_type = random.choice([t for t in instance_types if t.startswith(("m5", "r5"))])
            instance_count = random.randint(1, 5)
            hours_used = random.randint(720, 744)
            storage_gb = random.randint(100, 2000)
            data_transfer_gb = random.randint(10, 500)
        else:
            instance_type = ""
            instance_count = 0
            hours_used = 0
            storage_gb = 0
            data_transfer_gb = random.randint(1, 100)

        record = {
            "record_id": f"REC{i:06d}",
            "startup_name": fake.company(),
            "startup_stage": random.choice(startup_stages),
            "team_size": random.choice(team_sizes),
            "aws_service": service,
            "instance_type": instance_type,
            "instance_count": instance_count,
            "hours_used": hours_used,
            "storage_gb": storage_gb,
            "data_transfer_gb": data_transfer_gb,
            "monthly_cost_usd": monthly_cost,
            "aws_region": random.choice(aws_regions),
            "usage_date": usage_date.strftime("%Y-%m-%d"),
            "cost_optimization_status": random.choice(cost_optimization_status),
            "reserved_instance_coverage": random.randint(0, 100),
            "auto_scaling_enabled": random.choice([True, False]),
            "backup_enabled": random.choice([True, False]),
            "monitoring_enabled": random.choice([True, False]),
            "security_compliance": random.choice(["SOC2", "ISO27001", "HIPAA", "GDPR", "None"]),
            "funding_raised_usd": random.randint(0, 50000000),
            "monthly_revenue_usd": random.randint(0, 1000000),
            "cloud_budget_percentage": random.randint(5, 30),
            "cost_alert_threshold": random.randint(100, 1000),
            "last_cost_review": fake.date_between(
                start_date=start_date, end_date=end_date
            ).strftime("%Y-%m-%d"),
        }

        records.append(record)

    return records


def get_base_cost_for_service(service):
    """Get base monthly cost for AWS services"""
    base_costs = {
        "EC2": 150.0,
        "S3": 50.0,
        "RDS": 200.0,
        "Lambda": 30.0,
        "CloudFront": 80.0,
        "API Gateway": 40.0,
        "DynamoDB": 100.0,
        "ElastiCache": 120.0,
        "ECS": 60.0,
        "EKS": 100.0,
        "CloudWatch": 20.0,
        "SNS": 15.0,
        "SQS": 25.0,
        "Route 53": 35.0,
        "VPC": 10.0,
        "IAM": 5.0,
        "CloudFormation": 15.0,
        "CodePipeline": 45.0,
        "CodeBuild": 55.0,
        "X-Ray": 25.0,
    }
    return base_costs.get(service, 50.0)


def save_to_csv(records, filename="aws_startup_cloud_data.csv"):
    """Save AWS cloud data to CSV file"""
    if not records:
        print("No cloud data to save")
        return

    fieldnames = records[0].keys()

    with open(filename, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(records)

    print(f"Successfully generated {len(records)} AWS cloud infrastructure records")
    print(f"Data saved to: {filename}")


def main():
    """Main function to generate and save AWS cloud data"""
    print("Generating random AWS cloud infrastructure data for startup...")

    try:
        # Generate 500 records
        records = generate_aws_cloud_data(50)

        # Save to CSV
        save_to_csv(records)

        # Show sample data
        print("\nSample AWS cloud data:")
        print("=" * 60)
        for i, record in enumerate(records[:3]):
            print(f"Record {i + 1}:")
            for key, value in record.items():
                print(f"  {key}: {value}")
            print()

    except Exception as e:
        print(f"Error generating AWS cloud data: {e}")


if __name__ == "__main__":
    main()
