---
description:
globs:
alwaysApply: true
---

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup

```bash
# Install dependencies
uv sync

# Activate virtual environment
source .venv/bin/activate
```

### Code Quality & Testing

```bash
# Run tests with coverage
bash scripts/test.sh

# Lint and type check
bash scripts/lint.sh

# Format code
bash scripts/format.sh
```

### Development Server

```bash
# Run FastAPI development server with auto-reload
fastapi run --reload app/main.py

# Or using Docker
docker compose exec payment bash
fastapi run --reload app/main.py
```

### Database Operations

```bash
# Create new migration
alembic revision --autogenerate -m "Migration description"

# Apply migrations
alembic upgrade head
```

## Architecture Overview

This is a **FastAPI-based payment service** that integrates with Stripe for subscription management. The service follows a modular architecture with clear separation of concerns.

### Core Components

- **FastAPI Application** (`app/main.py`): Main application with middleware for CORS, rate limiting, and request tracking
- **API Router** (`app/api/main.py`): Centralized routing for all API endpoints
- **Models** (`app/models.py`): SQLModel-based database models for customers, subscriptions, products, and quotas
- **Services** (`app/services/`): Business logic layer including Stripe integration
- **Schemas** (`app/schemas/`): Pydantic models for request/response validation
- **Core Configuration** (`app/core/`): Settings, database, security, and shared utilities

### Key Integrations

- **Stripe Payment Processing**: Customer management, subscriptions, webhooks
- **PostgreSQL Database**: Primary data storage with SQLModel ORM
- **Redis**: Caching and background job queue management
- **Celery**: Asynchronous task processing
- **Alembic**: Database migration management
- **Sentry**: Error tracking and monitoring

### API Structure

The API is organized into domain-specific routers:

- `/customers` - Customer management and quota tracking
- `/products` - Stripe product and pricing information
- `/subscriptions` - Subscription lifecycle management
- `/webhooks` - Stripe webhook handling
- `/enterprise` - Enterprise customer enquiries
- `/plan-change` - Subscription plan modifications
- `/health-check` - Service health monitoring

### Database Schema

Key entities include:

- **Customer**: Links users to Stripe customers with quota tracking
- **Subscription**: Manages subscription lifecycle and status
- **Product**: Stripe product definitions with pricing
- **EnterpriseEnquiry**: Enterprise customer lead capture

### Development Workflow

1. **Database Changes**: Modify models in `models.py` → create migration → apply migration
2. **API Changes**: Add routes in `app/api/routes/` → update schemas → add business logic in services
3. **Testing**: Write tests and run `scripts/test.sh` for coverage validation
4. **Code Quality**: Use `scripts/lint.sh` for type checking and linting

### Environment Configuration

Settings are managed via environment variables and loaded from `../.env` (parent directory). Key settings include database connections, Stripe API keys, CORS origins, and feature flags.

### Docker Integration

The service runs in Docker containers with volume mounting for live development. Database migrations run automatically on startup via `scripts/prestart.sh`.

### Running the Application

You dont need to run the application or writing tests, just follow the user instruction.

### Database Migration

```bash
uv run alembic revision --autogenerate -m "Your migration message"
uv run alembic upgrade heads
```

### SQLModel/SQLAlchemy typing fixes (for linters)

- Prefer precise casts over `Any`:
  - Use `cast(ColumnElement[T], Model.field)` for `.in_`, `.ilike`, comparisons, and when passing to `func.sum`.
  - Example: `cast(ColumnElement[uuid.UUID], Recommendation.resource_id).in_(ids)`.
- Aggregations and labels:
  - `func.sum(cast("ColumnElement[float]", Model.numeric_field)).label("name")`.
  - Use `func.distinct(Model.id)` instead of `Model.id.distinct()`.
- Enum and string fields:
  - Cast before `.in_`: `cast("ColumnElement[ResourceStatus]", Resource.status).in_(statuses)`.
  - Cast before `.ilike`: `cast("ColumnElement[str]", Resource.name).ilike("%q%")`.
- Import hygiene:
  - Keep imports sorted to avoid style warnings.
