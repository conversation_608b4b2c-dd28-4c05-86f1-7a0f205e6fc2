"""Update quota definitions: remove max_fast_requests_per_month, add max_premium_messages, max_daily_messages, max_scheduled_tasks, max_kb_storage

Revision ID: 676b7500c964
Revises: d3249a9b605e
Create Date: 2025-08-19 13:45:04.028988

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '676b7500c964'
down_revision = 'd3249a9b605e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.drop_constraint('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.create_foreign_key(None, 'enterprise_enquiries', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('plan_change_requests_customer_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_current_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['requested_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['current_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('prices_product_id_fkey', 'prices', type_='foreignkey')
    op.create_foreign_key(None, 'prices', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.add_column('quota_definitions', sa.Column('max_premium_messages', sa.Integer(), nullable=True))
    op.add_column('quota_definitions', sa.Column('max_daily_messages', sa.Integer(), nullable=True))
    op.add_column('quota_definitions', sa.Column('max_scheduled_tasks', sa.Integer(), nullable=True))
    op.add_column('quota_definitions', sa.Column('max_kb_storage', sa.Integer(), nullable=True))
    op.drop_constraint('quota_definitions_product_id_fkey', 'quota_definitions', type_='foreignkey')
    op.create_foreign_key(None, 'quota_definitions', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_column('quota_definitions', 'max_fast_requests_per_month')
    op.drop_constraint('subscriptions_customer_id_fkey', 'subscriptions', type_='foreignkey')
    op.drop_constraint('subscriptions_price_id_fkey', 'subscriptions', type_='foreignkey')
    op.create_foreign_key(None, 'subscriptions', 'prices', ['price_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'subscriptions', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscriptions', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'subscriptions', schema='payment', type_='foreignkey')
    op.create_foreign_key('subscriptions_price_id_fkey', 'subscriptions', 'prices', ['price_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('subscriptions_customer_id_fkey', 'subscriptions', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.add_column('quota_definitions', sa.Column('max_fast_requests_per_month', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'quota_definitions', schema='payment', type_='foreignkey')
    op.create_foreign_key('quota_definitions_product_id_fkey', 'quota_definitions', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_column('quota_definitions', 'max_kb_storage')
    op.drop_column('quota_definitions', 'max_scheduled_tasks')
    op.drop_column('quota_definitions', 'max_daily_messages')
    op.drop_column('quota_definitions', 'max_premium_messages')
    op.drop_constraint(None, 'prices', schema='payment', type_='foreignkey')
    op.create_foreign_key('prices_product_id_fkey', 'prices', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.create_foreign_key('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', 'products', ['requested_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_current_product_id_fkey', 'plan_change_requests', 'products', ['current_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_customer_id_fkey', 'plan_change_requests', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.create_foreign_key('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###
