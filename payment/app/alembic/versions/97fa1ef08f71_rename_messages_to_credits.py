"""rename messages to credits

Revision ID: 97fa1ef08f71
Revises: fd6162567403
Create Date: 2025-08-28 16:47:28.178405

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision = '97fa1ef08f71'
down_revision = 'fd6162567403'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.drop_constraint('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.create_foreign_key(None, 'enterprise_enquiries', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_current_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_customer_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.create_foreign_key(None, 'plan_change_requests', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['requested_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['current_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('prices_product_id_fkey', 'prices', type_='foreignkey')
    op.create_foreign_key(None, 'prices', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')

    # Add new columns as nullable initially to avoid NOT NULL constraint violations
    op.add_column('quota_definitions', sa.Column('max_premium_credits', sa.Integer(), nullable=True))
    op.add_column('quota_definitions', sa.Column('max_daily_credits', sa.Integer(), nullable=True))

    # Copy data from old columns to new columns if they exist
    # Check if old columns exist before copying
    connection = op.get_bind()
    inspector = inspect(connection)

    # Check if old columns exist in the payment schema
    columns = [col['name'] for col in inspector.get_columns('quota_definitions', schema='payment')]

    if 'max_premium_messages' in columns:
        op.execute('UPDATE payment.quota_definitions SET max_premium_credits = max_premium_messages WHERE max_premium_credits IS NULL')
    if 'max_daily_messages' in columns:
        op.execute('UPDATE payment.quota_definitions SET max_daily_credits = max_daily_messages WHERE max_daily_credits IS NULL')

    # Set default values for any remaining NULL values
    op.execute('UPDATE payment.quota_definitions SET max_premium_credits = 0 WHERE max_premium_credits IS NULL')
    op.execute('UPDATE payment.quota_definitions SET max_daily_credits = 0 WHERE max_daily_credits IS NULL')

    # Make the new columns NOT NULL
    op.alter_column('quota_definitions', 'max_premium_credits', nullable=False, schema='payment')
    op.alter_column('quota_definitions', 'max_daily_credits', nullable=False, schema='payment')

    op.drop_constraint('quota_definitions_product_id_fkey', 'quota_definitions', type_='foreignkey')
    op.create_foreign_key(None, 'quota_definitions', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')

    # Drop old columns if they exist
    if 'max_premium_messages' in columns:
        op.drop_column('quota_definitions', 'max_premium_messages')
    if 'max_daily_messages' in columns:
        op.drop_column('quota_definitions', 'max_daily_messages')

    op.drop_constraint('subscriptions_customer_id_fkey', 'subscriptions', type_='foreignkey')
    op.drop_constraint('subscriptions_price_id_fkey', 'subscriptions', type_='foreignkey')
    op.create_foreign_key(None, 'subscriptions', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'subscriptions', 'prices', ['price_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop new foreign keys (constraint names are auto-generated, so we need to handle this differently)
    # For downgrade, we'll recreate the original constraints
    op.drop_constraint('subscriptions_price_id_fkey', 'subscriptions', schema='payment', type_='foreignkey')
    op.drop_constraint('subscriptions_customer_id_fkey', 'subscriptions', schema='payment', type_='foreignkey')
    op.create_foreign_key('subscriptions_price_id_fkey', 'subscriptions', 'prices', ['price_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('subscriptions_customer_id_fkey', 'subscriptions', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')

    # Add old columns back
    op.add_column('quota_definitions', sa.Column('max_daily_messages', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('quota_definitions', sa.Column('max_premium_messages', sa.INTEGER(), autoincrement=False, nullable=True))

    # Copy data back from new columns to old columns
    op.execute('UPDATE payment.quota_definitions SET max_premium_messages = max_premium_credits WHERE max_premium_messages IS NULL')
    op.execute('UPDATE payment.quota_definitions SET max_daily_messages = max_daily_credits WHERE max_daily_messages IS NULL')

    # Set defaults for any NULL values
    op.execute('UPDATE payment.quota_definitions SET max_premium_messages = 0 WHERE max_premium_messages IS NULL')
    op.execute('UPDATE payment.quota_definitions SET max_daily_messages = 0 WHERE max_daily_messages IS NULL')

    # Make old columns NOT NULL
    op.alter_column('quota_definitions', 'max_premium_messages', nullable=False, schema='payment')
    op.alter_column('quota_definitions', 'max_daily_messages', nullable=False, schema='payment')

    op.drop_constraint('quota_definitions_product_id_fkey', 'quota_definitions', schema='payment', type_='foreignkey')
    op.create_foreign_key('quota_definitions_product_id_fkey', 'quota_definitions', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_column('quota_definitions', 'max_daily_credits')
    op.drop_column('quota_definitions', 'max_premium_credits')
    op.drop_constraint('prices_product_id_fkey', 'prices', schema='payment', type_='foreignkey')
    op.create_foreign_key('prices_product_id_fkey', 'prices', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('plan_change_requests_customer_id_fkey', 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint('plan_change_requests_current_product_id_fkey', 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', schema='payment', type_='foreignkey')
    op.create_foreign_key('plan_change_requests_customer_id_fkey', 'plan_change_requests', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_current_product_id_fkey', 'plan_change_requests', 'products', ['current_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', 'products', ['requested_product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.drop_constraint('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.create_foreign_key('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###

