"""Make quota fields non-nullable integers with default 0

Revision ID: fd6162567403
Revises: 676b7500c964
Create Date: 2025-08-19 14:08:50.508217

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'fd6162567403'
down_revision = '676b7500c964'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.drop_constraint('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', type_='foreignkey')
    op.create_foreign_key(None, 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'enterprise_enquiries', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('plan_change_requests_customer_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.drop_constraint('plan_change_requests_current_product_id_fkey', 'plan_change_requests', type_='foreignkey')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['current_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'products', ['requested_product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'plan_change_requests', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('prices_product_id_fkey', 'prices', type_='foreignkey')
    op.create_foreign_key(None, 'prices', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    
    # Update NULL values to 0 before making columns NOT NULL
    op.execute("UPDATE payment.quota_definitions SET max_premium_messages = 0 WHERE max_premium_messages IS NULL")
    op.execute("UPDATE payment.quota_definitions SET max_daily_messages = 0 WHERE max_daily_messages IS NULL")
    op.execute("UPDATE payment.quota_definitions SET max_scheduled_tasks = 0 WHERE max_scheduled_tasks IS NULL")
    op.execute("UPDATE payment.quota_definitions SET max_kb_storage = 0 WHERE max_kb_storage IS NULL")
    
    op.alter_column('quota_definitions', 'max_premium_messages',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('quota_definitions', 'max_daily_messages',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('quota_definitions', 'max_scheduled_tasks',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('quota_definitions', 'max_kb_storage',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_constraint('quota_definitions_product_id_fkey', 'quota_definitions', type_='foreignkey')
    op.create_foreign_key(None, 'quota_definitions', 'products', ['product_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.drop_constraint('subscriptions_price_id_fkey', 'subscriptions', type_='foreignkey')
    op.drop_constraint('subscriptions_customer_id_fkey', 'subscriptions', type_='foreignkey')
    op.create_foreign_key(None, 'subscriptions', 'prices', ['price_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    op.create_foreign_key(None, 'subscriptions', 'customers', ['customer_id'], ['id'], source_schema='payment', referent_schema='payment', ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'subscriptions', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'subscriptions', schema='payment', type_='foreignkey')
    op.create_foreign_key('subscriptions_customer_id_fkey', 'subscriptions', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('subscriptions_price_id_fkey', 'subscriptions', 'prices', ['price_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'quota_definitions', schema='payment', type_='foreignkey')
    op.create_foreign_key('quota_definitions_product_id_fkey', 'quota_definitions', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.alter_column('quota_definitions', 'max_kb_storage',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('quota_definitions', 'max_scheduled_tasks',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('quota_definitions', 'max_daily_messages',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('quota_definitions', 'max_premium_messages',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_constraint(None, 'prices', schema='payment', type_='foreignkey')
    op.create_foreign_key('prices_product_id_fkey', 'prices', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'plan_change_requests', schema='payment', type_='foreignkey')
    op.create_foreign_key('plan_change_requests_current_product_id_fkey', 'plan_change_requests', 'products', ['current_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_requested_product_id_fkey', 'plan_change_requests', 'products', ['requested_product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('plan_change_requests_customer_id_fkey', 'plan_change_requests', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint(None, 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.drop_constraint(None, 'enterprise_enquiries', schema='payment', type_='foreignkey')
    op.create_foreign_key('enterprise_enquiries_product_id_fkey', 'enterprise_enquiries', 'products', ['product_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('enterprise_enquiries_customer_id_fkey', 'enterprise_enquiries', 'customers', ['customer_id'], ['id'], ondelete='CASCADE')
    # ### end Alembic commands ###
