{"include": ["backend"], "exclude": ["backend/.venv", "backend/__pycache__", "backend/.mypy_cache", "backend/.pytest_cache", "backend/alembic"], "venvPath": "backend", "venv": ".venv", "pythonVersion": "3.12", "executionEnvironments": [{"root": "backend", "pythonVersion": "3.12", "pythonPlatform": "<PERSON>", "extraPaths": ["backend"]}], "typeCheckingMode": "basic", "reportMissingImports": "error", "reportMissingTypeStubs": "warning"}