# Heartbeat Connection Health Monitoring - Test Instructions

## Overview
The heartbeat solution has been implemented to detect unstable WiFi connections during streaming sessions. Here's how to test the functionality:

## Test Scenarios

### 1. Normal Heartbeat Flow
**Expected Behavior:**
- Backend sends heartbeat events every 10 seconds during streaming
- Frontend receives heartbeats and resets timeout (15 seconds)
- No reconnection attempts occur

**To Test:**
1. Start a streaming conversation
2. Open browser developer tools → Console
3. Look for debug messages: "Received heartbeat for conversation {id}"
4. Verify heartbeats appear every 10 seconds

### 2. Heartbeat Timeout Detection
**Expected Behavior:**
- When connection is unstable, heartbeats are missed
- After 15 seconds without heartbeat, timeout triggers
- Automatic reconnection attempt starts
- User sees toast notifications

**To Test:**
1. Start a streaming conversation
2. Simulate network instability:
   ```javascript
   // In browser console - block network requests temporarily
   const originalFetch = window.fetch;
   window.fetch = () => Promise.reject(new Error('Simulated network error'));
   
   // Restore after 20 seconds to test reconnection
   setTimeout(() => {
     window.fetch = originalFetch;
     console.log('Network restored');
   }, 20000);
   ```

### 3. Connection Recovery
**Expected Behavior:**
- When network is restored, reconnection succeeds
- Heartbeat monitoring resumes
- User sees success notification

**To Verify:**
- Check console logs for "Connection restored successfully"
- Verify streaming continues normally
- Confirm heartbeats resume

## Manual Network Testing

### WiFi Instability Simulation
1. Start streaming conversation
2. Turn off WiFi for 10-15 seconds
3. Turn WiFi back on
4. Observe automatic reconnection

### Airplane Mode Test
1. Start streaming conversation  
2. Enable airplane mode for 20 seconds
3. Disable airplane mode
4. Verify reconnection behavior

## Expected Log Messages

### Backend Logs
```
INFO: Starting heartbeat task for conversation {conversation_id}
DEBUG: Sent heartbeat #1 for conversation {conversation_id}
DEBUG: Sent heartbeat #2 for conversation {conversation_id}
INFO: Heartbeat task finished for conversation {conversation_id}
```

### Frontend Console
```
DEBUG: Received heartbeat for conversation {id}: {heartbeat_data}
LOG: Heartbeat timeout detected, attempting reconnection...
LOG: Connection restored successfully
```

### Toast Notifications
- Warning: "Connection unstable, attempting to reconnect..."
- Success: "Connection restored successfully"
- Error: "Unable to reconnect. Please refresh the page."

## Configuration Parameters

### Backend (stream_persistence.py)
- **Heartbeat Interval**: 10 seconds (`await asyncio.sleep(10)`)
- **Heartbeat Event Type**: `"heartbeat"`

### Frontend (use-autonomous-message-stream.ts)
- **Timeout Duration**: 15 seconds (10s interval + 5s buffer)
- **Reconnection Strategy**: Uses existing `reconnectToStream()` function

## Key Benefits Verified

✅ **Automatic Detection**: Detects connection issues without page navigation
✅ **Seamless Recovery**: Transparent reconnection with user feedback  
✅ **Minimal Impact**: Lightweight 10-second heartbeat events
✅ **Clean Integration**: Works with existing reconnection system
✅ **Proper Cleanup**: Timeouts cleared on stream end/component unmount

## Troubleshooting

### If heartbeats aren't appearing:
1. Check backend stream processing is active
2. Verify Redis event queuing is working
3. Confirm frontend is in streaming state

### If reconnection fails:
1. Check network connectivity
2. Verify stream status API is accessible
3. Check browser console for error details

### If timeouts occur too frequently:
1. Increase timeout duration in frontend
2. Check for network performance issues
3. Verify backend heartbeat task is running

## Implementation Files Modified

### Backend
- `/backend/app/services/agent/stream_persistence.py`
  - Added `send_heartbeat_events()` function
  - Added `start_heartbeat_task()` and `stop_heartbeat_task()` functions
  - Integrated heartbeat task lifecycle with stream processing

### Frontend  
- `/frontend/hooks/use-autonomous-message-stream.ts`
  - Added heartbeat monitoring state
  - Added heartbeat event handler
  - Implemented timeout detection and reconnection logic
  - Added comprehensive cleanup in all exit paths

The solution provides robust connection health monitoring that automatically detects and recovers from unstable network conditions during streaming sessions.